<svg width="32" height="39" viewBox="0 0 32 39" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.48" cx="16.5" cy="13.5" r="13.5" fill="#F3EFFF" fill-opacity="0.44"/>
<g filter="url(#filter0_f_24885_6981)">
<ellipse cx="14.3962" cy="20.9407" rx="10.5701" ry="0.44654" fill="#B7A9FF"/>
</g>
<g filter="url(#filter1_f_24885_6981)">
<ellipse cx="15.515" cy="20.9407" rx="6.77377" ry="0.44654" fill="#6D83FF"/>
</g>
<g filter="url(#filter2_f_24885_6981)">
<path d="M21.2465 20.8663C20.2042 21.4616 19.1623 21.2384 14.9938 21.2384C8.88997 21.2384 9.33637 21.0718 9.33637 20.8663C9.33637 20.6607 11.5405 20.4941 14.9938 20.4941C18.4471 20.4941 21.2465 20.6607 21.2465 20.8663Z" fill="url(#paint0_linear_24885_6981)"/>
</g>
<path d="M22.8877 5.7041C22.9651 5.7041 23.041 5.70981 23.1154 5.72124V5.7041H22.8877Z" fill="url(#paint1_linear_24885_6981)"/>
<path d="M17.6121 5.7041H12.678C11.7876 5.7041 11.0654 6.42841 11.0654 7.32217V20.9747H19.8672C20.7071 20.9747 21.3883 20.2915 21.3883 19.4484V6.98706C21.5033 6.26009 22.1306 5.7041 22.8874 5.7041H17.6121Z" fill="url(#paint2_linear_24885_6981)"/>
<path d="M21.3926 17.8144V7.13263C21.3926 6.29141 22.0723 5.60938 22.9106 5.60938C23.749 5.60938 24.4287 6.29141 24.4287 7.13263V8.59876H21.3926" fill="url(#paint3_linear_24885_6981)"/>
<path d="M18.3535 19.4715V18.0244H8.14453V19.4715C8.14453 20.3127 8.82425 20.9948 9.6626 20.9948H19.8716C19.0332 20.9948 18.3535 20.3127 18.3535 19.4715Z" fill="url(#paint4_linear_24885_6981)"/>
<g filter="url(#filter3_i_24885_6981)">
<path d="M19.0926 9.62793H13.286C13.1184 9.62793 12.9824 9.76433 12.9824 9.93258C12.9824 10.1008 13.1184 10.2372 13.286 10.2372H19.0926C19.2603 10.2372 19.3963 10.1008 19.3963 9.93258C19.3963 9.76433 19.2603 9.62793 19.0926 9.62793Z" fill="url(#paint5_linear_24885_6981)"/>
</g>
<g filter="url(#filter4_i_24885_6981)">
<path d="M19.1122 11.665H13.3056C13.1379 11.665 13.002 11.8014 13.002 11.9697C13.002 12.1379 13.1379 12.2743 13.3056 12.2743H19.1122C19.2799 12.2743 19.4158 12.1379 19.4158 11.9697C19.4158 11.8014 19.2799 11.665 19.1122 11.665Z" fill="url(#paint6_linear_24885_6981)"/>
</g>
<g filter="url(#filter5_i_24885_6981)">
<path d="M17.8598 13.7207H13.3056C13.1379 13.7207 13.002 13.8571 13.002 14.0254C13.002 14.1936 13.1379 14.33 13.3056 14.33H17.8598C18.0274 14.33 18.1634 14.1936 18.1634 14.0254C18.1634 13.8571 18.0274 13.7207 17.8598 13.7207Z" fill="url(#paint7_linear_24885_6981)"/>
</g>
<g filter="url(#filter6_i_24885_6981)">
<path d="M17.2146 15.625H13.3056C13.1379 15.625 13.002 15.7614 13.002 15.9297C13.002 16.0979 13.1379 16.2343 13.3056 16.2343H17.2146C17.3823 16.2343 17.5182 16.0979 17.5182 15.9297C17.5182 15.7614 17.3823 15.625 17.2146 15.625Z" fill="url(#paint8_linear_24885_6981)"/>
</g>
<path d="M22.9069 5.60938C22.0177 5.60938 21.2939 6.33558 21.2939 7.22783V19.471C21.2939 20.2585 20.6556 20.899 19.8708 20.899C19.0859 20.899 18.4476 20.2585 18.4476 19.471V18.0048H18.2578V19.471C18.2578 20.3632 18.9816 21.0894 19.8708 21.0894C20.76 21.0894 21.4837 20.3632 21.4837 19.471V8.78916H24.5198V7.22783C24.5198 6.33558 23.7961 5.60938 22.9069 5.60938ZM24.3301 8.59875H21.4837V7.22783C21.4837 6.44031 22.1221 5.79978 22.9069 5.79978C23.6917 5.79978 24.3301 6.44031 24.3301 7.22783V8.59875Z" fill="url(#paint9_linear_24885_6981)"/>
<path d="M8.14453 18.0049H18.4294" stroke="#F8FBFD" stroke-miterlimit="10"/>
<g filter="url(#filter7_d_24885_6981)">
<path d="M25.615 8.90679L26.4729 9.62021C26.5568 9.68991 26.568 9.81538 26.4979 9.8996C26.2917 10.1474 25.9237 10.1812 25.6758 9.97512L25.4112 9.75508C25.1634 9.54896 25.1296 9.18097 25.3357 8.93315C25.4058 8.84893 25.5312 8.83709 25.615 8.90679Z" fill="url(#paint10_linear_24885_6981)"/>
<g filter="url(#filter8_ii_24885_6981)">
<path d="M19.5179 16.1087L20.5047 16.9293L19.1522 17.8647C18.9679 17.9982 18.7467 17.8143 18.8441 17.6084L19.5176 16.1084L19.5179 16.1087Z" fill="url(#paint11_linear_24885_6981)"/>
</g>
<path d="M26.1264 10.1699L25.1396 9.34926C25.0329 9.26046 24.8739 9.27546 24.7847 9.38276L19.4851 15.7533C19.3958 15.8606 19.41 16.0196 19.5168 16.1084L20.5036 16.929C20.6104 17.0178 20.7693 17.0028 20.8586 16.8955L26.1582 10.5249C26.2474 10.4176 26.2332 10.2587 26.1264 10.1699Z" fill="url(#paint12_linear_24885_6981)"/>
</g>
<defs>
<filter id="filter0_f_24885_6981" x="0.0261719" y="16.6941" width="28.7396" height="8.49355" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.9" result="effect1_foregroundBlur_24885_6981"/>
</filter>
<filter id="filter1_f_24885_6981" x="4.14121" y="15.8941" width="22.7479" height="10.0936" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.3" result="effect1_foregroundBlur_24885_6981"/>
</filter>
<filter id="filter2_f_24885_6981" x="7.9291" y="19.0941" width="14.717" height="3.58516" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.7" result="effect1_foregroundBlur_24885_6981"/>
</filter>
<filter id="filter3_i_24885_6981" x="12.9824" y="8.62793" width="6.41406" height="1.60938" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.237605 0 0 0 0 0.44091 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_24885_6981"/>
</filter>
<filter id="filter4_i_24885_6981" x="13.002" y="10.665" width="6.41406" height="1.60938" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.237605 0 0 0 0 0.44091 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_24885_6981"/>
</filter>
<filter id="filter5_i_24885_6981" x="13.002" y="12.7207" width="5.16113" height="1.60938" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.237605 0 0 0 0 0.44091 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_24885_6981"/>
</filter>
<filter id="filter6_i_24885_6981" x="13.002" y="14.625" width="4.5166" height="1.60938" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.237605 0 0 0 0 0.44091 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_24885_6981"/>
</filter>
<filter id="filter7_d_24885_6981" x="11.6203" y="8.86133" width="20.1236" height="29.2488" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="14"/>
<feGaussianBlur stdDeviation="3.1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.570043 0 0 0 0 0.570043 0 0 0 0 0.916748 0 0 0 0.34 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_24885_6981"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_24885_6981" result="shape"/>
</filter>
<filter id="filter8_ii_24885_6981" x="17.8203" y="15.1084" width="2.68457" height="3.30176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.345844 0 0 0 0 0.345844 0 0 0 0 0.859261 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_24885_6981"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1.05"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.440203 0 0 0 0 0.436236 0 0 0 0 0.944043 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_24885_6981" result="effect2_innerShadow_24885_6981"/>
</filter>
<linearGradient id="paint0_linear_24885_6981" x1="8.74087" y1="20.8663" x2="21.2463" y2="20.8663" gradientUnits="userSpaceOnUse">
<stop stop-color="#7462C4"/>
<stop offset="0.535" stop-color="#8380CA"/>
<stop offset="0.955" stop-color="#C1BEF7"/>
</linearGradient>
<linearGradient id="paint1_linear_24885_6981" x1="22.8987" y1="5.7048" x2="22.8991" y2="5.72377" gradientUnits="userSpaceOnUse">
<stop stop-color="#E5E8FC"/>
<stop offset="1" stop-color="#EAEEFF"/>
</linearGradient>
<linearGradient id="paint2_linear_24885_6981" x1="11.6348" y1="6.32205" x2="17.6751" y2="20.7022" gradientUnits="userSpaceOnUse">
<stop stop-color="#D8EAFF"/>
<stop offset="1" stop-color="#E6F6FF"/>
</linearGradient>
<linearGradient id="paint3_linear_24885_6981" x1="22.9106" y1="5.60938" x2="22.9106" y2="8.43746" gradientUnits="userSpaceOnUse">
<stop stop-color="#9FCAFF"/>
<stop offset="1" stop-color="#DDEBF7"/>
</linearGradient>
<linearGradient id="paint4_linear_24885_6981" x1="11.122" y1="18.1875" x2="11.122" y2="20.9412" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5F9FE"/>
<stop offset="1" stop-color="#BED2E7"/>
</linearGradient>
<linearGradient id="paint5_linear_24885_6981" x1="14.3963" y1="9.62909" x2="14.3963" y2="10.2245" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0C2FF"/>
</linearGradient>
<linearGradient id="paint6_linear_24885_6981" x1="14.4158" y1="11.6662" x2="14.4158" y2="12.2616" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0C2FF"/>
</linearGradient>
<linearGradient id="paint7_linear_24885_6981" x1="14.1397" y1="13.7219" x2="14.1397" y2="14.3173" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0C2FF"/>
</linearGradient>
<linearGradient id="paint8_linear_24885_6981" x1="13.9975" y1="15.6262" x2="13.9975" y2="16.2215" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0C2FF"/>
</linearGradient>
<linearGradient id="paint9_linear_24885_6981" x1="21.3888" y1="5.60938" x2="21.3888" y2="21.0894" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F0F3FF"/>
</linearGradient>
<linearGradient id="paint10_linear_24885_6981" x1="25.1979" y1="9.09877" x2="26.36" y2="10.0654" gradientUnits="userSpaceOnUse">
<stop stop-color="#9DA1F5"/>
<stop offset="0.205" stop-color="#A0A2EE"/>
<stop offset="0.545455" stop-color="#8081EA"/>
<stop offset="1" stop-color="#5352D0"/>
</linearGradient>
<linearGradient id="paint11_linear_24885_6981" x1="19.2294" y1="16.7068" x2="19.9689" y2="17.322" gradientUnits="userSpaceOnUse">
<stop stop-color="#9B9BEC"/>
<stop offset="0.3125" stop-color="#B5B3FF"/>
<stop offset="1" stop-color="#5959D6"/>
</linearGradient>
<linearGradient id="paint12_linear_24885_6981" x1="22.3114" y1="12.3558" x2="23.6847" y2="13.4982" gradientUnits="userSpaceOnUse">
<stop stop-color="#AEAAF7"/>
<stop offset="0.2" stop-color="#BFBDFF"/>
<stop offset="0.538462" stop-color="#9B99F6"/>
<stop offset="0.96" stop-color="#6163D7"/>
</linearGradient>
</defs>
</svg>
