<svg width="48" height="51" viewBox="0 0 48 51" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="24" cy="24" r="24" fill="white"/>
<g filter="url(#filter0_f_25037_33754)">
<ellipse cx="20.4717" cy="35.3189" rx="16.0791" ry="0.679204" fill="#B7A9FF"/>
</g>
<g filter="url(#filter1_f_25037_33754)">
<ellipse cx="22.1772" cy="35.3189" rx="10.3042" ry="0.679204" fill="#6D83FF"/>
</g>
<g filter="url(#filter2_f_25037_33754)">
<path d="M30.8943 35.2057C29.3087 36.1113 27.7238 35.7717 21.3827 35.7717C12.0976 35.7717 12.7767 35.5182 12.7767 35.2057C12.7767 34.8931 16.1296 34.6396 21.3827 34.6396C26.6358 34.6396 30.8943 34.8931 30.8943 35.2057Z" fill="url(#paint0_linear_25037_33754)"/>
</g>
<path d="M33.3906 12.1406C33.5084 12.1406 33.6239 12.1493 33.737 12.1667V12.1406H33.3906Z" fill="url(#paint1_linear_25037_33754)"/>
<path d="M25.365 12.1406H17.8593C16.5049 12.1406 15.4062 13.2423 15.4062 14.6018V35.3678H28.7954C30.073 35.3678 31.1093 34.3286 31.1093 33.0462V14.0921C31.2842 12.9863 32.2385 12.1406 33.3897 12.1406H25.365Z" fill="url(#paint2_linear_25037_33754)"/>
<path d="M31.1172 30.5614V14.314C31.1172 13.0345 32.1512 11.9971 33.4265 11.9971C34.7018 11.9971 35.7357 13.0345 35.7357 14.314V16.544H31.1172" fill="url(#paint3_linear_25037_33754)"/>
<path d="M26.4947 33.0819V30.8809H10.9648V33.0819C10.9648 34.3615 11.9988 35.3989 13.2741 35.3989H28.804C27.5287 35.3989 26.4947 34.3615 26.4947 33.0819Z" fill="url(#paint4_linear_25037_33754)"/>
<g filter="url(#filter3_i_25037_33754)">
<path d="M27.619 18.1084H18.7861C18.531 18.1084 18.3242 18.3159 18.3242 18.5718C18.3242 18.8277 18.531 19.0352 18.7861 19.0352H27.619C27.8741 19.0352 28.0809 18.8277 28.0809 18.5718C28.0809 18.3159 27.8741 18.1084 27.619 18.1084Z" fill="url(#paint5_linear_25037_33754)"/>
</g>
<g filter="url(#filter4_i_25037_33754)">
<path d="M27.6464 21.207H18.8134C18.5583 21.207 18.3516 21.4145 18.3516 21.6704C18.3516 21.9263 18.5583 22.1338 18.8134 22.1338H27.6464C27.9015 22.1338 28.1082 21.9263 28.1082 21.6704C28.1082 21.4145 27.9015 21.207 27.6464 21.207Z" fill="url(#paint6_linear_25037_33754)"/>
</g>
<g filter="url(#filter5_i_25037_33754)">
<path d="M25.7412 24.3359H18.8134C18.5583 24.3359 18.3516 24.5434 18.3516 24.7993C18.3516 25.0552 18.5583 25.2627 18.8134 25.2627H25.7412C25.9963 25.2627 26.2031 25.0552 26.2031 24.7993C26.2031 24.5434 25.9963 24.3359 25.7412 24.3359Z" fill="url(#paint7_linear_25037_33754)"/>
</g>
<g filter="url(#filter6_i_25037_33754)">
<path d="M24.7598 27.2305H18.8134C18.5583 27.2305 18.3516 27.4379 18.3516 27.6939C18.3516 27.9498 18.5583 28.1572 18.8134 28.1572H24.7598C25.0149 28.1572 25.2216 27.9498 25.2216 27.6939C25.2216 27.4379 25.0149 27.2305 24.7598 27.2305Z" fill="url(#paint8_linear_25037_33754)"/>
</g>
<path d="M33.4178 11.9971C32.0652 11.9971 30.9642 13.1017 30.9642 14.4588V33.0811C30.9642 34.2789 29.9932 35.2532 28.7993 35.2532C27.6054 35.2532 26.6344 34.2789 26.6344 33.0811V30.851H26.3457V33.0811C26.3457 34.4382 27.4466 35.5428 28.7993 35.5428C30.152 35.5428 31.2529 34.4382 31.2529 33.0811V16.8336H35.8714V14.4588C35.8714 13.1017 34.7705 11.9971 33.4178 11.9971ZM35.5828 16.544H31.2529V14.4588C31.2529 13.261 32.224 12.2867 33.4178 12.2867C34.6117 12.2867 35.5828 13.261 35.5828 14.4588V16.544Z" fill="url(#paint9_linear_25037_33754)"/>
<path d="M10.9648 30.8506H26.6102" stroke="#F8FBFD" stroke-miterlimit="10"/>
<g filter="url(#filter7_d_25037_33754)">
<path d="M37.5381 17.0128L38.8431 18.0979C38.9706 18.2039 38.9877 18.3947 38.8811 18.5229C38.5675 18.8998 38.0076 18.9512 37.6306 18.6377L37.228 18.303C36.851 17.9895 36.7996 17.4298 37.1132 17.0528C37.2198 16.9247 37.4106 16.9067 37.5381 17.0128Z" fill="url(#paint10_linear_25037_33754)"/>
<g filter="url(#filter8_ii_25037_33754)">
<path d="M28.2681 27.9682L29.7692 29.2164L27.7118 30.6392C27.4314 30.8422 27.095 30.5624 27.2431 30.2494L28.2676 27.9678L28.2681 27.9682Z" fill="url(#paint11_linear_25037_33754)"/>
</g>
<path d="M38.3226 18.9371L36.8215 17.6889C36.659 17.5538 36.4173 17.5766 36.2815 17.7398L28.2198 27.4298C28.084 27.593 28.1056 27.8348 28.2681 27.9698L29.7692 29.218C29.9316 29.3531 30.1734 29.3302 30.3091 29.167L38.3708 19.4771C38.5066 19.3139 38.485 19.0721 38.3226 18.9371Z" fill="url(#paint12_linear_25037_33754)"/>
</g>
<defs>
<filter id="filter0_f_25037_33754" x="0.592578" y="30.8396" width="39.7582" height="8.9584" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.9" result="effect1_foregroundBlur_25037_33754"/>
</filter>
<filter id="filter1_f_25037_33754" x="7.27305" y="30.0396" width="29.8094" height="10.5584" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.3" result="effect1_foregroundBlur_25037_33754"/>
</filter>
<filter id="filter2_f_25037_33754" x="11.3656" y="33.2396" width="20.9289" height="3.99434" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.7" result="effect1_foregroundBlur_25037_33754"/>
</filter>
<filter id="filter3_i_25037_33754" x="18.3242" y="17.1084" width="9.75586" height="1.92676" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.237605 0 0 0 0 0.44091 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25037_33754"/>
</filter>
<filter id="filter4_i_25037_33754" x="18.3516" y="20.207" width="9.75586" height="1.92676" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.237605 0 0 0 0 0.44091 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25037_33754"/>
</filter>
<filter id="filter5_i_25037_33754" x="18.3516" y="23.3359" width="7.85156" height="1.92676" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.237605 0 0 0 0 0.44091 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25037_33754"/>
</filter>
<filter id="filter6_i_25037_33754" x="18.3516" y="26.2305" width="6.86914" height="1.92676" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.237605 0 0 0 0 0.44091 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25037_33754"/>
</filter>
<filter id="filter7_d_25037_33754" x="20.007" y="16.9434" width="24.1441" height="33.9646" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="14"/>
<feGaussianBlur stdDeviation="3.1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.570043 0 0 0 0 0.570043 0 0 0 0 0.916748 0 0 0 0.34 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_25037_33754"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_25037_33754" result="shape"/>
</filter>
<filter id="filter8_ii_25037_33754" x="26.207" y="26.9678" width="3.5625" height="4.24023" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.345844 0 0 0 0 0.345844 0 0 0 0 0.859261 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_25037_33754"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1.05"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.440203 0 0 0 0 0.436236 0 0 0 0 0.944043 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_25037_33754" result="effect2_innerShadow_25037_33754"/>
</filter>
<linearGradient id="paint0_linear_25037_33754" x1="11.8708" y1="35.2057" x2="30.894" y2="35.2057" gradientUnits="userSpaceOnUse">
<stop stop-color="#7462C4"/>
<stop offset="0.535" stop-color="#8380CA"/>
<stop offset="0.955" stop-color="#C1BEF7"/>
</linearGradient>
<linearGradient id="paint1_linear_25037_33754" x1="33.4073" y1="12.1417" x2="33.408" y2="12.1705" gradientUnits="userSpaceOnUse">
<stop stop-color="#E5E8FC"/>
<stop offset="1" stop-color="#EAEEFF"/>
</linearGradient>
<linearGradient id="paint2_linear_25037_33754" x1="16.2724" y1="13.0805" x2="25.4593" y2="34.954" gradientUnits="userSpaceOnUse">
<stop stop-color="#D8EAFF"/>
<stop offset="1" stop-color="#E6F6FF"/>
</linearGradient>
<linearGradient id="paint3_linear_25037_33754" x1="33.4265" y1="11.9971" x2="33.4265" y2="16.2987" gradientUnits="userSpaceOnUse">
<stop stop-color="#9FCAFF"/>
<stop offset="1" stop-color="#DDEBF7"/>
</linearGradient>
<linearGradient id="paint4_linear_25037_33754" x1="15.4942" y1="31.1289" x2="15.4942" y2="35.3174" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5F9FE"/>
<stop offset="1" stop-color="#BED2E7"/>
</linearGradient>
<linearGradient id="paint5_linear_25037_33754" x1="20.475" y1="18.1102" x2="20.475" y2="19.0158" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0C2FF"/>
</linearGradient>
<linearGradient id="paint6_linear_25037_33754" x1="20.5023" y1="21.2088" x2="20.5023" y2="22.1144" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0C2FF"/>
</linearGradient>
<linearGradient id="paint7_linear_25037_33754" x1="20.0824" y1="24.3377" x2="20.0824" y2="25.2433" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0C2FF"/>
</linearGradient>
<linearGradient id="paint8_linear_25037_33754" x1="19.866" y1="27.2322" x2="19.866" y2="28.1378" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0C2FF"/>
</linearGradient>
<linearGradient id="paint9_linear_25037_33754" x1="31.1086" y1="11.9971" x2="31.1086" y2="35.5428" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F0F3FF"/>
</linearGradient>
<linearGradient id="paint10_linear_25037_33754" x1="36.9036" y1="17.3048" x2="38.6711" y2="18.7753" gradientUnits="userSpaceOnUse">
<stop stop-color="#9DA1F5"/>
<stop offset="0.205" stop-color="#A0A2EE"/>
<stop offset="0.545455" stop-color="#8081EA"/>
<stop offset="1" stop-color="#5352D0"/>
</linearGradient>
<linearGradient id="paint11_linear_25037_33754" x1="27.8292" y1="28.878" x2="28.954" y2="29.8138" gradientUnits="userSpaceOnUse">
<stop stop-color="#9B9BEC"/>
<stop offset="0.3125" stop-color="#B5B3FF"/>
<stop offset="1" stop-color="#5959D6"/>
</linearGradient>
<linearGradient id="paint12_linear_25037_33754" x1="32.5192" y1="22.2619" x2="34.6081" y2="23.9998" gradientUnits="userSpaceOnUse">
<stop stop-color="#AEAAF7"/>
<stop offset="0.2" stop-color="#BFBDFF"/>
<stop offset="0.538462" stop-color="#9B99F6"/>
<stop offset="0.96" stop-color="#6163D7"/>
</linearGradient>
</defs>
</svg>
