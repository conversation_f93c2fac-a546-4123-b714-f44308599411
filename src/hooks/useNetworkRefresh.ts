import { ref, onMounted, onUnmounted } from 'vue';

export interface NetworkRefreshOptions {
  /**
   * 当网络恢复连接时触发
   */
  onReconnect: () => void;
  /**
   * 当网络断开时触发，可选
   */
  onDisconnect?: () => void;
  /**
   * 连续触发最小时间间隔（毫秒），用于防抖，默认 3000ms
   */
  throttleDuration?: number;
}

/**
 * 监听网络状态变化的组合式函数。
 *
 * @example
 * useNetworkRefresh({
 *   onReconnect: () => {
 *     // 网络恢复后刷新数据
 *   },
 *   onDisconnect: () => {
 *     // 断网提示
 *   },
 *   throttleDuration: 3000
 * });
 */
export default function useNetworkRefresh(options: NetworkRefreshOptions) {
  const { onReconnect, onDisconnect, throttleDuration = 3000 } = options;

  // 记录上一次触发 reconnect 的时间戳，用于节流
  const lastTrigger = ref(0);
  // 当前网络连接状态；null 表示未知
  let currentConnected: boolean | null = null;

  const handleNetworkChange = (res: UniApp.OnNetworkStatusChangeSuccess) => {
    // 状态未变化则不处理
    if (res.isConnected === currentConnected) return;

    // 更新当前状态
    currentConnected = res.isConnected;

    if (!res.isConnected) {
      // 断网
      onDisconnect?.();
      return;
    }

    // 网络恢复，进行节流控制
    const now = Date.now();
    if (now - lastTrigger.value >= throttleDuration) {
      lastTrigger.value = now;
      onReconnect();
    }
  };

  onMounted(() => {
    // 初始化当前网络状态
    uni.getNetworkType({
      success: res => {
        currentConnected = res.networkType !== 'none';
      },
      complete: () => {
        // 注册监听
        uni.onNetworkStatusChange(handleNetworkChange);
      },
    });
  });

  onUnmounted(() => {
    // 取消监听，防止内存泄漏
    uni.offNetworkStatusChange(handleNetworkChange);
  });
}
