import { ref, onUnmounted } from 'vue';
import { startVoice, stopVoice } from '@/common/ai/voice';

export interface UseSpeechOptions {
  /**
   * 语音识别结果回调
   */
  onResult?: (text: string) => void;
  /**
   * 发生错误时的回调
   */
  onError?: (error: any) => void;
  /**
   * 自动触发录音结束的最长时间(毫秒)，默认60秒
   */
  maxDuration?: number;
  /**
   * 最小音量阈值，低于此值认为没有声音
   */
  minVolume?: number;
}

/**
 * 语音识别Hook，基于common/ai/voice.ts封装
 */
export function useSpeech(options: UseSpeechOptions = {}) {
  // 状态
  const isRecording = ref(false);
  const isTranscripting = ref(false);
  const errorMessage = ref('');
  const recognizedText = ref('');
  const currentVolume = ref(0);
  const hasSound = ref(false);

  // 语音控制器
  let voiceControl: ReturnType<typeof startVoice> | null = null;
  // 自动停止录音的计时器
  let autoStopTimer: number | null = null;
  // 音量检测计时器
  let volumeCheckTimer: number | null = null;

  // 检查音量
  const checkVolume = () => {
    const recorder = uni.getRecorderManager();
    recorder.onFrameRecorded(res => {
      const { frameBuffer, isLastFrame } = res;
      if (frameBuffer && frameBuffer.length > 0) {
        // 计算音量
        let sum = 0;
        for (let i = 0; i < frameBuffer.length; i++) {
          sum += Math.abs(frameBuffer[i]);
        }
        const volume = sum / frameBuffer.length;
        currentVolume.value = volume;

        // 判断是否有声音
        const minVolume = options.minVolume || 10;
        hasSound.value = volume > minVolume;

        console.log('当前音量:', volume, '是否有声音:', hasSound.value);
      }
    });
  };

  // 开始录音
  const startRecord = () => {
    console.log('开始录音');
    // 如果已经在录音，先停止
    if (isRecording.value) {
      stopRecord();
    }

    // 重置状态
    errorMessage.value = '';
    recognizedText.value = '';
    isRecording.value = true;
    isTranscripting.value = false;
    currentVolume.value = 0;
    hasSound.value = false;

    try {
      // 开始音量检测
      checkVolume();

      voiceControl = startVoice({
        onStart: () => {
          console.log('语音识别开始录音');
          isRecording.value = true;
          errorMessage.value = '';

          // 设置自动停止
          if (options.maxDuration) {
            if (autoStopTimer) clearTimeout(autoStopTimer);
            autoStopTimer = setTimeout(() => {
              console.log('达到最大录音时间，自动停止');
              stopRecord();
            }, options.maxDuration) as unknown as number;
          }
        },
        onResult: text => {
          console.log('语音识别结果:', text);
          isRecording.value = false;
          isTranscripting.value = false;
          recognizedText.value = text;

          // 清理定时器
          if (autoStopTimer) {
            clearTimeout(autoStopTimer);
            autoStopTimer = null;
          }

          // 调用外部结果回调
          options.onResult?.(text);

          // 清理
          voiceControl = null;
        },
        onCancel: () => {
          console.log('语音识别取消');
          isRecording.value = false;
          isTranscripting.value = false;

          // 清理定时器
          if (autoStopTimer) {
            clearTimeout(autoStopTimer);
            autoStopTimer = null;
          }

          // 清理
          voiceControl = null;
        },
        onError: error => {
          console.error('语音识别错误:', error);
          isRecording.value = false;
          isTranscripting.value = false;

          // 清理定时器
          if (autoStopTimer) {
            clearTimeout(autoStopTimer);
            autoStopTimer = null;
          }

          if (error?.errMsg) {
            errorMessage.value = error.errMsg;
          } else if (typeof error === 'string') {
            errorMessage.value = error;
          } else {
            errorMessage.value = '语音识别出错';
          }

          // 调用外部错误回调
          options.onError?.(error);

          // 清理
          voiceControl = null;
        },
      });
    } catch (error) {
      console.error('启动语音识别失败:', error);
      isRecording.value = false;
      isTranscripting.value = false;
      errorMessage.value = '启动语音识别失败';
      options.onError?.(error);
    }
  };

  // 停止录音
  const stopRecord = () => {
    console.log('停止录音');
    if (!isRecording.value) {
      console.log('当前未在录音，无需停止');
      return;
    }

    // 先重置录音状态，确保UI立即响应
    isRecording.value = false;
    isTranscripting.value = true; // 立即设置为转写状态

    // 清除自动停止计时器
    if (autoStopTimer) {
      clearTimeout(autoStopTimer);
      autoStopTimer = null;
    }

    // 清除音量检测计时器
    if (volumeCheckTimer) {
      clearInterval(volumeCheckTimer);
      volumeCheckTimer = null;
    }

    // 停止录音
    if (voiceControl) {
      console.log('停止语音识别');
      voiceControl.stop();
    } else {
      // 如果没有voiceControl，直接重置状态
      console.log('没有voiceControl，直接重置状态');
      isTranscripting.value = false;
    }
  };

  // 取消录音
  const cancelRecord = () => {
    console.log('取消录音');
    if (!isRecording.value) {
      console.log('当前未在录音，无需取消');
      return;
    }

    // 先重置录音状态，确保UI立即响应
    isRecording.value = false;
    isTranscripting.value = false;

    // 清除自动停止计时器
    if (autoStopTimer) {
      clearTimeout(autoStopTimer);
      autoStopTimer = null;
    }

    // 清除音量检测计时器
    if (volumeCheckTimer) {
      clearInterval(volumeCheckTimer);
      volumeCheckTimer = null;
    }

    // 取消录音
    if (voiceControl) {
      console.log('取消语音识别');
      voiceControl.stop('cancel');
      voiceControl = null;
    }
  };

  // 获取当前的识别状态
  const getState = () => {
    return voiceControl?.state || '';
  };

  // 组件卸载时清理资源
  onUnmounted(() => {
    console.log('组件卸载，清理资源');
    if (autoStopTimer) {
      clearTimeout(autoStopTimer);
      autoStopTimer = null;
    }

    if (volumeCheckTimer) {
      clearInterval(volumeCheckTimer);
      volumeCheckTimer = null;
    }

    if (voiceControl) {
      voiceControl.stop('cancel');
      voiceControl = null;
    }

    isRecording.value = false;
    isTranscripting.value = false;
  });

  return {
    // 状态
    isRecording,
    isTranscripting,
    errorMessage,
    recognizedText,
    currentVolume,
    hasSound,

    // 方法
    startRecord,
    stopRecord,
    cancelRecord,
    getState,
  };
}
