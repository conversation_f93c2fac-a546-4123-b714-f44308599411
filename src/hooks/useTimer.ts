import { ref, onMounted, onUnmounted, computed } from 'vue';

interface TimerConfig {
  initialSeconds?: number; // 初始秒数
  autoStart?: boolean; // 是否自动开始计时
  onTick?: (seconds: number) => void; // 每次计时更新时的回调函数
  interval?: number; // 计时间隔（毫秒），默认1000ms
  maxSeconds?: number; // 可选：最大秒数，达到后停止计时
  onFinished?: () => void; // 可选：计时结束时的回调函数
}

/**
 * 计时器Hook
 * @description 可用于计算做题时间，支持最大秒数限制
 * @param options 配置选项
 * @example
 * // 基础用法
 * const timer = useTimer({
 *   onTick: (seconds) => console.log('当前计时：', seconds)
 * });
 *
 * // 带最大秒数的用法
 * const timer = useTimer({
 *   maxSeconds: 3600, // 1小时
 *   onFinished: () => console.log('达到最大时间！')
 * });
 *
 * // 外部计算截止时间
 * const deadlineTime = "2025-08-30 21:00:00";
 * const deadlineTimestamp = new Date(deadlineTime).getTime();
 * const now = Date.now();
 * const maxSeconds = Math.floor((deadlineTimestamp - now) / 1000);
 *
 * const timer = useTimer({
 *   maxSeconds: maxSeconds,
 *   onFinished: () => console.log('时间到！')
 * });
 *
 * @returns 计时器相关方法和状态
 */
export function useTimer(options: TimerConfig = {}) {
  const {
    initialSeconds = 0,
    autoStart = false,
    onTick,
    interval = 1000, // 默认1秒
    maxSeconds,
    onFinished,
  } = options;

  // 当前配置
  const currentConfig = ref<TimerConfig>({
    initialSeconds,
    autoStart,
    onTick,
    interval,
    maxSeconds,
    onFinished,
  });

  // 累计秒数
  const seconds = ref(initialSeconds);
  // 计时器ID
  const timerId = ref<number | null>(null);
  // 是否正在计时
  const isRunning = ref(false);
  // 实际计时间隔
  const actualInterval = ref(interval);

  // 格式化时间显示
  const formattedTime = computed(() => {
    const hours = Math.floor(seconds.value / 3600);
    const minutes = Math.floor((seconds.value % 3600) / 60);
    const secs = seconds.value % 60;

    const hoursStr = hours.toString().padStart(2, '0');
    const minutesStr = minutes.toString().padStart(2, '0');
    const secsStr = secs.toString().padStart(2, '0');

    let resultTime = '';
    if (hours > 0) {
      resultTime = `${hoursStr}:${minutesStr}:${secsStr}`;
    } else {
      resultTime = `${minutesStr}:${secsStr}`;
    }

    return resultTime;
  });

  /**
   * 检查是否达到最大秒数
   * @returns true 表示已达到最大秒数，false 表示未达到
   */
  const checkMaxSeconds = (): boolean => {
    if (currentConfig.value.maxSeconds && seconds.value >= currentConfig.value.maxSeconds) {
      // 达到最大秒数，停止计时并触发回调
      pause();
      currentConfig.value.onFinished?.();
      return true;
    }
    return false;
  };

  /**
   * 开始计时
   * @description 如果设置了最大秒数且已达到，则不会启动计时
   */
  const start = () => {
    if (isRunning.value) return;

    // 检查是否已达到最大秒数
    if (checkMaxSeconds()) return;

    isRunning.value = true;

    // 计算每次间隔应该增加的秒数
    const secondsIncrement = actualInterval.value / 1000;

    timerId.value = setInterval(() => {
      // 每次计时前检查最大秒数
      if (checkMaxSeconds()) return;

      // 根据实际间隔增加秒数
      seconds.value += secondsIncrement;

      // 执行回调
      if (currentConfig.value.onTick) {
        currentConfig.value.onTick(seconds.value);
      }
    }, actualInterval.value) as unknown as number;
  };

  /**
   * 暂停计时
   */
  const pause = () => {
    if (!isRunning.value) return;

    clearInterval(timerId.value!);
    timerId.value = null;
    isRunning.value = false;
  };

  /**
   * 恢复计时
   * @description 如果设置了最大秒数且已达到，则不会恢复计时
   */
  const resume = () => {
    if (isRunning.value) return;
    start();
  };

  /**
   * 重置计时器
   * @param newInitialSeconds 新的初始秒数，默认为0
   */
  const reset = (newInitialSeconds = 0) => {
    pause();
    seconds.value = newInitialSeconds;
    if (currentConfig.value.autoStart) {
      start();
    }
  };

  /**
   * 获取当前累计秒数
   * @returns 当前累计的秒数
   */
  const getSeconds = () => seconds.value;

  /**
   * 设置累计秒数
   * @param newSeconds 新的秒数
   */
  const setSeconds = (newSeconds: number) => {
    seconds.value = newSeconds;
    // 设置时也执行回调
    if (currentConfig.value.onTick) {
      currentConfig.value.onTick(seconds.value);
    }
  };

  /**
   * 获取格式化后的时间字符串
   * @returns 格式化后的时间字符串，格式为 "HH:MM:SS" 或 "MM:SS"
   */
  const getFormattedTime = () => {
    return formattedTime.value.replace(/"/g, '');
  };

  /**
   * 设置计时器配置
   * @param config 新的配置选项
   */
  const setConfig = (config: Partial<TimerConfig>): void => {
    // 合并配置
    Object.assign(currentConfig.value, config);

    // 同步更新相关状态
    if (config.interval !== undefined) {
      actualInterval.value = config.interval;
    }

    if (config.initialSeconds !== undefined) {
      seconds.value = config.initialSeconds;
    }
  };

  /**
   * 获取当前配置
   * @returns 当前配置的只读副本
   */
  const getConfig = (): Readonly<TimerConfig> => {
    return { ...currentConfig.value };
  };

  /**
   * 获取剩余秒数
   * @returns 剩余秒数，如果未设置最大秒数则返回 null
   */
  const getRemainingSeconds = (): number | null => {
    if (!currentConfig.value.maxSeconds) return null;
    return Math.max(0, currentConfig.value.maxSeconds - seconds.value);
  };

  /**
   * 检查是否已达到最大秒数
   * @returns true 表示已达到最大秒数，false 表示未达到或未设置最大秒数
   */
  const isMaxSecondsReached = (): boolean => {
    if (!currentConfig.value.maxSeconds) return false;
    return seconds.value >= currentConfig.value.maxSeconds;
  };

  /**
   * 设置最大秒数
   * @param maxSeconds 最大秒数
   */
  const setMaxSeconds = (maxSeconds: number) => {
    currentConfig.value.maxSeconds = maxSeconds;
  };

  /**
   * 彻底清理计时器资源
   * @description 停止计时器并重置所有状态
   */
  const destroy = () => {
    // 停止计时器
    pause();

    // 清理所有响应式引用
    seconds.value = 0;
    timerId.value = null;
    isRunning.value = false;

    // 重置配置
    currentConfig.value = {
      initialSeconds: 0,
      autoStart: false,
      interval: 1000,
      onTick: undefined,
      maxSeconds: undefined,
      onFinished: undefined,
    };
  };

  // 组件挂载时，如果设置了自动开始，则启动计时器
  onMounted(() => {
    if (currentConfig.value.autoStart) {
      start();
    }
  });

  // 组件卸载时，清除计时器
  onUnmounted(() => {
    if (timerId.value) {
      clearInterval(timerId.value);
    }
  });

  return {
    seconds,
    isRunning,
    formattedTime,
    getFormattedTime,
    start,
    pause,
    resume,
    reset,
    getSeconds,
    setSeconds,
    setConfig,
    getConfig,
    destroy,
    interval: actualInterval,
    // 最大秒数相关方法
    getRemainingSeconds,
    isMaxSecondsReached,
    setMaxSeconds,
  };
}
