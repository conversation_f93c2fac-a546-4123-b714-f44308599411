// swiper 数组渲染优化
/**
 * @description 大量swiper数据渲染导致dom节点过多问题优化-动态控制渲染的swiper渲染
 * 目前默认按 virtualCount 个swiper-item进行轮播渲染（默认为3）
 * 根据当前data-current对应的数据项，动态维护数据项前一项和后一项内容
 * 然后数据子集根据swiper当前current值进行偏移对齐，保证swiper展示item与数据项对齐。
 *
 * @warning acceleration 属性必须关闭否则容易存在渲染结果偏差
 * 由于这种对数据子集进行偏移的操作，对于数据项总数有要求，否则在首尾位置时可能偏移后与实际数据顺序又不一致，
 * 因此对于首尾位置需要进行特殊处理，子集数量可以大于virtualCount，但不超过virtualCount+2.
 */

import { unref, ref, watch, computed, onMounted, nextTick } from 'vue';
import type { Ref } from 'vue';

/**
 * acceleration
 */

export type SwiperItem = Record<string, any>;

export interface VirtualSwiperProps {
  defaultCurrent?: number;
  data: Array<string | number | SwiperItem>;
  circular?: boolean;
  keyField?: string;
  duration?: number;
  ignoreChangeByManual?: boolean;
  triggerWhenMounted?: boolean;
  /** 默认渲染的虚拟 DOM 数量，默认为 3 */
  virtualCount?: number;
}

export interface VirtualSwiperReturn {
  swiperCurrent: Ref<number>;
  swiperCurrentTemp: Ref<number>;
  dataCurrent: Ref<number>;
  currentKey: Ref<string | number | undefined>;
  currentSwipers: Ref<SwiperItem[]>;
  finalyKeyField: Ref<string>;
  finalyDuration: Ref<number>;
  finalyCircular: Ref<boolean>;
  /** 当前使用的虚拟 DOM 数量 */
  virtualCount: number;
  onSwiperChange: (e: { detail: { current: number } }) => void;
  scrollIntoSwiper: (index: number) => void;
  index2Current: (index: number) => number;
}

export interface VirtualSwiperEmits {
  (
    event: 'swiper-change',
    key: string | number,
    dataCurrent: number,
    swiperCurrent: number,
    init: boolean
  ): void;
  (
    event: 'current-change',
    key: string | number,
    dataCurrent: number,
    prevCurrent: number,
    nextCurrent: number
  ): void;
}

export default function useVirtualSwiper(
  props: Ref<VirtualSwiperProps>,
  emits?: VirtualSwiperEmits
): VirtualSwiperReturn {
  let _durationTimeout: ReturnType<typeof setTimeout> | undefined;
  let _swiperTimeout: ReturnType<typeof setTimeout> | undefined;

  const changeManualFlag = ref(false);
  const isAnimating = ref(false); // 防抖状态标识
  const lastChangeTime = ref(0); // 记录最后一次切换时间
  const defaultCurrent = unref(props).defaultCurrent ?? 0;
  const defaultDuration = unref(props).duration ?? 250;
  const defaultCircular = unref(props).circular ?? false;
  const dataCurrent = ref(defaultCurrent);
  const swiperCurrent = ref(0);
  const swiperCurrentTemp = ref(0);
  const currentKey = ref<string | number | undefined>();
  const currentSwipers: Ref<SwiperItem[]> = ref([]);
  const finalyKeyField = ref(unref(props).keyField ?? 'id');
  const finalyDuration = ref(defaultDuration);
  const finalyCircular = ref(defaultCircular);

  const normalizeData = computed(() => {
    return unref(props).data.map((item: string | number | SwiperItem) => {
      if (typeof item === 'object') {
        const result = { ...item } as SwiperItem;
        result._id = result[unref(finalyKeyField) as keyof typeof result] ?? '';
        return result;
      }
      return {
        _id: item,
        [unref(finalyKeyField)]: item,
      } as SwiperItem;
    });
  });

  const virtualCount = unref(props).virtualCount ?? 3;
  const swiperCounts = computed(() => unref(normalizeData).length);
  const swiperOffset = computed(() => unref(swiperCounts) % virtualCount);
  const swiperStartOffset = computed(() => 0 + unref(swiperOffset));
  const swiperEndOffset = computed(() => unref(swiperCounts) - 1 - unref(swiperOffset));
  // 确保最大渲染数量不超过 virtualCount
  const swiperMaxCounts = computed(() => virtualCount);

  watch(
    dataCurrent,
    val => {
      emits &&
        emits(
          'current-change',
          currentKey.value as string | number,
          val,
          getPrevIndex(val, unref(swiperCounts)),
          getNextIndex(val, unref(swiperCounts))
        );
    },
    { immediate: false }
  );

  // swiper 绑定 change事件
  function onSwiperChange(e: any) {
    const currentTime = Date.now();

    // 防抖：如果距离上次切换时间过短，则忽略
    if (isAnimating.value || currentTime - lastChangeTime.value < defaultDuration) {
      return;
    }

    // 记录切换时间并设置动画状态
    lastChangeTime.value = currentTime;
    isAnimating.value = true;

    // 清理之前的定时器
    if (_swiperTimeout) {
      clearTimeout(_swiperTimeout);
    }
    if (_durationTimeout) {
      clearTimeout(_durationTimeout);
    }

    swiperCurrent.value = e.detail.current;

    if (changeManualFlag.value) {
      changeManualFlag.value = false;
      // 手动切换时立即重置状态
      setTimeout(() => {
        isAnimating.value = false;
      }, 50);
    } else {
      // 自动切换逻辑优化：减少duration的动态变化
      _swiperTimeout = setTimeout(() => {
        updateCurrentSwiper();

        // 动画完成后重置状态
        setTimeout(() => {
          isAnimating.value = false;
        }, defaultDuration);
      }, 50);
    }
  }

  function getCurrentIndex(index: number, len: number) {
    return !len ? 0 : (len + index) % len;
  }

  function getNextIndex(index: number, len: number, step = 1) {
    return !len ? 0 : (index + step) % len;
  }

  function getPrevIndex(index: number, len: number, step = 1) {
    return !len ? 0 : (len + (index - step)) % len;
  }

  function shiftLeft(arr: Array<any>, step: number): Array<any> {
    const result: Array<any> = [];

    for (let i = 0; i < arr.length; i++) {
      result[i] = arr[getNextIndex(i, arr.length, step)];
    }

    return result;
  }

  function shiftRight(arr: Array<any>, step: number): Array<any> {
    const result: Array<any> = [];

    for (let i = 0; i < arr.length; i++) {
      result[i] = arr[getPrevIndex(i, arr.length, step)];
    }

    return result;
  }

  // 偏移swiper-item数据
  function shiftSwipers(swipers: SwiperItem[]): SwiperItem[] {
    const direction = 1 - unref(swiperCurrent);

    if (direction === 1) {
      // 向左
      return shiftLeft(swipers, 1);
    } else if (direction === -1) {
      // 向右
      return shiftRight(swipers, 1);
    }
    return swipers;
  }

  // 更新渲染的swiper-item数据
  function updateCurrentSwiper() {
    const key = unref(currentSwipers)[unref(swiperCurrent)]?.[unref(finalyKeyField)] as
      | string
      | number;
    const current = unref(normalizeData).findIndex(
      (item: SwiperItem) => item[unref(finalyKeyField)] === key
    );

    updateDataCurrent(current);
  }

  // 更新可循环swiper数据的计算方法
  function updateCurrentSwiperByCircle() {
    let circular = defaultCircular;
    if (unref(swiperCounts) < virtualCount) {
      swiperCurrent.value = unref(dataCurrent);
      currentSwipers.value = unref(normalizeData);
    } else {
      const cIndex = unref(dataCurrent);
      const sIndex = getPrevIndex(cIndex, unref(swiperCounts));
      const eIndex = getNextIndex(cIndex, unref(swiperCounts));
      const newCurrentSwipers = [
        unref(normalizeData)[sIndex],
        unref(normalizeData)[cIndex],
        unref(normalizeData)[eIndex],
      ];
      currentSwipers.value = shiftSwipers(newCurrentSwipers);
    }
    finalyCircular.value = circular;
    nextTick(() => {
      swiperCurrentTemp.value = swiperCurrent.value;
    });
    console.log('>>>swiper circle', {
      dataCurrent: dataCurrent.value,
      swiperCurrent: swiperCurrent.value,
      currentSwipersLength: currentSwipers.value.length,
      virtualCount,
      swiperCounts: unref(swiperCounts),
    });
  }

  // 更新不可循环swiper数据的计算方法
  function updateCurrentSwiperByDefault() {
    let circular = defaultCircular;
    const totalCount = unref(swiperCounts);
    const currentIndex = unref(dataCurrent);

    if (totalCount <= virtualCount) {
      // 数据量小于等于虚拟数量，全部渲染
      swiperCurrent.value = currentIndex;
      currentSwipers.value = unref(normalizeData);
    } else {
      // 数据量大于虚拟数量，使用虚拟滚动
      if (currentIndex < virtualCount - 1) {
        // 开头：渲染前 virtualCount 个
        currentSwipers.value = unref(normalizeData).slice(0, virtualCount);
        swiperCurrent.value = currentIndex;
      } else if (currentIndex >= totalCount - (virtualCount - 1)) {
        // 结尾：渲染后 virtualCount 个
        currentSwipers.value = unref(normalizeData).slice(-virtualCount);
        swiperCurrent.value = currentIndex - (totalCount - virtualCount);
      } else {
        // 中间：渲染前一个、当前、后一个
        const prevIndex = currentIndex - 1;
        const nextIndex = currentIndex + 1;
        currentSwipers.value = [
          unref(normalizeData)[prevIndex],
          unref(normalizeData)[currentIndex],
          unref(normalizeData)[nextIndex],
        ];
        swiperCurrent.value = 1; // 中间位置
        circular = true;
      }
    }

    finalyCircular.value = circular;
    nextTick(() => {
      swiperCurrentTemp.value = swiperCurrent.value;
    });
    // console.log('>>>swiper default', {
    //   dataCurrent: dataCurrent.value,
    //   swiperCurrent: swiperCurrent.value,
    //   currentSwipersLength: currentSwipers.value.length,
    //   virtualCount,
    //   swiperCounts: unref(swiperCounts),
    //   totalCount,
    //   currentIndex,
    // });
  }

  // 更新渲染的swiper-item数据集
  function updateDataCurrent(index: number, trigger = true, init = false) {
    dataCurrent.value = getCurrentIndex(index, unref(swiperCounts));
    const item = unref(normalizeData)[unref(dataCurrent)];
    if (item) {
      const keyField = unref(finalyKeyField);
      currentKey.value = item[keyField] as string | number;
    }

    if (defaultCircular) {
      updateCurrentSwiperByCircle();
    } else {
      updateCurrentSwiperByDefault();
    }

    setTimeout(() => {
      trigger &&
        emits &&
        emits(
          'swiper-change',
          unref(currentKey) as string | number,
          unref(dataCurrent),
          unref(swiperCurrent),
          init
        );
    }, 50);
  }

  function index2Current(index: number) {
    if (defaultCircular) {
      return index % virtualCount;
    } else {
      const totalCount = unref(swiperCounts);
      if (totalCount <= virtualCount) {
        return index;
      } else {
        if (index < virtualCount - 1) {
          // 开头位置
          return index;
        } else if (index >= totalCount - (virtualCount - 1)) {
          // 结尾位置
          return index - (totalCount - virtualCount);
        } else {
          // 中间位置，始终返回1（中间位置）
          return 1;
        }
      }
    }
  }

  /**
   * @description滚动到指定索引的数据项
   * @param index 滚动到指定数据索引item
   * @param trigger 是否触发swiper-change事件
   * @param init 初始化渲染标识（mounted阶段），避免初始化swiper不会触发swiper-change事件导致 changeManualFlag 标识未重置。
   */
  function scrollIntoSwiper(index: number, trigger = false, init = false) {
    // 如果正在动画中且不是初始化，则忽略操作
    if (!init && isAnimating.value) {
      return;
    }

    const oldCurrent = swiperCurrent.value;
    const newCurrent = index2Current(index);
    if (oldCurrent !== newCurrent) {
      swiperCurrent.value = newCurrent; // 触发 swiper change 事件
      if (!init && unref(props).ignoreChangeByManual) {
        changeManualFlag.value = true;
      }
    }
    updateDataCurrent(index, trigger, init);
  }

  onMounted(() => {
    scrollIntoSwiper(
      getCurrentIndex(defaultCurrent, unref(swiperCounts)),
      !!unref(props).triggerWhenMounted,
      true
    );
  });

  return {
    finalyKeyField,
    finalyDuration,
    finalyCircular,
    currentKey,
    dataCurrent,
    swiperCurrent,
    swiperCurrentTemp,
    currentSwipers,
    virtualCount,
    onSwiperChange,
    scrollIntoSwiper,
    index2Current,
  };
}
