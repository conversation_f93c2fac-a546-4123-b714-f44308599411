// 创建事件总线
interface EventBus {
  // 存储事件及其回调函数
  events: Record<string, Function[]>;
  // 监听事件
  on(event: string, callback: Function): void;
  // 触发事件
  emit(event: string, ...args: any[]): void;
  // 移除监听
  off(event: string, callback?: Function): void;
}

// 创建全局单例
const eventBus: EventBus = {
  events: {} as Record<string, Function[]>,

  on(event: string, callback: Function) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  },

  emit(event: string, ...args: any[]) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(...args));
    }
  },

  off(event: string, callback?: Function) {
    if (!callback) {
      // 如果没有提供回调函数，则移除该事件的所有回调
      delete this.events[event];
    } else if (this.events[event]) {
      // 移除特定回调
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
  },
};

// 导出事件总线的组合式函数
export function useEventBus() {
  return eventBus;
}
