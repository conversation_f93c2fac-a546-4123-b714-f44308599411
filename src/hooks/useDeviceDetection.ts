import { ref, computed, onMounted } from 'vue';

/**
 * 设备检测 Hook
 * 基于 uni.getDeviceInfo() 官方API进行设备类型检测
 * 支持检测手机、平板、桌面设备
 */
export function useDeviceDetection() {
  // 设备信息
  const deviceInfo = ref<UniApp.GetDeviceInfoResult | null>(null);

  // 系统信息（用于获取屏幕尺寸等补充信息）
  const systemInfo = ref<UniApp.GetSystemInfoResult | null>(null);

  // 设备类型：扩展支持桌面设备
  const deviceType = ref<'mobile' | 'tablet' | 'desktop'>('mobile');

  // 设备方向
  const deviceOrientation = ref<'portrait' | 'landscape'>('portrait');

  // 是否为平板设备
  const isTablet = computed(() => deviceType.value === 'tablet');

  // 是否为手机设备
  const isMobile = computed(() => deviceType.value === 'mobile');

  // 是否为桌面设备
  const isDesktop = computed(() => deviceType.value === 'desktop');

  // 屏幕宽度
  const screenWidth = computed(() => systemInfo.value?.screenWidth || 0);

  // 屏幕高度
  const screenHeight = computed(() => systemInfo.value?.screenHeight || 0);

  // 屏幕宽高比
  const aspectRatio = computed(() => {
    const width = screenWidth.value;
    const height = screenHeight.value;
    return width > 0 ? height / width : 0;
  });

  // 设备像素比
  const devicePixelRatio = computed(() => deviceInfo.value?.devicePixelRatio || 1);

  /**
   * 检测设备类型
   * 优先使用 uni.getDeviceInfo() 官方API进行准确检测
   * 降级策略：API不可用时使用屏幕尺寸判断
   */
  const detectDeviceType = () => {
    try {
      // 获取设备信息
      const devInfo = uni.getDeviceInfo();
      deviceInfo.value = devInfo;

      // 获取系统信息（屏幕尺寸等）
      const sysInfo = uni.getSystemInfoSync();
      systemInfo.value = sysInfo;

      // 优先使用官方API的deviceType字段
      if (devInfo.deviceType) {
        // 映射官方设备类型到我们的类型定义
        const deviceTypeMap: Record<string, 'mobile' | 'tablet' | 'desktop'> = {
          phone: 'mobile',
          pad: 'tablet',
          pc: 'desktop',
        };

        deviceType.value = deviceTypeMap[devInfo.deviceType] || 'mobile';
      } else {
        // 降级到基于屏幕尺寸的判断
        const width = sysInfo.screenWidth;
        const height = sysInfo.screenHeight;
        const ratio = width > 0 ? height / width : 0;

        if (width >= 768) {
          deviceType.value = 'tablet';
        } else if (ratio < 1.75 && width >= 600) {
          deviceType.value = 'tablet';
        } else {
          deviceType.value = 'mobile';
        }
      }

      // 设置设备方向
      if (devInfo.deviceOrientation) {
        deviceOrientation.value = devInfo.deviceOrientation as 'portrait' | 'landscape';
      } else {
        // 基于屏幕尺寸推测方向
        const width = sysInfo.screenWidth;
        const height = sysInfo.screenHeight;
        deviceOrientation.value = width > height ? 'landscape' : 'portrait';
      }

      console.log('设备检测结果:', {
        deviceType: deviceType.value,
        deviceOrientation: deviceOrientation.value,
        screenWidth: sysInfo.screenWidth,
        screenHeight: sysInfo.screenHeight,
        aspectRatio: aspectRatio.value,
        platform: sysInfo.platform,
        deviceBrand: devInfo.deviceBrand,
        deviceModel: devInfo.deviceModel,
        usingOfficialAPI: !!devInfo.deviceType,
      });
    } catch (error) {
      console.error('设备检测失败:', error);
      // 默认为手机设备
      deviceType.value = 'mobile';
      deviceOrientation.value = 'portrait';
    }
  };

  /**
   * 获取响应式样式类名
   * @returns 设备类型对应的CSS类名
   */
  const getDeviceClass = () => {
    return `device-${deviceType.value}`;
  };

  /**
   * 获取设备特定的尺寸
   * @param mobileSize 手机端尺寸
   * @param tabletSize 平板端尺寸
   * @param desktopSize 桌面端尺寸（可选）
   * @returns 当前设备对应的尺寸
   */
  const getResponsiveSize = (
    mobileSize: number | string,
    tabletSize: number | string,
    desktopSize?: number | string
  ) => {
    if (isDesktop.value && desktopSize !== undefined) {
      return desktopSize;
    }
    return isTablet.value ? tabletSize : mobileSize;
  };

  /**
   * 获取设备特定的配置
   * @param mobileConfig 手机端配置
   * @param tabletConfig 平板端配置
   * @param desktopConfig 桌面端配置（可选）
   * @returns 当前设备对应的配置
   */
  const getResponsiveConfig = <T>(mobileConfig: T, tabletConfig: T, desktopConfig?: T): T => {
    if (isDesktop.value && desktopConfig !== undefined) {
      return desktopConfig;
    }
    return isTablet.value ? tabletConfig : mobileConfig;
  };

  /**
   * 获取设备详细信息
   * @returns 设备的详细信息对象
   */
  const getDeviceDetails = () => {
    return {
      type: deviceType.value,
      orientation: deviceOrientation.value,
      brand: deviceInfo.value?.deviceBrand || 'unknown',
      model: deviceInfo.value?.deviceModel || 'unknown',
      platform: systemInfo.value?.platform || 'unknown',
      pixelRatio: devicePixelRatio.value,
      screen: {
        width: screenWidth.value,
        height: screenHeight.value,
        aspectRatio: aspectRatio.value,
      },
    };
  };

  // 组件挂载时检测设备
  onMounted(() => {
    detectDeviceType();
  });

  return {
    // 响应式数据
    deviceInfo,
    systemInfo,
    deviceType,
    deviceOrientation,
    isTablet,
    isMobile,
    isDesktop,
    screenWidth,
    screenHeight,
    aspectRatio,
    devicePixelRatio,

    // 方法
    detectDeviceType,
    getDeviceClass,
    getResponsiveSize,
    getResponsiveConfig,
    getDeviceDetails,
  };
}

export default useDeviceDetection;
