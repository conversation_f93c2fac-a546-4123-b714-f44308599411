import type {
  LoginResponse,
  TenantInfo,
  WechatLoginParams,
  BindPhoneParams,
  ResetFirstPasswordParams,
  EducationInfo,
} from '@/types/api/auth';

const getHttp = () => uni.$u.http;

// 获取登录验证码
export function getSmsCode(params: any) {
  return getHttp().post(`/huayun-ai/app/auth/sms/code`, params);
}

// 验证登录
export function getAuthValid(params: {
  mobile: string;
  code: string;
  tenantId?: string;
}): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/valid', params, {
    custom: {
      toast: false,
    },
  });
}

// 获取验证码
export function getCaptcha(params: any) {
  return getHttp().post('/huayun-ai/app/auth/getCaptcha', params, {
    headers: {
      'content-type': 'application/json',
    },
  });
}

// 校验验证码
export function validCaptcha(params: any) {
  return getHttp().post(
    `/huayun-ai/app/auth/getCaptcha?moveLength=${params.moveLength}&ticket=${params.ticket}`,
    { params },
    {
      headers: {
        'content-type': 'application/json',
      },
    }
  );
}

// 获取租户列表
export function getTenantList(params: { accessKey: string }): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/tenant/list', params, {
    custom: {
      toast: false,
      dontRefreshToken: true,
    },
  });
}

// 登录
export function login(params: { accessKey: string }): Promise<LoginResponse> {
  return getHttp().post('/huayun-ai/app/auth/login', params, {
    custom: {
      toast: false,
    },
  });
}

// 更新密码
export function updatePassword(data: any) {
  return getHttp().post('/huayun-ai/app/auth/updatePassword', data, {
    custom: {
      toast: false,
    },
  });
}

// 账号密码登录
export function loginByPassword(params: any) {
  return getHttp().post('/huayun-ai/app/auth/tenantListByPassword', params, {
    custom: {
      toast: false,
    },
  });
}

// 学生端-账号密码登录
export function loginByPasswordOfStudent(params: {
  account: string;
  password: string;
  tenantId?: string;
}) {
  return getHttp().post('/huayun-ai/app/auth/studentLogin', params, {
    custom: {
      toast: false,
    },
  });
}

// 重置密码
export function resetPassword(params: {
  unionId?: string;
  account?: string;
  mobile?: string;
  code?: string;
  password: string;
  password1: string;
}) {
  return getHttp().post('/huayun-ai/app/auth/resetPwd', params, {
    custom: {
      toast: false,
    },
  });
}

// 重置密码验证码
export function resetPasswordSmsCode(params: any) {
  return getHttp().post('/huayun-ai/app/auth/resetPwdSmsCode', params, {
    custom: {
      toast: false,
    },
  });
}

// 获取租户列表
export function getTenantListByJsCode(params: { jsCode: string }): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/refresh/tenant/list', params);
}

// 绑定手机号到微信账号
export function bindPhoneToWechat(params: {
  phone: string;
  code: string;
  jsCode: string;
}): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/bind/phone/wechat', params);
}

// 微信登录：通过 unionId 获取租户列表
export function getTenantListByUnionId(params: WechatLoginParams): Promise<TenantInfo[] | null> {
  return getHttp().post('/huayun-ai/app/auth/tenantListByUnionId', params, {
    custom: {
      toast: false,
    },
  });
}

// 绑定手机号获取验证码（拼图验证码后）

// 首次修改密码（首次登录场景）
export function resetFirstPassword(params: ResetFirstPasswordParams): Promise<any> {
  return getHttp().post('/huayun-ai/app/auth/first/update', params, {
    custom: {
      toast: false,
    },
  });
}

// 苹果登录
export function appleLogin(params: {
  accessToken: string;
  phone?: string;
  code?: string;
  openid?: string;
}): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/tenantListBySub', params, {
    custom: {
      toast: false,
    },
  });
}

// 获取教育机构列表
export function getEducationList(): Promise<EducationInfo[]> {
  return getHttp().post('/huayun-ai/client/tenant/educationList', {});
}
