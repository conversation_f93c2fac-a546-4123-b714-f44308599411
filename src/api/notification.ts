import type {
  NotificationPageParams,
  NotificationPageResponse,
  MarkNotificationReadParams,
} from '@/types/api/notification';

// 获取HTTP请求实例
const getHttp = () => uni.$u.http;

/**
 * 获取通知页面数据
 * @param params 通知页面查询参数
 * @returns Promise<NotificationPageResponse> 分页的通知数据
 */
export const getNotificationPage = (
  params: NotificationPageParams
): Promise<NotificationPageResponse> => getHttp().post('/ai-notice/notification/page', params);

/**
 * 获取未读通知数量
 * @returns Promise<any> 未读通知数量
 */
export const getUnreadNotificationCount = (): Promise<any> =>
  getHttp().post('/ai-notice/notification/unReadNum');

/**
 * 将通知标记为已读
 * @param params 标记已读请求参数
 * @returns Promise<any> 标记结果
 */
export const markNotificationAsRead = (params: MarkNotificationReadParams): Promise<any> =>
  getHttp().post('/ai-notice/notification/read', params);
