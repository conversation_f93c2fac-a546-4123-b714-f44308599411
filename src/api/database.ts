const getHttp = () => uni.$u.http;

// 时间格式化函数
function formatTimeForItem(item: any) {
  // 统一处理时间字段格式
  ['createTime', 'updateTime'].forEach(key => {
    const time = item[key];
    if (time && typeof time === 'string') {
      const parts = time.split(':');
      // 如果时间格式为 HH:mm:ss，则截取到分钟
      if (parts.length === 3) {
        item[key] = `${parts[0]}:${parts[1]}`;
      }
    }
  });
}

// 学校数据空间列表
// shareType:
// 2等同0 能看到这个空间但是看不到空间里面有什么东西
// 1 能看到这个空间和里面有什么东西 (比如对一个父空间的子空间赋予了权限,但是没有给父空间赋予权限
// 不传 什么都看不到
// 后端逻辑是只要一个空间的shareType为1,就会递归所有子级都变为1,所以测试的时候要把所有父级空间都变为2才能出现shareType返回值为2的情况
export async function getSpaceFileList(params: any): Promise<any> {
  const res = await getHttp().post('/huayun-ai/app/cloud/space/folder/subListPages', params);
  if (res && res.records && Array.isArray(res.records)) {
    res.records = res.records.map((item: any) => {
      // 把parentId转换为字符串,和我的数据空间列表接口保持统一
      if (item.parentId !== undefined && item.parentId !== null) {
        item.parentId = item.parentId.toString();
      }
      formatTimeForItem(item);
      return item;
    });
  }
  return res;
}
// 我的数据空间列表
export async function getMySpaceFileList(params: any): Promise<any> {
  const res = await getHttp().post('/huayun-ai/app/cloud/folder/subListPage', params);
  if (res && res.records && Array.isArray(res.records)) {
    res.records.forEach((item: any) => {
      formatTimeForItem(item);
    });
  }
  return res;
}

// (看PC端单删除也用的这个)批量删除学校-文件/文件夹 {"list":[{"id":"53114","fileType":1},{"id":"53113","fileType":1}]}
export function mixBatchDelete(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/folder/mixBatchDelete', params);
}

// (看PC端单删除也用的这个)批量删除我的-文件/文件夹 {"list":[{"id":"53114","fileType":1},{"id":"53113","fileType":1}]}
export function myMixBatchDelete(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/mixBatchDelete', params);
}

// 删除学校数据空间
export function deleteSpaceFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/delete', params);
}

// 回收站列表
export async function getRecycleBinFileList(params: any): Promise<any> {
  const res = await getHttp().post('/huayun-ai/app/cloud/recycle/page', params);
  // 把bizType转换为字符串,和整个项目保持统一,不然会影响到后续其他组件的值判断
  if (res && res.records && Array.isArray(res.records)) {
    res.records.forEach((item: any) => {
      if (item.bizType !== undefined && item.bizType !== null) {
        item.bizType = item.bizType.toString();
      }
      formatTimeForItem(item);
    });
  }
  return res;
}

// 回收站列表-点击item后继续查询的子级列表
export function getRecycleBinFileSubList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/subListPage', params);
}

// 判断回收站文件父空间是否存在
export function hasParent(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/hasParent', params);
}

// 批量判断回收站文件父空间是否存在(有一个不存在就会返回false)
export function batchHasParent(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/batchHasParent', params);
}

// 回收站恢复
export function recoveryRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/recovery', params);
}

// 回收站批量恢复
export function batchRecoveryRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/batchRecovery', params);
}

// 回收站彻底删除
export function deleteRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/delete', params);
}

// 回收站批量彻底删除
export function batchDeleteRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/batchDelete', params);
}

// 上传到学校/我的数据空间
export function uploadToSpace(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/file/upload', params);
}

// 文件上传埋点
export function reportCloudFileUpload(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/client/activity/report/cloud/file/upload', params);
}

// 获取指定应用列表(思维导图,文档解读)
export function getAppointedAppList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/client/app/center/appointedAppList', params);
}

// 从我的空间选择文件添加到学校数据空间
export function addFromMyself(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/file/addFromMyself', params);
}

// 新增文件夹
export function addFolder(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/add', params);
}

// 重命名学校数据空间文件夹
export function updateFolder(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/cloud/space/folder/update', params);
}

// 重命名我的数据空间文件夹
export function renameFolder(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/cloud/folder/rename', params);
}

// 重命名学校/我的数据空间文件
export function renameFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/cloud/file/rename', params);
}

// 被移动的是学校数据空间的文件夹  id:"4543", parentId:"4441"
export function updateParentSpace(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/updateParent', params);
}

// 被移动的是我的数据空间的文件夹  id:"438", parentId:"416"
export function updateParentFolder(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/updateParent', params);
}

// 被移动的是学校数据空间/我的数据空间的文件   (学校)spaceId/(我的)folderId:"412", id:"52934", parentId:""
export function updateParentFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/file/updateParent', params);
}

// 批量被移动-学校数据空间/我的数据空间-文件/文件夹  参数完全一致 {"parentId":"441","ids":["52941","52937"],"folderIds":[],"spaceIds":[]}
export function batchUpdateParentFileAndFolder(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/file/batchUpdateParent', params);
}

// 获取文件详情(主要是为了产生访问记录)
export function getFileDetail(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/system/file/detail', params);
}

// 是否开启了学校数据空间审核
export function isAuditSwitch(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/audit/switch/detail', params);
}
