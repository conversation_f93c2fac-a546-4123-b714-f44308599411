import type {
  SubjectsList,
  TextbookVersions,
  TextbookVersionsList,
  TextbookVersionsItem,
  GradesList,
  HotSearch,
  ResourceTypes,
  ResourceFormats,
  SearchResourcesParams,
  ResourceSearchResponse,
  AppResourceCollectSearchRequest,
  CollectedResourceSearchResponse,
  AppResourceDetailVO,
  StudentGradeAndTextbook,
} from '@/types/students/resourceCenter';

const getHttp = () => uni.$u.http;

export function getSubjects(params: { gradeId?: number }): Promise<SubjectsList> {
  return getHttp().post(`/ai-resource/app/studentResources/subjects/list`, {
    ...params,
  });
}
/**
 * 教材版本列表
 */
export function getTextbookVersionsList(
  params: TextbookVersionsItem
): Promise<TextbookVersionsList[]> {
  return getHttp().post('/ai-resource/app/studentResources/textbookVersions/list', {
    params,
  });
}

/**
 * 年级列表
 */
export function getGradesList(): Promise<GradesList> {
  return getHttp().post('/ai-resource/app/studentResources/grades/list', {});
}

/**
 * 热搜资源
 */
export function getHotSearch(): Promise<HotSearch> {
  return getHttp().post('/ai-resource/app/studentResources/hotSearch', {});
}

/**
 * 资源类型列表
 */
export function getResourceTypes(): Promise<ResourceTypes> {
  return getHttp().post('/ai-resource/app/studentResources/resourceTypes/list', {});
}

/**
 * 资源格式树结构
 */
export function getResourceFormats(): Promise<ResourceFormats> {
  return getHttp().post('/ai-resource/app/studentResources/resourceFormats/tree', {});
}

/**
 * 学生端资源搜索
 */
export function getSearchResources(params: SearchResourcesParams): Promise<ResourceSearchResponse> {
  return getHttp().post('/ai-resource/app/studentResources/searchResources', {
    ...params,
  });
}

/**
 * 收藏资源
 */
export function getCollectResources(params: { resourceIds: number[] }): Promise<any> {
  return getHttp().post('/ai-resource/app/studentResources/collectResources', {
    resourceIds: params.resourceIds,
  });
}

/**
 * 搜索收藏资源
 */
export function getSearchCollectResources(
  params: AppResourceCollectSearchRequest
): Promise<CollectedResourceSearchResponse> {
  return getHttp().post('/ai-resource/app/studentResources/searchCollectResources', {
    ...params,
  });
}

/**
 * 取消收藏资源
 */
export function getCancelCollectResources(params: { resourceIds: number[] }): Promise<any> {
  return getHttp().post('/ai-resource/app/studentResources/cancelCollectResources', {
    resourceIds: params.resourceIds,
  });
}

/**
 * 资源详情
 */
export function getResourceDetail(params: { resourceId: number }): Promise<AppResourceDetailVO> {
  return getHttp().post('/ai-resource/app/studentResources/resourceDetail', {
    resourceId: params.resourceId,
  });
}

/**
 * 新增学生笔记
 */
export function addStudentsNotes(data: {
  noteContent: string;
  refId: number | string;
  type: number;
}): Promise<any> {
  return getHttp().post('/ai-resource/app/studentsNotes/add', data);
}

/**
 * 获取学生当前年级和教材版本
 */
export function getStudentGradeAndTextbook(): Promise<StudentGradeAndTextbook> {
  return getHttp().post('/ai-resource/app/studentResources/getStudentGradeAndTextbook', {});
}

/**
 * 获取视频播放凭证
 */
export function getVideoPlayAuth(params: { videoId: string }): Promise<string> {
  return getHttp().post('/huayun-ai/system/file/getPlayAuth', {
    videoId: params.videoId,
  });
}

/**
 * 获取视频播放进度
 */
export function getVideoProgressDetail(params: { fileKey: string }): Promise<any> {
  return getHttp().post('/huayun-ai/video/progress/detail', {
    fileKey: params.fileKey,
  });
}

export function getVideoProgressSaveOrUpdate(params: {
  fileKey: string;
  currentPosition: number;
}): Promise<any> {
  return getHttp().post('/huayun-ai/video/progress/saveOrUpdate', {
    ...params,
  });
}
