import type {
  CreateErrorBookParams,
  DeleteErrorBookParams,
  GetErrorBookInfoParams,
  GetErrorBookListParams,
  GetErrorBookPassParams,
  GetSemesterListParams,
  GetSubjectListParams,
  GetSemesterListResponse,
  GetCurrentSemesterResponse,
  GetSubjectListResponse,
  GetErrorBookInfoResponse,
  GetErrorBookListByStatusParams,
  GetQuestionRecommendParams,
  CheckAnswerParams,
  CheckAnswerResponse,
} from '@/types/students/errorBook';

const getHttp = () => uni.$u.http;

/**
 * 新增学生错题记录
 * @param params
 * @returns
 */
export const createErrorBook = (params: CreateErrorBookParams): Promise<any> =>
  getHttp().post('/ai-homework/app/student/question/errors/create', params);

/**
 * 删除错题本
 * @param params
 * @returns
 */
export const deleteErrorBook = (params: DeleteErrorBookParams): Promise<any> =>
  getHttp().post('/ai-homework/app/student/question/errors/delete', params);

/**
 * 错题信息
 * @param params
 * @returns
 */
export const getErrorBookInfo = (
  params: GetErrorBookInfoParams
): Promise<GetErrorBookInfoResponse> =>
  getHttp().post('/ai-homework/app/student/question/errors/info', params);

/**
 * 错题列表
 * @param params
 * @returns
 */
export const getErrorBookList = (params: GetErrorBookListParams): Promise<any> =>
  getHttp().post('/ai-homework/app/student/question/errors/list', params);

/**
 * 错题已过关
 * @param params
 * @returns
 */
export const getErrorBookPass = (params: GetErrorBookPassParams): Promise<any> =>
  getHttp().post('/ai-homework/app/student/question/errors/pass', params);

/**
 * 获取当前学期时间范围
 * @param params
 * @returns
 */
export const getCurrentSemester = (): Promise<GetCurrentSemesterResponse> =>
  getHttp().post('/ai-homework/app/student/question/errors/current-semester', {});

/**
 * 学年学期列表
 * @param params
 * @returns
 */
export const getSemesterList = (): Promise<GetSemesterListResponse[]> =>
  getHttp().post('/ai-homework/app/student/question/errors/semester/list', {});

/**
 * 学科列表
 * @param params
 * @returns
 */
export const getSubjectList = (): Promise<GetSubjectListResponse[]> =>
  getHttp().post('/ai-homework/app/student/question/errors/subject/list', {});

/**
 * 按状态查询错题列表
 * @param params 查询参数
 * @returns
 */
export const getErrorBookListByStatus = (params: GetErrorBookListByStatusParams): Promise<any> =>
  getHttp().post('/ai-homework/app/student/question/errors/list/byStatus', params);

/**
 * 推荐题目
 * @param params 推荐参数
 * @returns
 */
export const getQuestionRecommend = (params: GetQuestionRecommendParams): Promise<any> =>
  getHttp().post('/ai-homework/app/student/question/errors/recommend', params);

/**
 * 客观题答案判断
 * @param params 答案判断参数
 * @returns 答案判断结果
 */
export const checkAnswer = (params: CheckAnswerParams): Promise<CheckAnswerResponse> =>
  getHttp().post('/ai-homework/app/student/question/errors/check-answer', params);
