import type {
  StudentNoteListParams,
  StudentNoteItem,
  StudentNoteAddParams,
  StudentNoteUpdateParams,
} from '@/types/students/studentnote';
import type { PagingData } from '@/types/api/common';

const getHttp = () => uni.$u.http;

// 获取笔记列表
export const getStudentNoteList = (
  data: StudentNoteListParams
): Promise<PagingData<StudentNoteItem>> =>
  getHttp().post('/ai-resource/app/studentsNotes/page', data);

//新增笔记
export const createStudentNote = (data: StudentNoteAddParams) =>
  getHttp().post('/ai-resource/app/studentsNotes/add', data);

//修改学生笔记
export const updateStudentNote = (data: StudentNoteUpdateParams) =>
  getHttp().post('/ai-resource/app/studentsNotes/update', data);

//删除学生笔记
export const deleteStudentNote = (id: number) =>
  getHttp().post('/ai-resource/app/studentsNotes/delete', { id });
