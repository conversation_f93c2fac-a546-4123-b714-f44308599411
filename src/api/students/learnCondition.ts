import type {
  SemestersParams,
  SubjectsParams,
  SemestersItem,
  SubjectsItem,
  KnowledgeAnalysis,
  SubjectScoreTrendItem,
  ClassRankTrend,
  KnowledgeAnalysisParams,
} from '@/types/students/learnCondition';

const getHttp = () => uni.$u.http;

/**
 * 学情分析-学期列表
 */
export function getSemesters(): Promise<SemestersItem[]> {
  return getHttp().post(`/ai-homework/app/student/homework/semesters`, {});
}

/**
 * 学情分析-学科列表
 */
export function getSubjects(data: SubjectsParams): Promise<SubjectsItem[]> {
  return getHttp().post(`/ai-homework/app/student/homework/subjects`, data);
}

/**
 * 学情分析-单科成绩变化趋势
 */
export function getSubjectScoreTrend(data: SubjectsParams): Promise<SubjectScoreTrendItem[]> {
  return getHttp().post(`/ai-homework/app/student/homework/subjectScoreTrend`, data);
}

/**
 * 学情分析-班级排名波动
 */
export function getClassRankTrend(data: SubjectsParams): Promise<ClassRankTrend> {
  return getHttp().post(`/ai-homework/app/student/homework/classRankTrend`, data);
}

/**
 * 学情分析-统计正确率和错误率
 */
export function getKnowledgeAnalysis(data: KnowledgeAnalysisParams): Promise<KnowledgeAnalysis> {
  return getHttp().post(`/ai-homework/app/student/homework/knowledgeAnalysis`, data);
}
