import type {
  HomeworkPageParams,
  HomeworkPageItem,
  HomeworkSemesterItem,
  HomeworkSubjectItem,
  CreateSelfHomeworkParams,
  HomeworkTypeItem,
  SubmitHomeworkParams,
  HomeworkDetail,
  UpdateSelfHomeworkParams,
  GetSubjectScoreTrendParams,
  GetClassRankTrendParams,
  AddStudentsNoteParams,
  GetStudentsNoteParams,
  UpdateStudentsNoteParams,
  SelfHomeworkDetail,
  WritingCorrectResult,
} from '@/types/students/homework';
import type { PagingData } from '@/types/api/common';

const getHttp = () => uni.$u.http;

// 临时处理后端必填参数studentid
const commonParams = {
  // studentId: 2350,
  // gradeId: 171,
};

/**
 * 分页查询学生作业列表
 */
export function getHomeworkPage(data?: HomeworkPageParams): Promise<PagingData<HomeworkPageItem>> {
  return getHttp().post('/ai-homework/app/student/homework/page', {
    ...commonParams,
    ...data,
  });
}

/**
 * 查询学生作业学期列表
 */
export function getHomeworkSemesters(): Promise<HomeworkSemesterItem[]> {
  return getHttp().post('/ai-homework/app/student/homework/semesters', {
    ...commonParams,
  });
}

/**
 * 查询学生作业学科列表
 */
export function getHomeworkSubjects(): Promise<HomeworkSubjectItem[]> {
  return getHttp().post('/ai-homework/app/student/homework/selectBySemesterAndGradeId', {
    ...commonParams,
  });
}

/**
 * 查询学生作业类型列表
 */
export function getHomeworkTypeList(): Promise<HomeworkTypeItem[]> {
  return getHttp().post('/ai-homework/app/student/homework/homework-type/list', {
    ...commonParams,
  });
}

/**
 * 创建自主作业
 */
export function createSelfHomework(data?: CreateSelfHomeworkParams) {
  return getHttp().post('/ai-homework/app/student/homework/self/create', {
    ...commonParams,
    ...data,
  });
}

/**
 * 更新自主作业
 */
export function updateSelfHomework(data?: UpdateSelfHomeworkParams) {
  return getHttp().post('/ai-homework/app/student/homework/self/update', {
    ...data,
    ...commonParams,
    ...data,
  });
}

/**
 * 获取自主作业详情
 */
export function getSelfHomeworkDetail(data?: { id: number | string }): Promise<SelfHomeworkDetail> {
  return getHttp().post('/ai-homework/app/student/homework/self/detail', {
    ...commonParams,
    ...data,
  });
}

/**
 * 提交作业
 */
export function submitHomework(data?: SubmitHomeworkParams) {
  return getHttp().post('/ai-homework/app/student/homework/submit', {
    ...commonParams,
    ...data,
  });
}

/**
 * 查询作业详情
 */
export function getHomeworkDetail(data?: { id: number | string }): Promise<HomeworkDetail> {
  return getHttp().post('/ai-homework/app/student/homework/detail', {
    ...commonParams,
    ...data,
  });
}

/**
 * 单科成绩变化趋势
 */
export function getHomeworkSubjectScoreTrend(data?: GetSubjectScoreTrendParams) {
  return getHttp().post('/ai-homework/app/student/homework/subjectScoreTrend', {
    ...commonParams,
    ...data,
  });
}

/**
 * 班级排名波动
 */
export function getHomeworkClassRankTrend(data?: GetClassRankTrendParams) {
  return getHttp().post('/ai-homework/app/student/homework/classRankTrend', {
    ...commonParams,
    ...data,
  });
}

/**
 * 查询当前学期
 */
export function getCurrentSemester() {
  return getHttp().post('/huayun-ai/client/semester/current');
}

/**
 * 图片OCR识别
 */
export function getImageOcr(data: { url: string }) {
  return getHttp().post(
    '/ai-homework/teacher/essay/homework/ocr/handwriting',
    {
      ...data,
    },
    {
      custom: {
        toast: false,
      },
    }
  );
}

/**
 * 查询作业成绩详情
 */
export function getHomeworkScoreDetail(data?: { id: number | string }) {
  return getHttp().post('/ai-homework/app/student/homework/score', {
    ...commonParams,
    ...data,
  });
}

/**
 * 获取作业状态
 */
export function getHomeworkStatus(data?: { id: number | string }) {
  return getHttp().post('/ai-homework/app/student/homework/getStatus', {
    ...commonParams,
    ...data,
  });
}

/**
 * 新增题目反馈
 */
export function addQuestionFeedback(data?: any) {
  return getHttp().post('/ai-resource/app/studentFeedback/addQuestionFeedback', {
    ...data,
  });
}

/**
 * 新增学生笔记
 */
export function addStudentsNote(data?: AddStudentsNoteParams) {
  return getHttp().post('/ai-resource/app/studentsNotes/add', {
    ...data,
  });
}

/**
 * 修改学生笔记
 */
export function updateStudentsNote(data?: UpdateStudentsNoteParams) {
  return getHttp().post('/ai-resource/app/studentsNotes/update', {
    ...data,
  });
}

/**
 * 查询资源、题目映射学生笔记
 */
export function getStudentsNote(data?: GetStudentsNoteParams) {
  return getHttp().post('/ai-resource/app/studentsNotes/get', {
    ...data,
  });
}

/**
 * 获取学生提交记录列表
 */
export function getStudentSubmitList(data?: {
  /**
   * 学生作业ID
   */
  id: string | number;
}) {
  return getHttp().post('/ai-homework/app/student/homework/studentSubmit', {
    ...data,
  });
}

/**
 * 清空答题记录
 */
export function resetHomeworkAnswerRecord(data?: { id: number | string }) {
  return getHttp().post('/ai-homework/app/student/homework/reset', {
    ...data,
  });
}

/**
 * 获取通用智能体业务
 */
export function getChatCommonBusiness(data?: {
  /**
   * 业务类型
   */
  businessType: string;
  /**
   * 业务数据
   */
  messages: any[];
  /**
   * 是否流式
   */
  stream?: boolean;
}) {
  return getHttp().post('/huayun-tool/api/chat/commonBusiness', {
    ...commonParams,
    ...data,
  });
}

/**
 * 是否完成新功能指引
 * @param data.guideType 指引类型 5=做题
 * @returns
 */
export function validFinishGuide(data?: { guideType: number }): Promise<boolean> {
  return getHttp().post('/huayun-ai/system/tenant/guide/validFinish', {
    ...data,
  });
}

/**
 * 完成新功能指引
 * @param data.guideType 指引类型 5=做题
 */
export function finishGuide(data?: { guideType: number }) {
  return getHttp().post('/huayun-ai/system/tenant/guide/finish', {
    ...data,
  });
}

/**
 * 获取作文批改结果
 */
export function getWritingCorrectResult(data?: {
  submitId: number | string;
}): Promise<WritingCorrectResult> {
  return getHttp().get(`/ai-homework/app/student/homework/result/${data?.submitId}`, {
    ...data,
  });
}
