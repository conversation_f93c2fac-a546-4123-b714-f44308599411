import type { AppListItemType, AppListParams } from '@/types/api/app';
import type { PagingData } from '@/types/api/common';

const getHttp = () => uni.$u.http;

/**
 * 获取应用列表
 * @param params 应用列表请求参数
 * @returns 应用列表（转换为分页格式）
 */
export const getMyAppPage = async (params: AppListParams): Promise<PagingData<AppListItemType>> => {
  return getHttp()
    .post('/huayun-ai/app/app/center/list', params)
    .then((res: any) => {
      console.log(res, 'res');

      const result = res as AppListItemType[];

      // 转换为分页数据格式
      const pageSize = params.size || 10;
      const currentPage = params.current || 1;
      const startIndex = (currentPage - 1) * pageSize;
      let filteredList = [...result];

      // 如果有关键词，进行过滤
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase();
        filteredList = filteredList.filter(
          item =>
            item.name.toLowerCase().includes(keyword) ||
            (item.intro && item.intro.toLowerCase().includes(keyword))
        );
      }

      // 计算分页数据
      const total = filteredList.length;
      const records = filteredList.slice(startIndex, startIndex + pageSize);

      return {
        records,
        total,
        size: pageSize,
        current: currentPage,
        pages: Math.ceil(total / pageSize),
      };
    });
};
