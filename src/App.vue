<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import { useSystemStore } from '@/store/systemStore';
import { useAppCenterStore } from '@/store/useAppCenterStore';

const systemStore = useSystemStore();
const appCenterStore = useAppCenterStore();

onLaunch(async () => {
  //#ifdef APP-PLUS
  // plus.screen.lockOrientation('landscape-primary'); // 强制横屏
  plus.screen.lockOrientation('portrait-primary'); // 强制竖屏
  //#endif
  // 初始化系统信息
  console.log('App Launch');
  systemStore.initSystemInfo();

  // 初始化应用中心数据，开始缓存
  try {
    console.log('开始初始化应用中心数据...');
    await appCenterStore.initializeAppCenter();
    console.log('应用中心数据初始化完成');
  } catch (error) {
    console.error('应用中心数据初始化失败:', error);
  }
});

onShow(async () => {
  console.log('App Show');

  // 确保自定义TabBar在初始化时能正确显示
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    const path = `/${currentPage.route}`;
    uni.$emit('onShow', path);
  }
});

onHide(() => {
  console.log('App Hide');
});
</script>

<style lang="scss">
/* 引入uView基础样式 */
@import 'uview-plus/index.scss';
</style>
