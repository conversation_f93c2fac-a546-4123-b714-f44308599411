<template>
  <view class="l-drag l-class" :style="[areaStyles]" ref="dragRef" @touchstart="setDisabled">
    <!-- 条件渲染scroll-view -->
    <scroll-view
      v-if="props.scrollHeight && isReset"
      class="l-drag__scroll"
      :style="scrollViewStyles"
      scroll-y="true"
      :show-scrollbar="false"
    >
      <movable-area class="l-drag__inner" :style="[innerStyles]">
        <slot></slot>
        <movable-view
          class="l-drag__ghost"
          v-if="isDrag && props.ghost"
          :animation="true"
          :style="[viewStyles]"
          direction="all"
          :x="ghostEl.x"
          :y="ghostEl.y"
          key="l-drag-clone"
        >
          <slot name="ghost"></slot>
        </movable-view>
        <movable-view
          v-if="props.before"
          class="l-drag__before"
          disabled
          :animation="false"
          :style="[viewStyles]"
          :x="beforeEl.x"
          :y="beforeEl.y"
        >
          <slot name="before"></slot>
        </movable-view>
        <movable-view
          v-for="(item, oindex) in cloneList"
          :key="item.id"
          direction="all"
          :data-oindex="oindex"
          :style="[viewStyles]"
          class="l-drag__view"
          :class="[
            {
              'l-is-active': oindex == active,
              'l-is-hidden': !item.show,
              'l-is-inactive': (isActuallyDragging || isLongPressActive) && oindex !== active,
            },
            item.class,
          ]"
          :x="item.x"
          :y="item.y"
          :friction="friction"
          :damping="damping"
          :animation="animation"
          :disabled="isDisabled || props.disabled"
          @touchstart="touchStart"
          @change="touchMove"
          @touchend="touchEnd"
          @touchcancel="touchEnd"
          @longpress="setDisabled"
        >
          <!-- <view v-if="props.remove" class="l-drag__remove" :style="removeStyle" data-remove="true">
					<slot name="remove" :oindex="oindex" data-remove="true" />
				</view> -->
          <!-- <view v-if="props.handle" class="l-drag__handle" :style="handleStyle" data-handle="true">
					<slot name="handle" :oindex="oindex" :active="!isDisabled && !isDisabled && oindex == active" />
				</view> -->
          <slot
            name="grid"
            :oindex="oindex"
            :index="item.index"
            :oldindex="item.oldindex"
            :content="item.content"
            :active="!isDisabled && !isDisabled && oindex == active"
          />
          <view class="mask" v-if="!(isDisabled || props.disabled) && props.longpress"></view>
        </movable-view>

        <movable-view
          v-if="props.after"
          class="l-drag__after"
          disabled
          :animation="true"
          direction="all"
          :style="[viewStyles]"
          :x="afterEl.x"
          :y="afterEl.y"
        >
          <slot name="after"></slot>
        </movable-view>
      </movable-area>
    </scroll-view>

    <!-- 不使用scroll-view的情况 -->
    <movable-area v-else-if="isReset" class="l-drag__inner" :style="[innerStyles]">
      <slot></slot>
      <movable-view
        class="l-drag__ghost"
        v-if="isDrag && props.ghost"
        :animation="true"
        :style="[viewStyles]"
        direction="all"
        :x="ghostEl.x"
        :y="ghostEl.y"
        key="l-drag-clone"
      >
        <slot name="ghost"></slot>
      </movable-view>
      <movable-view
        v-if="props.before"
        class="l-drag__before"
        disabled
        :animation="false"
        :style="[viewStyles]"
        :x="beforeEl.x"
        :y="beforeEl.y"
      >
        <slot name="before"></slot>
      </movable-view>
      <movable-view
        v-for="(item, oindex) in cloneList"
        :key="item.id"
        direction="all"
        :data-oindex="oindex"
        :style="[viewStyles]"
        class="l-drag__view"
        :class="[
          {
            'l-is-active': oindex == active,
            'l-is-hidden': !item.show,
            'l-is-inactive': (isActuallyDragging || isLongPressActive) && oindex !== active,
          },
          item.class,
        ]"
        :x="item.x"
        :y="item.y"
        :friction="friction"
        :damping="damping"
        :animation="animation"
        :disabled="isDisabled || props.disabled"
        @touchstart="touchStart"
        @change="touchMove"
        @touchend="touchEnd"
        @touchcancel="touchEnd"
        @longpress="setDisabled"
      >
        <slot
          name="grid"
          :oindex="oindex"
          :index="item.index"
          :oldindex="item.oldindex"
          :content="item.content"
          :active="!isDisabled && !isDisabled && oindex == active"
        />
        <view class="mask" v-if="!(isDisabled || props.disabled) && props.longpress"></view>
      </movable-view>

      <movable-view
        v-if="props.after"
        class="l-drag__after"
        disabled
        :animation="true"
        direction="all"
        :style="[viewStyles]"
        :x="afterEl.x"
        :y="afterEl.y"
      >
        <slot name="after"></slot>
      </movable-view>
    </movable-area>
  </view>
</template>
<script lang="ts">
// @ts-nocheck
import {
  computed,
  onMounted,
  ref,
  getCurrentInstance,
  watch,
  nextTick,
  reactive,
  triggerRef,
  onUnmounted,
  defineComponent,
} from './vue';
import DragProps from './props';
import type { GridRect, Grid, Position } from './type';
// #ifdef APP-NVUE
const dom = weex.requireModule('dom');
// #endif

export default defineComponent({
  name: 'l-drag',
  externalClasses: ['l-class'],
  options: {
    addGlobalClass: true,
    virtualHost: true,
  },
  props: DragProps,
  emits: ['change'],
  setup(props, { emit, expose }) {
    /**
     * 防抖函数
     * 在一段时间内多次触发同一事件，只执行最后一次
     *
     * @param fn 需要防抖的函数
     * @param wait 延迟执行时间(毫秒)
     * @param immediate 是否立即执行
     * @returns 防抖后的函数
     */
    const debounce = (fn, wait = 300, immediate = false) => {
      let timer = null;

      // 返回的防抖函数
      function debounced(...args) {
        // 保存函数调用时的上下文
        const context = this;

        // 清除之前的定时器
        if (timer !== null) {
          clearTimeout(timer);
          timer = null;
        }

        // 立即执行
        if (immediate) {
          // 如果已经执行过，不再执行
          const callNow = !timer;
          // 设置定时器，wait毫秒后将timer设为null
          timer = setTimeout(() => {
            timer = null;
          }, wait);

          if (callNow) {
            fn.apply(context, args);
          }
        } else {
          // 延迟执行
          timer = setTimeout(() => {
            fn.apply(context, args);
            timer = null;
          }, wait);
        }
      }

      // 取消防抖
      debounced.cancel = function () {
        if (timer !== null) {
          clearTimeout(timer);
          timer = null;
        }
      };

      return debounced;
    };

    // #ifdef APP-NVUE
    const dragRef = ref(null);
    // #endif
    const app = getCurrentInstance();
    const isDrag = ref(false);
    const isActuallyDragging = ref(false); // 区分触摸开始和真正拖拽
    const isInit = ref(false);
    const isReset = ref(true);
    const colmunId = ref(-1);
    /** 选中项原始下标 */
    const active = ref(-1);
    const maxIndex = ref(-1);
    const animation = ref(true);
    const isDisabled = ref(props.handle || props.longpress ? true : false);
    // 添加长按激活状态
    const isLongPressActive = ref(false);
    // 长按定时器
    let longPressTimer = null;
    // 跟踪触摸移动状态
    const hasTouchMoved = ref(false);
    // 添加一个变量来追踪组件是否已挂载
    const isMounted = ref(false);

    const dragEl = reactive({
      content: null,
      /** 当前视图下标*/
      index: 0,
      /** 旧视图下标 */
      oldindex: -1,
      /** 上次原始下标 */
      lastindex: -1,
    });

    const ghostEl = reactive({
      content: null,
      x: 0,
      y: 0,
    });
    const beforeEl = reactive({
      x: 0,
      y: 0,
    });
    const afterEl = reactive({
      x: 0,
      y: 0,
    });

    let gridRects = []; //ref<GridRect[]>([])
    const areaWidth = ref(0);
    const cloneList = ref<Grid[]>([]);
    // 删除项时可能会减少行数影响到删除过渡动画，故增加此值在删除时保持高度不变，等动画完成后再归零
    const leaveRow = ref(0);
    const extra = computed(() => (props.before ? 1 : 0) + (props.after ? 1 : 0));
    const rows = computed(() =>
      Math.ceil(
        ((isInit.value ? cloneList.value.length : props.list.length) + extra.value) / props.column
      )
    );
    const gridHeight = computed(() =>
      props.aspectRatio
        ? girdWidth.value / props.aspectRatio
        : /rpx$/.test(`${props.gridHeight}`)
          ? uni.upx2px(parseInt(`${props.gridHeight}`))
          : parseInt(`${props.gridHeight}`)
    );
    const girdWidth = computed(() => areaWidth.value / props.column);
    const viewStyles = computed(() => ({
      width: girdWidth.value + 'px',
      height: gridHeight.value + 'px',
    }));
    const areaStyles = computed(() => {
      // 当启用scroll-view时，外层容器不需要固定高度，让scroll-view控制
      if (props.scrollHeight) {
        return {
          height: 'auto',
          minHeight: '100rpx',
        };
      }
      // 原有逻辑：不启用scroll-view时计算固定高度
      return {
        height: (rows.value + leaveRow.value) * gridHeight.value + 'px',
      };
    });
    const innerStyles = computed(() => ({
      // #ifdef APP-NVUE
      width: areaWidth.value + 'px',
      // #endif
      height: (rows.value + props.extraRow + leaveRow.value) * gridHeight.value + 'px',
    }));

    // scroll-view样式计算
    const scrollViewStyles = computed(() => {
      if (!props.scrollHeight) return {};

      let height = '';
      if (typeof props.scrollHeight === 'number') {
        height = props.scrollHeight + 'px';
      } else if (typeof props.scrollHeight === 'string') {
        // 支持rpx和px单位
        height = /rpx$/.test(props.scrollHeight)
          ? uni.upx2px(parseInt(props.scrollHeight)) + 'px'
          : props.scrollHeight;
      }

      return {
        height,
        width: '100%',
      };
    });

    const sleep = (cb: Function, time = 1000 / 60) => setTimeout(cb, time);
    const createGrid = (content: any, position?: Position | null): Grid => {
      colmunId.value++;
      maxIndex.value++;
      const index = maxIndex.value;
      const colmun = gridRects[index];

      let x = 0;
      let y = 0;
      if (colmun) {
        if (props.after) {
          let nxet = gridRects[index + 1];
          if (!nxet) {
            nxet = createGridRect(gridRects.length + (props.before ? 1 : 0));
            gridRects.push(nxet);
          }
          setReset(() => setAfter(nxet));
        } else {
          setReset();
        }
        x = colmun.x;
        y = colmun.y;
      } else {
        const nxet = createGridRect(gridRects.length + (props.before ? 1 : 0));
        gridRects.push(nxet);
        setReset();
        x = nxet.x;
        y = nxet.y;
      }
      if (position) {
        x = position.x;
        y = position.y;
      }
      return {
        id: `l-drag-item-${colmunId.value}`,
        index,
        oldindex: index,
        content,
        x,
        y,
        class: '',
        show: true,
      };
    };
    const setReset = (cb?: any) => {
      // const newRow = (cloneList.value.length + extra.value) % (props.column)
      if (isInit.value) {
        cb &&
          sleep(() => {
            if (!isMounted.value) return; // 组件卸载检查
            cb();
          });
      }
    };
    const setAfter = ({ x, y } = { x: 0, y: 0 }) => {
      if (props.after) {
        afterEl.x = x;
        afterEl.y = y;
      }
    };
    const setDisabled = (e: any, flag?: boolean = false) => {
      // e?.preventDefault()
      const type = `${e.type}`.toLowerCase();
      const { handle = props.touchHandle } = e.target.dataset;

      // 清理之前的定时器，除非是touchstart事件
      if (longPressTimer && !type.includes('touchstart')) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
      }

      // 处理长按事件
      if (props.longpress && type.includes('longpress')) {
        // 如果在长按过程中有滑动，则不进入拖拽状态
        if (hasTouchMoved.value) {
          hasTouchMoved.value = false;
          return;
        }

        // 设置一秒延时激活
        longPressTimer = setTimeout(() => {
          if (!isMounted.value) return; // 组件卸载检查

          if (props.handle && !handle) {
            isDisabled.value = true;
          } else if (props.handle && handle && !props.longpress) {
            isDisabled.value = flag;
          } else if (props.handle && handle && props.longpress) {
            isDisabled.value = false;
            isLongPressActive.value = true;
          } else if (props.longpress && !props.handle) {
            isDisabled.value = false;
            isLongPressActive.value = true;
          }
        }, 400);
        return;
      }

      // 非长按事件的处理
      if (props.handle && !handle) {
        isDisabled.value = true;
      } else if (props.handle && handle && !props.longpress) {
        isDisabled.value = flag;
      } else if (props.handle && handle && props.longpress && type.includes('longpress')) {
        isDisabled.value = false;
      } else if (props.longpress && type.includes('longpress') && !props.handle) {
        isDisabled.value = false;
      }

      // 触摸结束时重置状态
      if (type.includes('touchend') && props.longpress) {
        isDisabled.value = true;
        isLongPressActive.value = false;
      }
    };
    const createGridRect = (i: number, last?: GridRect): GridRect => {
      let { row } = last || gridRects[gridRects.length - 1] || { row: 0 };
      const col = i % props.column;
      const grid = (row: number, x: number, y: number): GridRect => {
        return { row, x, y, x1: x + girdWidth.value, y1: y + gridHeight.value };
      };
      if (col == 0 && i != 0) {
        row++;
      }
      return grid(row, col * girdWidth.value, row * gridHeight.value);
    };
    const createGridRects = () => {
      let rects: GridRect[] = [];
      const length = rows.value * props.column + extra.value;
      gridRects = [];
      for (var i = 0; i < length; i++) {
        const item = createGridRect(i, rects[rects.length - 1]);
        rects.push(item);
      }
      if (props.before) {
        const { x, y } = rects.shift();
        beforeEl.x = x;
        beforeEl.y = y;
      }
      setAfter(rects[props.list.length]);
      gridRects = rects as GridRect[];
    };
    const updateList = (v: any[]) => {
      cloneList.value = v.map(content => createGrid(content));
    };

    const touchStart = (e: any) => {
      if (e.target.dataset.remove) return;
      // 重置触摸移动状态
      hasTouchMoved.value = false;
      // 选中项原始下标
      const { oindex } = e.currentTarget?.dataset || e.target?.dataset || {};
      if (typeof oindex !== 'number') return;
      const target = cloneList.value[oindex];
      isDrag.value = true;
      // 选中项原始下标
      active.value = oindex;
      // 选中项的当前下标
      dragEl.index = dragEl.oldindex = target.index;
      ghostEl.x = target.x || 0;
      ghostEl.y = target.y || 0;
      dragEl.content = ghostEl.content = target.content;
    };

    const touchEnd = (e: any) => {
      // 清理长按定时器
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
      }

      // 重置长按状态
      isLongPressActive.value = false;
      hasTouchMoved.value = false;

      setTimeout(() => {
        if (!isMounted.value) return; // 组件卸载检查
        if (e.target.dataset.remove || active.value == -1) return;
        setDisabled(e, true);
        isDrag.value = false;
        isActuallyDragging.value = false; // 重置真正的拖拽状态
        const isEmit = dragEl.index !== dragEl.oldindex && dragEl.oldindex > -1; // active.value !== dragEl.index
        dragEl.lastindex = active.value;
        dragEl.oldindex = active.value = -1;
        const last = cloneList.value[dragEl.lastindex];
        if (!last) return;

        const position = gridRects[dragEl.index];
        if (!position) return;

        nextTick(() => {
          if (!isMounted.value) return; // 组件卸载检查
          last.x = position.x + 0.001;
          last.y = position.y + 0.001;
          sleep(() => {
            if (!isMounted.value) return; // 组件卸载检查
            last.x = position.x;
            last.y = position.y;
            isEmit && emitting();
          });
        });
      }, 80);
    };
    const emitting = () => {
      const clone = [...cloneList.value].sort((a, b) => a.index - b.index); //.map(item => ref(item.content))
      emit('change', clone);
    };

    const touchMove = (e: any) => {
      // 记录有触摸移动，防止长按误触发拖拽
      hasTouchMoved.value = true;

      if (!isDrag.value) return;
      // #ifndef APP-NVUE
      let { oindex } = e.currentTarget.dataset;
      // #endif
      // #ifdef APP-NVUE
      oindex = e.currentTarget.dataset['-oindex'];
      // #endif
      if (oindex != active.value) return;

      // 第一次移动时设置真正的拖拽状态
      if (!isActuallyDragging.value) {
        isActuallyDragging.value = true;
      }

      const { x, y } = e.detail;
      const centerX = x + girdWidth.value / 2;
      const centerY = y + gridHeight.value / 2;
      for (let i = 0; i < cloneList.value.length; i++) {
        const item = gridRects[i];
        if (centerX > item.x && centerX < item.x1 && centerY > item.y && centerY < item.y1) {
          ghostEl.x = item.x;
          ghostEl.y = item.y;
          if (dragEl.index != i) {
            _move(active.value, i);
          }
          break;
        }
      }
    };
    const getDragEl = (oindex: number) => {
      if (isDrag.value) {
        return dragEl;
      }
      return cloneList.value[oindex];
    };

    /**
     * 把原始数据中排序为index的项 移动到 toIndex
     * @param oindex 原始数据的下标
     * @param toIndex 视图中的下标
     * @param position 指定坐标
     */
    const _move = (
      oindex: number,
      toIndex: number,
      position?: Position | null,
      emit: boolean = true
    ) => {
      const length = cloneList.value.length - 1;
      if (toIndex > length || toIndex < 0) return;
      // 获取oIdnex在视图中的项目
      const dragEl = getDragEl(oindex);
      let speed = 0;
      let start = dragEl.index;
      // 比较开始index和终点index，设置方向
      if (start < toIndex) {
        speed = 1;
      }
      if (start > toIndex) {
        speed = -1;
      }
      if (!speed) return;
      // 距离
      let distance = start - toIndex;
      // 找到区间所有的项
      while (distance) {
        distance += speed;
        // 目标
        const target = isDrag.value ? (dragEl.index += speed) : (start += speed);
        let targetOindex = cloneList.value.findIndex(
          item => item.index == target && item.content != dragEl.content
        );
        if (targetOindex == oindex) return;
        if (targetOindex < 0) {
          targetOindex = cloneList.value.length - 1;
        }
        let targetEl = cloneList.value[targetOindex];
        if (!targetEl) return;
        // 上一个index
        const lastIndex = target - speed;
        const activeEl = cloneList.value[oindex];
        const rect = gridRects[lastIndex];
        targetEl.x = rect.x;
        targetEl.y = rect.y;
        targetEl.oldindex = targetEl.index;
        targetEl.index = lastIndex;
        activeEl.oldindex = activeEl.index; //oIndex
        activeEl.index = toIndex;
        // 到达终点，如果是拖拽则不处理
        if (!distance && !isDrag.value) {
          const rect = gridRects[toIndex];
          const { x, y } = position || rect;
          activeEl.x = dragEl.x = x;
          activeEl.y = dragEl.y = y;
          // triggerRef(cloneList)
          if (emit) {
            emitting();
          }
        }
      }
    };
    /**
     * 为区分是主动调用还是内部方法
     */
    const move = (oindex: number, toIndex: number) => {
      active.value = -1;
      isDrag.value = false;
      isActuallyDragging.value = false;
      _move(oindex, toIndex);
    };
    // 临时处理 待有空再完善
    const REMOVE_TIME = 400;
    let removeTimer = null;
    const remove = (oindex: number) => {
      active.value = -1;
      isDrag.value = false;
      isActuallyDragging.value = false;

      clearTimeout(removeTimer);
      const item = cloneList.value[oindex];
      if (props.disabled || !item) return;
      item.show = false;
      const after = cloneList.value.length - 1;
      _move(oindex, after, item, false);
      setAfter(gridRects[after]);
      maxIndex.value--;
      const _remove = (_index = oindex) => {
        // 小程序 删除会闪一下 所以先关闭动画再开启
        // animation.value = false
        const row = Math.ceil((cloneList.value.length - 1 + extra.value) / props.column);
        if (row < rows.value) {
          leaveRow.value = rows.value - row;
        }
        cloneList.value.splice(_index, 1)[0];
        emitting();
        removeTimer = setTimeout(() => {
          if (!isMounted.value) return; // 组件卸载检查
          leaveRow.value = 0;
        }, REMOVE_TIME);
      };
      _remove();
    };
    const push = (...args: any) => {
      if (props.disabled) return;
      if (Array.isArray(args)) {
        Promise.all(args.map(async item => await add(item, true))).then(emitting);
      }
    };
    const add = (content: any, after: boolean) => {
      return new Promise(resolve => {
        const item = createGrid(content, after ? null : { x: -100, y: 0 });
        item.class = 'l-drag-enter';
        cloneList.value.push(item);
        const length = cloneList.value.length - 1;
        nextTick(() => {
          if (!isMounted.value) {
            resolve(false);
            return;
          } // 组件卸载检查
          sleep(() => {
            if (!isMounted.value) {
              resolve(false);
              return;
            } // 组件卸载检查
            item.class = 'l-drag-leave';
            _move(length, after ? length : 0, null, false);
            triggerRef(cloneList);
            resolve(true);
          });
        });
      });
    };
    const unshift = (...args: any) => {
      if (props.disabled) return;
      if (Array.isArray(args)) {
        Promise.all(args.map(async item => await add(item))).then(emitting);
      }
    };

    // 暂时先简单处理，待有空再完善
    const shift = () => {
      if (!cloneList.value.length) return;
      remove(cloneList.value.findIndex(item => item.index == 0) || 0);
    };
    const pop = () => {
      const length = cloneList.value.length - 1;
      if (length < 0) return;
      remove(cloneList.value.findIndex(item => item.index == length) || length);
    };
    // const splice = (start, count, ...context) => {
    // 	// 暂未实现
    // }
    const clear = () => {
      isInit.value = isDrag.value = isActuallyDragging.value = false;
      maxIndex.value = colmunId.value = active.value = -1;
      cloneList.value = [];
      gridRects = [];
    };
    const init = () => {
      if (!isMounted.value) return; // 组件卸载检查
      clear();
      createGridRects();
      nextTick(() => {
        if (!isMounted.value) return; // 组件卸载检查
        updateList(props.list);
        isInit.value = true;
      });
    };
    // 使用组件内部定义的debounce函数创建防抖版本的init
    const debouncedInit = debounce(init, 300, false);

    // 防抖处理list更新
    const scheduleInit = () => {
      if (isMounted.value) {
        debouncedInit();
      }
    };
    let count = 0;
    const getRect = () => {
      if (!isMounted.value) return; // 如果组件已卸载则不执行
      count++;
      // #ifndef APP-NVUE
      uni
        .createSelectorQuery()
        .in(app.proxy)
        .select('.l-drag')
        .boundingClientRect((res: UniNamespace.NodeInfo) => {
          if (res && isMounted.value) {
            // 确保组件仍然挂载
            areaWidth.value = res.width || 0;
            // 当 width 无法获取或为 0 时使用屏幕宽度兜底，避免出现 NaN/0 宽度导致的渲染异常
            if (!areaWidth.value) {
              try {
                areaWidth.value = uni.getSystemInfoSync().windowWidth || 375;
              } catch (e) {
                areaWidth.value = 375;
              }
            }
            // 小程序居然无法响应式？
            init();
          }
        })
        .exec();
      // #endif
      // #ifdef APP-NVUE
      sleep(() => {
        if (!isMounted.value) return; // 如果组件已卸载则不执行
        nextTick(() => {
          if (!isMounted.value) return; // 再次检查，确保组件仍然挂载
          try {
            if (!dragRef.value) {
              if (count < 5 && isMounted.value) {
                getRect();
              }
              return;
            }
            dom.getComponentRect(dragRef.value, res => {
              if (!isMounted.value) return; // 如果组件已卸载则不执行
              if (!res || !res.size) {
                if (count < 5 && isMounted.value) {
                  getRect();
                } else {
                  // 使用默认宽度
                  try {
                    areaWidth.value = uni.getSystemInfoSync().windowWidth || 375;
                    if (isMounted.value) init();
                  } catch (e) {
                    areaWidth.value = 375;
                    if (isMounted.value) init();
                  }
                }
                return;
              }

              areaWidth.value = res.size.width || 0;
              if (!areaWidth.value) {
                try {
                  areaWidth.value = uni.getSystemInfoSync().windowWidth || 375;
                } catch (e) {
                  areaWidth.value = 375;
                }
              }
              if (isMounted.value) init();
            });
          } catch (e) {
            console.error('l-drag getComponentRect error:', e);
            // 使用默认宽度
            try {
              areaWidth.value = uni.getSystemInfoSync().windowWidth || 375;
              if (isMounted.value) init();
            } catch (e) {
              areaWidth.value = 375;
              if (isMounted.value) init();
            }
          }
        });
      });
      // #endif
    };
    onMounted(() => {
      isMounted.value = true;
      getRect();
    });
    onUnmounted(() => {
      isMounted.value = false;
      clear();
      // 取消未执行的防抖函数
      debouncedInit.cancel();
      // 清理长按定时器
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
      }
    });
    watch(
      () => props.list,
      (newValue, oldValue) => {
        // 只有当数组长度或内容真正变化时才触发重新初始化
        if (isMounted.value) {
          scheduleInit();
        }
      },
      { deep: true }
    );

    // #ifdef VUE3
    expose({
      remove,
      // add,
      move,
      push,
      unshift,
      shift,
      pop,
      cloneList,
    });
    // #endif
    return {
      // #ifdef APP-NVUE
      dragRef,
      // #endif
      cloneList,

      areaStyles,
      innerStyles,
      viewStyles,
      scrollViewStyles,

      setDisabled,
      isDisabled,
      isReset,
      isDrag,
      isActuallyDragging,
      isLongPressActive,
      hasTouchMoved,

      active,
      animation,

      afterEl,
      ghostEl,
      beforeEl,

      touchStart,
      touchMove,
      touchEnd,

      remove,
      // add,
      move,
      push,
      unshift,
      // shift,
      // pop,
      props,
      // isDelete: props.delete,
      // ...toRefs(props)
    };
  },
});
</script>
<style lang="scss">
@import './index';
</style>
