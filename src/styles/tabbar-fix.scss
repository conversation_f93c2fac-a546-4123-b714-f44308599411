/* 修复TabBar的样式 */
.tab-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
  overflow: auto;
}

.tab-content {
  flex: 1;
  position: relative;
  height: 100%;
}

.tab-page {
  width: 100%;
  height: 100%;
}

/* 确保Tab页面内容完全显示 */
.home-view,
.app-center-view,
.data-space-view,
.my-view {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  position: relative;
  background-color: #f5f5f5;
  backface-visibility: hidden; /* 防止内容闪烁 */
  -webkit-transform: translateZ(0); /* 强制硬件加速 */
  /* 默认内边距适用于Android和H5 */
  padding-bottom: 40rpx;
}

/* 避免iOS上的滚动回弹效果影响体验 */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
