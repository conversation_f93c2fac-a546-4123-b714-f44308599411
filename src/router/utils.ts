/**
 * 路由工具类和核心路由系统
 * 提供路由相关的工具方法和核心路由功能
 */

import { RouteConfigManager, type UserMode } from './config';

export class SimpleRouter {
  /**
   * 获取当前页面路径
   */
  static getCurrentPath(): string {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      return `/${currentPage.route}`;
    }
    return '';
  }

  /**
   * 获取当前页面参数
   */
  static getCurrentParams(): Record<string, any> {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      return (currentPage as any).options || {};
    }
    return {};
  }

  /**
   * 构建带参数的URL
   */
  static buildUrl(path: string, params?: Record<string, any>): string {
    if (!params || Object.keys(params).length === 0) {
      return path;
    }

    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    return `${path}?${queryString}`;
  }

  /**
   * 解析URL参数
   */
  static parseUrlParams(url: string): Record<string, string> {
    const params: Record<string, string> = {};
    const queryIndex = url.indexOf('?');

    if (queryIndex === -1) {
      return params;
    }

    const queryString = url.substring(queryIndex + 1);
    const pairs = queryString.split('&');

    for (const pair of pairs) {
      const [key, value] = pair.split('=');
      if (key && value) {
        params[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    }

    return params;
  }

  /**
   * 获取用户模式对应的首页路径
   * 从配置项中获取
   */
  static getHomePage(userMode?: UserMode): string {
    try {
      return RouteConfigManager.getEntryPage(userMode);
    } catch (error) {
      return '/pages/mode-entry/index';
    }
  }

  /**
   * 获取用户模式对应的登录页路径
   * 从配置项中获取
   */
  static getLoginPage(userMode?: UserMode): string {
    try {
      return RouteConfigManager.getLoginPage(userMode);
    } catch (error) {
      return '/pages/mode-entry/index';
    }
  }

  /**
   * 模式切换
   */
  static async switchMode(mode: UserMode): Promise<{ success: boolean; message?: string }> {
    try {
      // 保存用户模式
      uni.setStorageSync('userMode', mode);

      // 尝试初始化TabBar配置（如果TabStore可用）
      try {
        const { useTabStore } = await import('@/store/tabStore');
        const tabStore = useTabStore();
        tabStore.initializeTabBar(mode);
      } catch (error) {
        console.warn('TabBar初始化跳过:', error);
      }

      // 检查登录状态
      const token = uni.getStorageSync('token');
      const isLoggedIn = !!token;

      let targetUrl: string;
      if (isLoggedIn) {
        // 已登录，跳转到对应的首页
        targetUrl = SimpleRouter.getHomePage(mode);
      } else {
        // 未登录，跳转到登录页
        targetUrl = SimpleRouter.getLoginPage(mode);
      }

      const success = await uni.reLaunch({ url: targetUrl });

      if (success) {
        return {
          success: true,
          message: `已切换到${mode === 'teacher' ? '教师' : '学生'}模式`,
        };
      } else {
        return {
          success: false,
          message: '页面跳转失败',
        };
      }
    } catch (error) {
      console.error('模式切换失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '模式切换失败',
      };
    }
  }

  /**
   * 登录成功处理
   */
  static async loginSuccess(): Promise<{ success: boolean; message?: string }> {
    try {
      const userMode = uni.getStorageSync('userMode');
      if (!userMode) {
        return {
          success: false,
          message: '用户模式未设置',
        };
      }

      const targetUrl = SimpleRouter.getHomePage(userMode);
      const success = await uni.reLaunch({ url: targetUrl });

      if (success) {
        return {
          success: true,
          message: '登录成功',
        };
      } else {
        return {
          success: false,
          message: '登录后跳转失败',
        };
      }
    } catch (error) {
      console.error('登录成功处理失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '登录成功处理失败',
      };
    }
  }

  /**
   * 退出登录
   */
  static async logout(): Promise<{ success: boolean; message?: string }> {
    try {
      // 清除存储的数据
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');

      // 跳转到模式选择页
      const success = await uni.reLaunch({ url: '/pages/mode-entry/index' });

      if (success) {
        return {
          success: true,
          message: '已退出登录',
        };
      } else {
        return {
          success: false,
          message: '退出登录跳转失败',
        };
      }
    } catch (error) {
      console.error('退出登录失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '退出登录失败',
      };
    }
  }

  /**
   * 返回上一页
   */
  static async back(): Promise<boolean> {
    return new Promise(resolve => {
      uni.navigateBack({
        success: () => resolve(true),
        fail: () => resolve(false),
      });
    });
  }
}
