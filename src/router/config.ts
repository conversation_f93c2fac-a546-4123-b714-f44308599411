/**
 * 路由配置文件
 * 只包含 TabBar 配置和角色配置映射，不包含业务逻辑
 */

export type UserMode = 'teacher' | 'student';

export interface TabBarItem {
  pagePath: string;
  text: string;
  iconPath: string;
  selectedIconPath: string;
}

export interface RoleConfig {
  mode: UserMode;
  entryPage: string;
  loginPage: string;
  tabBar: TabBarItem[];
}

/**
 * 教师端TabBar配置
 */
const teacherTabBar: TabBarItem[] = [
  {
    pagePath: '/pages/index/index',
    text: '首页',
    iconPath: '/static/tabbar/home.svg',
    selectedIconPath: '/static/tabbar/home_1.svg',
  },
  {
    pagePath: '/pages/app-center/index',
    text: '应用',
    iconPath: '/static/tabbar/apps.svg',
    selectedIconPath: '/static/tabbar/apps_1.svg',
  },
  {
    pagePath: '/pages/data-space/index',
    text: '空间',
    iconPath: '/static/tabbar/space.svg',
    selectedIconPath: '/static/tabbar/space_1.svg',
  },
  {
    pagePath: '/pages/my/index',
    text: '个人',
    iconPath: '/static/tabbar/user.svg',
    selectedIconPath: '/static/tabbar/user_1.svg',
  },
];

/**
 * 学生端TabBar配置
 */
const studentTabBar: TabBarItem[] = [
  {
    pagePath: '/pages-subpackages/students/study/index',
    text: '学习中心',
    iconPath: '/static/tabbar/home.svg',
    selectedIconPath: '/static/tabbar/home_1.svg',
  },
  {
    pagePath: '/pages-subpackages/students/center/index',
    text: '应用中心',
    iconPath: '/static/tabbar/apps.svg',
    selectedIconPath: '/static/tabbar/apps_1.svg',
  },
  {
    pagePath: '/pages-subpackages/students/personal/index',
    text: '我的',
    iconPath: '/static/tabbar/user.svg',
    selectedIconPath: '/static/tabbar/user_1.svg',
  },
];

/**
 * 角色配置映射
 */
export const roleConfigs: Record<UserMode, RoleConfig> = {
  teacher: {
    mode: 'teacher',
    entryPage: '/pages/index/index',
    loginPage: '/pages-subpackages/auth-pkg/login/teacher',
    tabBar: teacherTabBar,
  },
  student: {
    mode: 'student',
    entryPage: '/pages-subpackages/students/index/index',
    loginPage: '/pages-subpackages/auth-pkg/login/student',
    tabBar: studentTabBar,
  },
};

/**
 * 路由配置管理器
 */
export class RouteConfigManager {
  /**
   * 根据用户模式获取角色配置
   */
  static getRoleConfig(mode: UserMode): RoleConfig {
    return roleConfigs[mode];
  }

  /**
   * 根据用户模式获取入口页面
   */
  static getEntryPage(mode?: UserMode): string {
    const effectiveMode = mode || uni.getStorageSync('userMode') || null;

    if (!effectiveMode || !roleConfigs[effectiveMode]) {
      return '/pages/mode-entry/index';
    }

    return roleConfigs[effectiveMode].entryPage;
  }

  /**
   * 根据用户模式获取登录页面
   */
  static getLoginPage(mode?: UserMode): string {
    const effectiveMode = mode || uni.getStorageSync('userMode') || null;

    if (!effectiveMode || !roleConfigs[effectiveMode]) {
      return '/pages/mode-entry/index';
    }

    return roleConfigs[effectiveMode].loginPage;
  }

  /**
   * 根据用户模式获取TabBar配置
   */
  static getTabBarConfig(mode: UserMode): TabBarItem[] {
    return roleConfigs[mode].tabBar;
  }
}
