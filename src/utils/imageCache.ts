/**
 * 图片缓存工具
 * 只在app端生效，web端直接返回原始URL
 */

// 图片缓存映射表
const imageCacheMap = new Map<string, string>();

/**
 * 缓存单个图片
 * @param imageUrl 图片URL
 * @returns 缓存后的本地路径或原始URL
 */
export async function cacheImage(imageUrl: string): Promise<string> {
  // #ifdef APP-PLUS
  if (!imageUrl || typeof imageUrl !== 'string') {
    return imageUrl;
  }

  // 如果不是网络图片，直接返回
  if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // 检查缓存中是否已存在
  if (imageCacheMap.has(imageUrl)) {
    const cachedPath = imageCacheMap.get(imageUrl)!;
    // 验证缓存文件是否仍然存在
    try {
      const fileInfo = await uni.getFileInfo({ filePath: cachedPath });
      if (fileInfo.size > 0) {
        return cachedPath;
      }
    } catch (error) {
      // 缓存文件不存在，从缓存中移除
      imageCacheMap.delete(imageUrl);
    }
  }

  try {
    // 下载图片到本地
    const downloadResult = await new Promise<any>((resolve, reject) => {
      uni.downloadFile({
        url: imageUrl,
        success: resolve,
        fail: reject,
      });
    });

    if (downloadResult.statusCode === 200 && downloadResult.tempFilePath) {
      // 将临时文件保存到永久存储
      const savedPath = await saveImageToPermanentStorage(downloadResult.tempFilePath, imageUrl);

      // 保存到缓存映射
      imageCacheMap.set(imageUrl, savedPath);

      return savedPath;
    }
  } catch (error) {
    console.warn('图片缓存失败:', imageUrl, error);
  }

  // 缓存失败，返回原始URL
  return imageUrl;
  // #endif
}

/**
 * 将临时文件保存到永久存储
 * @param tempFilePath 临时文件路径
 * @param originalUrl 原始URL，用于生成文件名
 * @returns 永久存储路径
 */
async function saveImageToPermanentStorage(
  tempFilePath: string,
  _originalUrl: string
): Promise<string> {
  // #ifdef APP-PLUS
  try {
    // 直接使用uni.saveFile保存到本地存储
    const savedResult = await new Promise<any>((resolve, reject) => {
      uni.saveFile({
        tempFilePath,
        success: resolve,
        fail: reject,
      });
    });

    return savedResult.savedFilePath || tempFilePath;
  } catch (error) {
    console.warn('保存图片到永久存储失败:', error);
    // 保存失败，返回临时路径
    return tempFilePath;
  }
  // #endif
}

/**
 * 批量缓存图片
 * @param imageUrls 图片URL数组
 * @returns 缓存后的路径数组
 */
export async function cacheImages(imageUrls: string[]): Promise<string[]> {
  if (!Array.isArray(imageUrls)) {
    return [];
  }

  const cachePromises = imageUrls.map(url => cacheImage(url));
  return Promise.all(cachePromises);
}

/**
 * 清理图片缓存
 * @param maxCacheSize 最大缓存数量，超过则清理最旧的缓存
 */
export function cleanImageCache(maxCacheSize: number = 100): void {
  // #ifdef APP-PLUS
  if (imageCacheMap.size > maxCacheSize) {
    const entries = Array.from(imageCacheMap.entries());
    const toDelete = entries.slice(0, entries.length - maxCacheSize);

    toDelete.forEach(([url, path]) => {
      imageCacheMap.delete(url);
      // 尝试删除文件
      try {
        uni.removeSavedFile({ filePath: path });
      } catch (error) {
        console.warn('删除缓存文件失败:', path, error);
      }
    });
  }
  // #endif
}

/**
 * 获取缓存统计信息
 * @returns 缓存统计
 */
export function getCacheStats(): { count: number; urls: string[] } {
  return {
    count: imageCacheMap.size,
    urls: Array.from(imageCacheMap.keys()),
  };
}
