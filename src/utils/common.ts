export const isImageByUrl = (url: string) => {
  return (
    url.endsWith('.jpg') ||
    url.endsWith('.jpeg') ||
    url.endsWith('.png') ||
    url.endsWith('.gif') ||
    url.endsWith('.bmp') ||
    url.endsWith('.webp')
  );
};

export const isImageByExt = (ext: string) => {
  return (
    ext === 'jpg' ||
    ext === 'jpeg' ||
    ext === 'png' ||
    ext === 'gif' ||
    ext === 'bmp' ||
    ext === 'webp'
  );
};

/**
 * 根据文件大小转成合适单位文本
 * @param size 文件大小
 * @param config 配置 提供精度配置默认只保留整数
 * @returns 合适单位
 */
export function getFileSizeText(size: number, config?: { precision: number }): string {
  config = Object.assign({ precision: 0 }, config);
  if (size < 1024) {
    return `${size}B`;
  }
  if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(config.precision)}KB`;
  }
  if (size < 1024 * 1024 * 1024) {
    return `${(size / 1024 / 1024).toFixed(config.precision)}MB`;
  }
  return `${(size / 1024 / 1024 / 1024).toFixed(config.precision)}GB`;
}

/**
 * 查询是否刘海屏设备
 */
export function hasNotch() {
  try {
    if (plus && plus.navigator.hasNotchInScreen()) {
      console.log('此设备是刘海屏');
      return true;
    } else {
      console.log('此设备不是刘海屏');
      return false;
    }
  } catch (error) {
    console.log('此设备不是刘海屏');
    return false;
  }
}
/**
 * 获取顶部安全区域高度
 */
export function getTopSafeAreaHeight() {
  console.log('获取安全区域高度');
  const info = uni.getSystemInfoSync();

  if (info.platform === 'ios' && info.deviceType === 'phone' && hasNotch()) {
    return (info.statusBarHeight || 0) + 50;
  } else {
    return info.statusBarHeight || 0;
  }
}

/**
 * 获取底部安全区域高度
 */
export function getBottomSafeAreaHeight() {
  const info = uni.getSystemInfoSync();
  return info.safeAreaInsets?.bottom || 0;
}
