import { useDeviceDetection } from '@/hooks/useDeviceDetection';

/**
 * 响应式尺寸设置函数
 * 根据设备类型和尺寸智能返回适配的尺寸值
 *
 * @param value 基础尺寸值（数字）
 * @param unitOrScale 单位字符串（如'px', 'rem', 'rpx'）或放大比例数字，默认'px'或1
 * @returns 响应式适配后的尺寸字符串
 *
 * @example
 * // 基础用法
 * responsiveSize(14) // 手机: '14px', 小平板: '16px', 大平板: '18px'
 * responsiveSize(14, 'rem') // 手机: '14rem', 小平板: '16rem', 大平板: '18rem'
 * responsiveSize(14, 1.2) // 手机: '16.8px', 小平板: '19.2px', 大平板: '21.6px'
 * responsiveSize(14, 'rpx') // 手机: '14rpx', 小平板: '16rpx', 大平板: '18rpx'
 */
export function responsiveSize(value: number, unitOrScale: string | number = 'px'): string {
  // 获取设备检测实例
  const { deviceType, screenWidth } = useDeviceDetection();

  // 定义设备类型对应的缩放系数
  const getScaleFactor = (): number => {
    const currentDeviceType = deviceType.value;
    const currentScreenWidth = screenWidth.value;

    switch (currentDeviceType) {
      case 'mobile':
        return 1.0; // 手机端基准

      case 'tablet':
        // 平板端细化分级
        if (currentScreenWidth <= 768) {
          // 小平板 (iPad mini, 7-8寸平板)
          return 1.14; // 14px -> 16px
        } else if (currentScreenWidth <= 1024) {
          // 中等平板 (iPad, 9-10寸平板)
          return 1.29; // 14px -> 18px
        } else if (currentScreenWidth <= 1366) {
          // 大平板 (iPad Pro 11寸, Surface Pro)
          return 1.43; // 14px -> 20px
        } else {
          // 超大平板 (iPad Pro 12.9寸, Surface Studio)
          return 1.57; // 14px -> 22px
        }

      case 'desktop':
        // 桌面端
        if (currentScreenWidth <= 1440) {
          return 1.71; // 14px -> 24px
        } else if (currentScreenWidth <= 1920) {
          return 2.0; // 14px -> 28px
        } else {
          return 2.29; // 14px -> 32px (4K及以上)
        }

      default:
        return 1.0;
    }
  };

  // 计算最终尺寸值
  let finalValue: number;
  let unit: string;

  if (typeof unitOrScale === 'string') {
    // 传入的是单位字符串
    unit = unitOrScale;
    finalValue = Math.round(value * getScaleFactor());
  } else {
    // 传入的是放大比例数字
    unit = 'px'; // 默认单位
    finalValue = Math.round(value * getScaleFactor() * unitOrScale);
  }

  return `${finalValue}${unit}`;
}

/**
 * 批量响应式尺寸设置
 *
 * @param values 尺寸值数组或对象
 * @param unitOrScale 单位或缩放比例
 * @returns 处理后的尺寸值
 *
 * @example
 * responsiveSizes([12, 14, 16]) // ['12px', '16px', '18px'] (在小平板上)
 * responsiveSizes({width: 100, height: 200}, 'rpx') // {width: '114rpx', height: '228rpx'}
 */
export function responsiveSizes<T extends number[] | Record<string, number>>(
  values: T,
  unitOrScale: string | number = 'px'
): T extends number[] ? string[] : Record<keyof T, string> {
  if (Array.isArray(values)) {
    return values.map(value => responsiveSize(value, unitOrScale)) as T extends number[]
      ? string[]
      : Record<keyof T, string>;
  } else {
    const result = {} as Record<keyof T, string>;
    for (const key in values) {
      if (values.hasOwnProperty(key)) {
        // 使用类型断言确保 values[key] 是 number 类型
        result[key] = responsiveSize(values[key] as number, unitOrScale);
      }
    }
    return result as T extends number[] ? string[] : Record<keyof T, string>;
  }
}

/**
 * 获取当前设备的缩放信息（调试用）
 *
 * @returns 设备缩放信息
 */
export function getDeviceScaleInfo() {
  const { deviceType, screenWidth, screenHeight, devicePixelRatio } = useDeviceDetection();

  // 模拟计算缩放系数（复用上面的逻辑）
  const testValue = 14;
  const scaledValue = parseInt(responsiveSize(testValue, 'px'));
  const scaleFactor = scaledValue / testValue;

  return {
    deviceType: deviceType.value,
    screenWidth: screenWidth.value,
    screenHeight: screenHeight.value,
    devicePixelRatio: devicePixelRatio.value,
    scaleFactor,
    example: {
      input: `${testValue}px`,
      output: `${scaledValue}px`,
      description: getDeviceDescription(deviceType.value, screenWidth.value),
    },
  };
}

/**
 * 获取设备描述信息
 */
function getDeviceDescription(deviceType: string, screenWidth: number): string {
  switch (deviceType) {
    case 'mobile':
      return '手机端';
    case 'tablet':
      if (screenWidth <= 768) return '小平板 (7-8寸)';
      if (screenWidth <= 1024) return '中等平板 (9-10寸)';
      if (screenWidth <= 1366) return '大平板 (11寸)';
      return '超大平板 (12.9寸+)';
    case 'desktop':
      if (screenWidth <= 1440) return '桌面端 (1440p)';
      if (screenWidth <= 1920) return '桌面端 (1080p)';
      return '桌面端 (4K+)';
    default:
      return '未知设备';
  }
}

/**
 * 响应式字体大小设置（专门针对字体优化的版本）
 * 字体缩放相对保守，避免过大影响阅读体验
 *
 * @param fontSize 基础字体大小
 * @param unitOrScale 单位或缩放比例
 * @returns 适配后的字体大小
 */
export function responsiveFontSize(fontSize: number, unitOrScale: string | number = 'px'): string {
  const { deviceType, screenWidth } = useDeviceDetection();

  // 字体专用的保守缩放系数
  const getFontScaleFactor = (): number => {
    const currentDeviceType = deviceType.value;
    const currentScreenWidth = screenWidth.value;

    switch (currentDeviceType) {
      case 'mobile':
        return 1.0;

      case 'tablet':
        if (currentScreenWidth <= 768) return 1.1; // 更保守的字体缩放
        if (currentScreenWidth <= 1024) return 1.2;
        if (currentScreenWidth <= 1366) return 1.3;
        return 1.4;

      case 'desktop':
        if (currentScreenWidth <= 1440) return 1.5;
        if (currentScreenWidth <= 1920) return 1.6;
        return 1.8;

      default:
        return 1.0;
    }
  };

  let finalValue: number;
  let unit: string;

  if (typeof unitOrScale === 'string') {
    unit = unitOrScale;
    finalValue = Math.round(fontSize * getFontScaleFactor());
  } else {
    unit = 'px';
    finalValue = Math.round(fontSize * getFontScaleFactor() * unitOrScale);
  }

  return `${finalValue}${unit}`;
}

export default responsiveSize;
