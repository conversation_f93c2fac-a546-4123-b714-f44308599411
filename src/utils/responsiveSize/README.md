# 响应式尺寸工具 (ResponsiveSize)

一个智能的响应式尺寸适配工具，根据设备类型和屏幕尺寸自动调整UI元素的大小，确保在不同设备上都有最佳的视觉体验。

## ✨ 特性

- 🎯 **智能设备检测** - 自动识别手机、平板、桌面设备
- 📏 **精确尺寸适配** - 基于屏幕宽度的细粒度缩放
- 🎨 **多单位支持** - 支持 px、rem、rpx 等多种单位
- 🔢 **批量处理** - 支持数组和对象的批量尺寸转换
- 📱 **字体优化** - 专门的字体尺寸适配函数
- 🛠️ **调试友好** - 提供设备信息查看功能

## 📦 安装使用

```typescript
import {
  responsiveSize,
  responsiveSizes,
  responsiveFontSize,
  getDeviceScaleInfo,
} from '@/utils/responsiveSize';
```

## 🎯 设备适配规则

### 缩放系数表

| 设备类型    | 屏幕宽度范围 | 缩放系数 | 示例 (14px) |
| ----------- | ------------ | -------- | ----------- |
| 📱 手机端   | -            | 1.0      | 14px        |
| 📱 小平板   | ≤ 768px      | 1.14     | 16px        |
| 📱 中等平板 | ≤ 1024px     | 1.29     | 18px        |
| 📱 大平板   | ≤ 1366px     | 1.43     | 20px        |
| 📱 超大平板 | > 1366px     | 1.57     | 22px        |
| 🖥️ 小桌面   | ≤ 1440px     | 1.71     | 24px        |
| 🖥️ 标准桌面 | ≤ 1920px     | 2.0      | 28px        |
| 🖥️ 4K桌面   | > 1920px     | 2.29     | 32px        |

### 字体专用缩放系数（更保守）

| 设备类型    | 屏幕宽度范围 | 字体缩放系数 | 示例 (14px) |
| ----------- | ------------ | ------------ | ----------- |
| 📱 手机端   | -            | 1.0          | 14px        |
| 📱 小平板   | ≤ 768px      | 1.1          | 15px        |
| 📱 中等平板 | ≤ 1024px     | 1.2          | 17px        |
| 📱 大平板   | ≤ 1366px     | 1.3          | 18px        |
| 📱 超大平板 | > 1366px     | 1.4          | 20px        |
| 🖥️ 小桌面   | ≤ 1440px     | 1.5          | 21px        |
| 🖥️ 标准桌面 | ≤ 1920px     | 1.6          | 22px        |
| 🖥️ 4K桌面   | > 1920px     | 1.8          | 25px        |

## 📚 API 参考

### responsiveSize(value, unitOrScale?)

基础响应式尺寸转换函数。

**参数：**

- `value: number` - 基础尺寸值
- `unitOrScale?: string | number` - 单位字符串或缩放比例，默认 'px'

**返回：** `string` - 适配后的尺寸字符串

### responsiveSizes(values, unitOrScale?)

批量响应式尺寸转换函数。

**参数：**

- `values: number[] | Record<string, number>` - 尺寸值数组或对象
- `unitOrScale?: string | number` - 单位字符串或缩放比例，默认 'px'

**返回：** `string[] | Record<string, string>` - 处理后的尺寸值

### responsiveFontSize(fontSize, unitOrScale?)

专门针对字体优化的响应式尺寸函数。

**参数：**

- `fontSize: number` - 基础字体大小
- `unitOrScale?: string | number` - 单位字符串或缩放比例，默认 'px'

**返回：** `string` - 适配后的字体大小

### getDeviceScaleInfo()

获取当前设备的缩放信息（调试用）。

**返回：** 设备缩放信息对象

## 🚀 使用示例

### 1. 基础用法

```typescript
// 在手机端 (375px 宽度)
responsiveSize(14); // 返回: "14px"
responsiveSize(20); // 返回: "20px"
responsiveSize(16); // 返回: "16px"

// 在小平板端 (768px 宽度)
responsiveSize(14); // 返回: "16px"  (14 * 1.14 = 15.96 ≈ 16)
responsiveSize(20); // 返回: "23px"  (20 * 1.14 = 22.8 ≈ 23)
responsiveSize(16); // 返回: "18px"  (16 * 1.14 = 18.24 ≈ 18)

// 在中等平板端 (1024px 宽度)
responsiveSize(14); // 返回: "18px"  (14 * 1.29 = 18.06 ≈ 18)
responsiveSize(20); // 返回: "26px"  (20 * 1.29 = 25.8 ≈ 26)
responsiveSize(16); // 返回: "21px"  (16 * 1.29 = 20.64 ≈ 21)

// 在桌面端 (1920px 宽度)
responsiveSize(14); // 返回: "28px"  (14 * 2.0 = 28)
responsiveSize(20); // 返回: "40px"  (20 * 2.0 = 40)
responsiveSize(16); // 返回: "32px"  (16 * 2.0 = 32)
```

### 2. 不同单位使用

```typescript
// 使用 rem 单位
// 在手机端
responsiveSize(1, 'rem'); // 返回: "1rem"
responsiveSize(1.5, 'rem'); // 返回: "2rem"  (1.5 * 1.0 = 1.5 ≈ 2)

// 在小平板端
responsiveSize(1, 'rem'); // 返回: "1rem"  (1 * 1.14 = 1.14 ≈ 1)
responsiveSize(1.5, 'rem'); // 返回: "2rem"  (1.5 * 1.14 = 1.71 ≈ 2)

// 在桌面端
responsiveSize(1, 'rem'); // 返回: "2rem"  (1 * 2.0 = 2)
responsiveSize(1.5, 'rem'); // 返回: "3rem"  (1.5 * 2.0 = 3)

// 使用 rpx 单位 (uni-app)
// 在手机端
responsiveSize(32, 'rpx'); // 返回: "32rpx"
responsiveSize(48, 'rpx'); // 返回: "48rpx"

// 在小平板端
responsiveSize(32, 'rpx'); // 返回: "36rpx"  (32 * 1.14 = 36.48 ≈ 36)
responsiveSize(48, 'rpx'); // 返回: "55rpx"  (48 * 1.14 = 54.72 ≈ 55)
```

### 3. 自定义缩放比例

```typescript
// 使用自定义缩放比例
// 在手机端
responsiveSize(14, 1.2); // 返回: "17px"  (14 * 1.0 * 1.2 = 16.8 ≈ 17)
responsiveSize(20, 0.8); // 返回: "16px"  (20 * 1.0 * 0.8 = 16)

// 在小平板端
responsiveSize(14, 1.2); // 返回: "19px"  (14 * 1.14 * 1.2 = 19.152 ≈ 19)
responsiveSize(20, 0.8); // 返回: "18px"  (20 * 1.14 * 0.8 = 18.24 ≈ 18)

// 在桌面端
responsiveSize(14, 1.2); // 返回: "34px"  (14 * 2.0 * 1.2 = 33.6 ≈ 34)
responsiveSize(20, 0.8); // 返回: "32px"  (20 * 2.0 * 0.8 = 32)
```

### 4. 批量尺寸处理

```typescript
// 数组批量处理
// 在手机端
responsiveSizes([12, 14, 16, 18]);
// 返回: ["12px", "14px", "16px", "18px"]

// 在小平板端
responsiveSizes([12, 14, 16, 18]);
// 返回: ["14px", "16px", "18px", "21px"]
// 计算: [12*1.14≈14, 14*1.14≈16, 16*1.14≈18, 18*1.14≈21]

// 在桌面端
responsiveSizes([12, 14, 16, 18]);
// 返回: ["24px", "28px", "32px", "36px"]
// 计算: [12*2.0=24, 14*2.0=28, 16*2.0=32, 18*2.0=36]

// 对象批量处理
// 在手机端
responsiveSizes({
  width: 100,
  height: 200,
  padding: 16,
  margin: 8,
});
// 返回: {
//   width: "100px",
//   height: "200px",
//   padding: "16px",
//   margin: "8px"
// }

// 在小平板端
responsiveSizes({
  width: 100,
  height: 200,
  padding: 16,
  margin: 8,
});
// 返回: {
//   width: "114px",    // 100 * 1.14 = 114
//   height: "228px",   // 200 * 1.14 = 228
//   padding: "18px",   // 16 * 1.14 = 18.24 ≈ 18
//   margin: "9px"      // 8 * 1.14 = 9.12 ≈ 9
// }

// 使用不同单位的批量处理
// 在小平板端
responsiveSizes([1, 1.5, 2], 'rem');
// 返回: ["1rem", "2rem", "2rem"]
// 计算: [1*1.14≈1, 1.5*1.14≈2, 2*1.14≈2]

responsiveSizes({ width: 32, height: 48 }, 'rpx');
// 返回: {width: "36rpx", height: "55rpx"}
// 计算: {width: 32*1.14≈36, height: 48*1.14≈55}
```

### 5. 字体专用函数

```typescript
// 字体尺寸适配（使用更保守的缩放）
// 在手机端
responsiveFontSize(14); // 返回: "14px"
responsiveFontSize(16); // 返回: "16px"
responsiveFontSize(18); // 返回: "18px"

// 在小平板端
responsiveFontSize(14); // 返回: "15px"  (14 * 1.1 = 15.4 ≈ 15)
responsiveFontSize(16); // 返回: "18px"  (16 * 1.1 = 17.6 ≈ 18)
responsiveFontSize(18); // 返回: "20px"  (18 * 1.1 = 19.8 ≈ 20)

// 在中等平板端
responsiveFontSize(14); // 返回: "17px"  (14 * 1.2 = 16.8 ≈ 17)
responsiveFontSize(16); // 返回: "19px"  (16 * 1.2 = 19.2 ≈ 19)
responsiveFontSize(18); // 返回: "22px"  (18 * 1.2 = 21.6 ≈ 22)

// 在桌面端 (1920px)
responsiveFontSize(14); // 返回: "22px"  (14 * 1.6 = 22.4 ≈ 22)
responsiveFontSize(16); // 返回: "26px"  (16 * 1.6 = 25.6 ≈ 26)
responsiveFontSize(18); // 返回: "29px"  (18 * 1.6 = 28.8 ≈ 29)

// 字体使用不同单位
// 在小平板端
responsiveFontSize(1, 'rem'); // 返回: "1rem"  (1 * 1.1 = 1.1 ≈ 1)
responsiveFontSize(1.5, 'rem'); // 返回: "2rem"  (1.5 * 1.1 = 1.65 ≈ 2)

// 在桌面端
responsiveFontSize(1, 'rem'); // 返回: "2rem"  (1 * 1.6 = 1.6 ≈ 2)
responsiveFontSize(1.5, 'rem'); // 返回: "2rem"  (1.5 * 1.6 = 2.4 ≈ 2)
```

### 6. 设备信息调试

```typescript
// 获取当前设备缩放信息
getDeviceScaleInfo();

// 在手机端 (iPhone 12, 390px 宽度) 返回:
// {
//   deviceType: "mobile",
//   screenWidth: 390,
//   screenHeight: 844,
//   devicePixelRatio: 3,
//   scaleFactor: 1,
//   example: {
//     input: "14px",
//     output: "14px",
//     description: "手机端"
//   }
// }

// 在小平板端 (iPad mini, 768px 宽度) 返回:
// {
//   deviceType: "tablet",
//   screenWidth: 768,
//   screenHeight: 1024,
//   devicePixelRatio: 2,
//   scaleFactor: 1.14,
//   example: {
//     input: "14px",
//     output: "16px",
//     description: "小平板 (7-8寸)"
//   }
// }

// 在桌面端 (1920px 宽度) 返回:
// {
//   deviceType: "desktop",
//   screenWidth: 1920,
//   screenHeight: 1080,
//   devicePixelRatio: 1,
//   scaleFactor: 2,
//   example: {
//     input: "14px",
//     output: "28px",
//     description: "桌面端 (1080p)"
//   }
// }
```

## 🎨 实际应用场景

### 1. Vue 组件中使用

```vue
<template>
  <div class="card" :style="cardStyles">
    <h2 class="title" :style="titleStyles">标题</h2>
    <p class="content" :style="contentStyles">内容文本</p>
    <button class="btn" :style="buttonStyles">按钮</button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { responsiveSize, responsiveFontSize, responsiveSizes } from '@/utils/responsiveSize';

// 单个样式计算
const cardStyles = computed(() => ({
  padding: responsiveSize(16), // 手机: "16px", 平板: "18px", 桌面: "32px"
  borderRadius: responsiveSize(8), // 手机: "8px", 平板: "9px", 桌面: "16px"
  margin: responsiveSize(12), // 手机: "12px", 平板: "14px", 桌面: "24px"
}));

// 字体样式
const titleStyles = computed(() => ({
  fontSize: responsiveFontSize(18), // 手机: "18px", 平板: "20px", 桌面: "29px"
  lineHeight: responsiveFontSize(24), // 手机: "24px", 平板: "26px", 桌面: "38px"
}));

const contentStyles = computed(() => ({
  fontSize: responsiveFontSize(14), // 手机: "14px", 平板: "15px", 桌面: "22px"
  lineHeight: responsiveFontSize(20), // 手机: "20px", 平板: "22px", 桌面: "32px"
}));

// 批量样式计算
const buttonStyles = computed(() => {
  const sizes = responsiveSizes({
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 4,
  });

  return {
    ...sizes,
    fontSize: responsiveFontSize(14),
    // 在小平板上结果为:
    // paddingTop: "9px", paddingBottom: "9px",
    // paddingLeft: "18px", paddingRight: "18px",
    // borderRadius: "5px", fontSize: "15px"
  };
});
</script>
```

## 💡 最佳实践

### 1. 性能优化

```typescript
// ✅ 推荐：在计算属性中使用，避免重复计算
const styles = computed(() => ({
  padding: responsiveSize(16),
  margin: responsiveSize(12),
}));

// ❌ 不推荐：在模板中直接调用
// <div :style="{ padding: responsiveSize(16) }">
```

### 2. 批量处理优先

```typescript
// ✅ 推荐：使用批量处理函数
const sizes = responsiveSizes({
  padding: 16,
  margin: 12,
  borderRadius: 8,
});

// ❌ 不推荐：多次单独调用
// const padding = responsiveSize(16);
// const margin = responsiveSize(12);
// const borderRadius = responsiveSize(8);
```

### 3. 字体使用专用函数

```typescript
// ✅ 推荐：字体使用专用函数（缩放更保守）
const titleSize = responsiveFontSize(18); // 更适合阅读

// ❌ 不推荐：字体使用通用函数（可能过大）
// const titleSize = responsiveSize(18);     // 在大屏上可能过大
```

### 4. 单位选择建议

```typescript
// Web 应用推荐使用 px
const webSize = responsiveSize(16, 'px');

// uni-app 推荐使用 rpx
const uniSize = responsiveSize(32, 'rpx');

// 需要相对单位时使用 rem
const relativeSize = responsiveSize(1, 'rem');
```

## ❓ 常见问题

### Q1: 为什么字体缩放比普通元素保守？

**A:** 字体过大会影响阅读体验，因此 `responsiveFontSize` 使用更保守的缩放系数，确保在大屏设备上字体不会过大。

```typescript
// 普通元素在桌面端 (1920px)
responsiveSize(14); // "28px" (2.0倍)

// 字体在桌面端 (1920px)
responsiveFontSize(14); // "22px" (1.6倍) - 更保守
```

### Q2: 如何调试当前设备的缩放信息？

**A:** 使用 `getDeviceScaleInfo()` 函数查看当前设备信息：

```typescript
console.log(getDeviceScaleInfo());
// 输出设备类型、屏幕尺寸、缩放系数等信息
```

### Q3: 可以自定义缩放系数吗？

**A:** 可以通过传入数字参数来自定义缩放：

```typescript
// 在设备缩放基础上再放大 1.2 倍
responsiveSize(14, 1.2);

// 在设备缩放基础上缩小到 0.8 倍
responsiveSize(14, 0.8);
```

### Q4: 支持负数和小数吗？

**A:** 支持，但会进行四舍五入：

```typescript
responsiveSize(-10); // 负数正常处理
responsiveSize(14.5); // 小数会根据缩放后四舍五入
```

### Q5: 在服务端渲染 (SSR) 中如何使用？

**A:** 服务端渲染时设备检测可能不准确，建议：

```typescript
// 设置默认值或在客户端重新计算
const size = process.client ? responsiveSize(14) : '14px';
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**注意：** 所有示例中的返回结果都是基于实际的缩放系数计算得出，可能因设备检测结果而略有差异。

### 2. React 组件中使用

```tsx
import React, { useMemo } from 'react';
import { responsiveSize, responsiveFontSize, responsiveSizes } from '@/utils/responsiveSize';

const Card: React.FC = () => {
  // 计算响应式样式
  const styles = useMemo(() => {
    const spacing = responsiveSizes([8, 12, 16, 20]);
    // 在桌面端返回: ["16px", "24px", "32px", "40px"]

    return {
      container: {
        padding: spacing[2], // "32px" (桌面端)
        margin: spacing[1], // "24px" (桌面端)
        borderRadius: spacing[0], // "16px" (桌面端)
        gap: spacing[1], // "24px" (桌面端)
      },
      title: {
        fontSize: responsiveFontSize(20), // "32px" (桌面端 1920px)
        marginBottom: spacing[1], // "24px" (桌面端)
      },
      button: {
        padding: `${spacing[0]} ${spacing[2]}`, // "16px 32px" (桌面端)
        fontSize: responsiveFontSize(14), // "22px" (桌面端)
      },
    };
  }, []);

  return (
    <div style={styles.container}>
      <h2 style={styles.title}>响应式标题</h2>
      <button style={styles.button}>响应式按钮</button>
    </div>
  );
};
```

### 3. CSS-in-JS 中使用

```typescript
import styled from 'styled-components';
import { responsiveSize, responsiveFontSize } from '@/utils/responsiveSize';

// 在样式组件中使用
const StyledCard = styled.div`
  padding: ${responsiveSize(16)}; /* 动态计算内边距 */
  margin: ${responsiveSize(12)}; /* 动态计算外边距 */
  border-radius: ${responsiveSize(8)}; /* 动态计算圆角 */
  font-size: ${responsiveFontSize(14)}; /* 动态计算字体大小 */

  /* 在小平板上编译为: */
  /* padding: 18px; */
  /* margin: 14px; */
  /* border-radius: 9px; */
  /* font-size: 15px; */
`;

// 主题配置中使用
const theme = {
  spacing: {
    xs: responsiveSize(4), // 手机: "4px", 桌面: "8px"
    sm: responsiveSize(8), // 手机: "8px", 桌面: "16px"
    md: responsiveSize(16), // 手机: "16px", 桌面: "32px"
    lg: responsiveSize(24), // 手机: "24px", 桌面: "48px"
    xl: responsiveSize(32), // 手机: "32px", 桌面: "64px"
  },
  fontSize: {
    xs: responsiveFontSize(12), // 手机: "12px", 桌面: "19px"
    sm: responsiveFontSize(14), // 手机: "14px", 桌面: "22px"
    md: responsiveFontSize(16), // 手机: "16px", 桌面: "26px"
    lg: responsiveFontSize(18), // 手机: "18px", 桌面: "29px"
    xl: responsiveFontSize(20), // 手机: "20px", 桌面: "32px"
  },
};
```

### 4. uni-app 中使用

```vue
<template>
  <view class="page" :style="pageStyles">
    <view class="header" :style="headerStyles">
      <text class="title" :style="titleStyles">标题</text>
    </view>
    <view class="content" :style="contentStyles">
      <text class="text" :style="textStyles">内容</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { responsiveSize, responsiveFontSize, responsiveSizes } from '@/utils/responsiveSize';

// 使用 rpx 单位适配 uni-app
const pageStyles = computed(() => ({
  padding: responsiveSize(32, 'rpx'), // 手机: "32rpx", 平板: "36rpx"
}));

const headerStyles = computed(() => {
  const sizes = responsiveSizes(
    {
      height: 88,
      paddingLeft: 32,
      paddingRight: 32,
    },
    'rpx'
  );

  return sizes;
  // 在小平板上返回:
  // { height: "100rpx", paddingLeft: "36rpx", paddingRight: "36rpx" }
});

const titleStyles = computed(() => ({
  fontSize: responsiveFontSize(36, 'rpx'), // 手机: "36rpx", 平板: "40rpx"
  fontWeight: 'bold',
}));

const contentStyles = computed(() => ({
  marginTop: responsiveSize(24, 'rpx'), // 手机: "24rpx", 平板: "27rpx"
  padding: responsiveSize(16, 'rpx'), // 手机: "16rpx", 平板: "18rpx"
}));

const textStyles = computed(() => ({
  fontSize: responsiveFontSize(28, 'rpx'), // 手机: "28rpx", 平板: "31rpx"
  lineHeight: responsiveFontSize(40, 'rpx'), // 手机: "40rpx", 平板: "44rpx"
}));
</script>
```

```

```
