declare module 'aliyun-aliplayer' {
  interface AliPlayerConfig {
    id: string;
    source: string;
    width?: string | number;
    height?: string | number;
    autoplay?: boolean;
    muted?: boolean;
    controlBarVisibility?: 'hover' | 'click' | 'always';
    autoSize?: boolean;
    cover?: string;
    loop?: boolean;
    skinLayout?: any[];
    preload?: boolean;
    language?: string;
    rePlay?: boolean;
    playsinline?: boolean;
    x5_type?: string;
    x5_fullscreen?: boolean;
    components?: Array<{
      name: string;
      type: string;
      args: any[];
    }>;
    [key: string]: any;
  }

  interface AliPlayerInstance {
    play(): void;
    pause(): void;
    seek(time: number): void;
    getCurrentTime(): number;
    getDuration(): number;
    setVolume(volume: number): void;
    getVolume(): number;
    loadByUrl(url: string): void;
    dispose(): void;
    on(event: string, callback: Function): void;
    off(event: string, callback?: Function): void;
    getStatus(): string;
    getOptions(): any;
    setPlayerSize(width: number | string, height: number | string): void;
    fullscreenService: {
      requestFullScreen(): void;
      cancelFullScreen(): void;
      getIsFullScreen(): boolean;
    };
  }

  class Aliplayer implements AliPlayerInstance {
    constructor(config: AliPlayerConfig);

    play(): void;
    pause(): void;
    seek(time: number): void;
    getCurrentTime(): number;
    getDuration(): number;
    setVolume(volume: number): void;
    getVolume(): number;
    loadByUrl(url: string): void;
    dispose(): void;
    on(event: string, callback: Function): void;
    off(event: string, callback?: Function): void;
    getStatus(): string;
    getOptions(): any;
    setPlayerSize(width: number | string, height: number | string): void;
    fullscreenService: {
      requestFullScreen(): void;
      cancelFullScreen(): void;
      getIsFullScreen(): boolean;
    };
  }

  export = Aliplayer;
}

declare module 'aliyun-aliplayer/build/skins/default/aliplayer-min.css' {
  const content: any;
  export default content;
}

// 全局类型定义
declare global {
  interface Window {
    AliPlayerComponent?: {
      PreviewVodComponent?: string;
    };
  }
}
