import type { PageParams } from './common';

// 聊天历史记录请求参数
export interface GetHistoriesParams extends PageParams {
  appId?: string;
  shareId?: string;
  outLinkUid?: string;
  keyword?: string;
}

// 历史记录基本类型
export interface HistoryItemType {
  chatId: string;
  updateTime?: string;
  tenantId?: string;
  title: string;
  appName?: string;
  appAvatarUrl?: string;
}

// 聊天历史记录项类型
export interface ChatHistoryItemType extends HistoryItemType {
  id?: string;
  tenantAppId?: string;
}
