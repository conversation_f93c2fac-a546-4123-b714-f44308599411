import type { DataSource } from '@/constants/api';
import type { PageParams } from './common';
import type {
  AppTypeEnum,
  ModeTypeEnum,
  PermissionTypeEnum,
  AppointedTypeEnum,
} from '@/constants/api/app';

// 应用列表请求参数
export interface AppListParams extends PageParams {
  keyword?: string;
}

// 标签类型
export interface LabelItemType {
  id: string;
  name: string;
}

// 应用列表项类型
export interface AppListItemType {
  id: string;
  name: string;
  avatarUrl: string;
  finalAppId: string;
  tenantId: string;
  intro: string;
  isOwner: boolean;
  canWrite: boolean;
  mode: ModeTypeEnum;
  permission: PermissionTypeEnum;
  appointedType?: AppointedTypeEnum;
  tenantSceneIds: string[];
  tenantLabelIds: string[];
  tmbId: string;
  appId: string;
  source: DataSource;
  updateTime: string;
  labelList?: LabelItemType[];
  type: AppTypeEnum;
  sceneId?: string;
  labelId?: string;
  homePageUse?: number;
  appTaskTypeId?: string | null;
  linkUrl?: string;
  config?: number;
  isCommonApp?: number;
}

// 常用应用列表项类型
export type CommonAppType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tmbId: number;
  tenantAppId: number;
  sort: number;
  isNavbar: number;
  navbarOrder: number;
  tenantApp: AppListItemType;
};

// 精选推荐
export type RecommendModuleItem = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  industry: number;
  dictId: number;
  imageUrl: string;
  imageBackgroundColor: string;
  type: number;
  appId: number;
  functionalModuleId: number;
  status: number;
  sort: number;
  moduleName: string;
  appAvatarUrl: string;
  appIntro: string;
  appName: string;
  functionalModuleAvatarUrl: string;
  functionalModuleIntro: string;
  functionalModuleName: string;
  tenantAppId: any;
  tenantApp: AppListItemType;
  tenantSceneIds: any[];
};

export interface FileModuleItem {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  fileId: string;
  tenantId: string;
  tmbId: string;
  useNum: number;
  status: number;
  fileName: string;
  fileUrl: string;
  fileKey: string;
  fileSize: number;
  fileJson: string;
  fileType: string;
  spaceName: string;
  username: string;
}
