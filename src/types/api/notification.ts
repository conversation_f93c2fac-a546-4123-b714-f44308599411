import type { PageParams, PagingData } from './common';

/**
 * 通知页面查询请求参数
 */
export interface NotificationPageParams extends PageParams {
  typeCode: string; // 通知类型代码，如 "RESOURCE"
}

/**
 * 通知项数据结构
 */
export interface NotificationItem {
  id: number;
  title: string;
  content: string;
  senderId: number;
  typeModuleId: number;
  status: number;
  readStatus: number; // 阅读状态：0-未读，1-已读
  notificationTime: string;
  createTime: string;
  extensionParams: {
    [key: string]: any; // 扩展参数，如resourceId等
  };
}

/**
 * 通知页面查询响应数据
 */
export interface NotificationPageResponse extends PagingData<NotificationItem> {
  orders: any[];
  searchCount: boolean;
}

/**
 * 标记通知为已读请求参数
 */
export interface MarkNotificationReadParams {
  notificationId: number; // 通知ID
}
