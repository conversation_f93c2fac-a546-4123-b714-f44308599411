export interface GetUserResponse {
  id: string; // 用户 ID
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  isDeleted: number; // 是否删除 (0: 否, 1: 是)
  status: number; // 状态 (1: 正常, 0: 禁用)
  username: string; // 用户名
  password: string; // 密码 (建议加密存储)
  account: string; // 账号
  phone: string; // 电话
  email: string; // 邮箱
  avatar: string; // 头像 URL
  unionId: string; // 微信 unionId
  openId: string; // 微信 openId
  eduId: string; // 教育 ID
  source: number; // 来源
  pwdUpdateTime: string; // 密码更新时间
  /** 性别 0: 未知, 1: 男, 2: 女 */
  gender: number;
  /** 班级名称 */
  clazzName?: string;
  /** 年级名称 */
  gradeName?: string;
}
