// 笔记查询参数
export type StudentNoteListParams = {
  pageNum: number;
  pageSize: number;
  type: number;
};

// 单条笔记项类型
export type StudentNoteItem = {
  homeworkId?: number;
  createTime: string;
  id: number;
  noteContent: string;
  questionId: number;
  refTitle: string;
  resourceId: number;
  tenandId: number;
  tmdId: number;
  type: number;
  updateTime: string;
};
export type StudentNoteAddParams = {
  homeworkId?: number;
  noteContent: string;
  refId: number;
  type: number;
};

export type StudentNoteUpdateParams = {
  id: number;
  noteContent: string;
};
