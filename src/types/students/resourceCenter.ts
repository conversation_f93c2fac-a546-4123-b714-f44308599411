export type SubjectsItem = {
  code: string;
  id: number;
  name: string;
  sortOrder: number;
};

export type SubjectsList = {
  subjects: SubjectsItem[];
};

export type TextbookVersionsItem = {
  gradeId: number;
};

export interface TextbookVersions {
  textbookVersions: TextbookVersionsList[];
}

export type TextbookVersionsList = {
  code: string;
  createTime: string;
  id: string;
  isDeleted: number;
  name: string;
  stageId: number;
  subjectId: number;
  tenantId: number;
  updateTime: string;
};

export type GradesList = {
  grades: GradesItem[];
};

export type GradesItem = {
  code: string;
  id: number;
  name: string;
  sortOrder: number;
  stageId: number;
};

export type HotSearch = {
  resourceNames: string[];
};

export type ResourceTypes = {
  resourceTypes: ResourceTypesItem[];
};

export type ResourceTypesItem = {
  code: string;
  hasChild: number;
  id: number;
  name: string;
  parentId: number;
  sortOrder: number;
};

export type ResourceFormats = {
  resourceFormats: ResourceFormatsItem[];
};

export type ResourceFormatsItem = {
  children: ResourceFormatsItem[];
  code: string;
  hasChild: number;
  id: number;
  name: string;
  parentId: number;
  sortOrder: number;
};

// 资源详情项
export type ResourceDetailItem = {
  areaId: number;
  areaName: string;
  attribution: string;
  createTime: string;
  description: string;
  fileFormatId: number;
  fileFormatName: string;
  fileName: string;
  fileSize: number;
  fileUrl: string;
  gradeId: number;
  gradeName: string;
  id: number;
  resourceTypeId: number;
  resourceTypeName: string;
  stageId: number;
  stageName: string;
  subjectId: number;
  subjectName: string;
  textbookVersionId: number;
  textbookVersionName: string;
  textbookVolumeId: number;
  textbookVolumeName: string;
  title: string;
  updateTime: string;
};

// 资源搜索参数
export type SearchResourcesParams = {
  aiResourceIds: number[]; // AI资源ID
  searchInfo: string; // 搜索信息
  attribution: number; // 归属：0全部，1个人，2学校，3官方，4第三方
  fileFormatIds: number[] | number; // 资源格式ID
  gradeId: number; // 年级ID
  pageNum: number; // 页码
  pageSize: number; // 每页大小
  resourceTypeIds: number[] | number; // 资源类型ID
  subjectIds: number[] | number; // 学科ID
  textbookVersionId?: number; // 教材版本ID
  textbookVolumeId?: number; // 教材册别ID
  textbookVolumeCategory?: number; // 教材册别分类：0不区分，1上册，2下册
};

// 资源搜索响应
export type ResourceSearchResponse = {
  aiResourceIds: number[];
  aiSearchResults: ResourceDetailItem[];
  pageNum: number;
  pageSize: number;
  resources: ResourceDetailItem[];
  total: number;
  totalPages: number;
};

// 收藏资源搜索参数
export type AppResourceCollectSearchRequest = {
  attribution?: number; // 归属ID：1个人，2学校，3官方（为空或0时不作为查询条件）
  fileFormatIds?: number[]; // 资源格式ID数组（为空或包含0时不作为查询条件）
  gradeId?: number; // 年级ID（为空或0时不作为查询条件）
  pageNum: number; // 页码
  pageSize: number; // 每页大小
  resourceTypeIds?: number[]; // 资源类型ID数组（为空或包含0时不作为查询条件）
  searchInfo?: string; // 搜索内容（支持拆词搜索）
  subjectId?: number; // 学科ID（为空或0时不作为查询条件）
  textbookVersionId?: number; // 教材版本ID（为空或0时不作为查询条件）
  textbookVolumeId?: number; // 教材册别ID（为空或0时不作为查询条件）
  textbookVolumeCategory?: number; // 教材册别分类：0不区分，1上册，2下册
  subjectName: string; // 学科名称
  gradeName: string; // 年级名称
};

// 收藏资源项
export type CollectedResourceItem = {
  attribution: number; // 归属
  attributionName: string; // 归属名称
  collectId: number; // 收藏ID
  fileFormatId: number; // 文件格式ID
  fileFormatName: string; // 文件格式名称
  fileSize: number; // 文件大小
  resourceId: number; // 资源ID
  resourceTypeId: number; // 资源类型ID
  resourceTypeName: string; // 资源类型名称
  title: string; // 标题
  updateTime: string; // 更新时间
  gradeName: string; // 年级名称
  subjectName: string; // 学科名称
};

// 收藏资源搜索响应
export type CollectedResourceSearchResponse = {
  pageNum: number; // 页码
  pageSize: number; // 每页大小
  records: CollectedResourceItem[]; // 收藏资源列表
  total: number; // 总数
  totalPages: number; // 总页数
};

// 学生端资源详情响应体
export interface AppResourceDetailVO {
  areaId: number; // 地区ID
  areaName: string; // 地区名称
  attribution: number; // 归属：1个人，2学校，3官方，4第三方
  attributionToId: number; // 归属关联ID
  attributionToName: string; // 归属管理名称
  createTime: string; // 创建时间
  createUserName: string; // 上传者名称
  curation: number; // 是否精选:0否；1是
  description: string; // 资源描述
  downloadCount: number; // 下载量
  fileFormatId: number; // 文件格式ID
  fileFormatName: string; // 文件格式名称
  fileKey: string; // 文件key
  fileName: string; // 文件名称
  fileSize: number | null; // 文件大小
  fileUrl: string; // 文件URL
  gradeId: number; // 年级ID
  gradeName: string; // 年级名称
  id: number; // 资源ID
  resourceTypeId: number; // 资源类型ID
  resourceTypeName: string; // 资源类型名称
  stageId: number; // 学段ID
  stageName: string; // 学段名称
  status: number; // 状态：0草稿，1审核中，2被驳回，3已发布，4已下架
  subjectId: number; // 学科ID
  subjectName: string; // 学科名称
  textbookChapterIds: number[]; // 教材章节ID数组
  textbookChapterNames: string[]; // 教材章节名称数组
  textbookVersionId: number; // 教材版本ID
  textbookVersionName: string; // 教材版本名称
  textbookVolumeId: number; // 教材册别ID
  textbookVolumeName: string; // 教材册别名称
  title: string; // 资源标题
  updateTime: string; // 更新时间
  viewCount: number; // 预览量
  vintages: number; // 年份
  videoId: string; // videoId
  isCollected: number; // 是否收藏
  isCuration: number; // 是否精选
}

// 学生年级和教材版本信息
export interface StudentGradeAndTextbook {
  gradeId: number; // 年级ID
  gradeName: string; // 年级名称
  textbookVersionId: number; // 教材版本ID
  textbookVersionName: string; // 教材版本名称
}
