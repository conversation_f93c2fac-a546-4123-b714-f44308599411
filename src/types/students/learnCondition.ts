export type SemestersParams = {
  endDate: string;
  semesterId: number;
  startDate: string;
  studentId: number;
  subjectId: number;
};

export type SubjectsParams = {
  endDate: string;
  semesterId: number;
  startDate: string;
};

export type SemestersItem = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  year: string;
  type: number; // 学期类型：1-上学期，2-下学期
  startDate: string;
  endDate: string;
  isCurrent: number; // 是否当前学期：0-否，1-是
  // 兼容旧字段
  semesterId?: number;
  semesterName?: string;
};

export type SubjectsItem = {
  id: number;
  createTime: string;
  isDeleted: number;
  name: string;
  tenantId: number;
  updateTime: string;
};

export type SubjectScoreTrendItem = {
  monthRankingTrendList: {
    correctRate: number;
    month: number;
  }[];
  subjectId: number;
  subjectName: string;
  weekRankingTrendList: {
    correctRate: number;
    week: number;
  }[];
};

export type ClassRankTrend = {
  monthRankingTrend: {
    classAvgRank: number;
    month: number;
  }[];
  weekRankingTrend: {
    classAvgRank: number;
    week: number;
  }[];
  totalCount: number;
};

export type KnowledgeAnalysisParams = {
  endDate: string;
  semesterId: number;
  startDate: string;
  subjectId: number;
};

export type KnowledgeAnalysis = {
  correctRateList: {
    pointId: number;
    pointName: string;
    rate: number;
  }[];
  wrongRateList: {
    pointId: number;
    pointName: string;
    rate: number;
  }[];
};
