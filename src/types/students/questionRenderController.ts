import type { HomeworkTaskType } from '@/constants/students/homework';

/**
 * Question 渲染层控制器抽象，避免组件直接依赖具体的 Pinia Store
 * 可由任意实现（如不同业务域的 store）注入到渲染组件中
 */
export interface QuestionRenderController {
  /** 是否只读（根据状态/权限等综合得出） */
  readonly: boolean;
  /** 是否显示解析 */
  showAnalysis: boolean;
  /** 是否显示题目笔记入口/内容 */
  showNote: boolean;
  /** 是否展示答案模式（开启后统一按答案态渲染） */
  showAnswer: boolean;
  /** 当前任务/作业类型 */
  answerType: HomeworkTaskType;

  /** 根据题目 id 获取题目结构（深层题也需支持） */
  getQuestion: (questionId: string) => any | undefined;
  /** 根据题目 id 获取用户答案（大题/子题需兼容） */
  getUserAnswer: (questionId: string) => any | undefined;
  /** 统一的答案写入接口（内部自行处理大题/子题写入路径） */
  setUserAnswer: (questionId: string, answer: any) => void;
}
