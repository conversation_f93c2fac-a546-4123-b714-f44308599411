import { QuestionType, AnswerType } from '@/constants/students/homework';

/**
 * 前端作业类型定义
 * ------------ start ------------
 */
// 选项接口
export interface FrontQuestionOption {
  /** 选项id */
  id: string;
  /** 选项内容 */
  content: string;
  /** 是否正确 */
  isCorrect?: boolean;
  /** 选项图片URL */
  imageUrl?: string;
}

// 基础题目接口
export interface FrontBaseQuestion {
  [key: string]: any;
  /** 题目id */
  id: string;
  /** 题目序号 */
  index: number;
  /** 题目类型 */
  type: QuestionType;
  /** 题目 */
  title: string;
  /** 题目分数 */
  score: number;
  /** 题目解析 */
  analysis?: string;
  /** 当前题目答案 */
  answer?: any;
  /** 当前题目后端数据缓存，提供后续扩展 */
  backendData?: any;
  /** 子题 */
  subQuestions?: any[];
}

// 子题接口
export interface FrontSubQuestion {
  /** 子题id */
  id: string;
  /** 子题序号 */
  index: number;
  /** 子题标题 */
  title?: string;
  /** 子题内容 */
  content?: string;
  /** 子题分数 */
  score?: number;
  /** 子题类型 */
  type?: QuestionType;
  /** 子题选项 */
  options?: FrontQuestionOption[];
  /** 子题答案 */
  answer?: string | string[] | Record<string, string>;
  /** 子题解析 */
  analysis?: string;
}

// 复杂答案内容接口
export interface FrontComplexAnswerContent {
  backendData?: any;
  files: any[];
  // 画板数据
  drawing?: {
    imageUrl: string;
    data: any;
    imageFile: any;
  };
  /** 是否正确 */
  correct?: boolean;
}

// 题目id
export type FrontQuestionId = string;
// 用户答案
export type FrontUserAnswersMap = Record<FrontQuestionId, FrontUserAnswer>;

// 用户答案接口
export interface FrontUserAnswer {
  backendData?: any;
  questionId: string;
  answer?:
    | string
    | string[]
    | Record<string, string>
    | Record<string, string[]>
    | Record<string, string | string[]>
    | Record<string, string>[]
    | FrontComplexAnswerContent
    | Record<string, FrontComplexAnswerContent>
    | Record<string, any>;
  /** 是否正确 */
  correct?: boolean;
  type?: string;
}

// 问题组件接口 - 用于热插拔组件系统
export interface FrontQuestionComponent {
  type: QuestionType;
  component: any; // Vue组件
}

// 复合体答案类型
export type FrontComplexAnswer = string | string[] | Record<string, string>;

// 问题组件映射类型
export type FrontQuestionComponentMap = Record<QuestionType, any>;

// 单选题
export interface FrontSingleQuestion extends FrontBaseQuestion {
  type: QuestionType.SINGLE;
  options: FrontQuestionOption[];
  answer: string;
}

// 多选题
export interface FrontMultipleQuestion extends FrontBaseQuestion {
  type: QuestionType.MULTIPLE;
  options: FrontQuestionOption[];
  answer?: string[];
  /** 最大可选数量，默认为2 */
  maxSelectCount?: number;
}

// 简答题
export interface FrontShortAnswerQuestion extends FrontBaseQuestion {
  type: QuestionType.SHORT_ANSWER;
  answer?: FrontComplexAnswerContent;
}

// 判断题
export interface FrontTrueFalseQuestion extends FrontBaseQuestion {
  type: QuestionType.TRUE_FALSE;
  options: FrontQuestionOption[];
  answer?: string;
}

// 填空题part
export interface FrontFillBlankPartItem {
  type: 'text' | 'blank';
  value?: string;
  index?: number;
}

// 填空题子项
export interface FrontFillBlanksItem {
  id: string | number;
  parts: Array<FrontFillBlankPartItem>;
}

// 填空题
export interface FrontFillBlankQuestion extends FrontBaseQuestion {
  type: QuestionType.FILL_BLANK;
  answer?: string[] | Record<string, any>;
  // 结构化填空题数据
  blanks?: Array<FrontFillBlanksItem>;
}

// 排序题
export interface FrontSortingQuestion extends FrontBaseQuestion {
  type: QuestionType.SORTING;
}

// 匹配题
export interface FrontMatchingQuestion extends FrontBaseQuestion {
  type: QuestionType.MATCHING;
}

// 完形填空题
export interface FrontClozeQuestion extends FrontBaseQuestion {
  type: QuestionType.CLOZE;
}

// 阅读理解题
export interface FrontReadingQuestion extends FrontBaseQuestion {
  type: QuestionType.READING;
  /** 子题 */
  subQuestions: any[];
}

// 材料分析题
export interface FrontMaterialAnalysisQuestion extends FrontBaseQuestion {
  type: QuestionType.MATERIAL_ANALYSIS;
}

// 题目联合类型
export type FrontQuestion =
  | FrontSingleQuestion
  | FrontMultipleQuestion
  | FrontShortAnswerQuestion
  | FrontTrueFalseQuestion
  | FrontFillBlankQuestion
  | FrontSortingQuestion
  | FrontMatchingQuestion
  | FrontClozeQuestion
  | FrontReadingQuestion
  | FrontMaterialAnalysisQuestion;

/**
 * ------------ end ------------
 */

/**
 * ------------ start ------------
 * 后端作业类型定义
 */

export interface HomeworkPageParams {
  /** 作业状态 */
  status?: string | number;
  /** 作业类型 */
  type?: string;
  /** 学科ID */
  subjectId?: string;
  /** 当前页码 */
  current?: number;
  /** 每页条数 */
  size?: number;
  /** 学期ID */
  semesterId?: string | number;
  /** 学生ID */
  studentId?: string;
  /** 学科ID列表 */
  subjectIds?: any[];
  /** 搜索关键字 */
  searchKey?: string;
  /** 作业类型别名列表 */
  aliases?: string[];
}

export interface HomeworkPageItem {
  [key: string]: any;
}

export interface HomeworkSemesterItem {
  /** 学期ID */
  id: string | number;
  /** 学期名称 */
  name?: string;
  /** 学期年份 */
  year?: string;
  /** 学期类型 */
  type?: string;
}

export interface HomeworkSubjectItem {
  /** 学科ID */
  id: string | number;
  /** 学科名称 */
  name: string;
}

export interface CreateSelfHomeworkParams {
  /** 作业名称 */
  name: string;
  /** 学科ID */
  subjectId: string | number;
  /** 作业文件列表 */
  fileList: any[];
}

export interface HomeworkTypeItem {
  [key: string]: any;
  /** 作业类型ID */
  id: number;
  /** 作业类型名称 */
  alias: string;
  /** 名称 */
  name: string;
}

export interface SubmitHomeworkParams {
  /** 作业学生ID */
  homeworkStudentId: number | string;
  /** 作业题目列表 */
  questionsRequestList: {
    questionId: number;
    answer?: string;
    files?: any[];
  }[];
}

export interface UpdateSelfHomeworkParams {
  [key: string]: any;
}

export interface HomeworkDetail {
  [key: string]: any;
  /** 作业ID */
  homeworkId: number;
  /** 主键ID */
  id: number;
  /** 作业名称 */
  name: string;
  /** 作业说明 */
  description?: string;
  /** 作业任务类型 1-在线作业；2-纸质作业；3-写作任务；4-自主作业； */
  taskType: number;
  /** 年级类型：1-小学低年级；2-小学高年级；3-初中；4-高中 */
  gradeType?: number;
  /** 学科类型：0 未选择；1文科；2理科*/
  subjectType?: number;
  /** 学科ID */
  subjectId: number;
  /** 学科名称 */
  subjectName: string;
  /** 作业类型ID */
  typeId: number;
  /** 作业类型别名 */
  typeAlias?: string;
  /** 作业类型名称 */
  typeName?: string;
  /** 作业状态：0-待提交，1-未提交，2-待批改，3-批改中，4-待确认，5-已完成，6-待订正，7-批改失败 */
  status: number;
  /** 提交方式：1-学生在线答题；2-学生平板拍照；3-教师帮录 */
  submitMethod: number;
  /** 截止时间 */
  deadlineTime?: string;
  /** 上传时间 */
  uploadTime?: string;
  /** AI答疑环节：1-学生练习中；2-学生提交作业后；3-教师确认批改后。多选字段，值以逗号分隔 */
  aiAssistantTriggers?: string;
  /** 是否开启AI答疑助手：0-关闭；1-开启 */
  enableAiAssistant?: number;
  /** 是否开启允许补交：0-关闭；1-开启 */
  enableAllowResubmit?: number;
  /** 是否开启批改后，答案对学生可见：0-关闭；1-开启 */
  enableAnswerVisible?: number;
  /** 失败原因 */
  failureReason?: string;
  /** 订正原因 */
  correctionReason?: string;
  /** 是否是学生自主作业：0-否，1-是 */
  isSelfHomework?: number;
  /** 学生作业题目列表 */
  questionList?: QuestionItem[];
  /** 学生提交记录 */
  submitList?: any[];
  /** 辅助材料文件 */
  materialFileList: any[];
  /** 自主作业文件列表 */
  selfHomeworkFiles?: any[];
  /** 总体分析 */
  overallAnalysis?: string;
  /** 详细解析 */
  detailedExplanation?: string;
}

export interface QuestionItem {
  [key: string]: any;
  /** 题目ID */
  id: number;
  /** 内容布局ID */
  contentLayoutId: number;
  /** 创建时间 */
  createTime: string;
  /** 评分标准 */
  gradingCriteria: string;
  /** 作业ID */
  homeworkId: number;
  /** 是否删除 */
  isDeleted: number;
  /** 层级：1-熟练掌握层；2-基本掌握层；3-初步学习层 */
  level: number;
  /** 父作业题目ID */
  parentId: number;
  /** 题目信息 */
  question: QuestionItemQuestion;
  /** 题目ID */
  questionId: number;
  /** 题号：题目在作业中的顺序 */
  questionOrder: number;
  /** 题目分值 */
  score: number;
  /** 更新时间 */
  updateTime: string;
}

export interface QuestionItemQuestion {
  [key: string]: any;
  /** 解析 */
  analysis?: string;
  /** 答案 */
  answer?: string;
  /** 答题形式：1选择；2填空；3解答 */
  answerType?: AnswerType;
  /** 地区Code */
  areaCode?: string;
  /** 地区ID */
  areaId?: number;
  /** 地区名称 */
  areaName?: string;
  /** 归属类型 */
  attribution?: number;
  /** 归属关联ID */
  attributionToId?: number;
  /** 题目归属关联名称 */
  attributionToName?: string;
  /** 子题列表 */
  children?: any[];
  /** 创建时间(时间戳) */
  createTime?: number;
  /** 难度 */
  difficulty?: number;
  /** 难度名称 */
  difficultyName?: string;
  /** 待处理反馈数量 */
  feedbackCount?: number;
  /** 年级ID */
  gradeId?: number;
  /** 年级名称 */
  gradeName?: string;
  /** 主键ID */
  id?: number;
  /** 知识点ID列表 */
  knowledgePointIds?: any[];
  /** 知识点名称列表 */
  knowledgePointNames?: any[];
  /** 题目选项 */
  options?: string;
  /** 题目来源 */
  originInfo?: string;
  /** 源题目ID */
  originQuestionId?: number;
  /** 来源资源ID */
  originResourceId?: number;
  /** 父级ID */
  parentId?: number;
  /** 题号 */
  questionOrder?: number;
  /** 题目类型编码 */
  questionTypeCode?: string;
  /** 题目类型ID */
  questionTypeId?: number;
  /** 题目类型名称 */
  questionTypeName?: string;
  /** 资源ID */
  resourceId?: number;
  /** 是否资源题 */
  resourceQuestion?: number;
  /** 共享状态：0私有，1共享到学校 */
  shareStatus?: number;
  /** 学段ID */
  stageId?: number;
  /** 学段名称 */
  stageName?: string;
  /** 状态 */
  status?: number;
  /** 题干 */
  stem?: string;
  /** 学生笔记 */
  studentsNotes?: any;
  /** 学科ID */
  subjectId?: number;
  /** 学科名称 */
  subjectName?: string;
  /** 提交题目信息 */
  submitQuestion?: any;
  /** 同步状态(0:未同步,1:已同步,2:同步失败) */
  syncStatus?: number;
  /** 标签列表 */
  tags?: any[];
  /** 租户ID */
  tenantId?: number;
  /** 教材章节ID列表 */
  textbookChapterIds?: any[];
  /** 教材章节名称列表 */
  textbookChapterNames?: any[];
  /** 教材版本ID */
  textbookVersionId?: number;
  /** 教材版本名称 */
  textbookVersionName?: string;
  /** 教材ID */
  textbookVolumeId?: number;
  /** 教材名称 */
  textbookVolumeName?: string;
  /** 租户用户ID */
  tmbId?: number;
  /** 租户用户名 */
  tmbName?: string;
  /** 更新时间(时间戳) */
  updateTime?: number;
  /** 年份 */
  vintages?: number;
}

export interface GetSubjectScoreTrendParams {
  /** 结束日期 */
  endDate?: string;
  /** 学期ID */
  semesterId?: number;
  /** 开始日期 */
  startDate?: string;
  /** 学生ID */
  studentId: number;
  /** 学科ID */
  subjectId?: number;
}

export interface GetClassRankTrendParams {
  /** 结束日期 */
  endDate?: string;
  /** 学期ID */
  semesterId?: number;
  /** 开始日期 */
  startDate?: string;
  /** 学生ID */
  studentId: number;
}

/**
 * 文件信息
 */
export interface HomeworkFileInfo {
  /** 文件ID */
  id?: number | string;
  /** 创建时间 */
  createTime?: string;
  /** 文件详情Json数据 */
  fileJson?: string;
  /** 文件objectKey */
  fileKey?: string;
  /** 文件名称 */
  fileName?: string;
  /** 文件大小 */
  fileSize?: number;
  /** 文件类型：图片、音频、视频等 */
  fileType?: string;
  /** 文件链接 */
  fileUrl?: string;
  /** 是否删除 */
  isDeleted?: number;
  /** 更新时间 */
  updateTime?: string;
}

export interface AddStudentsNoteParams {
  /**
   * 作业ID
   */
  homeworkId: number | string;
  /**
   * 关联ID
   */
  refId: string;
  /**
   * 笔记内容
   */
  noteContent: string;
  /**
   * 类型：1做题笔记；2学习笔记；3自由笔记
   */
  type: number;
}

export interface GetStudentsNoteParams {
  /**
   * 关联ID
   */
  refId: string;
  /**
   * 类型：1做题笔记；2学习笔记；3自由笔记
   */
  type: number;
}

export interface UpdateStudentsNoteParams {
  /**
   * 笔记ID
   */
  id: number | string;
  /**
   * 笔记内容
   */
  noteContent: string;
}

export interface SelfHomeworkDetail {
  [key: string]: any;
  selfHomeworkFiles: HomeworkFileInfo[];
  overallAnalysis: string;
  detailedExplanation: string;
}

/**
 * 作文批改结果
 */
export interface WritingCorrectResult {
  /** 描述信息 */
  description: string;
  /** 作文标注信息 */
  annotations: Array<{
    /** 标注类别 */
    annotationCategory: string;
    /** 标注ID */
    annotationId: string;
    /** 标注类型 */
    annotationType: string;
    /** 评论内容 */
    comment: string;
    /** 结束页码 */
    endPage: number;
    /** 结束X坐标 */
    endX: number;
    /** 结束Y坐标 */
    endY: number;
    /** 原文内容 */
    srcText: string;
    /** 开始页码 */
    startPage: number;
    /** 开始X坐标 */
    startX: number;
    /** 开始Y坐标 */
    startY: number;
  }>;
  /** 班级名称 */
  className: string;
  /** 批改记录 */
  correctionRecords: Array<{
    /** 批改ID */
    correctionId: number;
    /** 批改时间 */
    correctionTime: string;
    /** 批改类型 */
    correctionType: number;
    /** 批改版本 */
    correctionVersion: number;
    /** 批改者姓名 */
    correctorName: string;
  }>;
  /** 批改状态 */
  correctionStatus: number;
  /** 批改状态描述 */
  correctionStatusDesc: string;
  /** 维度得分 */
  dimensionScores: Array<{
    /** 评论内容 */
    comment: string;
    /** 维度ID */
    dimensionId: number;
    /** 维度名称 */
    dimensionName: string;
    /** 最高分 */
    maxScore: number;
    /** 评分规则 */
    rules: Array<{
      /** 得分 */
      achievedScore: number;
      /** 创建时间 */
      createTime: string;
      /** 维度结果ID */
      dimensionResultId: number;
      /** 主键ID */
      id: string;
      /** 是否删除 */
      isDeleted: number;
      /** 评论内容 */
      justification: string;
      /** 最高分 */
      maxScore: number;
      /** 规则名称 */
      ruleName: string;
      /** 得分 */
      score: number;
      /** 更新时间 */
      updateTime: string;
    }>;
    /** 维度分数 */
    score: number;
  }>;
  /** 作文详情 */
  essayDetails: Array<{
    /** 作文内容 HTML */
    comment: string;
    /** 维度ID */
    dimensionId: number;
    /** 维度名称 */
    dimensionName: string;
    /** 最高分 */
    maxScore: number;
    /** 分数 */
    score: number;
  }>;
  /** 年级代码 */
  gradeCode: string;
  /** 作业ID */
  homeworkId: number;
  /** 学生姓名 */
  homeworkName: string;
  /** 总评 */
  overallComment: string;
  /** 分数 */
  score: number;
  /** 学生ID */
  studentId: number;
  /** 学生姓名 */
  studentName: string;
  /** 提交ID */
  submitId: number;
  /** 提交时间 */
  submitTime: string;
  /** 开始时间 */
  startTime: string;
  /** 总分 */
  totalScore: number;
  /** 评定档次 */
  tierLevel: string;
  /** 作文图片URL列表 */
  essayImageUrls: string[];
  /** 档次 */
  justification: string;
  /** 最低分 */
  minScore: number;
  /** 最高分 */
  maxScore: number;
  /** 特殊扣分项 */
  globalRuleResults: Array<{
    /** 批改结果ID */
    correctionResultId: number;
    /** 创建时间 */
    createTime: string;
    /** 详情 */
    details: string;
    /** 主键ID */
    id: string;
    /** 是否删除 */
    isDeleted: number;
    /** 规则名称 */
    ruleName: string;
    /** 得分影响 */
    scoreImpact: number;
    /** 更新时间 */
    updateTime: string;
  }>;
  /** 档次名称 */
  tierName: string;
  /** 评分模式 */
  gradingMode: number | string;
}
/**
 * ------------ end --------------
 */
