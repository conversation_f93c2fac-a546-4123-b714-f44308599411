export interface CreateErrorBookParams {
  homeworkId: number;
  questionId: number;
  schoolId: number;
  semesterId: number;
  studentId: number;
  teacherId: number;
}

export interface DeleteErrorBookParams {
  id: number;
}

export interface GetErrorBookInfoParams {
  category: number;
  endDate: string;
  semesterId: number;
  startDate: string;
  subjectId: number;
}

export interface GetErrorBookListParams {
  categoryId: number; // 分类ID
  endDate: string; // 结束时间(yyyy-MM-dd)
  semesterId: number; // 学期ID
  startDate: string; // 开始时间(yyyy-MM-dd)
  subjectId?: number; // 学科ID（可选）
  type: number; // 分类类型（1作业任务，2题型分类，3章节分类，4知识点分类）
}

export interface GetErrorBookPassParams {
  id: number;
}

export interface GetSemesterListParams {
  schoolId: number;
}

export interface GetSemesterListResponse {
  semesterId: number;
  termType: string;
  year: string;
  startDate: string;
  endDate: string;
}

export interface GetSubjectListParams {
  schoolId: number;
}

export interface GetCurrentSemesterResponse {
  endDate: string;
  isCurrent: number;
  semesterId: number;
  startDate: string;
  type: number;
  typeText: string;
  year: string;
}

export interface GetSubjectListResponse {
  gradeId: number;
  gradeName: string;
  subjectId: number;
  subjectName: string;
}

// 错题本信息接口返回数据结构
export interface GetErrorBookInfoResponse {
  totalCount: number;
  unPassCount: number;
  passCount: number;
  errorRate: number;
  homeworkQuestionCount: number;
  categoryDataList: CategoryData[];
}

export interface CategoryData {
  categoryId: number;
  categoryName: string;
  questionCount: number;
}

// 按状态查询错题列表参数接口
export interface GetErrorBookListByStatusParams {
  endDate: string; // 结束时间(yyyy-MM-dd)
  semesterId: number; // 学期ID
  startDate: string; // 开始时间(yyyy-MM-dd)
  status: number; // 状态：0全部；1未过关；2已过关
  subjectId?: number; // 学科ID（可选）
  homeworkId?: number; // 作业ID（可选）
}

// 错题项接口
export interface ErrorBookItem {
  id: number;
  questionId: number;
  homeworkId: number;
  status: number; // 0未过关，1已过关
  createTime: string;
  updateTime: string;
  // 可根据实际返回数据补充更多字段
}

// 推荐题目请求参数接口
export interface GetQuestionRecommendParams {
  questionId?: number;
  errorId?: number;
  homeworkId?: number;
}

// 客观题答案判断请求参数接口
export interface CheckAnswerParams {
  questionId: number;
  studentAnswers: any[];
}

// 客观题答案判断响应接口
export interface CheckAnswerResponse {
  analysis: string; // 题目解析
  correctAnswers: string[]; // 正确答案
  correctResult: number; // 答题结果：0-错误；1-正确；2-部分正确
  correctResultDesc: string; // 答题结果描述
  isCorrect: boolean; // 是否完全正确
  questionId: number; // 题目ID
  studentAnswers: string[]; // 学生答案
}
