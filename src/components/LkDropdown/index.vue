<template>
  <view>
    <view class="lk-dropdown" :style="customStyle" @click="click">
      <slot></slot>
    </view>
    <view class="lk-dropdown-mask" :class="{ 'lk-dropdown-mask-show': maskShow }" @click="close">
      <view class="lk-dropdown-menu-container" :style="[layout]">
        <slot name="menu">
          <view class="menu" :style="{ flexDirection: layout.flexDirection || 'column' }">
            <view
              class="menu-item"
              :style="[menuStyle]"
              v-for="(i, idx) in menuList"
              @click="menuClick(i, idx)"
            >
              <slot name="menu-item" :item="i" :index="idx">
                <text v-if="getItemIcon(i)" class="menu-icon">{{ getItemIcon(i) }}</text>
                <text>{{ getItemText(i) }}</text>
              </slot>
            </view>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'lk-dropdown',
  props: {
    bgColor: {
      type: String,
      default: '#fff',
    },
    customStyle: {
      type: Object,
      default: () => ({}),
    },
    menuList: {
      type: Array,
      default: () => ['菜单1', '菜单2', '菜单3'],
    },
    menuStyle: {
      type: Object,
      default: () => ({}),
    },
    interspace: {
      type: [String, Number],
      default: '10rpx',
    },
    direction: {
      type: String,
      default: 'bottom',
      validator: function (value) {
        return [
          'top',
          'bottom',
          'left',
          'right',
          'top-right',
          'top-left',
          'bottom-right',
          'bottom-left',
        ].includes(value);
      },
    },
  },
  emits: ['open', 'close', 'change'],
  data() {
    return {
      maskShow: false,
      window: {
        width: 0,
        height: 0,
      },
      layout: {},
      innerInterspace: 0,
      dropdownRect: {
        width: 0,
      },
    };
  },
  mounted() {
    this.init();
    this.innerInterspace = this.str2px(this.interspace);
  },
  methods: {
    // 获取菜单项的文本
    getItemText(item) {
      if (typeof item === 'string') return item;
      return item.text || '';
    },

    // 获取菜单项的图标
    getItemIcon(item) {
      if (typeof item === 'string') return '';
      return item.icon || '';
    },

    str2px(str) {
      if (!str) return 0;
      str = String(str);
      if (str.endsWith('rpx')) return uni.upx2px(parseInt(str));
      if (str.endsWith('px')) return parseInt(str);
      return parseInt(str);
    },
    queryElementRect(selector) {
      if (Array.isArray(selector))
        return Promise.all(selector.map(i => queryElementRect.call(this, i)));
      return new Promise(resolve => {
        uni.createSelectorQuery().in(this).select(selector).boundingClientRect(resolve).exec();
      });
    },
    init() {
      // console.log('init');

      if (this.window.width === 0) {
        uni.getSystemInfo({
          success: res => {
            const { windowWidth, windowHeight } = res;
            this.window = {
              width: windowWidth,
              height: windowHeight,
            };
          },
          fail: () => {
            this.init();
          },
        });
      }

      if (this.dropdownRect.width === 0) {
        this.queryElementRect('.lk-dropdown')
          .then(res => {
            this.dropdownRect = res;
          })
          .catch(res => {
            this.init();
          });
      }
    },
    async click(e) {
      const dropdownRect = await this.queryElementRect('.lk-dropdown');
      const { innerInterspace, window } = this;
      const { left, right, top, bottom, width, height } = dropdownRect;
      const { width: windowWidth, height: windowHeight } = window;

      const layout = {};

      // 根据方向设置不同的位置和样式
      switch (this.direction) {
        case 'top':
          layout.bottom = `${windowHeight - top + innerInterspace}px`;
          layout.left = `${left}px`;
          layout.flexDirection = 'column';
          break;
        case 'bottom':
          layout.top = `${bottom + innerInterspace}px`;
          layout.left = `${left}px`;
          layout.flexDirection = 'column';
          break;
        case 'left':
          layout.top = `${top}px`;
          layout.right = `${windowWidth - left + innerInterspace}px`;
          layout.flexDirection = 'row-reverse';
          break;
        case 'right':
          layout.top = `${top}px`;
          layout.left = `${right + innerInterspace}px`;
          layout.flexDirection = 'row';
          break;
        // 对角线方向
        case 'top-right':
          layout.bottom = `${windowHeight - top + innerInterspace}px`;
          layout.left = `${right - width / 2}px`;
          layout.flexDirection = 'column';
          layout.transformOrigin = 'bottom left';
          break;
        case 'top-left':
          layout.bottom = `${windowHeight - top + innerInterspace}px`;
          layout.right = `${windowWidth - left - width / 2}px`;
          layout.flexDirection = 'column';
          layout.transformOrigin = 'bottom right';
          break;
        case 'bottom-right':
          layout.top = `${bottom + innerInterspace}px`;
          layout.left = `${right - width / 2}px`;
          layout.flexDirection = 'column';
          layout.transformOrigin = 'top left';
          break;
        case 'bottom-left':
          layout.top = `${bottom + innerInterspace}px`;
          layout.right = `${windowWidth - left - width / 2}px`;
          layout.flexDirection = 'column';
          layout.transformOrigin = 'top right';
          break;
        default:
          layout.top = `${bottom + innerInterspace}px`;
          layout.left = `${left}px`;
          layout.flexDirection = 'column';
      }

      this.layout = layout;
      this.maskShow = true;

      this.$emit('open');
      this.$emit('change', true);
    },
    menuClick(item, index) {
      this.$emit('menuClick', {
        value: typeof item === 'string' ? item : item.value || item.text,
        text: this.getItemText(item),
        index,
        item,
      });
    },
    close() {
      this.maskShow = false;
      this.layout = {};

      this.$emit('close');
      this.$emit('change', false);
    },
  },
};
</script>

<style lang="scss" scoped>
.lk-dropdown {
  width: fit-content;
  position: relative;
  overflow: hidden;
}

.lk-dropdown-mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 999;
  opacity: 0;
  pointer-events: none;

  .lk-dropdown-menu-container {
    position: absolute;
    overflow: visible;
    white-space: nowrap;
    max-width: none !important;
    width: auto !important;
    z-index: 1000;

    .menu {
      position: relative;
      background: #f3f3f3;
      border-radius: 12px;
      box-shadow:
        0px 6px 30px 5px rgba(0, 0, 0, 0.15),
        0px 16px 24px 2px rgba(0, 0, 0, 0.04),
        0px 8px 10px -5px rgba(0, 0, 0, 0.04);
      display: flex;
      flex-wrap: nowrap;
      width: max-content;
      min-width: 100%;
      align-items: flex-start;
      justify-content: flex-start;
      overflow: hidden;

      .menu-item {
        padding: 20rpx 24rpx;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        white-space: nowrap;
        flex-shrink: 0;

        .menu-icon {
          margin-right: 16rpx;
          font-size: 28rpx;
        }

        text {
          font-size: 28rpx;
          color: #000;
        }

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

.lk-dropdown-mask-show {
  opacity: 1;
  pointer-events: auto;
}
</style>
