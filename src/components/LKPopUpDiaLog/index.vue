<template>
  <view :class="mode">
    <up-popup
      :show="innerShow"
      :mode="mode"
      :round="round"
      :duration="duration"
      :closeOnClickOverlay="innerCloseOnClickOverlay"
      :safeAreaInsetBottom="safeAreaInsetBottom"
      :customStyle="innerCustomStyle"
      @close="cancel"
    >
      <view class="lk-confirm" :style="[innerBoxStyle]">
        <!-- 标题 -->
        <slot name="title">
          <view v-if="innerTitle" class="lk-confirm-title">
            {{ innerTitle }}
          </view>
        </slot>

        <!-- 显示内容 -->
        <view class="lk-confirm-content">
          <slot>
            <!-- 顶层 -->
            <view v-for="(item, index) in innerContent" :key="index" :style="[item.style]">
              <template v-if="item.text">
                {{ item.text }}
              </template>
              <template v-else-if="item.children">
                <!-- 第二层 -->
                <view v-for="(child, cindex) in item.children" :key="cindex" :style="[child.style]">
                  <template v-if="child.text">
                    {{ child.text }}
                  </template>
                  <template v-else-if="child.children">
                    <!-- 第三层 -->
                    <view
                      v-for="(leaf, lindex) in child.children"
                      :key="lindex"
                      :style="[leaf.style]"
                    >
                      {{ leaf.text || '' }}
                    </view>
                  </template>
                </view>
              </template>
            </view>
          </slot>

          <view v-if="innerConfirmText" class="lk-confirm-input-wrap">
            <view class="lk-confirm-input-tip">请在下面输入"{{ innerConfirmText }}"</view>
            <up-input v-model="confirmInputText" placeholder="请输入" border="surround"></up-input>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="lk-confirm-footer" :class="['theme-button-' + themeButtonType]">
          <template v-if="themeButtonType">
            <!-- 主题按钮 -->
            <!-- 取消 -->
            <up-button
              v-if="innerShowCancelButton"
              type="primary"
              plain
              color="#265c92"
              :text="innerCancelButtonText"
              :customStyle="cancelButtonStyle"
              @click="cancel"
            ></up-button>
            <!-- 确定 -->
            <up-button
              v-if="innerShowConfirmButton"
              type="primary"
              :text="innerConfirmButtonText"
              color="#265c92"
              :customStyle="confirmButtonStyle"
              @click="confirm"
            ></up-button>
          </template>
          <template v-else>
            <!-- 扁平按钮 -->
            <!-- 取消 -->
            <up-button
              v-if="innerShowCancelButton"
              type="text"
              :text="innerCancelButtonText"
              :customStyle="cancelButtonStyle"
              @click="cancel"
            ></up-button>
            <!-- 分隔线 -->
            <view
              v-if="innerShowCancelButton && innerShowConfirmButton"
              class="lk-confirm-footer-gap"
            ></view>
            <!-- 确定 -->
            <up-button
              v-if="innerShowConfirmButton"
              type="text"
              :text="innerConfirmButtonText"
              :customStyle="confirmButtonStyle"
              @click="confirm"
            ></up-button>
          </template>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineOptions } from 'vue';

// 定义组件选项（替代 mpMixin）
defineOptions({
  name: 'LKPopUpDiaLog',
  // #ifdef MP-WEIXIN
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
  // #endif
});

// 定义 props
interface Props {
  show?: boolean;
  title?: string;
  content?: string | object | any[] | null;
  mode?: string;
  round?: string;
  width?: string;
  height?: string;
  closeOnClickOverlay?: boolean;
  showCancelButton?: boolean;
  cancelButtonText?: string;
  cancelButtonColor?: string;
  showConfirmButton?: boolean;
  confirmButtonText?: string;
  confirmButtonColor?: string;
  confirmBoderRadius?: string;
  confirmNoClose?: boolean;
  themeButton?: string | boolean;
  confirmText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  title: '',
  content: null,
  mode: 'center',
  round: '40rpx',
  width: '',
  height: '',
  closeOnClickOverlay: false,
  showCancelButton: true,
  cancelButtonText: '取消',
  cancelButtonColor: '#777777',
  showConfirmButton: true,
  confirmButtonText: '确定',
  confirmButtonColor: '#3D7FFF',
  confirmBoderRadius: '40rpx',
  confirmNoClose: false,
  themeButton: false,
  confirmText: '',
});

// 定义 emits
const emit = defineEmits<{
  cancel: [];
  close: [];
  confirm: [];
}>();

// 响应式数据
const defaultWidth = '550rpx';
const duration = ref(300);
const opened = ref(false);
const openId = ref(0);
const options = ref<any>(null);
const confirmInputText = ref('');

// 计算属性
const safeAreaInsetBottom = computed(() => {
  return props.mode != 'top' && props.mode != 'center';
});

const innerCustomStyle = computed(() => {
  const style: any = {};
  style.overflow = 'hidden';
  if (props.width) {
    style.width = props.width;
    style.margin = 'auto';
  } else if (props.mode != 'top' && props.mode != 'bottom') {
    style.width = defaultWidth;
  }
  if (props.height) {
    style.height = props.height;
  }
  if (props.mode == 'top' || props.mode == 'center' || props.mode == 'bottom') {
    style.maxHeight = '100vh';
  }
  return style;
});

const innerBoxStyle = computed(() => {
  const style: any = {};
  if (props.width) {
    style.width = props.width;
  }
  return style;
});

const themeButtonType = computed(() => {
  if (props.themeButton == 'flex') {
    return 'flex';
  }
  return props.themeButton ? 'auto' : '';
});

const cancelButtonStyle = computed(() => {
  return themeButtonType.value
    ? {}
    : {
        color: props.cancelButtonColor,
      };
});

const confirmButtonStyle = computed(() => {
  return themeButtonType.value
    ? {}
    : {
        color: props.confirmButtonColor,
        boderRadius: props.confirmBoderRadius,
      };
});

const innerShow = computed(() => {
  return options.value ? opened.value : props.show;
});

const innerCloseOnClickOverlay = computed(() => {
  return options.value?.closeOnClickOverlay ?? props.closeOnClickOverlay;
});

const innerTitle = computed(() => {
  return options.value?.title ?? props.title;
});

const innerShowCancelButton = computed(() => {
  return options.value?.showCancelButton ?? props.showCancelButton;
});

const innerCancelButtonText = computed(() => {
  return options.value?.cancelButtonText ?? props.cancelButtonText;
});

const innerShowConfirmButton = computed(() => {
  return options.value?.showConfirmButton ?? props.showConfirmButton;
});

const innerConfirmButtonText = computed(() => {
  return options.value?.confirmButtonText ?? props.confirmButtonText;
});

const innerContent = computed(() => {
  const content = options.value?.content ?? props.content;
  return content ? fit(Array.isArray(content) ? content : [content]) : [];
});

const innerConfirmText = computed(() => {
  return options.value?.confirmText ?? props.confirmText;
});

// 监听器
watch(innerShow, val => {
  if (!val) {
    confirmInputText.value = '';
  }
});

// 方法
const fit = (list: any[]): any[] => {
  if (!list) {
    return [];
  }
  const res: any[] = [];
  list.forEach(it => {
    let e = null;
    if (Array.isArray(it)) {
      e = {
        children: it,
      };
    } else if (typeof it == 'string') {
      e = {
        text: it,
      };
    } else {
      e = it;
    }
    if (e.children) {
      e.children = fit(e.children);
    }
    res.push(e);
  });
  return res;
};

const cancel = () => {
  if (options.value) {
    if (opened.value) {
      _close();
      options.value.reject();
    }
  } else if (props.show) {
    emit('cancel');
    emit('close');
  }
};

const confirm = () => {
  if (innerConfirmText.value && innerConfirmText.value != confirmInputText.value) {
    uni.showToast({
      title: `请输入"${innerConfirmText.value}"`,
      icon: 'none',
    });
    return;
  }
  if (options.value) {
    if (opened.value) {
      _close();
      options.value.resolve();
    }
  } else if (props.show) {
    emit('confirm');
    if (!props.confirmNoClose) {
      emit('close');
    }
  }
};

/**
 * 打开弹窗
 * @param openOptions 弹窗配置选项
 */
const open = (openOptions: any) => {
  return new Promise((resolve, reject) => {
    opened.value = true;
    openId.value++;
    options.value = {
      ...openOptions,
      resolve,
      reject,
    };
  });
};

const _close = () => {
  opened.value = false;
  const currentOpenId = openId.value;
  setTimeout(() => {
    if (openId.value == currentOpenId) {
      options.value = null;
    }
  }, duration.value);
};

// 暴露方法给父组件
defineExpose({
  open,
  cancel,
  confirm,
});
</script>

<style lang="scss" scoped>
.lk-confirm {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding-top: 30rpx;

  &-title {
    text-align: center;
    font-size: 32rpx;
    padding: 0 30rpx 30rpx 30rpx;
    font-weight: bold;
    color: #333333;
  }

  &-content {
    flex: 1;
    padding: 10rpx 40rpx;
    font-size: 32rpx;
    line-height: 48rpx;
    color: #333333;
    overflow: auto;
  }

  &-input-wrap {
    margin-top: 20rpx;
  }

  &-input-tip {
    font-size: 26rpx;
    color: #ff3333;
  }

  &-footer {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 30rpx;
    border-top: #eeeeee solid 1px;

    &-gap {
      height: 100rpx;
      width: 1px;
      background-color: #eeeeee;
    }

    :deep(.up-button) {
      flex: 1;
      height: 100rpx;
    }

    &.theme-button-flex {
      padding: 0 20rpx 30rpx 20rpx;
      border: none;

      :deep(.up-button) {
        height: 80rpx;
        border-radius: 12rpx;
        margin: 0 20rpx;
      }
    }

    &.theme-button-auto {
      padding: 0 20rpx 30rpx 20rpx;
      border: none;

      :deep(.up-button) {
        flex: none;
        width: calc(50% - 40rpx);
        max-width: 280rpx;
        height: 80rpx;
        border-radius: 40rpx;
        margin: 0;
      }
    }
  }
}
</style>
