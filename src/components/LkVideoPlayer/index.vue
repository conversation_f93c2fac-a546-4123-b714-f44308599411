<template>
  <view class="lk-video-player" @video-ready="handleVideoReady">
    <view
      ref="videoContainer"
      :id="playerId"
      class="player-container"
      :style="containerStyle"
      :change:prop="renderScript.propChanged"
      :prop="playerConfig"
    >
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import type {
  AliplayerOptions,
  AliplayerInstance,
  SkinLayoutItem,
  AutoplayPolicy,
  PlayConfig,
} from './types';
import { getVideoProgressSaveOrUpdate } from '@/api/students/resourceCenter';

// renderjs 模块类型声明
declare const renderScript: {
  propChanged: (newVal: any, oldVal: any, ownerInstance: any, instance: any) => void;
};

interface VideoPlayerProps {
  videoId: string; // 视频ID (VidAuth required)
  playAuth: string; // 播放凭证 (VidAuth required)
  title?: string; // 视频标题
  // === 基础配置 ===
  width?: string | number; // 播放器宽度
  height?: string | number; // 播放器高度

  // === 播放行为 ===
  autoplay?: boolean; // 是否自动播放
  muted?: boolean; // 是否静音
  preload?: boolean | string; // 是否预加载
  cover?: string; // 封面图片
  loop?: boolean; // 是否循环播放
  isLive?: boolean; // 是否为直播
  rePlay?: boolean; // 是否自动重播
  playsinline?: boolean; // H5是否内置播放

  // === 播放器外观 ===
  controlBarVisibility?: 'click' | 'hover' | 'always' | 'never'; // 控制栏显示方式
  autoSize?: boolean | 'height' | 'width'; // 是否自适应大小
  skinLayout?: SkinLayoutItem[] | boolean; // 播放器皮肤布局
  skinRes?: string; // 皮肤资源
  enableSystemMenu?: boolean; // 是否允许系统右键菜单

  // === 播放控制 ===
  language?: string; // 播放器语言
  autoPlayDelay?: number; // 延迟播放时间
  autoplayPolicy?: AutoplayPolicy; // 自动播放策略

  // === 播放格式和质量 ===
  format?: string; // 播放格式
  mediaType?: 'video' | 'audio'; // 媒体类型
  definition?: string; // 视频清晰度
  defaultDefinition?: string; // 默认清晰度

  // === 技术配置 ===
  useH5Prism?: boolean; // 是否使用H5播放器

  // === 自定义配置 ===
  playConfig?: PlayConfig; // 播放配置
  customConfig?: Partial<AliplayerOptions>; // 自定义配置

  // === 播放进度控制 ===
  currentVideoPlayTime?: number; // 当前视频播放时间（秒），用于定位到指定位置
  fileKey?: string; // 文件key
}

interface VideoPlayerEmits {
  (e: 'ready'): void;
  (e: 'play'): void;
  (e: 'pause'): void;
  (e: 'ended'): void;
  (e: 'error', error: any): void;
  (e: 'timeupdate', currentTime: number): void;
  (e: 'loadedmetadata', duration: number): void;
  (e: 'fullscreenchange', isFullscreen: boolean): void;
  (e: 'ai-chat'): void;
  (e: 'take-note', noteData: { content: string; time: number; timestamp: string }): void;
  (e: 'save-progress', time: number): void;
}

const props = withDefaults(defineProps<VideoPlayerProps>(), {
  width: '100%',
  height: 'auto',
  autoplay: false,
  muted: false,
  controlBarVisibility: 'hover',
  autoSize: true,
  loop: false,
  preload: true,
  language: 'zh-cn',
});

const emit = defineEmits<VideoPlayerEmits>();

// 生成唯一的播放器ID
const playerId = ref(`aliplayer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`);
const player = ref<AliplayerInstance | null>(null);
const videoContainer = ref<HTMLElement | null>(null);

// 构建播放器配置的函数
const buildPlayerConfig = (): Record<string, any> => {
  // 验证必需的VidAuth参数
  if (!props.videoId || !props.playAuth) {
    console.error('videoId and playAuth are required for VidAuth playback');
    return {};
  }

  // 创建VidAuth配置
  const config: Record<string, any> = {
    id: String(playerId.value),
    width: String(props.width || '100%'),
    height: String(props.height || 'auto'),
    autoplay: Boolean(props.autoplay),
    muted: Boolean(props.muted),
    loop: Boolean(props.loop),
    isLive: Boolean(props.isLive),

    // VidAuth配置 - 使用视频ID和播放凭证
    vid: String(props.videoId),
    playauth: String(props.playAuth),

    // 基础功能配置
    controlBarVisibility: String(props.controlBarVisibility || 'hover'),
    autoSize: Boolean(props.autoSize !== false),
    preload: Boolean(props.preload !== false),
    language: String(props.language || 'zh-cn'),
    useH5Prism: Boolean(props.useH5Prism !== false),
    enableSystemMenu: false, // 禁用系统菜单
    playsinline: Boolean(props.playsinline),

    // 只保留基础控制功能
    keyShortCuts: true, // 启用快捷键（空格播放/暂停）
    clickPause: true, // 点击播放/暂停

    // 传递fileKey给renderjs用于进度保存
    fileKey: props.fileKey || null,
  };

  // 只添加封面图片
  if (props.cover && typeof props.cover === 'string' && props.cover.trim()) {
    config.cover = String(props.cover).trim();
  }

  // 确保 ID 唯一
  if (!config.id || config.id === '') {
    config.id = 'aliplayer-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);
  }

  console.log('Vue组件构建的VidAuth配置:', config);
  return config;
};

// 播放器配置（用于传递给renderjs）
const playerConfig = ref(buildPlayerConfig());

// 全屏和笔记相关状态
const isFullscreen = ref(false);
const showNoteModal = ref(false);
const noteContent = ref('');
// 计算容器样式
const containerStyle = computed(() => {
  const style: Record<string, string> = {};

  if (typeof props.width === 'number') {
    style.width = `${props.width}px`;
  } else {
    style.width = props.width;
  }

  if (props.height !== 'auto') {
    if (typeof props.height === 'number') {
      style.height = `${props.height}px`;
    } else {
      style.height = props.height;
    }
  }

  return style;
});

// App端使用 renderjs，播放控制方法通过 renderjs 实现

// 播放控制方法（App端通过 renderjs 实现）
const play = () => {
  console.log('App端播放控制通过 renderjs 实现');
};

const pause = () => {
  console.log('App端播放控制通过 renderjs 实现');
};

const stop = () => {
  console.log('App端播放控制通过 renderjs 实现');
};

const seek = (_time: number) => {
  console.log('App端播放控制通过 renderjs 实现');
};

const setVolume = (_volume: number) => {
  console.log('App端播放控制通过 renderjs 实现');
};

const getCurrentTime = () => {
  console.log('App端播放控制通过 renderjs 实现');
  return 0;
};

const getDuration = () => {
  console.log('App端播放控制通过 renderjs 实现');
  return 0;
};

const destroyPlayer = () => {
  console.log('App端播放器销毁通过 renderjs 实现');
};

// 处理智能答疑按钮点击
const handleAIChat = () => {
  emit('ai-chat');
};



// 处理做笔记按钮点击
const handleTakeNote = () => {
  showNoteModal.value = true;
  noteContent.value = '';
};

// 获取VidAuth信息 - 供renderjs调用
const getVidAuthInfo = () => {
  console.log('Vue组件: 获取VidAuth信息', { videoId: props.videoId, playAuth: props.playAuth });
  return {
    videoId: props.videoId,
    playAuth: props.playAuth,
  };
};

// 退出全屏 - 供renderjs调用
const exitFullscreen = () => {
  console.log('Vue组件: 退出全屏被调用');

  // 获取当前播放时间
  let currentTime = 0;
  try {
    // 检查document是否可用
    if (typeof document !== 'undefined' && document.getElementById) {
      const playerElement = document.getElementById(playerId.value);
      if (playerElement && (window as any).getPlayerCurrentTime) {
        currentTime = (window as any).getPlayerCurrentTime();
        console.log('退出全屏时的播放时间:', currentTime);
      }
    } else {
      console.warn('document.getElementById 不可用，跳过播放时间获取');
    }
  } catch (error) {
    console.warn('获取播放时间失败:', error);
  }

  // 获取资源ID
  let resourceId = '';
  try {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1] as any;
      resourceId = currentPage.options?.id || currentPage.options?.resourceId || '';
      console.log('退出全屏时的资源ID:', resourceId);
    }
  } catch (error) {
    console.warn('获取资源ID失败:', error);
  }

  // 跳转到资源详情页面
  if (resourceId) {
    const url = `/pages-subpackages/students/learning-resource/resource-details?id=${resourceId}`;
    console.log('退出全屏，跳转到:', url);

    uni.redirectTo({
      url: url,
      success: () => {
        console.log('成功跳转到资源详情页面');
      },
      fail: error => {
        console.error('跳转失败:', error);
        // 如果跳转失败，执行默认退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      },
    });
  } else {
    console.warn('没有资源ID，执行默认退出全屏');
    // 如果没有资源ID，执行默认退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
};

// 打开全屏页面
const openFullscreenPage = () => {
  console.log('打开全屏页面，当前VidAuth信息:', {
    videoId: props.videoId,
    playAuth: props.playAuth,
  });

  // 获取当前播放时间
  let currentTime = 0;

  // 尝试从renderjs获取播放时间
  try {
    // 检查document是否可用
    if (typeof document !== 'undefined' && document.getElementById) {
      const playerElement = document.getElementById(playerId.value);
      if (playerElement && (window as any).getPlayerCurrentTime) {
        currentTime = (window as any).getPlayerCurrentTime();
        console.log('从renderjs获取播放时间:', currentTime);
      }
    } else {
      console.warn('document.getElementById 不可用，跳过播放时间获取');
    }
  } catch (error) {
    console.warn('从renderjs获取播放时间失败:', error);
  }

  // 获取资源ID - 从当前页面的参数中获取
  let resourceId = '';
  try {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1] as any;
      resourceId = currentPage.options?.id || currentPage.options?.resourceId || '';
      console.log('从当前页面获取资源ID:', resourceId);
    }
  } catch (error) {
    console.warn('获取资源ID失败:', error);
  }

  // 构建跳转URL - 使用VidAuth参数
  let url = `/pages-subpackages/students/learning-resource/fullscreen-video`;
  const urlParams: string[] = [];

  // 添加VidAuth参数
  if (props.videoId) {
    urlParams.push(`videoId=${encodeURIComponent(props.videoId)}`);
  }
  if (props.playAuth) {
    urlParams.push(`playAuth=${encodeURIComponent(props.playAuth)}`);
  }

  // 添加视频标题参数
  if (props.title) {
    urlParams.push(`title=${encodeURIComponent(props.title)}`);
    console.log('添加视频标题到跳转参数:', props.title);
  }

  // 添加播放时间参数
  urlParams.push(`currentTime=${currentTime}`);
  urlParams.push(`chatType=25`);

  // 添加资源ID参数
  if (resourceId) {
    urlParams.push(`id=${encodeURIComponent(resourceId)}`);
    console.log('添加资源ID到跳转参数:', resourceId);
  } else {
    console.warn('未找到资源ID');
  }

  // 拼接完整URL
  if (urlParams.length > 0) {
    url += '?' + urlParams.join('&');
  }

  // 跳转到全屏页面
  uni.navigateTo({
    url: url,
    success: () => {
      console.log('成功跳转到全屏页面');
    },
    fail: error => {
      console.error('跳转到全屏页面失败:', error);
    },
  });
};

// 监听VidAuth参数变化，重新创建播放器
watch(
  () => [props.videoId, props.playAuth],
  ([newVideoId, newPlayAuth]) => {
    if (newVideoId && newPlayAuth && player.value) {
      // VidAuth模式下需要重新创建播放器
      playerConfig.value = buildPlayerConfig();
    }
  }
);

// DOM 事件处理方法
const handleVideoReady = (_event: CustomEvent) => {
  console.log('收到播放器准备就绪事件');
  onPlayerReady();
};

// renderjs 事件处理方法
const onPlayerReady = () => {
  console.log('Vue组件: 播放器准备就绪，当前播放时间参数:', props.currentVideoPlayTime);

  // 如果有指定的播放时间，定位到该位置
  if (props.currentVideoPlayTime && props.currentVideoPlayTime > 0) {
    console.log('Vue组件: 检测到需要定位，准备定位到:', props.currentVideoPlayTime, '秒');
    seekToTime(props.currentVideoPlayTime);
  } else {
    console.log('Vue组件: 无需定位，播放时间参数为:', props.currentVideoPlayTime);
  }

  emit('ready');
};

const onPlayerPlay = () => {
  emit('play');
};

const onPlayerPause = () => {
  emit('pause');
};

const onPlayerEnded = () => {
  emit('ended');
};

const onPlayerError = (error: any) => {
  emit('error', error);
};

const onPlayerTimeUpdate = (currentTime: number) => {
  emit('timeupdate', currentTime);
};

const onPlayerLoadedMetadata = (duration: number) => {
  emit('loadedmetadata', duration);
};

const onAIChat = () => {
  emit('ai-chat');
};

const onTakeNote = (data: { time: number }) => {
  const noteData = {
    content: noteContent.value || '笔记内容',
    time: data.time,
    timestamp: new Date().toISOString(),
  };
  emit('take-note', noteData);
};

// 进度保存相关变量（备用方案）
let lastSaveTime = 0;
let lastSavedProgress = 0;
const SAVE_INTERVAL = 2000; // 2秒内最多保存一次
const MIN_PROGRESS_DIFF = 1; // 进度差异至少1秒才保存

const onSaveProgress = (time?: number | { time: number }) => {
  let actualTime: number;
  if (typeof time === 'number') {
    actualTime = time;
  } else if (time && typeof time === 'object' && 'time' in time) {
    actualTime = time.time;
  } else {
    // 尝试从全局变量获取
    actualTime = (window as any).currentVideoTime || 0;
  }

  if (actualTime > 0) {
    console.log('Vue组件: onSaveProgress被调用，时间:', actualTime);

    // 备用方案：如果renderjs的API调用失败，Vue组件也尝试保存
    try {
      const now = Date.now();
      const progressDiff = Math.abs(actualTime - lastSavedProgress);

      // 节流控制：2秒内最多保存一次，且进度差异至少1秒
      if (now - lastSaveTime < SAVE_INTERVAL && progressDiff < MIN_PROGRESS_DIFF) {
        return;
      }

      // 只有当播放时间大于0且有文件key时才保存
      if (actualTime > 0 && props.fileKey) {
        lastSaveTime = now;
        lastSavedProgress = actualTime;

        getVideoProgressSaveOrUpdate({
          fileKey: props.fileKey,
          currentPosition: actualTime,
        })
          .then(res => {
            console.log('Vue组件: 备用进度保存成功:', res);
          })
          .catch(error => {
            console.warn('Vue组件: 备用进度保存失败:', error);
            // 保存失败时重置时间，允许下次重试
            lastSaveTime = 0;
          });
      }
    } catch (error) {
      console.warn('Vue组件: 备用进度保存异常:', error);
    }

    emit('save-progress', actualTime);
  }
};

// 定位到指定时间
const seekToTime = (time: number) => {
  console.log('Vue组件: 尝试定位到时间:', time, '秒');

  // 通过 renderjs 调用播放器的 seek 方法
  try {
    const seekData = { time };
    // 创建新的配置对象，触发renderjs的propChanged
    const newConfig = { ...playerConfig.value, seekTime: seekData };
    playerConfig.value = newConfig;
    console.log('Vue组件: 已发送定位指令到 renderjs，seekData:', seekData);
  } catch (error) {
    console.warn('Vue组件: 定位失败:', error);
  }
};

// 监听 props 变化，更新 playerConfig
watch(
  () => [props.videoId, props.playAuth, props.width, props.height, props.currentVideoPlayTime],
  () => {
    if (props.videoId && props.playAuth) {
      const newConfig = buildPlayerConfig();
      // 添加当前播放时间到配置中
      if (props.currentVideoPlayTime && props.currentVideoPlayTime > 0) {
        newConfig.currentVideoPlayTime = props.currentVideoPlayTime;
        console.log('Vue组件: 添加播放时间到配置:', props.currentVideoPlayTime);
      }
      playerConfig.value = newConfig;
    }
  },
  { immediate: true }
);

// 监听播放时间变化，实现定位功能
watch(
  () => props.currentVideoPlayTime,
  (newTime, oldTime) => {
    console.log('Vue组件: 检测到播放时间变化，新时间:', newTime, '旧时间:', oldTime);
    if (newTime && newTime > 0) {
      console.log('Vue组件: 准备定位到:', newTime, '秒');
      seekToTime(newTime);
    }
  },
  { immediate: true } // 立即执行一次
);

// 监听全屏状态变化 - 参考博客的方法
watch(
  () => isFullscreen.value,
  newVal => {
    console.log('全屏状态变化:', newVal);
    if (newVal) {
      // 进入全屏，延迟添加悬浮按钮确保DOM结构稳定
      setTimeout(() => {
        addFloatingButtonsToPlayer();
      }, 300); // 增加延迟时间
    } else {
      // 退出全屏，移除悬浮按钮
      removeFloatingButtonsFromPlayer();
    }
  }
);

// 处理全屏状态变化 - 参考博客的方法
const handleFullscreenChange = () => {
  const isCurrentlyFullscreen = !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );

  console.log('全屏状态变化:', isCurrentlyFullscreen);
  isFullscreen.value = isCurrentlyFullscreen;
  emit('fullscreenchange', isCurrentlyFullscreen);

  if (isCurrentlyFullscreen) {
    // 进入全屏，强制横屏
    // #ifdef APP-PLUS
    if (typeof plus !== 'undefined' && plus.screen && plus.screen.lockOrientation) {
      try {
        plus.screen.lockOrientation('landscape-primary');
        console.log('已设置强制横屏');
      } catch (error) {
        console.warn('设置横屏失败:', error);
      }
    }
    // #endif
  } else {
    // 退出全屏，强制竖屏
    // #ifdef APP-PLUS
    if (typeof plus !== 'undefined' && plus.screen && plus.screen.lockOrientation) {
      try {
        plus.screen.lockOrientation('portrait-primary');
        console.log('已设置强制竖屏');
      } catch (error) {
        console.warn('设置竖屏失败:', error);
      }
    }
    // #endif
  }
};

// 添加悬浮按钮到播放器容器 - 修复全屏video元素问题
const addFloatingButtonsToPlayer = () => {
  console.log('开始添加悬浮按钮到播放器容器');

  // 移除已存在的按钮
  removeFloatingButtonsFromPlayer();

  // 查找全屏容器，优先查找全屏元素
  let targetElement =
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement;

  console.log('全屏元素:', targetElement);

  // 如果没有找到全屏元素，查找video元素的父容器
  if (!targetElement) {
    // 尝试多种方式查找video元素
    const videoElement =
      document.querySelector('video[src*="huayun-ai-obs-public"]') ||
      document.querySelector('video[webkit-playsinline]') ||
      document.querySelector('video');

    if (videoElement) {
      targetElement = videoElement.parentElement;
      console.log('通过video元素找到父容器:', targetElement, 'video元素:', videoElement);
    }
  }

  // 如果还是没找到，尝试查找阿里云播放器相关的容器
  if (!targetElement) {
    targetElement =
      document.querySelector('.prism-player') ||
      document.querySelector('.aliplayer') ||
      document.getElementById(playerId.value);
    console.log('通过播放器类名找到容器:', targetElement);
  }

  // 最后的备用方案：添加到body
  if (!targetElement) {
    console.warn('未找到合适的容器，将添加到body');
    targetElement = document.body;
  }

  // 创建悬浮按钮容器
  const floatingContainer = document.createElement('div');
  floatingContainer.className = 'floating-buttons-fullscreen';
  floatingContainer.id = 'floating-buttons-fullscreen';

  // 根据目标容器类型选择定位方式
  const isBodyOrFullscreen =
    targetElement === document.body ||
    targetElement === document.fullscreenElement ||
    targetElement === (document as any).webkitFullscreenElement ||
    targetElement === (document as any).mozFullScreenElement ||
    targetElement === (document as any).msFullscreenElement;

  const positionStyle = isBodyOrFullscreen ? 'fixed' : 'absolute';

  floatingContainer.style.cssText = `
    position: ${positionStyle} !important;
    right: 20px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 999999 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 16px !important;
    pointer-events: auto !important;
  `;

  // 创建智能答疑按钮 - 使用新的样式规格
  const aiButton = document.createElement('div');
  aiButton.className = 'floating-button ai-button';
  aiButton.style.cssText = `
    width: 45px !important;
    height: 59px !important;
    flex-shrink: 0 !important;
    border-radius: 8px !important;
    background: rgba(0, 0, 0, 0.28) !important;
    color: white !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    border: none !important;
    user-select: none !important;
  `;
  aiButton.innerHTML = `
    <img src="/static/learning-resource/ai-logo.svg" style="width: 27px; height: 27px; margin-bottom: 2px;" alt="AI助手">
    <div style="font-size: 10px; text-align: center; line-height: 1.1;">智能答疑</div>
  `;
  aiButton.addEventListener('click', () => {
    console.log('智能答疑按钮被点击');
    handleAIChat();
  });

  // 创建做笔记按钮
  const noteButton = document.createElement('div');
  noteButton.className = 'floating-button note-button';
  noteButton.style.cssText = aiButton.style.cssText;
  noteButton.innerHTML = `
    <div style="font-size: 20px; margin-bottom: 2px;">📝</div>
    <div style="font-size: 10px; text-align: center; line-height: 1.1;">做笔记</div>
  `;
  noteButton.addEventListener('click', () => {
    console.log('做笔记按钮被点击');
    handleTakeNote();
  });

  // 添加按钮到容器
  floatingContainer.appendChild(aiButton);
  floatingContainer.appendChild(noteButton);

  // 将悬浮按钮容器添加到播放器容器中
  targetElement.appendChild(floatingContainer);
  console.log('悬浮按钮已添加到播放器容器');
};

// 移除悬浮按钮 - 参考博客的方法
const removeFloatingButtonsFromPlayer = () => {
  try {
    // 检查document是否可用
    if (typeof document !== 'undefined' && document.getElementById) {
      const element = document.getElementById('floating-buttons-fullscreen');
      if (element) {
        const parent = element.parentNode;
        if (parent) {
          parent.removeChild(element);
          console.log('悬浮按钮已移除');
        }
      }
    } else {
      console.warn('document.getElementById 不可用，跳过悬浮按钮移除');
    }
  } catch (error) {
    console.warn('移除悬浮按钮失败:', error);
  }
};

onMounted(() => {
  // App端使用 renderjs，不需要在这里初始化播放器

  // 监听全屏状态变化 - 添加App端兼容性检查
  try {
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('fullscreenchange', handleFullscreenChange);
      window.addEventListener('webkitfullscreenchange', handleFullscreenChange);
      window.addEventListener('mozfullscreenchange', handleFullscreenChange);
      window.addEventListener('MSFullscreenChange', handleFullscreenChange);
      console.log('全屏事件监听器已添加');
    } else {
      console.warn('window对象不可用，跳过全屏事件监听器添加');
    }
  } catch (error) {
    console.warn('添加全屏事件监听器失败:', error);
  }

  // 添加自定义播放器事件监听器（备用方案）- 添加App端兼容性检查
  try {
    if (typeof document !== 'undefined' && document.addEventListener) {
      const handlePlayerReadyEvent = () => {
        onPlayerReady();
      };

      document.addEventListener('player-ready', handlePlayerReadyEvent as EventListener);

      // 添加自定义进度保存事件监听器
      const handleSaveProgressEvent = (event: CustomEvent) => {
        if (event.detail && typeof event.detail.time === 'number') {
          onSaveProgress(event.detail.time);
        }
      };

      document.addEventListener('video-save-progress', handleSaveProgressEvent as EventListener);
    } else {
      console.warn('Vue组件: document对象不可用，跳过DOM事件监听器添加');
    }
  } catch (error) {
    console.warn('Vue组件: 添加DOM事件监听器失败:', error);
  }

  // 监听 uni 全局事件（备用方案）
  if (typeof uni !== 'undefined' && uni.$on) {
    uni.$on('video-save-progress', (time: number) => {
      onSaveProgress(time);
    });
  }

  console.log('Vue组件: 进度保存逻辑已移到renderjs中处理，Vue组件只保留事件监听');
});

// 暴露方法给父组件
defineExpose({
  play,
  pause,
  stop,
  seek,
  setVolume,
  getCurrentTime,
  getDuration,
  destroyPlayer,
  openFullscreenPage,
  getVidAuthInfo,
  exitFullscreen,
  player: computed(() => player.value),
  // renderjs 事件处理方法
  onPlayerReady,
  onPlayerPlay,
  onPlayerPause,
  onPlayerEnded,
  onPlayerError,
  onPlayerTimeUpdate,
  onPlayerLoadedMetadata,
  onAIChat,
  onTakeNote,
  onSaveProgress,
  seekToTime,
});

onUnmounted(() => {
  console.log('Vue组件: 开始卸载LkVideoPlayer组件');

  // 清理全屏事件监听器 - 添加安全检查
  try {
    if (typeof window !== 'undefined' && window.removeEventListener) {
      window.removeEventListener('fullscreenchange', handleFullscreenChange);
      window.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      window.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      window.removeEventListener('MSFullscreenChange', handleFullscreenChange);
      console.log('Vue组件: 全屏事件监听器已清理');
    }
  } catch (error) {
    console.warn('Vue组件: 清理全屏事件监听器失败:', error);
  }

  // 移除悬浮按钮
  try {
    removeFloatingButtonsFromPlayer();
  } catch (error) {
    console.warn('Vue组件: 移除悬浮按钮失败:', error);
  }

  // 销毁播放器
  try {
    destroyPlayer();
  } catch (error) {
    console.warn('Vue组件: 销毁播放器失败:', error);
  }

  // 移除 uni 全局事件监听
  if (typeof uni !== 'undefined' && uni.$off) {
    uni.$off('video-save-progress');
  }

  console.log('Vue组件: LkVideoPlayer组件已卸载，事件监听器已清理');
});
</script>

<style scoped>
.lk-video-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 自定义浮层内容 */
:deep(.custom-overlay-content) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2147483647;
}

/* 悬浮按钮容器 */
:deep(.floating-buttons-container) {
  position: absolute !important;
  left: 20px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 2147483647 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 16px !important;
  pointer-events: auto !important;
}

.player-container {
  width: 100%;
  position: relative;
}

/* 确保播放器在移动端的适配 */
.player-container :deep(.prism-player) {
  width: 100% !important;
  height: auto !important;
}

/* 全屏时的悬浮按钮 */
.fullscreen-floating-buttons {
  position: fixed;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.floating-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.floating-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.floating-button:active {
  transform: scale(0.95);
}

.button-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.button-text {
  font-size: 12px;
  color: white;
  text-align: center;
  line-height: 1.2;
}

.ai-button:hover {
  background: rgba(74, 144, 226, 0.8);
}

.note-button:hover {
  background: rgba(139, 69, 19, 0.8);
}

/* 笔记弹窗样式 */
.note-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.note-modal {
  width: 90%;
  max-width: 500px;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.note-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.note-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.note-modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  color: #666;
  font-size: 18px;
  transition: all 0.2s ease;
}

.note-modal-close:hover {
  background: #e0e0e0;
  color: #333;
}

.note-modal-content {
  padding: 24px;
}

.note-textarea {
  width: 100%;
  min-height: 120px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.note-textarea:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.note-textarea::placeholder {
  color: #999;
}

.note-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 12px;
  color: #666;
}

.note-time {
  color: #4a90e2;
  font-weight: 500;
}

.note-count {
  color: #999;
}

.note-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  justify-content: flex-end;
}

.note-save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.note-save-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.note-save-btn:active {
  transform: translateY(0);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .fullscreen-floating-buttons {
    left: 10px;
    gap: 12px;
  }

  .floating-button {
    width: 60px;
    height: 60px;
  }

  .button-icon {
    font-size: 20px;
  }

  .button-text {
    font-size: 10px;
  }

  .note-modal {
    width: 95%;
    margin: 20px;
  }

  .note-modal-header,
  .note-modal-content,
  .note-modal-footer {
    padding: 16px;
  }

  .note-textarea {
    min-height: 100px;
    padding: 12px;
  }
}
</style>

<script module="renderScript" lang="renderjs">
// renderjs 脚本，用于在 App 端处理阿里云播放器
let player = null;
let currentConfig = null;
let progressSaveTimer = null; // 进度保存定时器
let isPlayerReady = false; // 播放器是否准备好
let pendingSeekTime = null; // 待执行的定位时间

export default {
  mounted() {
    console.log('renderjs mounted');
    // 修复uni-app和阿里云播放器的DOM兼容性问题
    this.fixDOMCompatibility();
    // 动态加载阿里云播放器
    this.loadAliPlayer();
  },

  methods: {
    // 修复DOM兼容性问题 - 针对uni-app的特殊处理
    fixDOMCompatibility() {
      try {
        // 直接修复uni-app的removeAttribute问题
        // 错误来源：uni-shared.es.js:643 中的 key.startsWith
        const originalRemoveAttribute = Element.prototype.removeAttribute;

        Element.prototype.removeAttribute = function(name) {
          // 确保name参数有startsWith方法
          if (name && typeof name === 'object' && !name.startsWith) {
            // 如果name是对象但没有startsWith方法，转换为字符串
            name = String(name);
          }

          // 确保name是字符串
          if (typeof name !== 'string') {
            name = String(name || '');
          }

          // 如果name为空，直接返回
          if (!name) {
            return;
          }

          try {
            return originalRemoveAttribute.call(this, name);
          } catch (error) {
            console.warn('removeAttribute调用失败:', error, 'name:', name, 'type:', typeof name);
          }
        };

        console.log('DOM兼容性修复已应用 - 针对uni-app优化');
      } catch (error) {
        console.warn('DOM兼容性修复失败:', error);
      }
    },

    // 确保DOM方法的安全性
    ensureDOMSafety() {
      try {
        // 检查document是否可用
        if (typeof document !== 'undefined' && document.getElementById) {
          // 临时替换可能有问题的方法
          const container = document.getElementById(currentConfig && currentConfig.id);
          if (container) {
            // 为当前容器元素添加安全的方法
            const originalRemoveAttribute = container.removeAttribute;
            container.removeAttribute = function(name) {
              if (name && typeof name === 'string') {
                return originalRemoveAttribute.call(this, name);
              }
            };

            const originalSetAttribute = container.setAttribute;
            container.setAttribute = function(name, value) {
              if (name && typeof name === 'string') {
                return originalSetAttribute.call(this, name, String(value || ''));
              }
            };
          }
        } else {
          console.warn('document.getElementById 不可用，跳过DOM安全性确保');
        }
      } catch (error) {
        console.warn('DOM安全性确保失败:', error);
      }
    },

    // 备份DOM方法
    backupDOMMethods() {
      try {
        return {
          removeAttribute: Element.prototype.removeAttribute,
          setAttribute: Element.prototype.setAttribute,
          getAttribute: Element.prototype.getAttribute
        };
      } catch (error) {
        console.warn('备份DOM方法失败:', error);
        return null;
      }
    },

    // 恢复DOM方法
    restoreDOMMethods(backup) {
      try {
        if (backup) {
          Element.prototype.removeAttribute = backup.removeAttribute;
          Element.prototype.setAttribute = backup.setAttribute;
          Element.prototype.getAttribute = backup.getAttribute;
        }
      } catch (error) {
        console.warn('恢复DOM方法失败:', error);
      }
    },



    // 加载阿里云播放器脚本
    loadAliPlayer() {
      if (window.Aliplayer) {
        console.log('阿里云播放器已加载');
        return;
      }

      // 加载阿里云播放器CSS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://g.alicdn.com/de/prismplayer/2.15.2/skins/default/aliplayer-min.css';
      document.head.appendChild(cssLink);

      // 加载阿里云播放器JS
      const script = document.createElement('script');
      script.src = 'https://g.alicdn.com/de/prismplayer/2.15.2/aliplayer-min.js';
      script.onload = () => {
        console.log('阿里云播放器脚本加载完成');
        if (currentConfig) {
          this.createPlayer(currentConfig);
        }
      };
      script.onerror = (error) => {
        console.error('阿里云播放器脚本加载失败:', error);
      };
      document.head.appendChild(script);
    },

    // 清理播放器配置 - 使用VidAuth配置避免错误
    cleanPlayerConfig(config) {
      // 验证输入配置
      if (!config || typeof config !== 'object') {
        console.error('无效的配置对象');
        return null;
      }

      // 验证VidAuth必需字段
      if (!config.vid || String(config.vid).trim() === '' ||
          !config.playauth || String(config.playauth).trim() === '') {
        console.warn('VidAuth参数为空或无效 - vid:', config.vid, 'playauth:', config.playauth);
        return null;
      }

      // 创建VidAuth配置
      const finalConfig = {
        id: String(config.id || 'aliplayer-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11)),
        vid: String(config.vid).trim(),
        playauth: String(config.playauth).trim(),
        width: String(config.width || '100%'),
        height: String(config.height || 'auto'),
        autoplay: Boolean(config.autoplay),
        preload: Boolean(config.preload !== false),

        // 自定义工具栏 - 根据阿里云文档配置
        useH5Prism: true,
        enableSystemMenu: false,
        skinLayout: [
          {
            name: "bigPlayButton",
            align: "blabs",
            x: 30,
            y: 80
          },
          {
            name: "H5Loading",
            align: "cc"
          },
          {
            name: "errorDisplay",
            align: "tlabs",
            x: 0,
            y: 0
          },
          {
            name: "infoDisplay"
          },
          {
            name: "tooltip",
            align: "blabs",
            x: 0,
            y: 56
          },
          {
            name: "thumbnail"
          },
          {
            name: "controlBar",
            align: "blabs",
            x: 0,
            y: 0,
            children: [
              {
                name: "progress",
                align: "blabs",
                x: 0,
                y: 44
              },
              {
                name: "playButton",
                align: "tl",
                x: 15,
                y: 12
              }
              // 保留播放按钮和进度条，移除其他控件（时间显示、音量、设置等）
            ]
          }
          // 移除fullScreenButton，不显示任何全屏按钮
        ]
      };

      // 只添加确实需要且安全的可选配置
      if (config.cover && typeof config.cover === 'string' && config.cover.trim()) {
        finalConfig.cover = String(config.cover).trim();
      }

      if (config.muted !== undefined) {
        finalConfig.mute = Boolean(config.muted);
      }

      if (config.isLive !== undefined) {
        finalConfig.isLive = Boolean(config.isLive);
      }

      console.log('清理前的VidAuth配置:', config);
      console.log('清理后的VidAuth配置:', finalConfig);

      return finalConfig;
    },

    // 创建播放器
    createPlayer(config) {
      if (!window.Aliplayer) {
        console.warn('阿里云播放器未加载');
        return;
      }

      if (!config || !config.vid || !config.playauth) {
        console.warn('配置或VidAuth参数为空');
        return;
      }

      try {
        // 销毁旧播放器
        this.destroyPlayer();

        // 重置播放器状态
        isPlayerReady = false;

        // 清理配置，移除 undefined 值和非法属性
        const cleanConfig = this.cleanPlayerConfig(config);

        // 检查清理后的配置是否有效
        if (!cleanConfig) {
          console.error('配置清理失败');
          return;
        }

        console.log('创建播放器:', cleanConfig);

        // 确保容器存在
        let container = null;
        if (typeof document !== 'undefined' && document.getElementById) {
          container = document.getElementById(cleanConfig.id);
          console.log('container', container);
          if (!container) {
            console.error('播放器容器不存在:', cleanConfig.id);
            return;
          }

          // 清空容器内容，确保干净的环境
          container.innerHTML = '';
        } else {
          console.error('document.getElementById 不可用，无法创建播放器');
          return;
        }

        console.log('player', window.Aliplayer);
        console.log('即将创建播放器，配置:', JSON.stringify(cleanConfig, null, 2));

        // 在创建播放器前再次确保DOM兼容性
        this.ensureDOMSafety();

        // 直接创建播放器，使用最简单的方法
        player = new window.Aliplayer(cleanConfig);

        // 将播放器实例和配置暴露到全局，供Vue组件调用
        window.getPlayerCurrentTime = () => {
          if (player && typeof player.getCurrentTime === 'function') {
            try {
              return player.getCurrentTime();
            } catch (error) {
              console.warn('获取播放时间失败:', error);
              return 0;
            }
          }
          return 0;
        };

        // 保存当前VidAuth信息到全局
        window.currentVidAuthInfo = {
          videoId: cleanConfig.vid,
          playAuth: cleanConfig.playauth
        };
        console.log('保存VidAuth信息到全局:', window.currentVidAuthInfo);

        // 绑定事件
        this.bindEvents();

        // 启动进度保存定时器
        console.log('renderjs: 准备启动进度保存定时器');
        this.startProgressSaveTimer();

        // // 添加自定义全屏按钮事件监听
        // this.addCustomFullscreenListener();

        // // 备用方案：直接添加自定义全屏按钮
        // this.addDirectFullscreenButton();

        // 拦截退出全屏按钮
        this.interceptExitFullscreenButton();



      } catch (error) {
        console.error('创建播放器失败:', error);
      }
    },

    // 绑定播放器事件
    bindEvents() {
      if (!player) return;

      try {
        // 使用安全的事件绑定方式
        player.on('ready', () => {
          console.log('renderjs: 播放器准备就绪');
          isPlayerReady = true;

          // 检查是否需要定位到指定时间
          let shouldSeek = false;
          let seekTime = 0;

          // 如果有待执行的定位，立即执行
          if (pendingSeekTime !== null) {
            console.log('renderjs: 执行待定的定位请求:', pendingSeekTime, '秒');
            shouldSeek = true;
            seekTime = pendingSeekTime;
            pendingSeekTime = null; // 清除待定定位
          }

          // 执行定位
          if (shouldSeek && seekTime > 0) {
            try {
              console.log('renderjs: 开始定位到:', seekTime, '秒');
              player.seek(seekTime);
              this.showSeekToast(seekTime);
              console.log('renderjs: 定位成功');
            } catch (error) {
              console.warn('renderjs: 执行定位失败:', error);
            }
          }

          // 额外检查：从当前配置中获取定位时间
          setTimeout(() => {
            try {
              if (currentConfig && currentConfig.currentVideoPlayTime && currentConfig.currentVideoPlayTime > 0) {
                const configSeekTime = currentConfig.currentVideoPlayTime;
                console.log('renderjs: 从配置中检测到需要定位的时间:', configSeekTime, '秒');
                player.seek(configSeekTime);
                this.showSeekToast(configSeekTime);
                console.log('renderjs: 配置定位成功');
              }
            } catch (error) {
              console.warn('renderjs: 配置定位失败:', error);
            }
          }, 1000); // 延迟1秒执行，确保播放器完全准备好

          // 方法1：尝试调用 Vue 组件方法（主要方法）
          try {
            this.$ownerInstance.callMethod('onPlayerReady');
            console.log('renderjs: 成功调用Vue组件onPlayerReady方法');
          } catch (e) {
            console.warn('renderjs: 调用 onPlayerReady 失败:', e);
          }

          // 方法2：备用方案 - 触发自定义事件
          try {
            if (typeof document !== 'undefined' && document.dispatchEvent) {
              const event = new CustomEvent('player-ready', {
                detail: { source: 'renderjs' }
              });
              document.dispatchEvent(event);
              console.log('renderjs: 成功触发player-ready自定义事件');
            }
          } catch (e) {
            console.warn('renderjs: 触发自定义事件失败:', e);
          }
        });

        player.on('play', () => {
          try {
            this.$ownerInstance.callMethod('onPlayerPlay');
            // 播放时启动进度保存定时器
            console.log('renderjs: 播放开始，重新启动进度保存定时器');
            this.startProgressSaveTimer();
          } catch (e) {
            console.warn('调用 onPlayerPlay 失败:', e);
          }
        });

        player.on('pause', () => {
          try {
            this.$ownerInstance.callMethod('onPlayerPause');
            // 暂停时保存当前进度但不停止定时器（继续监控）
            console.log('renderjs: 播放暂停，保存当前进度');
            const currentTime = Number(player.getCurrentTime() || 0);
            if (currentTime > 0) {
              try {
                this.$ownerInstance.callMethod('onSaveProgress', currentTime);
              } catch (saveError) {
                console.warn('renderjs: 暂停时保存进度失败:', saveError);
              }
            }
          } catch (e) {
            console.warn('调用 onPlayerPause 失败:', e);
          }
        });

        player.on('ended', () => {
          try {
            this.$ownerInstance.callMethod('onPlayerEnded');
            // 播放结束时保存最终进度并停止定时器
            console.log('renderjs: 播放结束，保存最终进度');
            const currentTime = Number(player.getCurrentTime() || 0);
            if (currentTime > 0) {
              try {
                this.$ownerInstance.callMethod('onSaveProgress', currentTime);
              } catch (saveError) {
                console.warn('renderjs: 播放结束时保存进度失败:', saveError);
              }
            }
            this.stopProgressSaveTimer();
          } catch (e) {
            console.warn('调用 onPlayerEnded 失败:', e);
          }
        });

        player.on('error', (error) => {
          console.error('播放器错误:', error);
          try {
            // 确保错误对象是可序列化的
            const safeError = {
              message: String((error && error.message) || '未知错误'),
              code: (error && error.code) || 0
            };
            this.$ownerInstance.callMethod('onPlayerError', safeError);
          } catch (e) {
            console.warn('调用 onPlayerError 失败:', e);
          }
        });

        player.on('timeupdate', () => {
          try {
            const currentTime = Number(player.getCurrentTime() || 0);
            this.$ownerInstance.callMethod('onPlayerTimeUpdate', currentTime);
          } catch (e) {
            console.warn('调用 onPlayerTimeUpdate 失败:', e);
          }
        });

        player.on('loadedmetadata', () => {
          try {
            const duration = Number(player.getDuration() || 0);
            this.$ownerInstance.callMethod('onPlayerLoadedMetadata', duration);
          } catch (e) {
            console.warn('调用 onPlayerLoadedMetadata 失败:', e);
          }
        });

        // 监听全屏事件
        player.on('requestFullScreen', () => {
          console.log('进入全屏');
          // 强制横屏
          if (typeof plus !== 'undefined' && plus.screen && plus.screen.lockOrientation) {
            try {
              plus.screen.lockOrientation('landscape-primary');
              console.log('已设置强制横屏');
            } catch (error) {
              console.warn('设置横屏失败:', error);
            }
          }
          try {
            this.$ownerInstance.callMethod('onPlayerFullscreen', true);
          } catch (e) {
            console.warn('调用 onPlayerFullscreen 失败:', e);
          }
        });

        player.on('cancelFullScreen', () => {
          console.log('退出全屏');
          // 强制竖屏
          if (typeof plus !== 'undefined' && plus.screen && plus.screen.lockOrientation) {
            try {
              plus.screen.lockOrientation('portrait-primary');
              console.log('已设置强制竖屏');
            } catch (error) {
              console.warn('设置竖屏失败:', error);
            }
          }
          try {
            this.$ownerInstance.callMethod('onPlayerFullscreen', false);
          } catch (e) {
            console.warn('调用 onPlayerFullscreen 失败:', e);
          }
        });

      } catch (error) {
        console.error('绑定播放器事件失败:', error);
      }
    },

    // 启动进度保存定时器 - 直接在renderjs中处理进度保存
    startProgressSaveTimer() {
      console.log('renderjs: 启动进度保存定时器');
      // 先清除已存在的定时器
      this.stopProgressSaveTimer();

      // 进度保存相关变量
      let lastSaveTime = 0;
      let lastSavedProgress = 0;
      const SAVE_INTERVAL = 2000; // 2秒内最多保存一次
      const MIN_PROGRESS_DIFF = 1; // 进度差异至少1秒才保存

      progressSaveTimer = setInterval(() => {
        if (player && typeof player.getCurrentTime === 'function') {
          try {
            const currentTime = Number(player.getCurrentTime() || 0);
            // 只有当播放时间大于0时才保存进度
            if (currentTime > 0) {
              const now = Date.now();
              const progressDiff = Math.abs(currentTime - lastSavedProgress);

              // 节流控制：2秒内最多保存一次，且进度差异至少1秒
              if (now - lastSaveTime < SAVE_INTERVAL && progressDiff < MIN_PROGRESS_DIFF) {
                return;
              }

              // 获取fileKey - 从Vue组件的props中获取
              let fileKey = null;
              try {
                // 尝试从Vue组件实例获取fileKey
                if (this.$ownerInstance && this.$ownerInstance.$props) {
                  fileKey = this.$ownerInstance.$props.fileKey;
                } else if (currentConfig && currentConfig.fileKey) {
                  fileKey = currentConfig.fileKey;
                }
              } catch (error) {
                console.warn('renderjs: 获取fileKey失败:', error);
              }
              console.log('renderjs: 获取fileKey', this.$ownerInstance.$props);
              // 只有当有fileKey时才保存进度
              if (fileKey) {
                lastSaveTime = now;
                lastSavedProgress = currentTime;

                // 方案1：直接在renderjs中调用API保存进度
                this.saveVideoProgress(fileKey, currentTime);

                // 方案2：同时通过多种方式通知Vue组件保存进度（备用方案）
                this.notifyVueComponentToSaveProgress(currentTime);

                console.log('renderjs: 保存进度，fileKey:', fileKey, '时间:', currentTime);
              } else {
                // 每10秒输出一次调试信息，避免日志过多
                if (Date.now() % 10000 < 1000) {
                  console.log('renderjs: 没有fileKey，跳过进度保存');
                }
              }

              // 同时更新DOM属性和全局变量，供Vue组件使用
              try {
                const container = document.getElementById(currentConfig.id);
                if (container) {
                  container.setAttribute('data-video-progress', currentTime.toString());
                  container.setAttribute('data-video-timestamp', Date.now().toString());
                }
                window.currentVideoProgress = {
                  time: currentTime,
                  timestamp: Date.now()
                };
              } catch (error) {
                console.warn('renderjs: 更新进度数据失败:', error);
              }
            } else {
              // 每10秒输出一次调试信息，避免日志过多
              if (Date.now() % 10000 < 1000) {
                console.log('renderjs: 当前播放时间为0，不保存进度');
              }
            }
          } catch (error) {
            console.warn('renderjs: 保存视频进度失败:', error);
          }
        } else {
          // 每10秒输出一次调试信息，避免日志过多
          if (Date.now() % 10000 < 1000) {
            console.log('renderjs: 播放器不可用，player:', !!player, 'getCurrentTime:', player ? typeof player.getCurrentTime : 'N/A');
          }
        }
      }, 1000); // 每秒执行一次

      console.log('renderjs: 进度保存定时器已启动，ID:', progressSaveTimer);
    },

    // 直接在renderjs中保存视频进度
    saveVideoProgress(fileKey, currentPosition) {
      console.log('renderjs: ownerInstance', this.$ownerInstance.callMethod);
      this.$ownerInstance.callMethod('test', currentPosition);
      try {
        // 构建请求数据
        const requestData = {
          fileKey: fileKey,
          currentPosition: currentPosition
        };

        // 获取token和其他认证信息
        let token = '';
        let headers = {
          'Content-Type': 'application/json'
        };

        try {
          // 尝试从uni存储中获取token
          if (typeof uni !== 'undefined' && uni.getStorageSync) {
            token = uni.getStorageSync('token') || '';
            if (token) {
              // 按照项目的token格式设置请求头
              headers['Authorization'] = token;
              headers['anxun-auth'] = 'bearer ' + token;
            }
          }

          // 尝试从localStorage获取token（备用方案）
          if (!token && typeof localStorage !== 'undefined') {
            token = localStorage.getItem('token') || '';
            if (token) {
              headers['Authorization'] = token;
              headers['anxun-auth'] = 'bearer ' + token;
            }
          }

          // 尝试从全局变量获取token（备用方案）
          if (!token && typeof window !== 'undefined') {
            try {
              // 尝试从可能的全局变量获取token
              token = window.token || window.access_token || window.userToken || '';
              if (token) {
                headers['Authorization'] = token;
                headers['anxun-auth'] = 'bearer ' + token;
              }
            } catch (globalError) {
              console.warn('renderjs: 从全局变量获取token失败:', globalError);
            }
          }
        } catch (tokenError) {
          console.warn('renderjs: 获取token失败:', tokenError);
        }

        console.log('renderjs: 准备保存进度，token存在:', !!token);

        // 使用uni.request发送请求
        if (typeof uni !== 'undefined' && uni.request) {
          uni.request({
            url: '/huayun-ai/video/progress/saveOrUpdate',
            method: 'POST',
            data: requestData,
            header: headers,
            success: (res) => {
              console.log('renderjs: 进度保存成功:', res);
            },
            fail: (error) => {
              console.warn('renderjs: 进度保存失败:', error);
              // 如果是认证失败，记录详细信息
              if (error.statusCode === 401 || error.statusCode === 403) {
                console.error('renderjs: 认证失败，token:', !!token, 'statusCode:', error.statusCode);
              }
            }
          });
        } else {
          // 备用方案：使用fetch
          fetch('/huayun-ai/video/progress/saveOrUpdate', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestData)
          }).then(response => {
            if (response.ok) {
              console.log('renderjs: 进度保存成功（fetch）');
            } else {
              console.warn('renderjs: 进度保存失败（fetch）:', response.status);
              if (response.status === 401 || response.status === 403) {
                console.error('renderjs: 认证失败（fetch），token:', !!token, 'status:', response.status);
              }
            }
          }).catch(error => {
            console.warn('renderjs: 进度保存失败（fetch）:', error);
          });
        }
      } catch (error) {
        console.error('renderjs: 保存进度异常:', error);
      }
    },

    // 通知Vue组件保存进度（备用方案）
    notifyVueComponentToSaveProgress(currentTime) {
      try {
        // 方法1：尝试callMethod（主要方案）
        try {
          this.$ownerInstance.callMethod('onSaveProgress', currentTime);
          console.log('renderjs: 成功通过callMethod通知Vue组件保存进度');
        } catch (callMethodError) {
          console.warn('renderjs: callMethod通知失败:', callMethodError);

          // 方法2：触发自定义事件（备用方案）
          try {
            if (typeof document !== 'undefined' && document.dispatchEvent) {
              const event = new CustomEvent('video-save-progress', {
                detail: { time: currentTime },
                bubbles: true
              });
              document.dispatchEvent(event);
              console.log('renderjs: 成功通过自定义事件通知Vue组件保存进度');
            }
          } catch (eventError) {
            console.warn('renderjs: 自定义事件通知失败:', eventError);

            // 方法3：使用uni全局事件（最后备用方案）
            try {
              if (typeof uni !== 'undefined' && uni.$emit) {
                uni.$emit('video-save-progress', currentTime);
                console.log('renderjs: 成功通过uni全局事件通知Vue组件保存进度');
              }
            } catch (uniError) {
              console.warn('renderjs: uni全局事件通知失败:', uniError);
            }
          }
        }
      } catch (error) {
        console.error('renderjs: 通知Vue组件保存进度异常:', error);
      }
    },

    // 停止进度保存定时器
    stopProgressSaveTimer() {
      if (progressSaveTimer) {
        console.log('renderjs: 停止进度保存定时器');
        clearInterval(progressSaveTimer);
        progressSaveTimer = null;
      } else {
        console.log('renderjs: 进度保存定时器已经是停止状态');
      }
    },

    // 添加自定义全屏按钮事件监听
    // addCustomFullscreenListener() {
    //   console.log('添加自定义全屏按钮事件监听');

    //   // 延迟执行，确保DOM已渲染
    //   setTimeout(() => {
    //     // 查找阿里云播放器的全屏按钮
    //     const fullscreenBtn = document.querySelector('.prism-fullscreen-btn') ||
    //                          document.querySelector('.aliplayer-fullscreen-btn') ||
    //                          document.querySelector('[data-role="fullscreen"]') ||
    //                          document.querySelector('.prism-controlbar-fullscreen') ||
    //                          document.querySelector('.aliplayer-controlbar-fullscreen');

    //     if (fullscreenBtn) {
    //       // 移除原有的点击事件
    //       const newBtn = fullscreenBtn.cloneNode(true);
    //       fullscreenBtn.parentNode.replaceChild(newBtn, fullscreenBtn);

    //       // 修改按钮样式和内容
    //       newBtn.innerHTML = '<span style="font-size: 16px;">⛶</span>';
    //       newBtn.title = '全屏播放';

    //       // 添加自定义点击事件
    //       newBtn.addEventListener('click', (e) => {
    //         e.preventDefault();
    //         e.stopPropagation();
    //         console.log('替换的全屏按钮被点击');

    //         // 直接在renderjs中处理页面跳转
    //         this.handleCustomFullscreen();
    //       });

    //       console.log('全屏按钮功能已替换');
    //     } else {
    //       console.warn('未找到阿里云播放器全屏按钮');
    //       // 如果没找到，可能需要再次尝试
    //       if (this.retryCount < 5) {
    //         this.retryCount = (this.retryCount || 0) + 1;
    //         setTimeout(() => {
    //           this.addCustomFullscreenListener();
    //         }, 1000);
    //       } else {
    //         console.error('多次尝试后仍未找到全屏按钮');
    //       }
    //     }
    //   }, 1000); // 增加延迟时间
    // },

    // 备用方案：直接添加自定义全屏按钮 - 已禁用，不显示全屏按钮
    // addDirectFullscreenButton() {
    //   console.log('使用备用方案：直接添加自定义全屏按钮');

    //   setTimeout(() => {
    //     const playerContainer = document.getElementById(currentConfig && currentConfig.id);
    //     if (!playerContainer) {
    //       console.warn('未找到播放器容器');
    //       return;
    //     }

    //     // 检查是否已经添加了自定义全屏按钮
    //     if (playerContainer.querySelector('.direct-custom-fullscreen-btn')) {
    //       console.log('自定义全屏按钮已存在');
    //       return;
    //     }

    //     // 创建自定义全屏按钮
    //     const customFullscreenBtn = document.createElement('div');
    //     customFullscreenBtn.className = 'direct-custom-fullscreen-btn';
    //     customFullscreenBtn.style.cssText = `
    //       position: absolute !important;
    //       top: 10px !important;
    //       right: 10px !important;
    //       width: 45px !important;
    //       height: 59px !important;
    //       background: rgba(0, 0, 0, 0.28) !important;
    //       border-radius: 8px !important;
    //       display: flex !important;
    //       align-items: center !important;
    //       justify-content: center !important;
    //       cursor: pointer !important;
    //       z-index: 1000 !important;
    //       transition: all 0.3s ease !important;
    //       user-select: none !important;
    //       flex-shrink: 0 !important;
    //     `;

    //     customFullscreenBtn.innerHTML = '<span style="color: white; font-size: 18px; font-weight: bold;">⛶</span>';
    //     customFullscreenBtn.title = '全屏播放';

    //     // 添加点击事件
    //     customFullscreenBtn.addEventListener('click', (e) => {
    //       e.preventDefault();
    //       e.stopPropagation();
    //       console.log('直接添加的自定义全屏按钮被点击');

    //       // 直接在renderjs中处理页面跳转
    //       this.handleCustomFullscreen();
    //     });

    //     // 添加悬停效果
    //     customFullscreenBtn.addEventListener('mouseenter', () => {
    //       customFullscreenBtn.style.background = 'rgba(0, 0, 0, 0.6)';
    //       customFullscreenBtn.style.transform = 'scale(1.05)';
    //     });

    //     customFullscreenBtn.addEventListener('mouseleave', () => {
    //       customFullscreenBtn.style.background = 'rgba(0, 0, 0, 0.28)';
    //       customFullscreenBtn.style.transform = 'scale(1)';
    //     });

    //     // 添加到播放器容器
    //     playerContainer.appendChild(customFullscreenBtn);
    //     console.log('自定义全屏按钮已直接添加到播放器容器');
    //   }, 1500);
    // },

    // 拦截退出全屏按钮
    interceptExitFullscreenButton() {
      console.log('开始拦截退出全屏按钮');

      // 监听全屏状态变化
      const handleFullscreenChange = () => {
        const isFullscreen = !!(
          document.fullscreenElement ||
          document.webkitFullscreenElement ||
          document.mozFullScreenElement ||
          document.msFullscreenElement
        );

        console.log('全屏状态变化:', isFullscreen);

        if (isFullscreen) {
          // 进入全屏时，查找并拦截退出全屏按钮
          setTimeout(() => {
            this.findAndInterceptExitButton();
          }, 1000);
        }
      };

      // 添加全屏状态监听器
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.addEventListener('mozfullscreenchange', handleFullscreenChange);
      document.addEventListener('MSFullscreenChange', handleFullscreenChange);

      console.log('全屏状态监听器已添加');
    },

    // 查找并拦截退出全屏按钮
    findAndInterceptExitButton() {
      console.log('查找退出全屏按钮');

      // 查找可能的退出全屏按钮选择器
      const exitButtonSelectors = [
        '.prism-fullscreen-btn',
        '.aliplayer-fullscreen-btn',
        '[data-role="fullscreen"]',
        '.prism-controlbar-fullscreen',
        '.aliplayer-controlbar-fullscreen',
        '.prism-exitfullscreen-btn',
        '.aliplayer-exitfullscreen-btn'
      ];

      let exitButton = null;

      for (const selector of exitButtonSelectors) {
        exitButton = document.querySelector(selector);
        if (exitButton) {
          console.log('找到退出全屏按钮:', selector);
          break;
        }
      }

      if (exitButton) {
        // 移除原有的点击事件
        const newExitBtn = exitButton.cloneNode(true);
        exitButton.parentNode.replaceChild(newExitBtn, exitButton);

        // 添加自定义点击事件
        newExitBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('拦截到退出全屏按钮点击');

          try {
            // 调用Vue组件的exitFullscreen方法
            this.$ownerInstance.callMethod('exitFullscreen');
          } catch (error) {
            console.warn('调用exitFullscreen失败:', error);
            // 如果调用失败，执行默认退出全屏
            if (document.exitFullscreen) {
              document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
              document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
              document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
              document.msExitFullscreen();
            }
          }
        });

        console.log('退出全屏按钮已被拦截');
      } else {
        console.warn('未找到退出全屏按钮');
        // 重试
        setTimeout(() => {
          this.findAndInterceptExitButton();
        }, 1000);
      }
    },

    // 处理自定义全屏按钮点击
    handleCustomFullscreen() {
      console.log('处理自定义全屏按钮点击');

      try {
        // 获取当前播放时间
        let currentTime = 0;
        if (player && typeof player.getCurrentTime === 'function') {
          try {
            currentTime = player.getCurrentTime();
            console.log('获取到当前播放时间:', currentTime);
          } catch (error) {
            console.warn('获取播放时间失败:', error);
          }
        }

        // 获取VidAuth信息 - 多种方式尝试
        let vidAuthInfo = null;

        // 方式1：从全局变量获取（最可靠）
        if (window.currentVidAuthInfo) {
          vidAuthInfo = window.currentVidAuthInfo;
          console.log('从全局变量获取VidAuth信息:', vidAuthInfo);
        }

        // 方式2：从currentConfig获取
        if (!vidAuthInfo && currentConfig && currentConfig.vid && currentConfig.playauth) {
          vidAuthInfo = {
            videoId: currentConfig.vid,
            playAuth: currentConfig.playauth
          };
          console.log('从currentConfig获取VidAuth信息:', vidAuthInfo);
        }

        // 方式3：通过Vue组件调用获取
        if (!vidAuthInfo) {
          try {
            // 调用Vue组件方法获取VidAuth信息
            this.$ownerInstance.callMethod('getVidAuthInfo', (authInfo) => {
              if (authInfo && authInfo.videoId && authInfo.playAuth) {
                vidAuthInfo = authInfo;
                console.log('从Vue组件方法获取VidAuth信息:', vidAuthInfo);
                // 如果通过回调获取到了VidAuth信息，重新执行跳转
                this.executeFullscreenNavigation(vidAuthInfo, currentTime);
                return;
              }
            });
          } catch (error) {
            console.warn('从Vue组件方法获取VidAuth信息失败:', error);
          }
        }

        // 执行跳转
        this.executeFullscreenNavigation(vidAuthInfo, currentTime);
      } catch (error) {
        console.error('处理全屏点击失败:', error);
      }
    },

    // 执行全屏页面跳转
    executeFullscreenNavigation(vidAuthInfo, currentTime) {
      console.log('执行全屏页面跳转, VidAuth信息:', vidAuthInfo, '播放时间:', currentTime);

      if (!vidAuthInfo || !vidAuthInfo.videoId || !vidAuthInfo.playAuth) {
        console.error('VidAuth信息为空，无法跳转');
        if (typeof uni !== 'undefined' && uni.showToast) {
          uni.showToast({
            title: 'VidAuth信息为空，无法全屏播放',
            icon: 'none'
          });
        }
        return;
      }

      // 获取资源ID
      let resourceId = '';
      try {
        const pages = getCurrentPages();
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          resourceId = (currentPage.options && currentPage.options.id) || (currentPage.options && currentPage.options.resourceId) || '';
          console.log('renderjs: 从当前页面获取资源ID:', resourceId);
        }
      } catch (error) {
        console.warn('renderjs: 获取资源ID失败:', error);
      }

      // 构建跳转URL - 使用VidAuth参数
      let url = `/pages-subpackages/students/learning-resource/fullscreen-video`;
      const urlParams = [];

      // 添加VidAuth参数
      if (vidAuthInfo.videoId) {
        urlParams.push('videoId=' + encodeURIComponent(vidAuthInfo.videoId));
      }
      if (vidAuthInfo.playAuth) {
        urlParams.push('playAuth=' + encodeURIComponent(vidAuthInfo.playAuth));
      }

      // 添加播放时间参数
      urlParams.push('currentTime=' + currentTime);

      // 如果有资源ID，添加到参数中
      if (resourceId) {
        urlParams.push('id=' + encodeURIComponent(resourceId));
        console.log('renderjs: 添加资源ID到跳转参数:', resourceId);
      }

      // 拼接完整URL
      if (urlParams.length > 0) {
        url += '?' + urlParams.join('&');
      }

      console.log('准备跳转到:', url);

      // 使用uni.navigateTo跳转
      if (typeof uni !== 'undefined' && uni.navigateTo) {
        console.log('开始执行uni.navigateTo');
        uni.navigateTo({
          url: url,
          success: (res) => {
            console.log('成功跳转到全屏页面:', res);
          },
          fail: (error) => {
            console.error('跳转到全屏页面失败:', error);
            if (typeof uni.showToast === 'function') {
              uni.showToast({
                title: '打开全屏失败: ' + (error.errMsg || '未知错误'),
                icon: 'none'
              });
            }
          }
        });
      } else {
        console.error('uni对象不可用');
        // 备用方案：通过Vue组件调用
        try {
          console.log('尝试备用方案：调用Vue组件方法');
          this.$ownerInstance.callMethod('openFullscreenPage');
        } catch (e) {
          console.warn('备用方案也失败:', e);
        }
      }
    },

    // 添加悬浮按钮


    // 销毁播放器
    destroyPlayer() {
      // 停止进度保存定时器
      this.stopProgressSaveTimer();

      if (player) {
        try {
          player.dispose();
          player = null;
          console.log('播放器已销毁');
        } catch (error) {
          console.error('销毁播放器失败:', error);
        }
      }

      // 移除悬浮按钮
      try {
        if (typeof document !== 'undefined' && document.getElementById) {
          const existingButtons = document.getElementById('floating-buttons-app');
          if (existingButtons) {
            existingButtons.remove();
          }
        } else {
          console.warn('document.getElementById 不可用，跳过悬浮按钮移除');
        }
      } catch (error) {
        console.warn('移除悬浮按钮失败:', error);
      }
    },

    // 属性变化处理
    propChanged(newVal, oldVal, ownerInstance, instance) {
      console.log('renderjs: props changed, 新配置:', newVal);
      console.log('renderjs: old config:', oldVal);
      currentConfig = newVal;

      // 处理定位请求
      if (newVal.seekTime && newVal.seekTime.time) {
        const seekTime = newVal.seekTime.time;
        console.log('renderjs: 收到定位请求:', seekTime, '秒, 播放器状态 - 存在:', !!player, '准备好:', isPlayerReady);

        if (player && isPlayerReady) {
          // 播放器已准备好，立即执行定位
          try {
            console.log('renderjs: 播放器已准备好，立即执行定位到:', seekTime, '秒');
            player.seek(seekTime);
            console.log('renderjs: 定位成功，已跳转到:', seekTime, '秒');
            this.showSeekToast(seekTime);
          } catch (error) {
            console.warn('renderjs: 定位失败:', error);
          }
        } else {
          // 播放器还没准备好，保存待执行的定位时间
          console.log('renderjs: 播放器未准备好，保存待定位时间:', seekTime, '秒');
          pendingSeekTime = seekTime;
        }

        // 清除定位请求，避免重复执行
        delete newVal.seekTime;
      }

      if (window.Aliplayer && player) {
        // 检查是否只是尺寸变化（更严格的检测）
        const isSizeChange = oldVal &&
          (newVal.width !== oldVal.width || newVal.height !== oldVal.height);

        const isVidAuthSame = oldVal &&
          newVal.vid === oldVal.vid &&
          newVal.playauth === oldVal.playauth;
        const isAutoplaySame = oldVal && newVal.autoplay === oldVal.autoplay;
        const isMutedSame = oldVal && newVal.muted === oldVal.muted;

        if (isSizeChange && isVidAuthSame && isAutoplaySame && isMutedSame) {
          // 只是尺寸变化，使用setPlayerSize方法
          console.log('只是尺寸变化，使用setPlayerSize调整:', newVal.width, newVal.height);
          try {
            player.setPlayerSize(newVal.width, newVal.height);
            console.log('setPlayerSize调用成功');
          } catch (error) {
            console.warn('setPlayerSize失败，重新创建播放器:', error);
            this.createPlayer(newVal);
          }
        } else if (!isVidAuthSame) {
          // VidAuth参数变化，重新创建播放器
          console.log('VidAuth参数变化，重新创建播放器');
          this.createPlayer(newVal);
        } else {
          // 其他属性变化，但保持播放状态
          console.log('其他属性变化，但尝试保持播放状态');
          // 可以选择不重新创建播放器，或者只更新特定属性
        }
      } else if (window.Aliplayer) {
        // 播放器不存在，创建新播放器
        this.createPlayer(newVal);
      }
    },

    // 显示定位成功提示
    showSeekToast(time) {
      try {
        // 格式化时间显示
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        const secondsStr = seconds < 10 ? '0' + seconds : seconds.toString();
        const timeStr = minutes + ':' + secondsStr;

        // 创建提示元素
        const toast = document.createElement('div');
        toast.textContent = '已定位到上次播放位置 ' + timeStr;
        toast.style.cssText =
          'position: fixed;' +
          'top: 50%;' +
          'left: 50%;' +
          'transform: translate(-50%, -50%);' +
          'background: rgba(0, 0, 0, 0.8);' +
          'color: white;' +
          'padding: 12px 20px;' +
          'border-radius: 6px;' +
          'font-size: 14px;' +
          'z-index: 9999;' +
          'pointer-events: none;';

        document.body.appendChild(toast);

        // 2秒后移除提示
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
        }, 2000);

        console.log('已显示定位提示:', timeStr);
      } catch (error) {
        console.warn('显示定位提示失败:', error);
      }
    },

    // 播放控制方法
    play() {
      if (player) {
        player.play();
      }
    },

    pause() {
      if (player) {
        player.pause();
      }
    },

    seek(time) {
      if (player) {
        player.seek(time);
      }
    },

    setVolume(volume) {
      if (player) {
        player.setVolume(volume);
      }
    },

    getCurrentTime() {
      return player ? player.getCurrentTime() : 0;
    },

    getDuration() {
      return player ? player.getDuration() : 0;
    }
  },

  beforeDestroy() {
    // 停止进度保存定时器
    this.stopProgressSaveTimer();
    this.destroyPlayer();
  }
}
</script>
