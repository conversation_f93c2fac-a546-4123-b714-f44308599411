/**
 * 阿里云播放器组件类型定义
 * 基于官方API文档: https://www.alibabacloud.com/help/zh/vod/developer-reference/api-operations
 */

// 播放配置接口
export interface PlayConfig {
  PlayDomain?: string;
  PreviewTime?: string;
  MtsHlsUriToken?: string;
}

// 自动播放策略配置
export interface AutoplayPolicy {
  fallbackToMute?: boolean; // 有声自动播放失败后，是否降级为静音自动播放
  showUnmuteBtn?: boolean; // 静音自动播放时，是否居中显示静音大按钮
}

// 皮肤布局项
export interface SkinLayoutItem {
  name: string;
  align?: string;
  x?: number;
  y?: number;
  children?: SkinLayoutItem[];
}

// 截图水印配置
export interface SnapshotWatermark {
  left?: number;
  top?: number;
  text?: string;
  font?: string;
}

// HLS选项配置
export interface HlsOption {
  maxLiveSyncPlaybackRate?: number; // HLS直播模式下，设置直播追帧时的播放速度
  liveSyncDurationCount?: number; // 设置触发追帧的延迟切片个数
}

// 字幕轨道配置
export interface TextTrack {
  kind: 'subtitles' | 'captions';
  label: string;
  src: string;
  srclang: string;
  default?: boolean;
}

// 倍速级别配置
export interface SpeedLevel {
  key: number;
  text: string;
}

// Logo配置
export interface LogoConfig {
  src: string;
  width?: number;
  height?: number;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  origin?: 'box' | 'content';
  offsetX?: number;
  offsetY?: number;
}

// 水印配置
export interface WatermarkConfig {
  id: string;
  url: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

// 进度条标记
export interface ProgressMarker {
  time: number;
  text: string;
}

// License配置
export interface LicenseConfig {
  domain: string;
  key: string;
}

// 扩展语言文本配置
export interface ExtLanguageTexts {
  [language: string]: {
    [key: string]: string;
  };
}

// 播放器状态接口
export interface PlayerStatus {
  duration: number;
  currentTime: number;
  buffered: number;
  volume: number;
  muted: boolean;
  ended: boolean;
  paused: boolean;
  seeking: boolean;
  networkState: number;
  readyState: number;
}

// 截图结果接口
export interface SnapshotResult {
  time: number;
  base64: string;
  binary: string;
  error: Error | null;
}

// 阿里云播放器选项配置
export interface AliplayerOptions {
  // === 基础配置 ===
  id: string | HTMLElement; // 播放器外层容器的DOM元素ID
  width?: string | number; // 播放器宽度
  height?: string | number; // 播放器高度

  // === 播放源配置 ===
  source?: string; // URL播放方式
  vid?: string; // 媒体转码服务的媒体ID
  playauth?: string; // 播放凭证
  customVodServer?: string; // 自定义点播代理域名

  // === 播放配置 ===
  playConfig?: PlayConfig; // 自定义设置字段
  authTimeout?: number; // 播放URL有效时长，默认7200秒

  // === 视频尺寸 ===
  autoSize?: boolean | 'height' | 'width'; // 播放器尺寸自动适配
  videoWidth?: string; // 视频宽度
  videoHeight?: string; // 视频高度

  // === 播放行为 ===
  preload?: boolean | string; // 播放器自动加载
  cover?: string; // 播放器默认封面图片
  isLive?: boolean; // 播放内容是否为直播
  autoplay?: boolean; // 播放器是否自动播放
  autoplayPolicy?: AutoplayPolicy; // 自适应静音自动播放策略
  rePlay?: boolean; // 播放器自动循环播放
  useH5Prism?: boolean; // 指定使用H5播放器
  playsinline?: boolean; // H5是否内置播放

  // === 播放器外观 ===
  skinRes?: string; // 皮肤图片
  skinLayout?: SkinLayoutItem[] | boolean; // 功能组件布局配置
  skinLayoutIgnore?: string[]; // 需要隐藏的UI组件
  controlBarVisibility?: 'click' | 'hover' | 'always' | 'never'; // 控制面板显示方式
  showBarTime?: number; // 控制栏自动隐藏时间
  enableSystemMenu?: boolean; // 是否允许系统右键菜单显示

  // === 播放格式和质量 ===
  format?: string; // 指定播放地址格式: mp4, hls, m3u8, flv, mp3
  mediaType?: 'video' | 'audio'; // 指定返回音频还是视频
  qualitySort?: 'desc' | 'asc'; // 排序方式
  definition?: string; // 显示视频清晰度: FD,LD,SD,HD,OD,2K,4K
  defaultDefinition?: string; // 默认视频清晰度

  // === 播放控制 ===
  autoPlayDelay?: number; // 延迟播放时间
  language?: string; // 国际化，默认zh-cn
  languageTexts?: { [key: string]: { [key: string]: string } }; // 自定义国际化文本

  // === 截图配置 ===
  snapshotWatermark?: SnapshotWatermark; // H5设置截图水印

  // === Safari和FLV特殊配置 ===
  useHlsPluginForSafari?: boolean; // Safari浏览器是否启用HLS插件播放
  enableStashBufferForFlv?: boolean; // H5播放FLV时，设置是否启用播放缓存
  stashInitialSizeForFlv?: number; // H5播放FLV时，初始缓存大小

  // === 超时和重试配置 ===
  loadDataTimeout?: number; // 缓冲超时时间，默认20秒
  waitingTimeout?: number; // 最大缓冲超时时间，默认60秒
  diagnosisButtonVisible?: boolean; // 是否显示检测按钮
  disableSeek?: boolean; // 禁用进度条的Seek
  vodRetry?: number; // 点播失败重试次数，默认3次
  liveRetry?: number; // 直播播放失败重试次数，默认5次

  // === 加密配置 ===
  encryptType?: 0 | 1; // 是否播放阿里云视频加密视频

  // === 进度条和追帧配置 ===
  progressMarkers?: ProgressMarker[]; // 进度条打点内容数组
  hlsFrameChasing?: boolean; // HLS直播模式下，是否开启追帧（2.21.0以下版本）
  chasingFirstParagraph?: number; // 第一段追帧时间（2.21.0以下版本）
  chasingSecondParagraph?: number; // 第二段追帧时间（2.21.0以下版本）
  chasingFirstSpeed?: number; // 第一段追帧的倍速（2.21.0以下版本）
  chasingSecondSpeed?: number; // 第二段追帧的倍速（2.21.0以下版本）
  hlsOption?: HlsOption; // HLS选项配置（2.21.0及以上版本）
  flvFrameChasing?: boolean; // FLV直播模式下，是否开启追帧

  // === 快捷键配置 ===
  keyShortCuts?: boolean; // 是否启用快捷键
  keyFastForwardStep?: number; // 快进快退的时间长度，默认10秒

  // === RTS配置 ===
  rtsFallback?: boolean; // RTS降级配置
  rtsFallbackType?: 'HLS' | 'FLV'; // 指定RTS降级到的协议
  rtsFallbackSource?: string; // 指定固定的拉流地址进行降级

  // === 埋点和追踪 ===
  traceId?: string; // 用户唯一标识符

  // === 字幕配置 ===
  textTracks?: TextTrack[]; // 设置WebVTT外挂字幕

  // === 比例和缩放 ===
  ratio?: number; // 设置播放器按照固定比例缩放

  // === 自定义配置 ===
  extLanguageTexts?: ExtLanguageTexts; // 自定义界面显示文案
  speedLevels?: SpeedLevel[]; // 设置自定义倍速列表数组
  logo?: LogoConfig[]; // 设置自定义Logo图片

  // === License配置 ===
  license?: LicenseConfig; // License授权配置

  // === 其他配置 ===
  mute?: boolean; // 设置是否静音播放
  clickPause?: boolean; // 点击视频画面进行暂停或播放
  disablePip?: boolean; // 隐藏浏览器自带的画中画按钮
  env?: 'SEA'; // 海外数据合规，数据上传到新加坡数据中心

  // === 时间范围配置 ===
  watchStartTime?: number; // 开始播放的时间
  watchEndTime?: number; // 结束播放的时间
  start?: number; // 截取视频开始时间
  end?: number; // 截取视频结束时间

  // === 移动端配置 ===
  dbClickFullscreen?: boolean; // 是否开启双击全屏
  longPressFastForward?: boolean; // 长按倍速功能（仅支持移动端）
  dbClickSkip?: boolean; // 双击左侧区域快退、双击右侧区域快进功能（仅支持移动端）

  // === 水印配置 ===
  watermark?: WatermarkConfig[]; // 水印配置
}

// 播放器事件类型
export type PlayerEvent =
  | 'ready'
  | 'play'
  | 'pause'
  | 'canplay'
  | 'playing'
  | 'ended'
  | 'liveStreamStop'
  | 'onM3u8Retry'
  | 'hideBar'
  | 'showBar'
  | 'waiting'
  | 'timeupdate'
  | 'snapshoted'
  | 'requestFullScreen'
  | 'cancelFullScreen'
  | 'error'
  | 'startSeek'
  | 'completeSeek'
  | 'resolutionChange'
  | 'seiFrame'
  | 'rtsFallback'
  | 'settingSelected'
  | 'rtsTraceId'
  | 'autoplay'
  | 'mutedAutoplay'
  | 'videoUnavailable';

// 阿里云播放器类接口
export interface AliplayerInstance {
  // 播放控制方法
  play(): void;
  pause(showPlayButton?: boolean): AliplayerInstance;
  replay(): AliplayerInstance;
  seek(time: number): AliplayerInstance;

  // 音量控制
  setVolume(volume: number): void;
  getVolume(): number | undefined;
  mute(quiet?: boolean): AliplayerInstance;
  unMute(quiet?: boolean): AliplayerInstance;

  // 播放状态
  getCurrentTime(): number;
  getDuration(): number;
  getPlayTime(): number;
  getStatus(): string;

  // 视频切换
  loadByUrl(url: string, seconds?: number, autoPlay?: boolean): void;
  replayByVidAndPlayAuth(vid: string, playauth: string): void;
  replayByVidAndAuthInfo(
    vid: string,
    accId: string,
    accSecret: string,
    stsToken: string,
    authInfo: string,
    domainRegion: string
  ): void;
  replayByMediaAuth(mediaAuth: string): void;

  // 播放器控制
  setPlayerSize(width: string, height: string): void;
  setSpeed(speed: number): void;
  setTraceId(traceId: string): void;
  setSanpshotProperties(width: number, height: number, rate: number): void;

  // 全屏控制
  fullscreenService: {
    requestFullScreen(): AliplayerInstance;
    cancelFullScreen(): AliplayerInstance;
    getIsFullScreen(): boolean;
  };

  // 直播时移
  liveShiftSerivce: {
    setLiveTimeRange(start: string, end: string): void;
  };

  // 显示控制
  setRotate(rotate: number): void;
  getRotate(): number;
  setImage(type: 'horizon' | 'vertical'): void;
  setCover(coverUrl: string): void;

  // 进度和标记
  setProgressMarkers(markers: ProgressMarker[]): void;
  setPreviewTime(time: number): void;
  getPreviewTime(): number;
  isPreview(): boolean;

  // HLS特殊功能
  getCurrentPDT(): number | undefined;

  // 字幕和Logo
  setTextTracks(textTracks: TextTrack[]): void;
  setLogo(logoList: LogoConfig[]): void;

  // 时间范围设置
  setWatchTime(start: number, end: number): void;
  setNextWatchTime(start: number, end: number): void;
  setStartEnd(start: number, end: number): void;
  setNextStartEnd(start: number, end: number): void;

  // 截图功能
  takeSnapshot(): SnapshotResult;

  // 事件监听
  on(event: string, callback: (data?: any) => void): void;
  off(event: string, callback?: (data?: any) => void): void;
  one(event: string, callback: (data?: any) => void): void;

  // 销毁播放器
  dispose(): void;
}

// 阿里云播放器构造函数类型
export interface AliplayerConstructor {
  new (
    options: AliplayerOptions,
    callback?: (player: AliplayerInstance) => void
  ): AliplayerInstance;
}

// 全局类型声明
declare global {
  interface Window {
    Aliplayer: AliplayerConstructor;
  }
}

// 播放器配置接口
export interface AliPlayerConfig {
  id: string;
  source: string;
  width?: string | number;
  height?: string | number;
  autoplay?: boolean;
  muted?: boolean;
  controlBarVisibility?: 'hover' | 'click' | 'always' | 'never';
  autoSize?: boolean;
  cover?: string;
  loop?: boolean;
  skinLayout?: any[];
  preload?: boolean | string;
  language?: string;
  rePlay?: boolean;
  playsinline?: boolean;
  x5_type?: string;
  x5_fullscreen?: boolean;
  [key: string]: any;
}

// 播放器实例接口
export interface AliPlayerInstance {
  play(): void;
  pause(): void;
  seek(time: number): void;
  getCurrentTime(): number;
  getDuration(): number;
  setVolume(volume: number): void;
  getVolume(): number;
  loadByUrl(url: string): void;
  dispose(): void;
  on(event: string, callback: Function): void;
  off(event: string, callback?: Function): void;
  getStatus(): string;
  getOptions(): any;
  setPlayerSize(width: number | string, height: number | string): void;
  fullscreenService: {
    requestFullScreen(): void;
    cancelFullScreen(): void;
    getIsFullScreen(): boolean;
  };
}

// 播放器事件类型
export type PlayerEventType =
  | 'ready'
  | 'play'
  | 'pause'
  | 'ended'
  | 'error'
  | 'timeupdate'
  | 'loadedmetadata'
  | 'loadstart'
  | 'loadeddata'
  | 'canplay'
  | 'canplaythrough'
  | 'seeking'
  | 'seeked'
  | 'volumechange'
  | 'ratechange'
  | 'fullscreenchange'
  | 'waiting'
  | 'stalled'
  | 'progress';

// 播放器状态字符串类型
export type PlayerStatusString =
  | 'init'
  | 'ready'
  | 'loading'
  | 'play'
  | 'pause'
  | 'ended'
  | 'error';

// 错误信息接口
export interface PlayerError {
  code: number;
  message: string;
  details?: any;
}

// 视频质量选项
export interface VideoQuality {
  definition: string;
  url: string;
  [key: string]: any;
}

// 播放器组件 Props
export interface VideoPlayerProps {
  source: string;
  width?: string | number;
  height?: string | number;
  autoplay?: boolean;
  muted?: boolean;
  controlBarVisibility?: 'hover' | 'click' | 'always';
  autoSize?: boolean;
  cover?: string;
  loop?: boolean;
  skinLayout?: any[];
  preload?: boolean;
  language?: string;
  customConfig?: Record<string, any>;
}

// 播放器组件 Emits
export interface VideoPlayerEmits {
  (e: 'ready'): void;
  (e: 'play'): void;
  (e: 'pause'): void;
  (e: 'ended'): void;
  (e: 'error', error: PlayerError): void;
  (e: 'timeupdate', currentTime: number): void;
  (e: 'loadedmetadata', duration: number): void;
  (e: 'loadstart'): void;
  (e: 'loadeddata'): void;
  (e: 'canplay'): void;
  (e: 'canplaythrough'): void;
  (e: 'seeking'): void;
  (e: 'seeked'): void;
  (e: 'volumechange', volume: number): void;
  (e: 'ratechange', rate: number): void;
  (e: 'fullscreenchange', isFullscreen: boolean): void;
  (e: 'waiting'): void;
  (e: 'stalled'): void;
  (e: 'progress', buffered: number): void;
}

// 播放器组件暴露的方法
export interface VideoPlayerExpose {
  play(): void;
  pause(): void;
  stop(): void;
  seek(time: number): void;
  setVolume(volume: number): void;
  getCurrentTime(): number;
  getDuration(): number;
  destroyPlayer(): void;
  player: AliPlayerInstance | null;
}

// 常用的皮肤布局配置
export const DEFAULT_SKIN_LAYOUTS = {
  // 简洁模式
  simple: [
    { name: 'bigPlayButton', align: 'blabs', x: 30, y: 80 },
    { name: 'H5Loading', align: 'cc' },
    { name: 'errorDisplay', align: 'tlabs', x: 0, y: 0 },
    { name: 'infoDisplay' },
    {
      name: 'controlBar',
      align: 'blabs',
      x: 0,
      y: 0,
      children: [
        { name: 'progress', align: 'blabs', x: 0, y: 44 },
        { name: 'playButton', align: 'tl', x: 15, y: 12 },
        { name: 'timeDisplay', align: 'tl', x: 10, y: 7 },
        { name: 'fullScreenButton', align: 'tr', x: 10, y: 12 },
      ],
    },
  ],

  // 完整模式
  full: [
    { name: 'bigPlayButton', align: 'blabs', x: 30, y: 80 },
    { name: 'H5Loading', align: 'cc' },
    { name: 'errorDisplay', align: 'tlabs', x: 0, y: 0 },
    { name: 'infoDisplay' },
    { name: 'tooltip', align: 'blabs', x: 0, y: 56 },
    { name: 'thumbnail' },
    {
      name: 'controlBar',
      align: 'blabs',
      x: 0,
      y: 0,
      children: [
        { name: 'progress', align: 'blabs', x: 0, y: 44 },
        { name: 'playButton', align: 'tl', x: 15, y: 12 },
        { name: 'timeDisplay', align: 'tl', x: 10, y: 7 },
        { name: 'fullScreenButton', align: 'tr', x: 10, y: 12 },
        { name: 'volume', align: 'tr', x: 5, y: 10 },
        { name: 'setting', align: 'tr', x: 15, y: 12 },
      ],
    },
  ],
};

// 常用配置预设
export const PLAYER_PRESETS = {
  // 移动端配置
  mobile: {
    autoplay: false,
    muted: true,
    controlBarVisibility: 'click' as const,
    playsinline: true,
    x5_type: 'h5',
    x5_fullscreen: true,
    skinLayout: DEFAULT_SKIN_LAYOUTS.simple,
  },

  // 桌面端配置
  desktop: {
    autoplay: false,
    muted: false,
    controlBarVisibility: 'hover' as const,
    skinLayout: DEFAULT_SKIN_LAYOUTS.full,
  },

  // 自动播放配置（需要静音）
  autoplay: {
    autoplay: true,
    muted: true,
    controlBarVisibility: 'hover' as const,
  },
};
