<template>
  <uni-popup ref="popupRef" :type="type" :is-mask-click="maskClosable" background-color="#fff">
    <view class="my-popup">
      <view class="popup-header">
        <view class="header-left"></view>
        <text class="popup-title">{{ title }}</text>
        <text class="close-icon" @click="close">×</text>
      </view>

      <view class="popup-body">
        <slot></slot>
      </view>
      <!-- 如果有 footer 插槽才显示 -->
      <template v-if="$slots.footer">
        <view class="popup-footer">
          <slot name="footer"></slot>
        </view>
      </template>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 组件属性
interface Props {
  title: string;
  type?: 'top' | 'bottom' | 'center' | 'left' | 'right';
  show?: boolean;
  maskClosable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'bottom',
  show: false,
  maskClosable: true,
});

// 组件事件
const emit = defineEmits(['close']);

// 组件引用
const popupRef = ref();

// 打开弹窗
const open = () => {
  popupRef.value.open();
};

// 关闭弹窗
const close = () => {
  popupRef.value.close();
  emit('close');
};

// 向父组件暴露的方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.my-popup {
  width: 100%;
  background-color: #fff;
  border-radius: v-bind(
    "type === 'bottom' ? '20rpx 20rpx 0 0' : type === 'top' ? '0 0 20rpx 20rpx' : '20rpx'"
  );
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 30rpx;
  // border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.header-left {
  width: 60rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-body {
  padding: 20rpx 30rpx;
}

.popup-footer {
  display: flex;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  gap: 20rpx;
  border-top: 1rpx solid #e5e5e5;
}
</style>
