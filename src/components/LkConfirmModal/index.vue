<script lang="ts" setup>
import { defineOptions, ref, computed, type PropType } from 'vue';

// 预设配置
const presets = {
  delete: {
    title: '删除',
    content: '请确认是否删除',
    confirmText: '确定删除',
    cancelText: '取消',
    confirmType: 'danger' as const,
  },
  warning: {
    title: '警告',
    content: '此操作可能会产生不可逆的结果，请谨慎操作',
    confirmText: '确定',
    cancelText: '取消',
    confirmType: 'warning' as const,
  },
  info: {
    title: '提示',
    content: '请确认是否继续此操作',
    confirmText: '确定',
    cancelText: '取消',
    confirmType: 'primary' as const,
  },
  confirm: {
    title: '确认',
    content: '请确认是否继续此操作',
    confirmText: '确定',
    cancelText: '取消',
    confirmType: 'primary' as const,
  },
};

interface Props {
  show: boolean;
  type?: keyof typeof presets | 'custom';
  title?: string;
  content?: string;
  confirmText?: string;
  cancelText?: string;
  confirmType?: 'primary' | 'danger' | 'warning';
  showCancel?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'confirm',
  showCancel: true,
});

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const emit = defineEmits(['confirm', 'cancel']);
const curValue = ref<any>(null);

// 计算最终配置
const finalConfig = computed(() => {
  const preset =
    props.type !== 'custom'
      ? presets[props.type]
      : {
          title: '确认',
          content: '请确认是否继续此操作',
          confirmText: '确定',
          cancelText: '取消',
          confirmType: 'primary' as const,
        };

  return {
    title: props.title ?? preset.title,
    content: props.content ?? preset.content,
    confirmText: props.confirmText ?? preset.confirmText,
    cancelText: props.cancelText ?? preset.cancelText,
    confirmType: props.confirmType ?? preset.confirmType,
  };
});

const onOpen = (value?: any) => {
  curValue.value = value;
  show.value = true;
};

const onClose = () => {
  show.value = false;
  emit('cancel', curValue.value);
};

const onConfirm = () => {
  show.value = false;
  emit('confirm', curValue.value);
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'LkConfirmModal' });
</script>

<template>
  <up-popup
    :show="show"
    :round="10"
    mode="center"
    @close="onClose"
    v-bind="$attrs"
    customStyle="background-color: transparent;"
  >
    <view class="confirm-modal">
      <view class="confirm-modal__body flex-center">
        <LkText bold size="xlarge">{{ finalConfig.title }}</LkText>
        <LkText type="secondary" size="large" style="text-align: center">
          {{ finalConfig.content }}
        </LkText>
      </view>
      <view class="confirm-modal__footer">
        <LkButton
          v-if="showCancel"
          type="plain"
          block
          @click="onClose"
          size="large"
          style="min-width: 200rpx"
        >
          {{ finalConfig.cancelText }}
        </LkButton>
        <LkButton
          :type="finalConfig.confirmType"
          block
          @click="onConfirm"
          size="large"
          style="min-width: 200rpx"
        >
          {{ finalConfig.confirmText }}
        </LkButton>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.confirm-modal {
  background: #fff;
  border-radius: 28rpx;
  padding: 20rpx;
  padding-top: 40rpx;

  &__body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 80vw;
    gap: 20rpx;
  }

  &__footer {
    margin-top: 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 30rpx;
    padding: 20rpx;

    &-item {
      flex-direction: column;
      width: 150rpx;
      gap: 10rpx;
      padding: 20rpx;
      border-radius: 28rpx;
      background: white;
    }
  }
}
</style>
