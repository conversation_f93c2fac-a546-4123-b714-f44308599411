<template>
  <view class="dialog-provider">
    <!-- 内容插槽 -->
    <slot></slot>

    <!-- 对话框组件 -->
    <MyDialog
      ref="dialogRef"
      :type="dialogOptions.type"
      :title="dialogOptions.title"
      :content="dialogOptions.content"
      :cancelText="dialogOptions.cancelText"
      :confirmText="dialogOptions.confirmText"
      :maskClick="dialogOptions.maskClick"
      :confirmColor="dialogOptions.confirmColor"
      :cancelColor="dialogOptions.cancelColor"
      :width="dialogOptions.width"
      :height="dialogOptions.height"
      :visible="isVisible"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      @update:visible="updateVisible"
      @close="handleClose"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, provide, onUnmounted } from 'vue';
import {
  DIALOG_INJECTION_KEY,
  type DialogOptions,
  type ConfirmDialogOptions,
  type InfoDialogOptions,
  type DangerDialogOptions,
} from './types';
import { setGlobalDialogAPI, removeGlobalDialogAPI } from './dialog-utils';
import MyDialog from './MyDialog.vue';

// 定义 Provider 的配置属性
interface ProviderConfig {
  // 默认对话框配置
  defaultOptions?: Partial<DialogOptions>;
}

// 定义 props
const props = withDefaults(defineProps<ProviderConfig>(), {
  defaultOptions: () => ({}),
});

// 对话框引用
const dialogRef = ref<InstanceType<typeof MyDialog> | null>(null);
// 是否可见
const isVisible = ref(false);
// API 实例的唯一标识符
let apiId: string;
// 当前对话框配置
const dialogOptions = reactive<DialogOptions>({
  type: 'info',
  title: '提示',
  content: '',
  cancelText: '取消',
  confirmText: '确定',
  maskClick: false,
  confirmColor: '',
  cancelColor: '',
  width: '',
  height: '',
  ...props.defaultOptions, // 合并默认配置
});

// 处理确认和取消的Promise回调
let resolvePromise: ((value: boolean) => void) | null = null;

// 更新可见状态
const updateVisible = (val: boolean) => {
  isVisible.value = val;
};

// 打开对话框
const showDialog = (options: DialogOptions): Promise<boolean> => {
  try {
    // 先关闭当前弹框并resolve前一个Promise（如果有）
    closeDialog();

    // 重置Promise回调
    resolvePromise = null;

    // 重置默认值（包含 Provider 的默认配置）
    const defaultConfig = {
      type: 'info',
      title: '提示',
      content: '',
      cancelText: '取消',
      confirmText: '确定',
      maskClick: false,
      confirmColor: '',
      cancelColor: '',
      width: '',
      height: '',
      ...props.defaultOptions, // 合并 Provider 默认配置
    };

    // 应用默认配置
    Object.assign(dialogOptions, defaultConfig);
    dialogOptions.onConfirm = undefined;
    dialogOptions.onClose = undefined;

    // 应用新选项（使用安全的方式）
    if (options) {
      if (options.type) dialogOptions.type = options.type;
      if (options.title !== undefined) dialogOptions.title = options.title;
      if (options.content) dialogOptions.content = options.content;
      if (options.cancelText !== undefined) dialogOptions.cancelText = options.cancelText;
      if (options.confirmText !== undefined) dialogOptions.confirmText = options.confirmText;
      if (options.maskClick !== undefined) dialogOptions.maskClick = options.maskClick;
      if (options.confirmColor !== undefined) dialogOptions.confirmColor = options.confirmColor;
      if (options.cancelColor !== undefined) dialogOptions.cancelColor = options.cancelColor;
      if (options.width !== undefined) dialogOptions.width = options.width;
      if (options.height !== undefined) dialogOptions.height = options.height;
      if (typeof options.onConfirm === 'function') dialogOptions.onConfirm = options.onConfirm;
      if (typeof options.onClose === 'function') dialogOptions.onClose = options.onClose;
    }

    // 延迟一下再显示对话框，确保之前的对话框已完全关闭
    return new Promise<boolean>(resolve => {
      setTimeout(() => {
        isVisible.value = true;
        resolvePromise = resolve;
      }, 10); // 延迟可适当缩短
    });
  } catch (error) {
    console.error('打开对话框出错:', error);
    return Promise.resolve(false);
  }
};

// 显示确认对话框（双按钮）
const showDialogConfirm = (options: ConfirmDialogOptions): Promise<boolean> => {
  return showDialog({
    type: 'confirm',
    ...options,
  });
};

// 显示危险操作对话框（双按钮红色确认）
const showDialogDanger = (options: DangerDialogOptions): Promise<boolean> => {
  return showDialog({
    type: 'danger',
    ...options,
  });
};

// 显示信息对话框（单按钮）
const showDialogInfo = (options: InfoDialogOptions): Promise<boolean> => {
  return showDialog({
    type: 'info',
    ...options,
  });
};

// 处理取消
const handleCancel = () => {
  // 简化处理，直接解析Promise
  const currentResolve = resolvePromise;
  resolvePromise = null;

  if (dialogOptions.onClose && typeof dialogOptions.onClose === 'function') {
    try {
      dialogOptions.onClose();
    } catch (error) {
      console.error('关闭回调执行错误:', error);
    }
  }

  // 重置回调函数
  dialogOptions.onConfirm = undefined;
  dialogOptions.onClose = undefined;

  // 解析Promise
  if (currentResolve) {
    currentResolve(false);
  }
};

// 处理确认
const handleConfirm = () => {
  //console.log('handleConfirm 被调用');

  // 简化处理，直接解析Promise
  const currentResolve = resolvePromise;
  resolvePromise = null;

  if (dialogOptions.onConfirm && typeof dialogOptions.onConfirm === 'function') {
    //console.log('执行 onConfirm 回调');
    try {
      dialogOptions.onConfirm();
      //console.log('onConfirm 回调执行完成');
    } catch (error) {
      console.error('确认回调执行错误:', error);
    }
  }

  // 确认时也要执行 onClose 回调
  if (dialogOptions.onClose && typeof dialogOptions.onClose === 'function') {
    //console.log('执行 onClose 回调');
    try {
      dialogOptions.onClose();
      //console.log('onClose 回调执行完成');
    } catch (error) {
      console.error('关闭回调执行错误:', error);
    }
  }

  // 重置回调函数
  dialogOptions.onConfirm = undefined;
  dialogOptions.onClose = undefined;

  // 解析Promise
  if (currentResolve) {
    currentResolve(true);
  }
};

// 处理关闭
const handleClose = () => {
  //console.log('handleClose 被调用');

  // 简化处理，直接解析Promise
  const currentResolve = resolvePromise;
  resolvePromise = null;

  if (dialogOptions.onClose && typeof dialogOptions.onClose === 'function') {
    //console.log('执行 onClose 回调');
    try {
      dialogOptions.onClose();
      //console.log('onClose 回调执行完成');
    } catch (error) {
      console.error('关闭回调执行错误:', error);
    }
  } else {
    //console.log('没有 onClose 回调函数');
  }

  // 重置回调函数
  dialogOptions.onConfirm = undefined;
  dialogOptions.onClose = undefined;

  // 解析Promise
  if (currentResolve) {
    currentResolve(false);
  }
};

// 关闭对话框
const closeDialog = () => {
  // 更新状态
  isVisible.value = false;

  // 简化处理，直接解析Promise
  const currentResolve = resolvePromise;
  resolvePromise = null;

  // 重置回调函数
  dialogOptions.onConfirm = undefined;
  dialogOptions.onClose = undefined;

  // 解析Promise
  if (currentResolve) {
    currentResolve(false);
  }
};

// 创建对话框API对象
const dialogAPI = {
  showDialog,
  showDialogConfirm,
  showDialogDanger,
  showDialogInfo,
  closeDialog,
};

// 提供对话框API
provide(DIALOG_INJECTION_KEY, dialogAPI);

// 设置全局对话框API，并保存返回的ID
apiId = setGlobalDialogAPI(dialogAPI);

// 组件卸载时清理全局API
onUnmounted(() => {
  if (apiId) {
    removeGlobalDialogAPI(apiId);
  }
});
</script>

<style scoped>
.dialog-provider {
  width: 100%;
  height: 100%;
}
</style>
