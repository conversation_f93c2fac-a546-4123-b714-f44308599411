import type { InjectionKey } from 'vue';

// 对话框配置选项
export interface DialogOptions {
  // 对话框类型: confirm-双按钮, info-信息框(单按钮), danger-危险操作(双按钮红色确认)
  type?: 'confirm' | 'info' | 'danger';
  // 标题
  title?: string;
  // 内容
  content: string;
  // 取消按钮文本（仅在confirm/danger类型时有效）
  cancelText?: string;
  // 确认按钮文本
  confirmText?: string;
  // 点击蒙层是否关闭
  maskClick?: boolean;
  // 确认按钮颜色
  confirmColor?: string;
  // 取消按钮颜色
  cancelColor?: string;
  // 弹框宽度 (支持 rpx, px, vw, % 等单位)
  width?: string;
  // 弹框高度 (支持 rpx, px, vh, % 等单位)
  height?: string;
  // 确认按钮点击回调
  onConfirm?: () => void;
  // 关闭回调（取消按钮点击或关闭弹框）
  onClose?: () => void;
}

// 确认对话框选项（双按钮）
export interface ConfirmDialogOptions extends Omit<DialogOptions, 'content' | 'type'> {
  // 对话框内容
  content: string;
  // 标题
  title?: string;
}

// 危险操作对话框选项（双按钮红色确认）
export interface DangerDialogOptions extends Omit<DialogOptions, 'content' | 'type'> {
  // 对话框内容
  content: string;
  // 标题
  title?: string;
}

// 信息对话框选项（单按钮）
export interface InfoDialogOptions extends Omit<DialogOptions, 'content' | 'type' | 'cancelText'> {
  // 对话框内容
  content: string;
  // 标题
  title?: string;
  // 确认按钮文本
  confirmText?: string;
}

// 对话框API接口
export interface DialogAPI {
  showDialog: (options: DialogOptions) => Promise<boolean>;
  showDialogConfirm: (options: ConfirmDialogOptions) => Promise<boolean>;
  showDialogDanger: (options: DangerDialogOptions) => Promise<boolean>;
  showDialogInfo: (options: InfoDialogOptions) => Promise<boolean>;
  closeDialog: () => void;
}

// 依赖注入的Key
export const DIALOG_INJECTION_KEY: InjectionKey<DialogAPI> = Symbol('dialog');
