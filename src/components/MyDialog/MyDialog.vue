<template>
  <uni-popup ref="popupRef" type="center" :mask-click="maskClick" :animation="true">
    <view class="my-dialog-container">
      <view class="my-dialog" :class="[`type-${type}`]" :style="dialogStyle">
        <view class="my-dialog-title">{{ title }}</view>
        <view class="my-dialog-content">{{ content }}</view>
        <view class="my-dialog-footer" :class="{ 'single-btn': type === 'info' }">
          <button
            v-if="type === 'confirm' || type === 'danger'"
            class="my-dialog-btn cancel-btn"
            hover-class="button-hover"
            :style="cancelBtnStyle"
            @click="handleCancel"
          >
            {{ cancelText }}
          </button>
          <button
            class="my-dialog-btn confirm-btn"
            hover-class="button-hover"
            :class="{ 'danger-btn': type === 'danger' }"
            :style="confirmBtnStyle"
            @click="handleConfirm"
          >
            {{ confirmText }}
          </button>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';

// 定义组件属性
interface Props {
  // 对话框类型: confirm-双按钮, info-信息框(单按钮), danger-危险操作(双按钮红色确认)
  type?: 'info' | 'confirm' | 'danger';
  // 标题
  title?: string;
  // 内容
  content: string;
  // 取消按钮文本
  cancelText?: string;
  // 确认按钮文本
  confirmText?: string;
  // 点击蒙层是否关闭
  maskClick?: boolean;
  // 是否显示
  visible?: boolean;
  // 确认按钮颜色
  confirmColor?: string;
  // 取消按钮颜色
  cancelColor?: string;
  // 弹框宽度
  width?: string;
  // 弹框高度
  height?: string;
}

// 定义事件
const emit = defineEmits(['cancel', 'confirm', 'update:visible', 'close']);

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  title: '提示',
  cancelText: '取消',
  confirmText: '确定',
  maskClick: false,
  visible: false,
  confirmColor: '',
  cancelColor: '',
  width: '',
  height: '',
});

// popup引用
const popupRef = ref<any>(null);

// 计算按钮样式
const confirmBtnStyle = computed(() => {
  if (props.confirmColor) {
    return {
      backgroundColor: props.type === 'danger' ? '#D54941' : props.confirmColor,
      color: '#FFFFFF',
    };
  }
  return {};
});

const cancelBtnStyle = computed(() => {
  if (props.cancelColor) {
    return {
      backgroundColor: props.cancelColor,
      color: '#FFFFFF',
    };
  }
  return {};
});

// 计算容器样式
const dialogStyle = computed(() => {
  const style: Record<string, any> = {};
  if (props.width) {
    style.width = props.width;
  }
  if (props.height) {
    style.height = props.height;
  }
  return style;
});

// 监听visible变化，控制弹窗显示与隐藏
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      open();
    } else if (popupRef.value) {
      close();
    }
  },
  { immediate: true }
);

// 打开对话框
function open() {
  if (popupRef.value) {
    popupRef.value.open('center');
  }
}

// 关闭对话框
function close() {
  if (!popupRef.value) return;

  try {
    // 先更新visible状态
    emit('update:visible', false);

    // 发送关闭事件
    emit('close');

    // 尝试关闭弹窗
    try {
      popupRef.value.close();
    } catch (closeError) {
      console.error('关闭popup出错:', closeError);
    }
  } catch (error) {
    console.error('关闭对话框时出错:', error);
  }
}

// 处理取消事件
function handleCancel() {
  // 简化处理流程，只发送取消事件
  emit('cancel');

  // 仅在明确不会出错的情况下执行
  if (popupRef.value) {
    // 更新visible状态
    emit('update:visible', false);
    // 尝试关闭弹窗
    popupRef.value.close();
  }

  // 发送关闭事件
  emit('close');
}

// 处理确认事件
function handleConfirm() {
  try {
    // 简化处理流程，只发送确认事件
    emit('confirm');

    // 仅在明确不会出错的情况下执行
    if (popupRef.value) {
      // 更新visible状态
      emit('update:visible', false);
      // 尝试关闭弹窗
      popupRef.value.close();
    }

    // 发送关闭事件
    emit('close');
  } catch (error) {
    console.error('确认处理出错:', error);
  }
}

// 对外暴露方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.my-dialog-container {
  padding: 60rpx 48rpx;
}

.my-dialog {
  width: 80vw;
  height: auto;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-sizing: border-box;
  overflow: hidden;
}

/* 重置按钮样式 */
.my-dialog button {
  margin: 0;
  padding: 0;

  &::after {
    display: none !important;
    border: none !important;
  }
}

.my-dialog-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
  margin-bottom: 20rpx;
}

.my-dialog-content {
  font-size: 32rpx;
  color: #4e5969;
  text-align: center;
  line-height: 1.6;
  word-break: break-all;
  padding: 20rpx 0 30rpx;
}

.my-dialog-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;

  &.single-btn {
    justify-content: center;

    .confirm-btn {
      width: 100%;
    }
  }
}

.my-dialog-btn {
  flex: 1;
  height: 80rpx;
  font-size: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &.cancel-btn {
    margin-right: 20rpx;
    background-color: #f3ecff;
    color: #7d4dff;
    border: none;
    outline: none;
  }

  &.confirm-btn {
    background-color: #7d4dff;
    color: #ffffff;
    border: none;
  }

  &.danger-btn {
    background-color: #d54941;
    color: #ffffff;
  }
}

// 信息框类型(单按钮)
.type-info {
  .my-dialog-footer {
    justify-content: center;
  }
}

// 按钮按下效果
.button-hover {
  opacity: 0.8;
}
</style>
