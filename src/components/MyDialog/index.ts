import MyDialog from './MyDialog.vue';
import MyDialogProvider from './MyDialogProvider.vue';
import {
  showDialog,
  showDialogConfirm,
  showDialogDanger,
  showDialogInfo,
  closeDialog,
} from './dialog-utils';
import {
  DIALOG_INJECTION_KEY,
  type DialogAPI,
  type DialogOptions,
  type ConfirmDialogOptions,
  type DangerDialogOptions,
  type InfoDialogOptions,
} from './types';

// 命名导出组件
export { MyDialog, MyDialogProvider, DIALOG_INJECTION_KEY };

// 解构导出部分工具函数
export { showDialog, showDialogConfirm, showDialogDanger, showDialogInfo, closeDialog };

// 导出类型
export type {
  DialogAPI,
  DialogOptions,
  ConfirmDialogOptions,
  DangerDialogOptions,
  InfoDialogOptions,
};
