# MyDialog 组件使用说明

基于uni-popup封装的对话框组件，通过依赖注入方式实现跨组件调用。

## 特性

- 三种对话框类型：确认框(双按钮)、危险操作框(双按钮红色确认)、信息框(单按钮)
- 使用依赖注入机制，方便子组件调用
- Promise API，支持异步等待用户操作结果
- 自定义配置：标题、内容、按钮文字、按钮颜色等
- 支持确认按钮点击回调和关闭回调

## ⚠️ 重要提示

**必须在父级页面套入 MyDialogProvider 组件**，其子组件才能调用对话框函数。对话框的依赖注入机制要求必须有一个祖先组件提供API服务。

如果在没有 MyDialogProvider 包裹的组件中调用对话框函数，将会抛出错误：`未找到对话框API，请确保在setup中使用，或已通过setGlobalDialogAPI设置全局API，或在MyDialogProvider内部使用`

## 安装与使用

### 1. 在父级页面添加 MyDialogProvider

```vue
<template>
  <MyDialogProvider :default-options="dialogConfig">
    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 子组件 -->
      <child-component></child-component>
    </view>
  </MyDialogProvider>
</template>

<script setup>
import { MyDialogProvider } from '@/components/MyDialog';

// 配置默认的对话框选项
const dialogConfig = {
  width: '600rpx',
  height: '400rpx',
  confirmColor: '#7d4dff',
  cancelColor: '#f3ecff',
  maskClick: true,
};
</script>
```

### 1.1 Provider 配置选项

MyDialogProvider 支持通过 `defaultOptions` 属性设置默认配置：

```typescript
interface ProviderConfig {
  // 默认对话框配置
  defaultOptions?: Partial<DialogOptions>;
}
```

**可配置的默认选项：**

- `type`: 对话框类型 ('info' | 'confirm' | 'danger')
- `title`: 默认标题
- `content`: 默认内容
- `cancelText`: 取消按钮文本
- `confirmText`: 确认按钮文本
- `maskClick`: 点击蒙层是否关闭
- `confirmColor`: 确认按钮颜色
- `cancelColor`: 取消按钮颜色
- `width`: 弹框宽度
- `height`: 弹框高度

**配置示例：**

```vue
<template>
  <MyDialogProvider
    :default-options="{
      width: '80vw',
      height: '50vh',
      confirmColor: '#1890ff',
      cancelColor: '#f5f5f5',
      maskClick: true,
      title: '系统提示',
    }"
  >
    <!-- 页面内容 -->
  </MyDialogProvider>
</template>
```

### 2. 在子组件中使用对话框

```vue
<template>
  <view>
    <button @click="showConfirmDialog">显示确认对话框</button>
  </view>
</template>

<script setup>
import { showDialogConfirm } from '@/components/MyDialog';

// 显示确认对话框
const showConfirmDialog = async () => {
  try {
    const result = await showDialogConfirm({
      content: '确定要提交作业吗?',
      title: '提示',
    });

    if (result) {
      // 用户点击了确认按钮
      console.log('用户确认了操作');
    } else {
      // 用户点击了取消按钮
      console.log('用户取消了操作');
    }
  } catch (error) {
    console.error('对话框操作异常', error);
  }
};
</script>
```

## 使用场景示例

### 场景1：单个页面使用

在每个需要使用对话框的页面组件中添加 MyDialogProvider：

```vue
<!-- pages/some-page/index.vue -->
<template>
  <MyDialogProvider>
    <view class="page-content">
      <!-- 页面内容 -->
      <button @click="showDialog">显示对话框</button>
    </view>
  </MyDialogProvider>
</template>

<script setup>
import { MyDialogProvider, showDialog } from '@/components/MyDialog';

const showDialog = () => {
  showDialog({
    title: '提示',
    content: '这是一个对话框',
    confirmText: '确定',
  });
};
</script>
```

### 场景2：多页面共用

如果需要在多个子组件中使用对话框，可以在它们的共同父组件中添加 MyDialogProvider：

```vue
<!-- 父组件 -->
<template>
  <MyDialogProvider>
    <child-a></child-a>
    <child-b></child-b>
    <child-c></child-c>
  </MyDialogProvider>
</template>

<!-- 子组件A -->
<script setup>
import { showDialogConfirm } from '@/components/MyDialog';

const confirm = async () => {
  const result = await showDialogConfirm({
    content: '确认操作?',
  });
  // 处理结果
};
</script>
```

## API 参考

### 组件

| 组件名           | 描述                                         |
| ---------------- | -------------------------------------------- |
| MyDialog         | 基础对话框组件                               |
| MyDialogProvider | 对话框提供者，必须包裹在使用对话框的组件外层 |

### 方法

| 方法名            | 说明                               | 参数                          | 返回值             |
| ----------------- | ---------------------------------- | ----------------------------- | ------------------ |
| showDialog        | 显示自定义对话框                   | options: DialogOptions        | Promise\<boolean\> |
| showDialogConfirm | 显示确认对话框(双按钮)             | options: ConfirmDialogOptions | Promise\<boolean\> |
| showDialogDanger  | 显示危险操作对话框(双按钮红色确认) | options: DangerDialogOptions  | Promise\<boolean\> |
| showDialogInfo    | 显示信息对话框(单按钮)             | options: InfoDialogOptions    | Promise\<boolean\> |
| closeDialog       | 关闭对话框                         | -                             | void               |

### DialogOptions 参数

| 参数名       | 类型                            | 默认值    | 说明                                       |
| ------------ | ------------------------------- | --------- | ------------------------------------------ |
| type         | 'confirm' \| 'info' \| 'danger' | 'info'    | 对话框类型                                 |
| title        | string                          | '提示'    | 对话框标题                                 |
| content      | string                          | -         | 对话框内容(必填)                           |
| cancelText   | string                          | '取消'    | 取消按钮文本(仅在confirm/danger类型时有效) |
| confirmText  | string                          | '确定'    | 确认按钮文本                               |
| maskClick    | boolean                         | false     | 点击蒙层是否关闭对话框                     |
| confirmColor | string                          | ''        | 确认按钮颜色(CSS颜色值)                    |
| cancelColor  | string                          | ''        | 取消按钮颜色(CSS颜色值)                    |
| width        | string                          | '90vw'    | 弹框宽度(支持 rpx, px, vw, % 等单位)       |
| height       | string                          | ''        | 弹框高度(支持 rpx, px, vh, % 等单位)       |
| onConfirm    | () => void                      | undefined | 确认按钮点击回调函数                       |
| onClose      | () => void                      | undefined | 关闭回调函数(取消按钮点击或关闭弹框时触发) |

### 配置优先级

对话框的配置优先级如下（从高到低）：

1. **调用时传入的选项** - 最高优先级
2. **Provider 的 defaultOptions** - 中等优先级
3. **组件内置默认值** - 最低优先级

**示例：**

```vue
<template>
  <!-- Provider 设置默认宽度和颜色 -->
  <MyDialogProvider
    :default-options="{
      width: '600rpx',
      confirmColor: '#1890ff',
    }"
  >
    <button @click="showDialog">显示对话框</button>
  </MyDialogProvider>
</template>

<script setup>
const showDialog = () => {
  // 这里的 width 会覆盖 Provider 的默认值
  showDialog({
    content: '这是一个对话框',
    width: '800rpx', // 会使用这个值，而不是 Provider 的 600rpx
    confirmColor: '#ff4d4f', // 会使用这个值，而不是 Provider 的 #1890ff
  });
};
</script>
```

## 使用示例

### 确认对话框(双按钮)

```js
import { showDialogConfirm } from '@/components/MyDialog';

const result = await showDialogConfirm({
  content: '确定要删除这条记录吗?',
  title: '提示',
});

if (result) {
  // 用户点击了确认按钮
} else {
  // 用户点击了取消按钮
}
```

### 危险操作对话框(双按钮红色确认)

```js
import { showDialogDanger } from '@/components/MyDialog';

const result = await showDialogDanger({
  content: '此操作将永久删除该文件, 是否继续?',
  title: '警告',
});

if (result) {
  // 用户确认了危险操作
} else {
  // 用户取消了危险操作
}
```

### 信息对话框(单按钮)

```js
import { showDialogInfo } from '@/components/MyDialog';

const result = await showDialogInfo({
  content: '操作成功!',
  title: '成功',
  confirmText: '知道了',
});

// result 始终为 true，因为信息对话框只有确认按钮
```

### 使用 Provider 配置的完整示例

```vue
<template>
  <MyDialogProvider :default-options="dialogConfig">
    <view class="page">
      <button @click="showBasicDialog">基础对话框</button>
      <button @click="showCustomDialog">自定义对话框</button>
      <button @click="showLargeDialog">大尺寸对话框</button>
    </view>
  </MyDialogProvider>
</template>

<script setup>
import { MyDialogProvider, showDialog, showDialogConfirm } from '@/components/MyDialog';

// 配置默认的对话框选项
const dialogConfig = {
  width: '600rpx',
  height: '400rpx',
  confirmColor: '#7d4dff',
  cancelColor: '#f3ecff',
  maskClick: true,
  title: '系统提示',
};

// 使用默认配置的对话框
const showBasicDialog = () => {
  showDialog({
    content: '这是一个使用默认配置的对话框',
    type: 'info',
  });
};

// 覆盖部分默认配置
const showCustomDialog = () => {
  showDialogConfirm({
    content: '这个对话框覆盖了默认的颜色配置',
    confirmColor: '#ff4d4f',
    cancelColor: '#d9d9d9',
  });
};

// 完全自定义配置
const showLargeDialog = () => {
  showDialog({
    content: '这是一个大尺寸的对话框，完全自定义了配置',
    width: '80vw',
    height: '60vh',
    confirmColor: '#52c41a',
    title: '自定义标题',
  });
};
</script>
```

### 自定义按钮颜色

```js
import { showDialog } from '@/components/MyDialog';

const result = await showDialog({
  type: 'confirm',
  title: '自定义颜色',
  content: '这是一个自定义颜色的对话框',
  confirmText: '确定',
  cancelText: '取消',
  confirmColor: '#00C853', // 绿色确认按钮
  cancelColor: '#BDBDBD', // 灰色取消按钮
});
```

### 使用回调函数

```js
import { showDialog } from '@/components/MyDialog';

await showDialog({
  type: 'confirm',
  title: '带回调的对话框',
  content: '点击按钮触发对应回调',
  onConfirm: () => {
    console.log('确认按钮被点击');
    // 执行确认后的逻辑
  },
  onClose: () => {
    console.log('对话框已关闭');
    // 执行关闭后的逻辑
  },
});
```

## 注意事项

1. **全局唯一弹框**：本组件只允许页面上同时存在一个弹框。如果在 onConfirm/onClose/onCancel 等回调里再次调用 showDialogXxx，前一个弹框会自动关闭，始终只显示最新弹框。
2. **嵌套弹框建议**：如果你需要在弹框的回调（如 onConfirm）里立即再次弹出新弹框，建议用 `setTimeout` 包裹新弹框的调用，避免状态冲突。例如：

```js
showDialogConfirm({
  title: '提示',
  content: '确定要提交吗？',
  onConfirm() {
    setTimeout(() => {
      showDialogInfo({ content: '提交成功' });
    }, 0);
  },
});
```

这样可以确保前一个弹框的关闭动画和状态完全结束后再弹出新弹框，避免偶发的 UI 闪烁或状态错乱。

3. 其余 API、用法、参数详见下方详细文档。
