import { getCurrentInstance, inject } from 'vue';
import { DIALOG_INJECTION_KEY } from './types';
import type {
  DialogOptions,
  ConfirmDialogOptions,
  InfoDialogOptions,
  DangerDialogOptions,
  DialogAPI,
} from './types';

// 全局存储对话框API的栈结构
interface DialogAPIItem {
  id: string;
  api: DialogAPI;
}

let globalDialogAPIStack: DialogAPIItem[] = [];

/**
 * 设置全局对话框API（推入栈）
 * @param api 对话框API
 * @param id 唯一标识符
 */
export function setGlobalDialogAPI(api: DialogAPI, id?: string): string {
  const apiId = id || `dialog_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  // 推入栈顶
  globalDialogAPIStack.push({
    id: apiId,
    api: api,
  });

  return apiId;
}

/**
 * 移除全局对话框API（从栈中移除）
 * @param id API的唯一标识符
 */
export function removeGlobalDialogAPI(id: string): void {
  const index = globalDialogAPIStack.findIndex(item => item.id === id);
  if (index !== -1) {
    globalDialogAPIStack.splice(index, 1);
  }
}

/**
 * 获取当前组件实例中的对话框API
 * @returns 对话框API
 */
function getDialogAPI(): DialogAPI {
  // 优先使用全局栈顶的API（最新注册的）
  if (globalDialogAPIStack.length > 0) {
    return globalDialogAPIStack[globalDialogAPIStack.length - 1].api;
  }

  try {
    // 尝试通过依赖注入获取
    const dialogFromInject = inject(DIALOG_INJECTION_KEY, null);
    if (dialogFromInject) {
      return dialogFromInject;
    }

    // 尝试通过全局属性获取
    const instance = getCurrentInstance();
    if (instance) {
      const dialogFromGlobal = instance.appContext.config.globalProperties.$dialog;
      if (dialogFromGlobal) {
        return dialogFromGlobal;
      }
    }
  } catch (e) {
    // 忽略错误
  }

  // 如果都无法获取，则抛出异常
  throw new Error(
    '未找到对话框API，请确保在setup中使用，或已通过setGlobalDialogAPI设置全局API，或在MyDialogProvider内部使用'
  );
}

/**
 * 显示自定义对话框
 * @param options 对话框选项
 * @returns Promise<boolean> 用户操作结果（确认为true，取消为false）
 */
export function showDialog(options: DialogOptions): Promise<boolean> {
  return getDialogAPI().showDialog(options);
}

/**
 * 显示确认对话框（双按钮）
 * @param options 确认对话框选项
 * @returns Promise<boolean> 用户操作结果（确认为true，取消为false）
 */
export function showDialogConfirm(options: ConfirmDialogOptions): Promise<boolean> {
  return getDialogAPI().showDialogConfirm(options);
}

/**
 * 显示危险操作对话框（双按钮红色确认）
 * @param options 危险操作对话框选项
 * @returns Promise<boolean> 用户操作结果（确认为true，取消为false）
 */
export function showDialogDanger(options: DangerDialogOptions): Promise<boolean> {
  return getDialogAPI().showDialogDanger(options);
}

/**
 * 显示信息对话框（单按钮）
 * @param options 信息对话框选项
 * @returns Promise<boolean> 用户操作结果（确认为true）
 */
export function showDialogInfo(options: InfoDialogOptions): Promise<boolean> {
  return getDialogAPI().showDialogInfo(options);
}

/**
 * 关闭当前对话框
 */
export function closeDialog(): void {
  getDialogAPI().closeDialog();
}
