<template>
  <uni-popup ref="popupRef" type="bottom" @change="handleChange" :is-mask-click="false">
    <view class="my-upload-popup">
      <!-- 标题栏 -->
      <view class="popup-title">
        <text class="title-text">上传文件</text>
        <view class="close-icon" @click="handleClose">
          <text class="close-icon-text">×</text>
        </view>
      </view>

      <!-- 上传选项列表 -->
      <view class="upload-options">
        <!-- 拍照上传 -->
        <view class="upload-option-item" @click="handleCameraUpload">
          <view class="option-icon camera-icon">
            <LkSvg
              src="/static/uploadall/camera.svg"
              width="44rpx"
              height="44rpx"
              color="#786FFF"
            />
          </view>
          <text class="option-text">拍照上传</text>
        </view>

        <!-- 相册选择 -->
        <view class="upload-option-item" @click="handleAlbumUpload">
          <view class="option-icon album-icon">
            <LkSvg src="/static/homework/album.svg" width="44rpx" height="44rpx" color="#786FFF" />
          </view>
          <text class="option-text">相册选择</text>
        </view>

        <!-- 语音录入 -->
        <view class="upload-option-item" @click="handleVoiceInput">
          <view class="option-icon voice-icon">
            <LkSvg src="/static/homework/voice.svg" width="44rpx" height="44rpx" color="#786FFF" />
          </view>
          <text class="option-text">语音录入</text>
        </view>

        <!-- 文件上传 -->
        <view class="upload-option-item" @click="handleFileUpload">
          <view class="option-icon file-icon">
            <LkSvg
              src="/static/uploadall/folder.svg"
              width="44rpx"
              height="44rpx"
              color="#786FFF"
            />
          </view>
          <view class="file-upload-text">
            <text class="option-text">文件上传</text>
            <text class="option-desc">(支持文件类型:Word、PDF、MP4、图片)</text>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>

  <!-- 引入 MyBasicUpload 组件 -->
  <MyBasicUpload
    ref="myBasicUploadRef"
    @upload-success="handleUploadSuccess"
    @upload-fail="handleUploadFail"
    @capture-success="handleCaptureSuccess"
    @capture-fail="handleCaptureFail"
  />

  <!-- 引入 MyRecoderPopup 组件 -->
  <MyRecoderPopup
    ref="myRecoderPopupRef"
    @record-success="handleRecordSuccess"
    @record-cancel="handleRecordCancel"
  />
</template>

<script setup lang="ts">
import { ref, defineEmits, defineExpose } from 'vue';
import { LkSvg } from '@/components';
import MyBasicUpload, {
  type UploadFileData,
  type FilePickerOptions,
  UploadType,
} from '@/components/MyBasicUpload';
import MyRecoderPopup, { type RecordResult } from '@/components/MyRecoderPopup';

// 定义组件事件
const emit = defineEmits<{
  // 上传成功事件
  (e: 'upload-success', data: UploadFileData): void;
  // 上传失败事件
  (e: 'upload-fail', error: any): void;
  // 录音完成事件（开始上传前）
  (e: 'record-complete', data: { duration: number; path: string }): void;
  // 关闭弹窗事件
  (e: 'close'): void;
}>();

// uni-popup 组件引用
const popupRef = ref();

// MyBasicUpload 组件引用
const myBasicUploadRef = ref<InstanceType<typeof MyBasicUpload> | null>(null);

// MyRecoderPopup 组件引用
const myRecoderPopupRef = ref();

// 上传选项
const uploadOptions = ref<FilePickerOptions | undefined>();

// 打开弹窗
const open = (options?: FilePickerOptions) => {
  uploadOptions.value = options;

  popupRef.value?.open();
};

// 关闭弹窗
const close = () => {
  popupRef.value?.close();
};

// 处理弹窗状态变化
const handleChange = (e: { show: boolean }) => {
  if (!e.show) {
    emit('close');
  }
};

// 处理关闭
const handleClose = () => {
  close();
};

// 处理拍照上传
const handleCameraUpload = () => {
  myBasicUploadRef.value?.openCamera();
  close(); // 点击后关闭弹窗
};

// 处理相册上传
const handleAlbumUpload = () => {
  myBasicUploadRef.value?.openAlbum(uploadOptions.value);
  close(); // 点击后关闭弹窗
};

// 处理文件上传
const handleFileUpload = () => {
  myBasicUploadRef.value?.openFilePicker(uploadOptions.value);
  close(); // 点击后关闭弹窗
};

// 处理语音录入
const handleVoiceInput = () => {
  if (myRecoderPopupRef.value && typeof myRecoderPopupRef.value.open === 'function') {
    myRecoderPopupRef.value.open();
  }
  close(); // 点击后关闭弹窗
};

// 处理上传成功
const handleUploadSuccess = (fileData: UploadFileData) => {
  console.log('上传成功:', fileData);
  emit('upload-success', fileData);
};

// 处理上传失败
const handleUploadFail = (error: any) => {
  console.error('上传失败:', error);
  emit('upload-fail', error);
};

// 处理拍照/相册选择成功
const handleCaptureSuccess = (result: any) => {
  console.log(`${result.type}选择成功:`, result.files);
};

// 处理拍照/相册选择失败
const handleCaptureFail = (error: any) => {
  console.error('拍照/相册选择失败:', error);
};

// 处理录音成功
const handleRecordSuccess = (recordResult: { duration: number; path: string }) => {
  console.log('录音成功:', recordResult);

  // 先发出录音完成事件，通知父组件录音已完成，开始上传
  emit('record-complete', recordResult);

  // 构造录音文件数据，适配 MyBasicUpload 的 uploadToServer 方法
  const audioFiles = [
    {
      tempFilePath: recordResult.path,
      name: `录音_${new Date().getTime()}.mp3`,
      fileType: 'file', // 标记为文件类型
    },
  ];

  // 使用 MyBasicUpload 组件上传录音文件到服务器
  if (myBasicUploadRef.value && typeof myBasicUploadRef.value.uploadToServer === 'function') {
    try {
      myBasicUploadRef.value.uploadToServer(audioFiles, UploadType.FILE);
    } catch (error) {
      console.error('上传录音文件失败:', error);
      emit('upload-fail', { error: '上传录音文件失败', details: error });
    }
  } else {
    console.error('MyBasicUpload 组件未正确加载或 uploadToServer 方法不可用');
    // 如果上传组件不可用，发出上传失败事件
    emit('upload-fail', { error: 'MyBasicUpload 组件不可用' });
  }
};

// 处理录音取消
const handleRecordCancel = () => {
  console.log('录音取消');
};

// 向父组件暴露方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.my-upload-popup {
  background-color: #f2f3f5;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 32rpx 68rpx;
  position: relative;
}

.popup-title {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 52rpx;
  margin-bottom: 48rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1d2129;
  line-height: 52rpx;
}

.close-icon {
  position: absolute;
  right: 0;
  top: 0;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  &-text {
    font-size: 48rpx;
    color: #1d2129;
  }
}

.upload-options {
  padding: 0 32rpx;
  background-color: #fff;
  border-radius: 24rpx;
}

.upload-option-item {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 32rpx 0;
  border-bottom: 1rpx solid #e7e7e7;

  &:last-child {
    border-bottom: none;
  }
}

.option-icon {
  margin-right: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 44rpx;
  height: 44rpx;
}

.option-text {
  font-size: 32rpx;
  color: #1d2129;
}

.option-desc {
  font-size: 28rpx;
  color: #4e5969;
  margin-top: 4rpx;
}

.file-upload-text {
  display: flex;
  flex-direction: column;
}
</style>
