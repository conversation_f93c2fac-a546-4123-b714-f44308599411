# MyUploadPopup 组件

基于 uni-popup 和 MyBasicUpload 封装的文件上传弹窗组件。

## 功能特点

- 支持拍照上传
- 支持相册选择
- 支持文件上传
- 支持语音录入功能（通过 MyRecoderPopup 组件实现）
- 基于 uni-popup 实现底部弹出效果

## 安装与使用

### 引入组件

```js
import MyUploadPopup from '@/components/MyUploadPopup';
```

### 使用组件

```vue
<template>
  <view>
    <button @click="openUploadPopup">打开上传弹窗</button>

    <MyUploadPopup
      ref="uploadPopupRef"
      @upload-success="handleUploadSuccess"
      @upload-fail="handleUploadFail"
      @record-complete="handleRecordComplete"
      @close="handleClose"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import MyUploadPopup from '@/components/MyUploadPopup';

const uploadPopupRef = ref(null);

// 打开上传弹窗
const openUploadPopup = () => {
  uploadPopupRef.value?.open();
};

// 处理上传成功
const handleUploadSuccess = fileData => {
  console.log('上传成功:', fileData);
  // fileData 包含文件信息，如 fileUrl, fileName, fileKey 等
  // 对于录音文件，fileType 为 'audio'
};

// 处理上传失败
const handleUploadFail = error => {
  console.error('上传失败:', error);
};

// 处理录音完成（开始上传前）
const handleRecordComplete = recordData => {
  console.log('录音完成，开始上传:', recordData);
  // recordData 包含 { duration: number, path: string }
  // 可以在这里显示上传进度提示
};

// 处理弹窗关闭
const handleClose = () => {
  console.log('弹窗已关闭');
};
</script>
```

## 组件属性

该组件没有可配置的属性。

## 组件事件

| 事件名          | 说明                         | 回调参数                     |
| --------------- | ---------------------------- | ---------------------------- |
| upload-success  | 文件上传成功时触发           | fileData: 上传成功的文件信息 |
| upload-fail     | 文件上传失败时触发           | error: 错误信息              |
| record-complete | 录音完成时触发（开始上传前） | recordData: 录音文件信息     |
| close           | 弹窗关闭时触发               | -                            |

## 组件方法

| 方法名 | 说明     | 参数 |
| ------ | -------- | ---- |
| open   | 打开弹窗 | -    |
| close  | 关闭弹窗 | -    |

## 注意事项

1. 该组件依赖于 uni-popup、MyBasicUpload 和 MyRecoderPopup 组件，请确保项目中已引入这些组件。
2. 语音录入功能通过 MyRecoderPopup 组件实现，支持录音、播放和提交。
3. 上传文件支持的类型包括：Word、PDF、MP4、图片、音频等。
4. 组件内部已处理上传成功和失败的回调，使用时只需监听相应事件即可。
5. **录音文件会自动上传到服务器**：录音完成后，组件会自动调用 MyBasicUpload 的 uploadToServer 方法将录音文件上传到服务器。
6. 录音上传流程：录音完成 → 触发 `record-complete` 事件 → 自动上传 → 触发 `upload-success` 或 `upload-fail` 事件。
7. 建议在 `record-complete` 事件中显示上传进度提示，在 `upload-success` 或 `upload-fail` 事件中处理最终结果。

## 示例

详细使用示例请参考 `example.vue` 文件。
