<template>
  <view
    class="header-nav"
    :class="{ 'header-nav--transparent': transparent }"
    :style="{ paddingTop: `${statusBarHeight * 2}rpx` }"
  >
    <view class="header-nav__content">
      <view class="header-nav__left" @click="handleBack">
        <u-icon name="arrow-left" size="20" color="#1D2129"></u-icon>
      </view>
      <view class="header-nav__title" :class="{ 'header-nav__title--center': centerTitle }">
        <view class="header-nav__title-icon" v-if="titleIcon" @click="handleTitleIconClick">
          <image :src="titleIcon" mode="aspectFit" class="title-icon-image"></image>
        </view>
        <text
          class="header-nav__title-text"
          :style="{ textAlign: centerTitle ? 'center' : 'left' }"
          >{{ title }}</text
        >
      </view>
      <view class="header-nav__right" :style="{ width: rightWidth || '' }">
        <!-- <view class="header-nav__right-item" v-if="showRefresh" @click="handleRefresh">
          <LkSvg width="22rpx" height="22rpx" src="/static/chat/comment.svg" />
        </view> -->
        <slot name="right">
          <view class="header-nav__right-item" v-if="rightContent">
            {{ rightContent }}
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Props {
  /** 标题文字 */
  title: string;
  /** 标题图标 */
  titleIcon?: string;
  /** 是否显示刷新按钮 */
  showRefresh?: boolean;
  /** 是否显示更多按钮 */
  showMore?: boolean;
  /** 是否透明 */
  transparent?: boolean;
  /** 右侧宽度 */
  rightWidth?: string;
  // 标题是否居中
  centerTitle?: boolean;

  /** 右侧内容 */
  rightContent?: any;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  titleIcon: '',
  showRefresh: false,
  showMore: false,
  transparent: false,
  rightWidth: '',
  centerTitle: false,
  rightContent: '',
});

const emit = defineEmits(['back', 'refresh', 'more', 'title-icon-click']);

// 系统信息
const statusBarHeight = ref(0);

onMounted(() => {
  // 获取系统状态栏高度
  uni.getSystemInfo({
    success: res => {
      statusBarHeight.value = res.statusBarHeight || 0;
    },
  });
});

// 处理返回
const handleBack = () => {
  console.log('handleBack');
  emit('back');
  // uni.navigateBack({
  //   fail: () => {
  //     uni.switchTab({
  //       url: '/pages/index/index',
  //     });
  //   },
  // });
};

// 处理刷新
const handleRefresh = () => {
  emit('refresh');
};

// 处理更多
const handleMore = () => {
  emit('more');
};

// 处理标题图标点击
const handleTitleIconClick = () => {
  emit('title-icon-click');
};
</script>

<style lang="scss">
.header-nav {
  width: 100%;
  background-color: #fff;
  position: relative;
  z-index: 999;

  &--transparent {
    background-color: transparent;
  }

  &__content {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
  }

  &__left {
    position: absolute;
    left: 30rpx;
    display: flex;
    align-items: center;
    height: 100%;
    z-index: 999999;
  }

  &__title {
    display: flex;
    align-items: center;
    margin-left: 64rpx;
    justify-content: flex-start;
    height: 100%;

    &--center {
      position: absolute;
      left: 0;
      right: 0;
      margin: 0 auto;
      justify-content: center;
    }

    &-icon {
      margin-right: 5px;
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .title-icon-image {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        overflow: hidden;
      }
    }

    &-text {
      font-size: 34rpx;
      font-weight: 500;
      color: #333333;
      text-align: left;
    }
  }

  &__right {
    position: absolute;
    right: 30rpx;
    display: flex;
    align-items: center;
    height: 100%;

    &-item {
      margin-left: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
