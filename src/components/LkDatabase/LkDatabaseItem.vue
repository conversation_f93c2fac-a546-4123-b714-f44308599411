<script setup lang="ts">
import LkSvg from '@/components/svg/index.vue';
import { getSpaceFileList, getMySpaceFileList, getRecycleBinFileList } from '@/api/database';
import { ref, nextTick, watch } from 'vue';
import FileViewer from '@/components/LkFileViewer/index.vue';
import LkPageList from '@/components/LkPageList/index.vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/userStore';

type NavOptions = {
  id: string;
  navTitle: string;
  bizType: string;
  itemBizType: number;
  shareType: number;
};

interface DatabaseItem {
  fileName: string;
  fileType: string;
  fileSize: string;
  fileDate: string;
  creator: string;
  spaceName: string;
  records: any[];
}

const props = defineProps({
  isCreator: {
    type: Boolean,
    default: false,
  },
  isFileInfo: {
    type: Boolean,
    default: false,
  },
  parentId: {
    type: String,
    default: '0',
  },
  isPopup: {
    type: Boolean,
    default: false,
  },
  // 1数据空间 2数据空间+号 3对话页面 4回收站 5数据空间-移动 6数据空间-编辑
  layoutType: {
    type: Number,
    required: true,
  },
  bizType: {
    type: String,
    required: true,
  },
  optionsList: {
    type: Array as () => any[],
    // 初始值是[{}]不能是[],否则disabled不生效
    default: () => [{}],
    required: false,
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
  preSelectedIds: {
    type: Array as () => string[],
    default: () => [],
  },
  needGuide: {
    type: Boolean,
    default: false,
  },
});

const urlOptions = ref<NavOptions>();
const userStore = useUserStore();
const currentParentId = ref(props.parentId || '0');
const fileProps = ref<{
  fileUrl: string;
  fileKey: string;
  fileType?: number;
}>({
  fileUrl: '',
  fileKey: '',
  fileType: undefined,
});
const lkPageListRef = ref<InstanceType<typeof LkPageList>>();
const searchKeywordRef = ref('');

onLoad(options => {
  urlOptions.value = { ...options } as NavOptions;
});

// 当前已选的项的id(给checkbox使用)
const checkboxValue = ref<string[]>([]);
// 当前已选的项(回传给父组件使用)
const selectedItems = ref<any[]>([]);
// item数据
const currentList = ref<any[]>([]);
// 保存不同目录下的选中状态
const dirSelectionMap = ref<Record<string, { ids: string[]; items: any[] }>>({});

// 监听目录变化，保存当前选中状态
watch(
  () => currentParentId.value,
  (newParentId, oldParentId) => {
    console.log('目录变化:', oldParentId, '->', newParentId);
    if (oldParentId && oldParentId !== newParentId) {
      // 过滤出当前目录下的选中项
      const currentDirItems = selectedItems.value.filter(item =>
        currentList.value.some(currItem => currItem.id === item.id)
      );

      const currentDirIds = currentDirItems.map(item => item.id);

      // 保存旧目录的选中状态
      console.log(`保存目录${oldParentId}的选中状态:`, currentDirIds.length);
      dirSelectionMap.value[oldParentId] = {
        ids: currentDirIds,
        items: currentDirItems,
      };

      // 重要：不再移除当前目录的选中项，保留所有选中状态
      // 仅清空当前目录的checkbox显示
      checkboxValue.value = [];

      // 保持全部选中项不变，确保统计的是所有目录的选中项
      console.log('目录切换后保留的总选中项:', selectedItems.value.length);
    }
  }
);

// 监听编辑状态变化
watch(
  () => props.isEditing,
  (isEditing, wasEditing) => {
    // 从非编辑状态进入编辑状态时，清空所有选项
    if (isEditing && !wasEditing) {
      console.log('进入编辑模式，清空所有选中项');
      checkboxValue.value = [];
      selectedItems.value = [];
      dirSelectionMap.value = {};
      emit('updateSelectedItems', []);
    }
  }
);

const changeCheckbox = (value: string[], item: any) => {
  console.log('changeCheckbox', item);
  toggleItemSelection(item);
};

const emit = defineEmits(['updatePathData', 'updateSelectedItems', 'clickOption', 'dataLoaded']);
// 使用 LkPageList 替代原有的加载逻辑
const fetchSpaceFileList = async (params: any) => {
  const apiParams = {
    ...params,
    privilege: userStore.curPrivilege,
    searchKey: searchKeywordRef.value,
    shareType: urlOptions.value?.shareType,
  };
  console.log('params with keyword:', apiParams);

  let result;
  if (props.bizType === '1' || urlOptions.value?.itemBizType == 1) {
    result = await getSpaceFileList(apiParams);
  } else if (props.bizType === '2' || urlOptions.value?.itemBizType == 2) {
    result = await getMySpaceFileList(apiParams);
  } else if (props.bizType === '3' && !urlOptions.value?.itemBizType) {
    result = await getRecycleBinFileList(apiParams);
  }

  // 数据加载完成后发送事件通知
  if (result?.records && Array.isArray(result?.records)) {
    emit('dataLoaded', result.records);
  }

  return result;
};

const getOptionsByItem = (item: any) => {
  // 如果optionsList为空或无效，则返回空对象
  if (
    !props.optionsList ||
    props.optionsList.length === 0 ||
    (props.optionsList.length === 1 && Object.keys(props.optionsList[0]).length === 0)
  ) {
    return [{}];
  }

  // --- 权限逻辑 ---
  const userInfo = userStore.getUserInfo;
  // 是否为上传者
  const isUploader = item.uploader === userInfo?.username;
  // 是否为管理员
  const isAdmin = Number(userInfo?.roleType) === 1 || Number(userInfo?.roleType) === 3;

  // 权限级别：优先从数组中取，然后直接取值，默认为4（管理）
  const privileges = item?.privileges?.[0] || 4;
  console.log(privileges);
  // 是否为"我的数据空间"
  const isAdminMode = props.bizType === '2';
  // 是否有管理权限
  const isManagePrivilege = privileges >= 3 || isAdmin;

  // 根据权限判断是否可删除
  const canDelete =
    isAdminMode || isManagePrivilege || (isUploader && (privileges === 1 || privileges === 2));

  console.log(`左滑删除权限 for item ${item.fileName}:`, {
    canDelete,
    privileges,
    isUploader,
    isAdmin,
    isAdminMode,
    isManagePrivilege,
  });

  const originalOptions = [...props.optionsList];

  // 文件类型为1（文件）
  if (item.fileType === 1) {
    // 如果不能删除，则移除最后一个按钮（删除按钮）
    if (!canDelete) {
      originalOptions.pop();
    }
    return originalOptions;
  }
  // 文件类型为2（文件夹）
  else if (item.fileType === 2) {
    const moreButton = originalOptions[0];
    const deleteButton = originalOptions[originalOptions.length - 1];
    const folderOptions = [moreButton];
    if (canDelete) {
      folderOptions.push(deleteButton);
    }
    return folderOptions;
  }
  // 其他类型（如空间）
  else {
    return [{}];
  }
};

const processSpaceFileData = (data: any) => {
  console.log(data);
  return data;
};

const clickDatabaseItem = (item: any) => {
  console.log('item', item);
  const maxPrivilege = item.privileges?.length ? Math.max(...item.privileges) : 4;
  userStore.curPrivilege = maxPrivilege || 4;
  if (item.fileType === 3 || item.fileType === 2) {
    if (props.isPopup) {
      // 更新路径数据
      emit('updatePathData', item);
      // 更新空间数据
      currentParentId.value = item.id;
    } else {
      let navTitle = '';
      if (props.bizType === '1') {
        navTitle = item.spaceName;
      } else if (props.bizType === '2') {
        navTitle = item.folderName;
      } else if (props.bizType === '3') {
        navTitle = item.fileName;
      }
      // 回收站的情况下用itemBizType来请求对应空间的数据,bizType用来处理前端交互逻辑
      if (props.bizType === '3') {
        // 跳转子空间页面
        uni.navigateTo({
          url: `/pages-subpackages/data-space-pkg/sub-space/index?id=${item.bizId}&navTitle=${navTitle}&bizType=${props.bizType}&itemBizType=${item.bizType}&shareType=${item.shareType || ''}&privileges=${item?.privileges?.length ? item?.privileges?.[0] : 4}`,
        });
      } else {
        // 跳转子空间页面
        uni.navigateTo({
          url: `/pages-subpackages/data-space-pkg/sub-space/index?id=${item.id}&navTitle=${navTitle}&bizType=${props.bizType}&shareType=${item.shareType || ''}&privileges=${item?.privileges?.length ? item?.privileges?.[0] : 4}`,
        });
      }
    }
  } else if (item.fileType === 1) {
    // 如果类型符合,则打开文件
    console.log('打开文件');
    fileProps.value = {
      fileUrl: item.file?.fileUrl,
      fileKey: item.file?.fileKey,
      fileType: item.fileType,
    };
    console.log(fileProps);
  }
};

// 当数据列表加载完成后，恢复该目录下的选中状态
watch(
  () => currentList.value,
  newList => {
    if (newList.length > 0) {
      console.log(
        '列表数据加载完成，当前目录:',
        currentParentId.value,
        '列表长度:',
        newList.length
      );

      // 获取当前显示目录下应该选中的项
      if (dirSelectionMap.value[currentParentId.value]) {
        console.log('恢复目录选中状态:', currentParentId.value);
        const savedSelection = dirSelectionMap.value[currentParentId.value];

        // 更新checkbox以反映当前目录的选中状态
        checkboxValue.value = [...savedSelection.ids];

        // 关键：检查当前目录有哪些项已经在selectedItems中
        const currentDirExistingIds = selectedItems.value
          .filter(item => currentList.value.some(currItem => currItem.id === item.id))
          .map(item => item.id);

        console.log('当前目录已在选中列表中的项:', currentDirExistingIds.length);

        // 添加当前目录已保存但未在selectedItems中的项
        savedSelection.items.forEach(item => {
          if (!currentDirExistingIds.includes(item.id)) {
            selectedItems.value.push(item);
          }
        });

        // 通知父组件更新选中状态
        console.log('恢复后的总选中项数量:', selectedItems.value.length);
        emit('updateSelectedItems', [...selectedItems.value]);
      }

      // 优先应用预选ID，如果有的话
      if (props.preSelectedIds && props.preSelectedIds.length > 0) {
        setPreSelectedIds(props.preSelectedIds);
      }
    }
  }
);

// 切换项目选中状态
const toggleItemSelection = (item: any) => {
  console.log('checkboxValue', checkboxValue.value);
  // 检查项目是否已经选中
  const index = selectedItems.value.findIndex(i => i.id === item.id);
  if (index > -1) {
    // 如果已选中，则移除
    selectedItems.value.splice(index, 1);
    // 同时更新checkbox值
    const checkboxIndex = checkboxValue.value.indexOf(item.id);
    if (checkboxIndex > -1) {
      checkboxValue.value.splice(checkboxIndex, 1);
    }
  } else {
    // 如果未选中，则添加
    selectedItems.value.push(item);
    // 同时更新checkbox值
    if (!checkboxValue.value.includes(item.id)) {
      checkboxValue.value.push(item.id);
    }
  }

  // 确保父组件收到更新
  console.log('切换选中状态，当前选中数:', selectedItems.value.length);
  emit('updateSelectedItems', [...selectedItems.value]);
};

// 检查项目是否被选中
const isItemSelected = (item: any) => {
  return selectedItems.value.some(i => i.id === item.id);
};

const fileIcon = (item: any): string => {
  if (item.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item.fileType === 1) {
    // 截取文件扩展名
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

const formatFileSize = (bytes: number, withoutB?: boolean): string => {
  if (bytes === 0) return '0B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + (withoutB ? sizes[i][0] : sizes[i]);
};

const setSearchKeywordAndRefresh = (keyword: string) => {
  searchKeywordRef.value = keyword;
  nextTick(() => {
    if (lkPageListRef.value) {
      lkPageListRef.value.refresh();
    }
  });
};

function syncList(listFromSlot: any[]) {
  currentList.value = listFromSlot;

  // 通知父组件更新选中状态，以便检查全选按钮状态
  // 这确保在滑动加载新数据时也会更新全选按钮状态
  if (props.layoutType === 2) {
    emit('updateSelectedItems', [...selectedItems.value]);
  }
}

async function toggleCheckAll(bool?: boolean) {
  // 只有在layoutType === 2时才处理全选逻辑
  if (props.layoutType === 2) {
    console.log('执行数据空间+号全选操作，当前状态:', bool);

    // 保存其他目录的选中项
    const otherDirSelectedItems = selectedItems.value.filter(
      item => !currentList.value.some(currItem => currItem.id === item.id)
    );

    // 获取当前目录下可选择的文件
    const currentDirSelectableItems = currentList.value.filter(item => item.fileType === 1);

    // 判断当前目录的选中状态
    const currentDirIds = checkboxValue.value;
    const isCurrentDirAllSelected =
      currentDirSelectableItems.length > 0 &&
      currentDirSelectableItems.every(item => currentDirIds.includes(item.id));

    if (bool === false || isCurrentDirAllSelected) {
      // 取消全选当前目录
      console.log('取消全选当前目录');

      // 更新checkbox，清空当前目录的选中状态
      checkboxValue.value = [];

      // 更新selectedItems，只保留其他目录的选中项
      selectedItems.value = [...otherDirSelectedItems];
    } else {
      // 全选当前目录
      console.log('全选当前目录');

      // 更新checkbox，全选当前目录
      checkboxValue.value = currentDirSelectableItems.map(item => item.id);

      // 更新selectedItems，合并当前目录和其他目录的选中项
      const newSelectedItems = [...otherDirSelectedItems];

      // 添加当前目录的可选项
      currentDirSelectableItems.forEach(item => {
        if (!newSelectedItems.some(i => i.id === item.id)) {
          newSelectedItems.push(item);
        }
      });

      selectedItems.value = newSelectedItems;
    }

    console.log('全选操作后 - 当前目录选中数:', checkboxValue.value.length);
    console.log('全选操作后 - 所有目录总选中数:', selectedItems.value.length);

    // 通知父组件选中项变化
    emit('updateSelectedItems', [...selectedItems.value]);
    return;
  }

  if (bool === false) {
    checkboxValue.value = [];
    selectedItems.value = [];
    return;
  }
  // layoutType 6 为子空间的编辑模式
  if (props.layoutType === 6) {
    const selectableItems = currentList.value.filter(item => item.fileType !== 3);
    if (checkboxValue.value.length === selectableItems.length) {
      checkboxValue.value = [];
      selectedItems.value = [];
    } else {
      checkboxValue.value = selectableItems.map(item => item.id);
      selectedItems.value = [...selectableItems];
    }
  }
  // 对话页面
  else if (props.layoutType === 3) {
    // 只获取文件类型为1的项（可选择的文件）
    const selectableItems = currentList.value.filter(item => item.fileType === 1);

    if (checkboxValue.value.length === selectableItems.length) {
      // 如果全选了，则取消全选
      checkboxValue.value = [];
      selectedItems.value = [];
    } else {
      // 如果没全选，则全选可选项目
      checkboxValue.value = selectableItems.map(item => item.id);
      selectedItems.value = [...selectableItems];
    }
  }
  // 回收站
  else if (props.layoutType === 4 || props.layoutType === 6) {
    // 获取所有项目
    const selectableItems = currentList.value;

    if (checkboxValue.value.length === selectableItems.length) {
      // 如果全选了，则取消全选
      checkboxValue.value = [];
      selectedItems.value = [];
    } else {
      // 如果没全选，则全选可选项目
      checkboxValue.value = selectableItems.map(item => item.id);
      selectedItems.value = [...selectableItems];
    }
  }

  console.log('选中的checkbox值:', checkboxValue.value);
  console.log('选中项:', selectedItems.value);

  // 通知父组件选中项变化
  emit('updateSelectedItems', [...selectedItems.value]);
}

// 设置预选中的文件IDs
const setPreSelectedIds = (ids: string[]) => {
  if (!ids || ids.length === 0) return;

  console.log('设置预选ID:', ids);

  // 不再清空当前选择，而是合并选择
  // 获取当前列表中与预选ID匹配的项
  const matchedItems = currentList.value.filter(item => ids.includes(item.id));

  if (matchedItems.length > 0) {
    // 更新选中状态，合并而不是替换
    matchedItems.forEach(item => {
      if (!selectedItems.value.some(i => i.id === item.id)) {
        selectedItems.value.push(item);
      }
      if (!checkboxValue.value.includes(item.id)) {
        checkboxValue.value.push(item.id);
      }
    });

    // 通知父组件
    emit('updateSelectedItems', [...selectedItems.value]);
  }

  console.log('预选匹配项:', matchedItems);
};

// 初始化时也检查预选ID
watch(
  () => props.preSelectedIds,
  (newIds: string[]) => {
    if (!newIds || newIds.length === 0) return;

    // 只添加新的ID，不清空现有选择
    // 1. 更新checkboxValue - 添加新的ID
    newIds.forEach(id => {
      if (!checkboxValue.value.includes(id)) {
        checkboxValue.value.push(id);
      }
    });

    // 2. 更新selectedItems - 添加新的选中项
    const newItems = currentList.value.filter(
      item => newIds.includes(item.id) && !selectedItems.value.some(i => i.id === item.id)
    );

    if (newItems.length > 0) {
      selectedItems.value.push(...newItems);
      emit('updateSelectedItems', [...selectedItems.value]);
    }
  },
  { immediate: true }
);

const refreshPageList = () => {
  console.log('refreshPageList');
  if (lkPageListRef.value) {
    lkPageListRef.value?.refresh();
  }
};

defineExpose({
  currentParentId,
  lkPageListRef,
  setSearchKeywordAndRefresh,
  setPreSelectedIds,
  refreshPageList,
  toggleCheckAll,
  currentList,
});

const clickOption = (obj: any, item: any, fn: Function) => {
  console.log(obj);
  emit('clickOption', { eventData: obj, itemData: item, fn });
};

const getSpacePath = (item: any) => {
  let segments = [];

  if (item.path.length > 3) {
    segments = [
      { type: 'text', content: item.path[0] },
      { type: 'separator' },
      { type: 'text', content: '...' },
      { type: 'separator' },
      { type: 'text', content: item.path[item.path.length - 1] },
    ];
  } else if (item.path.length === 3) {
    segments = [
      { type: 'text', content: item.bizType === '1' ? '学校数据空间' : '我的数据空间' },
      { type: 'separator' },
      { type: 'text', content: '...' },
      { type: 'separator' },
      { type: 'text', content: item.path[item.path.length - 1] },
    ];
  } else if (item.path.length === 2) {
    segments = [
      { type: 'text', content: item.bizType === '1' ? '学校数据空间' : '我的数据空间' },
      { type: 'separator' },
      { type: 'text', content: item.path[0] },
      { type: 'separator' },
      { type: 'text', content: item.path[1] },
    ];
  } else if (item.path.length === 1) {
    segments = [{ type: 'text', content: item.path[item.path.length - 1] }];
  } else {
    segments = [{ type: 'text', content: item.location || '' }];
  }

  return segments;
};

const onDataLoaded = (data: any[]) => {
  console.log('dataLoaded', data.length);
  // 数据加载完成后，通知父组件更新选中状态
  // 这确保在滑动加载新数据时也会更新全选按钮状态
  if (props.layoutType === 2) {
    emit('updateSelectedItems', [...selectedItems.value]);
  }
};
</script>

<template>
  <view class="lk-database-main" :style="{ height: isPopup ? '100%' : 'calc(100vh - 44px)' }">
    <u-swipe-action>
      <LkPageList
        ref="lkPageListRef"
        :fetch-method="fetchSpaceFileList"
        :extra-params="{ parentId: currentParentId }"
        :process-data="processSpaceFileData"
        :page-size="10"
        :margin-top="0"
        @dataLoaded="onDataLoaded"
      >
        <template #default="{ list }">
          <component
            :is="
              () => {
                syncList(list);
                return null;
              }
            "
          />
          <u-checkbox-group v-model="checkboxValue" placement="column">
            <u-swipe-action-item
              v-for="(item, index) in list"
              :key="item.id"
              :options="getOptionsByItem(item)"
              @click="(obj: any) => clickOption(obj, item, getOptionsByItem)"
              :disabled="
                isPopup || layoutType === 3 || JSON.stringify(getOptionsByItem(item)) === '[{}]'
              "
              :show="
                needGuide && list.length > 0
                  ? list.length <= 3
                    ? index === list.length - 1
                    : index === 2
                  : false
              "
            >
              <view class="lk-database-item" @click="clickDatabaseItem(item)">
                <view class="wrapContent">
                  <!-- 复选框 -->
                  <up-checkbox
                    v-if="
                      (layoutType === 2 || layoutType === 3) &&
                      isEditing &&
                      (item.fileType === 1 || (props.bizType === '2' && item.fileType === 2))
                    "
                    :name="item.id"
                    activeColor="#6d51f6"
                    :customStyle="{}"
                    shape="circle"
                    @change="changeCheckbox($event, item)"
                    :disabled="item.fileType === 2"
                  >
                  </up-checkbox>
                  <up-checkbox
                    v-else-if="layoutType === 4 && isEditing"
                    :name="item.id"
                    activeColor="#6d51f6"
                    :customStyle="{}"
                    shape="circle"
                    @change="changeCheckbox($event, item)"
                  >
                  </up-checkbox>
                  <up-checkbox
                    v-else-if="layoutType === 6 && isEditing"
                    :name="item.id"
                    activeColor="#6d51f6"
                    :customStyle="{}"
                    shape="circle"
                    :disabled="item.fileType === 3"
                    @change="changeCheckbox($event, item)"
                  >
                  </up-checkbox>
                  <view class="content">
                    <LkSvg
                      class="fileIcon"
                      width="84rpx"
                      height="84rpx"
                      :src="fileIcon(item)"
                      :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                    />
                    <view class="wrapTxt">
                      <view class="fileName" v-if="bizType === '3'">
                        {{ item.fileName }}
                      </view>
                      <view class="fileName" v-else>{{
                        item.fileType === 3 ? item.spaceName : item.fileName
                      }}</view>
                      <!-- 数据空间 -->
                      <view class="fileInfo" v-if="layoutType === 1">
                        <text class="fileSize" v-if="item.fileType === 1">{{
                          formatFileSize(item.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.fileType === 1"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                        <text class="fileCreator" v-if="bizType === '1'">{{ item.uploader }}</text>
                      </view>
                      <!-- 数据空间+号 -->
                      <view class="fileInfo" v-if="layoutType === 2">
                        <text class="fileSize" v-if="item.fileType === 1">{{
                          formatFileSize(item.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.fileType === 1"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                      </view>
                      <!-- 对话页面 -->
                      <view class="fileInfo" v-if="layoutType === 3">
                        <text class="fileSize" v-if="item.fileType === 1">{{
                          formatFileSize(item.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.fileType === 1"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                        <text class="fileCreator" v-if="bizType === '1'">{{ item.uploader }}</text>
                      </view>
                      <!-- 回收站 -->
                      <view class="fileInfo" v-if="layoutType === 4">
                        <text class="fileSize" v-if="item.file">{{
                          formatFileSize(item.file.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.file"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                      </view>
                      <!-- 数据空间-移动 -->
                      <view class="fileInfo" v-if="layoutType === 5">
                        <text class="fileSize" v-if="item.fileType === 1">{{
                          formatFileSize(item.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.fileType === 1"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                        <text class="fileCreator" v-if="bizType === '1'">{{ item.uploader }}</text>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="bottom" v-if="layoutType === 4 && !isPopup">
                  <LkSvg width="36rpx" height="36rpx" src="/static/recycleBin/pathBg.svg" />
                  <view class="wrapPath">
                    <template
                      v-for="(segment, index) in getSpacePath(item)"
                      :key="index"
                      class="path"
                    >
                      <view v-if="segment.type === 'text'">{{ segment.content }}</view>
                      <LkSvg
                        class="separator"
                        v-else-if="segment.type === 'separator'"
                        width="4px"
                        height="13px"
                        src="/static/database/pathArrow.svg"
                      />
                    </template>
                  </view>
                  <view class="delUser">
                    {{ item.deleterName }}
                  </view>
                </view>
              </view>
            </u-swipe-action-item>
          </u-checkbox-group>
        </template>
      </LkPageList>
    </u-swipe-action>
    <FileViewer
      :file="fileProps"
      @resetFile="fileProps = { fileUrl: '', fileKey: '', fileType: undefined }"
    />
  </view>
</template>
<style lang="scss" scoped>
.lk-database-main {
  .u-swipe-action-item {
    margin-top: 12px;
    background: #fff;
    box-shadow: 0px 0px 6px 0px rgba(237, 237, 237, 0.62);
    border-radius: 14px;
    border: 1px solid #f4f4f4;
  }
  .u-swipe-action {
    height: 100%;
  }
  ::v-deep .u-swipe-action-item__content {
    display: flex;
    align-items: center;
    height: 100%;
  }
  .lk-database-item {
    display: flex;
    flex-direction: column;
    padding: 14.39px 16px 13.61px 16px;
    overflow: hidden;
    flex: 1;
    > .wrapContent {
      display: flex;
      align-items: center;
      ::v-deep .u-checkbox__icon-wrap {
        flex-shrink: 0;
        margin-right: 12px;
        width: 21px !important;
        height: 21px !important;
      }
      > .content {
        display: flex;
        align-items: center;
        width: 100%;
        .fileIcon {
          flex-shrink: 0;
          margin-right: 10px;
        }
        .wrapTxt {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .fileName {
            color: #1d2129;
            font-family: 'PingFang SC';
            font-size: 32rpx;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-all;
          }
          .fileInfo {
            display: flex;
            align-items: center;
            margin-top: 6px;
            .fileSize {
              font-family: 'PingFang SC';
              font-size: 24rpx;
              color: #86909c;
            }
            .sign {
              width: 1px;
              height: 9px;
              background: #86909c;
              margin: 0 6px;
            }
            .fileDate {
              font-family: 'PingFang SC';
              font-size: 24rpx;
              color: #86909c;
            }
            .fileCreator {
              margin-left: auto;
              font-size: 24rpx;
              color: #4e5969;
              letter-spacing: 0.06px;
              text-align: right;
            }
          }
        }
      }
    }
    > .bottom {
      width: 100%;
      margin-top: 12px;
      padding-top: 10px;
      border-top: 1px solid #f3f3f3;
      display: flex;
      align-items: center;
      > uni-image {
        flex-shrink: 0;
      }
      .wrapPath {
        margin-left: 4px;
        color: #86909c;
        font-size: 24rpx;
        display: flex;
        align-items: center;
        .separator {
          margin-left: 3px;
          margin-right: 2px;
        }
      }
      .delUser {
        margin-left: auto;
        color: #86909c;
        font-size: 24rpx;
      }
    }
  }
}
</style>
