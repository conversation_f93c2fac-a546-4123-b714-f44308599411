<template>
  <view class="message-edit-wrapper" :class="{ 'ios-platform': isIOS }" v-if="visible">
    <view class="message-edit-container" :style="containerStyle">
      <view class="message-edit-box">
        <textarea
          class="message-edit-input"
          v-model="editContent"
          auto-height
          :maxlength="-1"
          :focus="visible"
          :adjust-position="false"
        />
        <view class="message-edit-controls">
          <view class="control-button" @tap="handleCancel">
            <view class="circle-button cancel-button">
              <LkSvg width="64rpx" height="64rpx" src="/static/chat/close-circle.svg"></LkSvg>
            </view>
          </view>
          <view class="control-button" @tap="handleConfirm">
            <view class="circle-button confirm-button">
              <LkSvg width="40rpx" height="40rpx" src="/static/chat/check.svg" color="#fff"></LkSvg>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['cancel', 'confirm', 'update:visible']);

const editContent = ref(props.content);
const keyboardHeight = ref(0);
const isIOS = ref(false);

// 检测是否为 iOS 平台
uni.getSystemInfo({
  success: res => {
    if (res.platform === 'ios') {
      isIOS.value = true;
      console.log('检测到 iOS 平台，应用特殊层级处理');
    }
  },
});

const containerStyle = computed(() => {
  const paddingBottom =
    keyboardHeight.value > 0 ? `${keyboardHeight.value}px` : 'env(safe-area-inset-bottom)';
  return { paddingBottom };
});

// 监听props.content的变化
watch(
  () => props.content,
  newVal => {
    editContent.value = newVal;
  }
);

// 监听props.visible的变化
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      editContent.value = props.content;
    }
  }
);

// // 处理键盘高度变化
// const handleKeyboardHeightChange = (e: any) => {
//   keyboardHeight.value = e.detail.height;
// };

// 处理取消编辑
const handleCancel = () => {
  emits('cancel');
  emits('update:visible', false);
};

// 处理确认编辑
const handleConfirm = () => {
  emits('confirm', editContent.value);
  emits('update:visible', false);
};
</script>

<style scoped>
/* 包装器 - 提供定位上下文 */
.message-edit-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 999999999;
  /* iOS 特定处理 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* iOS 平台特定样式 */
.message-edit-wrapper.ios-platform {
  /* 使用更高的 z-index */
  z-index: 9999999999;
  /* 强制创建新的层叠上下文 */
  -webkit-transform: translate3d(0, 0, 9999px);
  transform: translate3d(0, 0, 9999px);
  /* 确保覆盖所有系统组件 */
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  /* 强制硬件加速 */
  will-change: transform;
  /* iOS Safari 特定优化 */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.message-edit-container {
  /* 使用 absolute 定位，相对于最近的定位祖先 */
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  transition: padding-bottom 0.2s ease-out;
  z-index: 999999999;

  /* iOS 特定的层级处理 - 使用 translate3d 强制硬件加速 */
  -webkit-transform: translate3d(0, 0, 999px);
  transform: translate3d(0, 0, 999px);

  /* 确保在 iOS 上覆盖系统组件 */
  isolation: isolate;

  /* 强制创建新的层叠上下文 */
  will-change: transform;

  /* iOS Safari 特定优化 */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;

  /* 确保覆盖所有可能的系统层级 */
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;

  /* iOS 特定的层级强制提升 */
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

.message-edit-box {
  width: 690rpx;
  margin: 30rpx;
  background-color: #fff;
  border-radius: 36rpx;
  overflow: hidden;
  border: 2rpx solid #e7eaef;
}

.message-edit-input {
  width: 100%;
  min-height: 120rpx;
  padding: 30rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  box-sizing: border-box;
  border: none;
}

.message-edit-controls {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  align-items: center;
}

.control-button {
  pointer-events: auto;
}

.cancel {
  padding-left: 0;
}

.confirm {
  padding-right: 0;
}

.circle-button {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-button {
  background-color: transparent;
}

.icon-close {
  font-size: 36rpx;
  line-height: 1;
  color: #fff;
  text-align: center;
}

.confirm-button {
  background-color: #7d4dff;
}

.icon-check {
  font-size: 28rpx;
  color: white;
}
</style>
