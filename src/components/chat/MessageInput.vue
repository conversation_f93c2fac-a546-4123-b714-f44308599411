<script setup>
import { ref, computed, watch, nextTick, onUnmounted } from 'vue';
import ai from '@/common/ai';
import { respDims } from '@/utils/respDims';
import LkUploadAll from '../LKUploadall/index.vue';
import LkLottie from '../LkLottie/index';
import LkSvg from '../svg/index';
import MessageVoice from './MessageVoice.vue';
import { useChatStore } from '@/store/chatStore';
import { useUserStore } from '@/store/userStore';
import { RouteConfigManager } from '@/router/config';

// 使用 import 语句导入所有需要的SVG图标
import sendIcon from '/static/chat/send.svg';
import addFileIcon from '/static/chat/addfile.svg';
import voiceIcon from '/static/chat/voice.svg';
import stopIcon from '/static/chat/stop.svg';
import fullscreenIcon from '/static/chat/fullscreen.svg';
import fullscreenExitIcon from '/static/chat/fullscreen_exit.svg';

// 导入常用的文件类型图标
import spaceIcon from '/static/fileTypeIcon/space.svg';
import folderIcon from '/static/fileTypeIcon/folder.svg';
import unknownIcon from '/static/fileTypeIcon/unknown.svg';

// Props定义
const props = defineProps({
  placeholder: {
    type: String,
    default: '有问题尽管问',
  },
  chatting: {
    type: Boolean,
    default: false,
  },
  uploadLimit: {
    type: Object,
    default: () => null,
  },
  sideBarMessage: {
    type: Object,
    default: () => ({
      type: null,
      chatAppId: null,
      step: null,
      message: null,
    }),
  },
  messageType: {
    type: [String, Number],
    default: '',
  },
  file: {
    type: Object,
    default: () => null,
  },
  isMessageEditVisible: {
    type: Boolean,
    default: false,
  },
});

// 事件
const emit = defineEmits([
  'scrollToBottom',
  'send',
  'keyboardChange',
  'uploadExpandChange',
  'inputFocus',
]);

// 响应式状态
const inputLineCount = ref(1);
const content = ref('');
const voice = ref(null);
const bottom = ref(0);
const height = ref(0);
const focused = ref(false);
const keyboardHeight = ref(0);
const uploadExpanded = ref(false);
const uploadList = ref([]);
const showVoiceInput = ref(false);
const textInputRef = ref(null);
const popupRef = ref(null);
const showPopup = ref(false);
const isTextInputFocused = ref(false);
const isVoiceProcessing = ref(false);
const voiceStartTime = ref(0);

watch(uploadList, newList => {
  console.log('uploadList', newList);
});

// 计算属性
const translateY = computed(() => {
  if (!focused.value || !keyboardHeight.value || showVoiceInput.value) {
    return 0;
  }
  // iOS 特殊处理：直接返回键盘高度作为底部偏移
  return keyboardHeight.value;
});

const style = computed(() => {
  console.log('translateY', `${translateY.value}px`);
  return {
    height: `${height.value}px`,
  };
});

// 格式化文件大小
const formatFileSize = size => {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
};

const innerStyle = computed(() => {
  console.log('bottom', bottom.value, 'translateY', translateY.value);
  // 当键盘弹起时，使用fixed定位
  if (focused.value && keyboardHeight.value > 0) {
    return {
      position: 'fixed',
      bottom: `${translateY.value}px`,
      left: '0',
      right: '0',
      top: 'unset',
    };
  }
  // 默认使用relative定位，让chat-footer能够推起MessageInput
  return {
    position: 'relative',
    bottom: '0px',
    top: 'unset',
  };
});

const unsendableReason = computed(() => {
  if (props.chatting) {
    return '请等待聊天结束';
  }
  if (!content.value) {
    return '内容为空';
  }
  return '';
});

const sendable = computed(() => {
  return !unsendableReason.value;
});

const showIconWrap = computed(() => {
  return props.uploadLimit && (props.uploadLimit.canSelectImg || props.uploadLimit.canSelectFile);
});

const canSelectImg = computed(() => {
  return props.uploadLimit ? props.uploadLimit.canSelectImg : false;
});

const canSelectFile = computed(() => {
  return props.uploadLimit ? props.uploadLimit.canSelectFile : false;
});

const maxFiles = computed(() => {
  return props.uploadLimit ? props.uploadLimit.maxFiles : 20;
});

// 使用防抖来避免死循环
const showExpandButton = ref(false);
let expandButtonTimer = null;
let isLayoutChanging = ref(false); // 添加一个标志来防止循环触发

// 监听行数变化，使用防抖更新展开状态
watch(
  inputLineCount,
  newLineCount => {
    if (expandButtonTimer) {
      clearTimeout(expandButtonTimer);
    }

    // 如果正在布局变化中，不处理此次更新
    if (isLayoutChanging.value) return;

    expandButtonTimer = setTimeout(() => {
      const shouldExpand = newLineCount > 6;
      if (showExpandButton.value !== shouldExpand) {
        isLayoutChanging.value = true; // 设置标记，正在切换布局
        showExpandButton.value = shouldExpand;

        // 给足够的时间让布局稳定后再允许下一次状态变化
        setTimeout(() => {
          isLayoutChanging.value = false;
        }, 300);
      }
    }, 200); // 增加到200ms防抖延迟
  },
  { immediate: true }
);

watch(
  uploadList,
  () => {
    refeshPage();
  },
  { deep: true }
);

watch(
  () => translateY.value,
  () => {
    emit('scrollToBottom');
  }
);

watch(
  () => voice.value?.state,
  val => {
    console.log('voice state changed:', val);
    if (!val || val !== 'recording') {
      // 如果voice状态不是录音中，延迟一下再重算高度
      nextTick(() => {
        getHeight(50);
      });
    }
  }
);

watch(
  () => props.sideBarMessage,
  newVal => {
    if (newVal) {
      if (newVal.message) {
        content.value = newVal.message;
      }
    }
  },
  { deep: true, immediate: true }
);

// 监听messageType和file的变化
watch(
  [() => props.messageType, () => props.file],
  ([newMessageType, newFile]) => {
    console.log('messageType changed:', newMessageType);

    // 根据messageType设置输入框内容
    if (newMessageType === '1' || newMessageType === 1) {
      console.log('newMessageType', newMessageType);
      // 思维导图
      content.value = '生成思维导图';
    } else if (newMessageType === '2' || newMessageType === 2) {
      console.log('newMessageType', newMessageType);
      // 文档解读
      content.value = '文档解读';
    }

    // 处理文件展示
    if (newFile && Object.keys(newFile).length > 0) {
      // 检查文件是否已经存在于uploadList中，避免重复添加
      const fileExists = uploadList.value.some(
        item => item.fileName === newFile.fileName && item.fileSize === newFile.fileSize
      );

      if (!fileExists) {
        // 将file添加到uploadList中进行展示
        uploadList.value = [newFile];
        // 刷新页面高度
        refeshPage();
      }
    } else if (!newFile || Object.keys(newFile).length === 0) {
      // 如果没有文件，清空uploadList（仅在messageType变化时）
      if (
        newMessageType === '1' ||
        newMessageType === 1 ||
        newMessageType === '2' ||
        newMessageType === 2
      ) {
        uploadList.value = [];
        refeshPage();
      }
    }
  },
  { deep: true, immediate: true }
);

const fileIcon = item => {
  return `/static/fileTypeIcon/${item.fileType}.svg`;
};

// 方法
function getBottom(delay = 0) {
  if (delay) {
    setTimeout(() => getBottom(0), delay);
    return;
  }

  uni
    .createSelectorQuery()
    .select('#message-input')
    .boundingClientRect(res => {
      bottom.value = res ? res.bottom : 0;
    })
    .exec();
}

function closePopup() {
  if (popupRef.value) {
    popupRef.value.close();
  }
  showPopup.value = false;
  // 关闭弹窗时确保键盘状态正确
  emit('keyboardChange', false);
}

function openPopup() {
  if (popupRef.value) {
    popupRef.value.open('bottom');
  }
  showPopup.value = true;
  // 打开弹窗时隐藏键盘
  focused.value = false;
  isTextInputFocused.value = false;
  emit('keyboardChange', false);
}

function onPopupChange(e) {
  // uni-popup 状态变化回调
  showPopup.value = e.show;
}

function closeVoiceInput() {
  showVoiceInput.value = false;
  // If voice is active, stop it.
  if (voice.value && voice.value.state === 'recording') {
    voice.value?.stop('cancel');
    voice.value = null;
  }
  // 清理所有语音相关状态
  isVoiceProcessing.value = false;
  voice.value = null;
  // 关闭语音输入时，确保键盘状态正确
  emit('keyboardChange', false);
  // 延迟重算高度，确保UI更新完成
  nextTick(() => {
    getHeight(10);
    getBottom(10);
  });
}

function getHeight(delay = 0) {
  if (delay) {
    setTimeout(() => getHeight(0), delay);
    return;
  }

  uni
    .createSelectorQuery()
    .select('#message-input-inner')
    .boundingClientRect(res => {
      console.log('height', res);
      height.value = res ? res.height : 0;
    })
    .exec();
}

function onKeyboardHeightChange(e) {
  // if (!isIOS()) {
  //   keyboardHeight.value = e.detail.height;
  // }
}

// 检测是否为 iOS 设备
function isIOS() {
  const systemInfo = uni.getSystemInfoSync();
  return systemInfo.platform === 'ios';
}

function onFocus() {
  focused.value = true;
  // 当textarea获取焦点时关闭LkUploadAll
  uploadExpanded.value = false;
  emit('uploadExpandChange', false);
  // 发射键盘显示事件
  emit('keyboardChange', true);
  // 发射输入框获取焦点事件，用于关闭longpress-popup
  emit('inputFocus');
  refeshPage();
}

function onBlur() {
  focused.value = false;
  // 发射键盘隐藏事件
  emit('keyboardChange', false);
  // 延迟重置键盘高度，避免闪烁
  setTimeout(() => {
    keyboardHeight.value = 0;

    // iOS 特殊处理：恢复页面样式
    if (isIOS()) {
      document.body.classList.remove('ios-keyboard-active');
    }
  }, 100);
  isTextInputFocused.value = false;
}

function onLineChange(e) {
  const newLineCount = e.detail.lineCount;

  // 防止无效的行数变化
  if (newLineCount && newLineCount !== inputLineCount.value) {
    inputLineCount.value = newLineCount;

    // 延迟执行高度计算，避免与布局切换冲突
    nextTick(() => {
      getHeight(20);
    });
  }
}

function startVoice() {
  if (!checkLogin()) {
    return;
  }
  voice.value = ai.startVoice({
    onResult: res => {
      content.value = res;
      isVoiceProcessing.value = false;
      voice.value = null; // 识别完成后清空voice
    },
  });
}

function stopVoice(reason = '') {
  voice.value?.stop(reason);
  voice.value = null; // 清空voice状态
}

function toggleVoiceInput() {
  if (!checkLogin()) {
    return;
  }
  showVoiceInput.value = !showVoiceInput.value;

  if (showVoiceInput.value) {
    focused.value = false;
    keyboardHeight.value = 0;
    isTextInputFocused.value = false;
    uploadExpanded.value = false;
    emit('uploadExpandChange', false);
    // 语音输入时隐藏键盘，发射键盘隐藏事件
    emit('keyboardChange', false);
    refeshPage();
  }

  refeshPage();
}

function handleVoiceRecordingStart() {
  // 开始录音逻辑
  voiceStartTime.value = Date.now(); // 记录开始时间
  voice.value = ai.startVoice({
    onResult: res => {
      console.log('handleVoiceRecordingStart', res);
      content.value = res;
      isVoiceProcessing.value = false; // 语音识别完成，关闭loading
      voice.value = null; // 识别完成后清空voice
      refeshPage(); // 刷新页面高度
    },
  });

  focused.value = false;
  keyboardHeight.value = 0;
}

function handleVoiceRecordingFinish() {
  // 完成录音逻辑
  const recordingDuration = Date.now() - voiceStartTime.value;
  const minDuration = 500; // 最小录音时长（毫秒）

  if (recordingDuration < minDuration) {
    // 录音时间太短
    uni.showToast({
      title: '说话时间太短',
      icon: 'none',
      duration: 1500,
    });
    voice.value?.stop('cancel');
    voice.value = null; // 清空voice状态
    showVoiceInput.value = false;
    isVoiceProcessing.value = false;
    refeshPage();
    return;
  }

  isVoiceProcessing.value = true; // 开始语音识别，显示loading
  voice.value?.stop();
  // 不要在这里清空voice，等待onResult回调
  showVoiceInput.value = false;
  refeshPage();
}

function handleVoiceRecordingCancel() {
  // 取消录音逻辑
  voice.value?.stop('cancel');
  voice.value = null; // 清空voice状态
  showVoiceInput.value = false;
  isVoiceProcessing.value = false; // 取消时也要关闭loading
  refeshPage();
}

function handleSwitchToUpload() {
  showVoiceInput.value = false;
  voice.value = null; // 确保清空
  uploadExpanded.value = true;
  emit('uploadExpandChange', true);
  nextTick(() => {
    isTextInputFocused.value = true;
    getHeight(10); // 重算高度
  });
  refeshPage();
}

function handleSwitchToKeyboard() {
  showVoiceInput.value = false;
  voice.value = null; // 确保清空
  refeshPage();
  nextTick(() => {
    isTextInputFocused.value = true;
    getHeight(10); // 重算高度
  });
}

function send() {
  if (!sendable.value) {
    uni.showToast({
      title: unsendableReason.value,
      icon: 'none',
    });
    return;
  }

  if (!checkLogin()) {
    uni.showModal({
      content: '登录后继续使用',
      success: res => {
        if (res.confirm) {
          uni.navigateTo({ url: RouteConfigManager.getLoginPage() });
        }
      },
    });
    return;
  }

  closePopup();

  emit('send', {
    content: content.value,
    uploadList: uploadList.value,
  });

  content.value = '';
  uploadList.value = [];
  uploadExpanded.value = false;
  emit('uploadExpandChange', false);
  refeshPage();
}

function setInput(input) {
  content.value = typeof input == 'string' ? input : input?.content || '';
}

function reset() {
  content.value = '';
  stopVoice('cancel');
  voice.value = null; // 确保清空
}

function showUploadList() {
  uploadExpanded.value = !uploadExpanded.value;
  // 发射上传组件展开状态变化事件
  emit('uploadExpandChange', uploadExpanded.value);
  if (uploadExpanded.value) {
    // 当展开上传组件时，确保键盘隐藏
    focused.value = false;
    isTextInputFocused.value = false;
    emit('keyboardChange', false);
    refeshPage();
  }
}

function refeshPage() {
  nextTick(() => {
    getHeight(1);
  });
}

function deleteImage(index) {
  uploadList.value.splice(index, 1);
  refeshPage();
}

function extension(val) {
  if (!val) {
    return;
  }
  const filename = val;
  const extension = filename.split('.').pop();
  const firstChar = extension.charAt(0);
  return firstChar.toUpperCase();
}

function getExtensionColor(filename) {
  const match = extension(filename);
  switch (match) {
    case 'P':
      return '#ef6000';
    case 'D':
      return '#15a4ff';
    case 'X':
      return '#40d48d';
    default:
      return '#767a82'; // 默认颜色
  }
}

function getAfterColor(filename) {
  const match = extension(filename);
  switch (match) {
    case 'P':
      return '#ffc9a4';
    case 'D':
      return '#89d1ff';
    case 'X':
      return '#9affcf';
    default:
      return '#ccc'; // 默认颜色
  }
}

function openPrewvieIimg(images, index) {
  uni.previewImage({
    urls: images,
    current: index,
  });
}

function handleUploadFiles(files) {
  console.log('handleUploadFiles', files);
  uploadList.value = files;
  refeshPage();
}

function checkLogin() {
  const token = uni.getStorageSync('token') || '';
  if (!token) {
    uni.navigateTo({ url: RouteConfigManager.getLoginPage() });
    return false;
  }
  return true;
}

function handleExpandContent() {
  // TODO: 实现放大内容的逻辑，例如弹出一个模态框显示完整文本
  console.log('放大按钮被点击');
  openPopup();
}

// LkUploadAll 动画结束后的回调
function onUploadTransitionEnd() {
  console.log('LkUploadAll transition ended. Refreshing page.');
  refeshPage();
}

// 暴露组件方法，使父组件可以调用
defineExpose({
  setInput,
  reset,
});

// 生命周期钩子
onUnmounted(() => {
  stopVoice('cancel');
  // 清理定时器
  if (expandButtonTimer) {
    clearTimeout(expandButtonTimer);
  }
  // 清理 iOS 样式
  if (isIOS()) {
    document.body.classList.remove('ios-keyboard-active');
  }
});

// 初始化
getBottom(50);
getHeight(50);
</script>

<template>
  <view id="message-input" class="message-input" :style="style" v-show="!isMessageEditVisible">
    <view id="message-input-inner" class="message-input-inner" :style="innerStyle">
      <view class="message-input-main">
        <template v-if="showVoiceInput">
          <up-overlay opacity="0" :show="showVoiceInput" @close="closeVoiceInput">
            <MessageVoice
              class="voice-input"
              @start-recording="handleVoiceRecordingStart"
              @finish-recording="handleVoiceRecordingFinish"
              @cancel-recording="handleVoiceRecordingCancel"
              @switch-to-upload="handleSwitchToUpload"
              @switch-to-keyboard="handleSwitchToKeyboard"
            />
          </up-overlay>
        </template>
        <template v-else>
          <view class="text-input-box" :class="{ 'has-files': uploadList.length > 0 }">
            <!-- Moved File Preview List -->
            <view class="upload-preview-list" v-if="uploadList.length > 0">
              <view class="upload-preview-item" v-for="(item, index) in uploadList" :key="index">
                <view
                  class="item-content-wrapper"
                  @tap.stop="
                    item.type === 'image_url' &&
                    openPrewvieIimg(
                      uploadList.filter(f => f.type === 'image_url').map(f => f.fileUrl),
                      uploadList.filter(f => f.type === 'image_url').indexOf(item)
                    )
                  "
                >
                  <view class="upload-image-list-item" v-if="item.type === 'image_url'">
                    <image mode="aspectFill" class="cover" :src="item.fileUrl" />
                  </view>

                  <view v-if="item.type !== 'image_url'" class="upload-file-list-item">
                    <view class="file-icon-placeholder">
                      <LkSvg
                        class="fileIcon"
                        width="42px"
                        height="42px"
                        :src="fileIcon(item)"
                        :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                      />
                    </view>
                    <view class="file-details-container">
                      <view class="file-name">
                        {{ item.fileName }}
                      </view>
                      <view class="file-info">
                        {{ item.fileType }}, {{ formatFileSize(item.fileSize) }}
                      </view>
                    </view>
                  </view>
                </view>
                <view class="delete-btn" @tap.stop="deleteImage(index)">
                  <LkSvg
                    class="fileIcon"
                    width="42px"
                    height="42px"
                    :src="`/static/chat/close-circle-filled.svg`"
                    :errorSrc="`/static/chat/close-circle-filled.svg`"
                  />
                </view>
              </view>
            </view>

            <!-- 统一的输入控制区域，通过CSS类控制布局 -->
            <view class="input-controls-container" :class="{ 'expanded-layout': showExpandButton }">
              <!-- 输入框区域 -->
              <view class="input-area">
                <!-- 在普通布局中显示文件上传按钮 -->
                <view
                  v-if="showIconWrap && !showExpandButton"
                  class="icon-wrap"
                  @tap="showUploadList"
                >
                  <LkSvg v-if="!uploadExpanded" width="48rpx" height="48rpx" :src="addFileIcon" />
                  <text v-else class="close-icon">×</text>
                </view>

                <uv-textarea
                  class="text-input"
                  placeholder-class="text-input-placeholder"
                  v-model="content"
                  :placeholder="isVoiceProcessing ? '语音识别中...' : placeholder"
                  :show-confirm-bar="false"
                  :keyboard-height="keyboardHeight"
                  :auto-height="true"
                  :height="Math.min(Math.max(inputLineCount * 20, 20), 120)"
                  :maxlength="-1"
                  :adjust-position="false"
                  :disable-default-padding="true"
                  :hold-keyboard="true"
                  confirm-type="send"
                  :focus="isTextInputFocused"
                  :disabled="isVoiceProcessing"
                  border="none"
                  @linechange="onLineChange"
                  @focus="onFocus"
                  @blur="
                    isTextInputFocused = false;
                    onBlur();
                  "
                  @keyboardheightchange="onKeyboardHeightChange"
                  @confirm="send"
                  ref="textInputRef"
                />

                <!-- 语音识别loading动画 -->
                <view v-if="isVoiceProcessing" class="voice-processing-loading">
                  <view class="loading-dot"></view>
                  <view class="loading-dot"></view>
                  <view class="loading-dot"></view>
                </view>
              </view>

              <!-- 按钮区域 -->
              <view class="buttons-area">
                <!-- 在展开布局中显示文件上传按钮 -->
                <view
                  v-if="showIconWrap && showExpandButton"
                  class="icon-wrap"
                  @tap="showUploadList"
                >
                  <LkSvg v-if="!uploadExpanded" width="48rpx" height="48rpx" :src="addFileIcon" />
                  <text v-else class="close-icon">×</text>
                </view>

                <!-- 右侧按钮组 -->
                <view class="right-buttons-group">
                  <view
                    v-if="showExpandButton && !isVoiceProcessing"
                    class="expand-button"
                    @tap="handleExpandContent"
                  >
                    <LkSvg width="48rpx" height="48rpx" :src="fullscreenIcon" />
                  </view>

                  <view class="voice-wrap" @tap="toggleVoiceInput" v-if="!showExpandButton">
                    <LkSvg width="48rpx" height="48rpx" :src="voiceIcon" />
                  </view>

                  <view
                    v-if="voice && voice.state && voice.state === 'recording'"
                    class="voice-wrap"
                    @tap="() => stopVoice()"
                  >
                    <LkSvg width="48rpx" height="48rpx" :src="stopIcon" />
                  </view>

                  <view
                    class="send"
                    @tap="send()"
                    :style="{ backgroundColor: sendable ? '#7D4DFF' : '#E5E7EB' }"
                  >
                    <LkSvg width="36rpx" height="36rpx" :src="sendIcon" />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
      </view>
      <up-transition
        :show="uploadExpanded"
        mode="slide-up"
        :duration="350"
        :custom-style="{
          transitionTimingFunction: 'cubic-bezier(0.34, 1.3, 0.64, 1)',
        }"
        @after-leave="onUploadTransitionEnd"
      >
        <LkUploadAll
          v-model="uploadList"
          :maxCount="maxFiles"
          :canSelectImg="canSelectImg"
          :canSelectFile="canSelectFile"
        />
      </up-transition>
      <LkLottie
        v-if="voice && voice.state && voice.state === 'recording'"
        name="waveform"
        :custom-style="{
          width: '486rpx',
          height: '92rpx',
          marginTop: '28rpx',
          alignSelf: 'center',
        }"
      ></LkLottie>

      <uni-popup
        ref="popupRef"
        type="bottom"
        :border-radius="20"
        :safe-area="false"
        @change="onPopupChange"
      >
        <view class="popup-content">
          <view class="popup-header" @touchmove.stop.prevent>
            <view class="popup-title" @tap="closePopup">
              <LkSvg width="48rpx" height="48rpx" :src="fullscreenExitIcon" />
            </view>
          </view>
          <view class="popup-textarea-wrapper" @touchmove.stop>
            <scroll-view
              class="popup-scroll-container"
              scroll-y
              :enhanced="true"
              :show-scrollbar="true"
              :enable-passive="false"
            >
              <textarea
                v-model="content"
                class="popup-textarea"
                placeholder="有问题尽管问..."
                :show-confirm-bar="false"
                :adjust-position="false"
                :maxlength="-1"
                :auto-height="true"
              />
            </scroll-view>
          </view>
        </view>
        <view class="popup-footer">
          <view
            class="popup-send"
            @tap="send()"
            :style="{ backgroundColor: sendable ? '#7D4DFF' : '#F0F0F0' }"
          >
            <LkSvg
              width="48rpx"
              height="48rpx"
              :src="sendIcon"
              :color="sendable ? '#FFFFFF' : '#A8ABB2'"
            />
          </view>
        </view>
        <!-- iOS 底部安全区域填充 -->
        <view class="popup-safe-area-bottom"></view>
      </uni-popup>
    </view>
  </view>
</template>

<style scoped>
.message-input-inner {
  display: flex;
  flex-direction: column;
  padding: 24rpx 24rpx 0rpx 24rpx;
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
  margin-bottom: var(--window-bottom);
  flex-shrink: 0;
  transition: bottom 0.3s ease-in-out;
  z-index: 1000;
}

.message-input-main {
  display: flex;
  align-items: center;

  /* overflow: hidden; */
  position: relative;
}

.voice-input {
  position: fixed;
  display: flex;
  flex-direction: column;
  align-items: center;
  bottom: 60rpx;
  left: 0;
  right: 0;
  z-index: 10080;
}

.text-input-box {
  flex: 1;
  display: flex;
  flex-direction: column; /* Changed to column */
  padding: 16rpx 24rpx; /* Adjusted padding */
  min-height: auto; /* Adjusted min-height */
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 40rpx; /* Adjusted border-radius from image */
  /* align-items: center; Removed */
  border: 2rpx solid #f0f0f0;
  transition: border-color 0.2s ease-in-out;
  box-shadow:
    0px 2px 4px 0px rgba(0, 0, 0, 0.05),
    0px 0px 16.7px -4px rgba(0, 0, 0, 0.09);
}

.text-input-box:focus-within,
.text-input-box.has-files {
  border-color: #7d4dff;
}

.input-controls-row {
  display: flex;
  align-items: center; /* Changed from flex-end to center */
  width: 100%;
  overflow: hidden;
}

.text-input {
  flex: 1;
  min-width: 0;
  padding: 0 16rpx;
  font-size: 28rpx;
  background: transparent;
  border: none;
  max-height: 200rpx;
  overflow-y: auto;
  align-self: stretch;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

/* uv-textarea 样式覆盖 */
:deep(.text-input .uv-textarea) {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  min-height: 40rpx !important;
  max-height: 200rpx !important;
  overflow-y: auto !important;
}

:deep(.text-input .uv-textarea__field) {
  padding: 0 !important;
  font-size: 28rpx !important;
  background: transparent !important;
  border: none !important;
  min-height: 40rpx !important;
  line-height: 40rpx !important;
  resize: none !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  overflow-y: auto !important;
  scrollbar-width: thin !important;
  color: #000 !important;
}

:deep(.text-input-placeholder) {
  font-size: 16px;
  color: #86909c !important;
}

.voice-wrap {
  display: flex;
  align-items: center;
  padding: 0 10rpx; /* Adjusted padding */
  /* align-self: flex-end; Removed */
  /* margin-bottom: 6rpx; Removed */
}

.popup-send {
  flex: 0 0 auto;
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-left: 16rpx; /* Adjusted margin */
  /* align-self: flex-end; Removed */
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s ease-in-out;
}

.send {
  flex: 0 0 auto;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-left: 16rpx; /* Adjusted margin */
  /* align-self: flex-end; Removed */
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s ease-in-out;
  /* margin-bottom: 6rpx; Removed */
}

.icon-wrap {
  display: flex;
  align-items: center;
  /* align-self: flex-end; Removed */
  margin-right: 10rpx; /* Space between + and textarea */
  /* margin-bottom: 6rpx; Removed */
}

.plus-icon {
  font-size: 48rpx;
  color: #2d2e30;
}
.plus-icon::before {
  content: '+';
}

.close-icon {
  /* For the 'x' when upload is expanded */
  font-size: 48rpx;
  color: #2d2e30;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-preview-list {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  overflow-y: visible; /* 只在y轴上保持visible，确保删除按钮不被裁剪 */
  white-space: nowrap;
  padding: 0; /* 移除padding */
  width: 100%;
  margin-bottom: 10rpx; /* Space below the list */
  padding-top: 10rpx;
  /* 添加滚动条样式 */
  scrollbar-width: thin;
}

.upload-preview-item {
  position: relative;
  margin-right: 16rpx;
  background-color: #f7f8fa;
  border-radius: 12rpx; /* Rounded corners for item */
  padding: 10rpx;
  display: flex; /* To help align content if needed */
  align-items: center; /* To center content vertically */
  overflow: visible; /* 确保子元素不会被裁剪 */
  flex-shrink: 0; /* 防止项目被压缩 */
  min-width: 120rpx; /* 设置最小宽度，确保有足够空间触发滚动 */
}
.upload-preview-item:last-child {
  margin-right: 0;
}

.item-content-wrapper {
  display: flex;
  align-items: center;
}

.upload-image-list-item {
  /* display: flex; Removed, parent is flex */
  /* flex-direction: row; Removed */
  /* align-items: center; Removed */
  position: relative;
  font-size: 24rpx; /* Reduced font size from image */
  /* padding: 10rpx; Removed, parent has padding */
  /* border-radius: 8rpx; Removed */
  width: 100rpx; /* Fixed width for image container */
  height: 100rpx; /* Fixed height for image container */
}

.upload-image-list-item .cover {
  object-fit: cover;
  height: 100%;
  width: 100%;
  border-radius: 8rpx; /* Cover image radius */
}

.delete-btn {
  width: 36rpx;
  height: 36rpx;
  position: absolute;
  top: -12rpx; /* Position outside the item a bit */
  right: -12rpx; /* Position outside the item a bit */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx; /* Size of 'x' */
  line-height: 1;
  cursor: pointer;
  z-index: 1001; /* 提高z-index值 */
}
.delete-btn text {
  transform: translateY(-2rpx); /* Minor adjustment for 'x' centering */
}

.upload-file-list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #6a6a6d;
  font-size: 20rpx;
  height: 100rpx;
  box-sizing: border-box;
}

.file-icon-placeholder {
  width: 60rpx;
  border-radius: 8rpx;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.file-details-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  overflow: hidden; /* To enable text-overflow */
  max-width: 280rpx; /* Limit width to allow for icon and padding */
}

.file-name {
  font-size: 26rpx; /* Adjusted from image */
  font-weight: 500;
  color: #1d2129;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 240rpx; /* Take available width in details container */
  margin-bottom: 4rpx;
}

.file-info {
  font-size: 22rpx; /* Adjusted from image */
  color: #888e99; /* Adjusted from image */
  font-weight: 400;
  /* width: 210rpx; Removed, let it be natural */
}

/* 统一的输入控制容器 */
.input-controls-container {
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

/* 展开布局时的样式 */
.input-controls-container.expanded-layout {
  flex-direction: column;
  align-items: stretch;
}

/* 输入区域 */
.input-area {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 展开布局时的输入区域 */
.expanded-layout .input-area {
  margin-bottom: 16rpx; /* 输入框和按钮区域之间的间距 */
}

/* 按钮区域 */
.buttons-area {
  display: flex;
  align-items: center;
  gap: 16rpx; /* 按钮之间的间距 */
}

/* 展开布局时的按钮区域 */
.expanded-layout .buttons-area {
  justify-content: space-between; /* 文件上传按钮在左边，其他按钮在右边 */
  width: 100%;
}

/* 右侧按钮组 */
.right-buttons-group {
  display: flex;
  align-items: center;
}

/* 展开布局中的按钮样式调整 */
.expanded-layout .voice-wrap {
  margin: 0; /* 移除margin */
  padding: 0 10rpx;
}

.expanded-layout .expand-button {
  margin: 0; /* 移除margin */
  padding: 0 10rpx;
}

.expanded-layout .send {
  margin: 0; /* 移除margin，在展开布局中 */
}

.expanded-layout .icon-wrap {
  margin: 0; /* 移除margin */
  padding: 0 10rpx;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  /* align-self: flex-end; Removed */
  /* margin-bottom: 6rpx; Removed */
  margin-left: 10rpx; /* Space from textarea */
  margin-right: 10rpx; /* Space to next icon */
  padding: 0 10rpx;
  height: 48rpx; /* Match other icons touch area */
  /* background-color: #f0f0f0; */ /* Optional: for visual debugging */
  /* border: 1rpx solid #ccc; */ /* Optional */
}

.expand-button-text {
  font-size: 28rpx;
  color: #007bff; /* Standard action color */
  font-weight: 500;
}
.popup-content {
  padding: 32rpx;
  height: 70vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #ffffff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  position: relative;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  margin-bottom: 15rpx;
}

.popup-textarea-wrapper {
  flex: 1;
  width: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  position: relative;
}

.popup-scroll-container {
  flex: 1;
  width: 100%;
  height: 100%;
  max-height: 80vh;
}

.popup-textarea {
  width: 100%;
  min-height: 400rpx;
  padding: 15rpx;
  font-size: 28rpx;
  line-height: 1.4;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  box-sizing: border-box;
  resize: none;
  word-wrap: break-word;
  outline: none;
  border: none;
  background: transparent;
}

/* scroll-view 滚动条样式 */
:deep(.popup-scroll-container) {
  scrollbar-width: thin;
}

:deep(.popup-scroll-container::-webkit-scrollbar) {
  width: 6rpx;
}

:deep(.popup-scroll-container::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 3rpx;
}

:deep(.popup-scroll-container::-webkit-scrollbar-thumb) {
  background: rgba(125, 77, 255, 0.4);
  border-radius: 3rpx;
}

:deep(.popup-scroll-container::-webkit-scrollbar-thumb:hover) {
  background: rgba(125, 77, 255, 0.6);
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
  padding: 28rpx;
  background-color: #ffffff;
}

/* iOS 底部安全区域处理 */
.popup-safe-area-bottom {
  width: 100%;
  height: env(safe-area-inset-bottom);
  height: constant(safe-area-inset-bottom); /* iOS < 11.2 兼容 */
  background-color: #ffffff;
  flex-shrink: 0;
}

:deep(.u-transition) {
  transition-property: opacity, transform !important;
}

:deep(.u-slide-up-enter-active),
:deep(.u-slide-up-leave-active) {
  transition-property: transform, opacity !important;
  transition-timing-function: cubic-bezier(0.34, 1.3, 0.64, 1) !important;
}

:deep(.u-slide-up-enter-from),
:deep(.u-slide-up-leave-to) {
  transform: translate3d(0, 100%, 0) !important;
  opacity: 0 !important;
}

.voice-processing-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 16rpx;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #7d4dff;
  border-radius: 50%;
  margin: 0 4rpx;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.text-input:disabled {
  color: #999;
}

/* 滚动条样式 */
:deep(.text-input .uv-textarea__field::-webkit-scrollbar) {
  width: 4rpx !important;
}

:deep(.text-input .uv-textarea__field::-webkit-scrollbar-track) {
  background: transparent !important;
}

:deep(.text-input .uv-textarea__field::-webkit-scrollbar-thumb) {
  background: rgba(125, 77, 255, 0.3) !important;
  border-radius: 2rpx !important;
}

:deep(.text-input .uv-textarea__field::-webkit-scrollbar-thumb:hover) {
  background: rgba(125, 77, 255, 0.5) !important;
}

/* uni-popup 样式覆盖 */
:deep(.uni-popup__wrapper-box) {
  border-top-left-radius: 20rpx !important;
  border-top-right-radius: 20rpx !important;
  overflow: hidden !important;
}

:deep(.uni-popup) {
  z-index: 10000 !important;
}

/* 修复 iOS 底部空隙问题 */
:deep(.uni-popup__wrapper) {
  padding-bottom: 0 !important;
}

:deep(.uni-popup.uni-popup--bottom) {
  padding-bottom: 0 !important;
}

/* 确保弹窗内容紧贴底部 */
:deep(.uni-popup--bottom .uni-popup__wrapper-box) {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* 文件预览列表滚动条样式 */
:deep(.upload-preview-list::-webkit-scrollbar) {
  height: 4rpx;
}

:deep(.upload-preview-list::-webkit-scrollbar-track) {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2rpx;
}

:deep(.upload-preview-list::-webkit-scrollbar-thumb) {
  background: rgba(125, 77, 255, 0.3);
  border-radius: 2rpx;
}

:deep(.upload-preview-list::-webkit-scrollbar-thumb:hover) {
  background: rgba(125, 77, 255, 0.5);
}

/* iOS 键盘处理相关样式 */
.message-input.ios-keyboard-active {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 100vh;
  overflow: hidden;
}
</style>
