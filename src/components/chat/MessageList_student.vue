<template>
  <view class="message-list" :style="customStyle">
    <!-- 欢迎消息 -->
    <view v-if="welcomeText" class="welcome-message top-welcome">
      <view class="welcome-content">
        <view class="welcome-message-content">
          <template v-if="!!resource && chatType !== '25'">
            <view class="resource-details-welcome-message">
              <view class="file-icon-welcome-message-icon">
                <image
                  :src="
                    fileIcon({
                      fileType: resource.fileFormatId,
                      fileName: resource.fileName,
                      format: resource.fileFormatName,
                    })
                  "
                />
              </view>
              <!-- 资源详情 -->
              <view class="resource-details">
                <!-- 资源名称 -->
                <view class="resource-name">
                  {{ resource.title }}
                  <LkSvg
                    v-if="resource.curation === 1"
                    class="curation-icon"
                    width="16px"
                    height="16px"
                    src="/static/learning-resource/suggestions.svg"
                  />
                </view>

                <!-- 标签区域 -->
                <view class="tags-container">
                  <!-- 资源标签 -->
                  <view class="resource-tags">
                    <text v-if="resource.resourceTypeName" class="tag">
                      {{ resource.resourceTypeName }}
                    </text>
                    <text v-if="resource.subjectName" class="tag">
                      {{ resource.subjectName }}
                    </text>
                    <text v-if="resource.gradeName" class="tag">
                      {{ resource.gradeName }}
                    </text>
                  </view>
                </view>

                <!-- 文件信息 -->
                <view class="resource-meta-bottom">
                  <view class="resource-meta">
                    <view class="file-size-container">
                      <LkSvg
                        width="18px"
                        height="18px"
                        src="/static/learning-resource/file_more_line.svg"
                      />
                      <view class="file-size">{{ formatFileSize(resource.fileSize || 0) }}</view>
                      <view
                        style="margin: 0 10px; background-color: #86909c; width: 1px; height: 12px"
                      ></view>
                      <LkSvg
                        width="18px"
                        height="18px"
                        src="/static/learning-resource/time_line.svg"
                      />
                      <view class="file-size">{{ formatUpdateTime(resource.updateTime) }}</view>
                    </view>
                    <view class="file-size-container-right">
                      <view class="file-tag-wrapper">
                        <text class="tag" :class="getSourceClass(resource.attribution)">
                          {{ getAttributionText(resource.attribution) }}
                        </text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </template>
          <template v-for="(item, index) in parsedText" :key="index">
            <view class="message-content">
              <LKMarkdown v-if="item.type === 'text'" :source="item.content" />
              <text
                v-else-if="item.type === 'link'"
                class="dynamic-link"
                @click="handleLinkClick(item.content)"
                >{{ item.content }}</text
              >
            </view>
          </template>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view
      v-for="(item, index) in sortedList"
      :key="item.dataId || index"
      class="message-item"
      :class="[item.obj === ChatRoleEnum.human ? 'user-message-item' : 'ai-message-item']"
      @tap="handleMessageListTap"
    >
      <!-- 用户消息 -->
      <template v-if="item.obj === ChatRoleEnum.human">
        <view class="user-message-wrapper">
          <!-- 文本消息 -->
          <view v-if="getMessageContent(item)" class="message-content user-message-content">
            <view
              class="message-bubble user-message"
              :id="`msg-bubble-${item.dataId || index}`"
              @longpress="showMessageActions(item, index, $event)"
            >
              {{ getMessageContent(item) }}
            </view>
          </view>

          <!-- 文件列表 - 独立容器 -->
          <view v-if="hasFiles(item)" class="user-file-list-wrapper">
            <view class="user-file-list">
              <!-- 图片文件网格布局 -->
              <view v-if="getDisplayImages(item).length > 0" class="user-image-grid">
                <view
                  v-for="(file, fileIndex) in getDisplayImages(item)"
                  :key="`image-${fileIndex}`"
                  class="user-image-item"
                  @tap="openFile(file)"
                >
                  <image :src="file.url" class="user-image-preview" mode="aspectFill"></image>
                </view>
              </view>

              <!-- 非图片文件列表 -->
              <view v-if="getDisplayDocuments(item).length > 0" class="user-document-list">
                <view
                  v-for="(file, fileIndex) in getDisplayDocuments(item)"
                  :key="`doc-${fileIndex}`"
                  class="user-file-item"
                  @tap="openFile(file)"
                >
                  <view class="user-file-document">
                    <view class="user-file-card">
                      <view class="file-icon-placeholder">
                        <LkSvg
                          class="fileIcon"
                          width="42px"
                          height="42px"
                          :src="fileIcon(item)"
                          :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                        />
                      </view>
                      <view class="file-details-container">
                        <view class="file-name">
                          {{ file.name }}
                        </view>
                        <view class="file-info">
                          {{ file.name.split('.').pop() }}, {{ formatFileSize(file.size) }}
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 展开/收起按钮 -->
              <view
                v-if="getFiles(item).length > 3"
                class="user-expand-toggle"
                @tap.stop="toggleFileListExpand(item.dataId || '')"
              >
                <text>{{ expandedFileListsMap[item.dataId || ''] ? '收起' : '展开' }}</text>
                <LkSvg
                  v-if="expandedFileListsMap[item.dataId || '']"
                  width="24rpx"
                  height="24rpx"
                  src="/static/chat/chevron-down.svg"
                />
                <LkSvg v-else width="24rpx" height="24rpx" src="/static/chat/chevron-up.svg" />
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- AI 消息 -->
      <template v-else>
        <view class="message-content ai-message-content">
          <!-- 加载状态 -->

          <!-- 消息内容 -->
          <view class="message-bubble ai-message">
            <!-- 加载指示器 - 当消息未完成时显示 -->
            <view v-if="item.status != 'finish'" class="loading-indicator">
              <c-lottie
                :width="`${(130 / 3) * 2}rpx`"
                :height="`${(45 / 3) * 2}rpx`"
                :src="soundlottie.chatting"
                :loop="true"
              />
              <view v-if="isParsingBackground"> 解析背景知识中... </view>
              <view v-else-if="isParsingFiles"> 解析文件中... </view>
              <view v-else-if="isParsingImage"> 生成图片中... </view>
              <view v-else-if="isParsingDocument"> 解析文档中... </view>
              <view v-else> 生成中... </view>
            </view>

            <!-- 骨架屏 - 当消息正在加载且没有内容时显示 -->
            <view v-if="shouldShowMessageSkeleton(item)" class="skeleton-content">
              <view class="skeleton-line skeleton-line-long"></view>
              <view class="skeleton-line skeleton-line-medium"></view>
            </view>

            <MessageThink
              v-if="getReasoningContent(item)"
              :content="getReasoningContent(item)"
              :isLoading="item.status !== 'finish'"
            />
            <LKMarkdown v-if="getMessageContent(item)" :source="getMessageContent(item)" />
            <view class="message-actions" v-if="item.status === 'finish'">
              <view class="action-list">
                <view class="action-item">
                  <view v-if="!playingMessageMap[item.dataId]">
                    <LkSvg
                      v-if="item.status === 'finish'"
                      width="44rpx"
                      height="44rpx"
                      @tap="handleSound(item)"
                      src="/static/chat/sound.svg"
                    />
                  </view>
                  <view v-else>
                    <c-lottie
                      :width="`${(130 / 3) * 2}rpx`"
                      :height="`${(45 / 3) * 2}rpx`"
                      :src="soundlottie.waveform"
                      :loop="true"
                      @tap="handleSound(item)"
                    />
                  </view>
                </view>
                <view class="action-item">
                  <LkSvg
                    @tap="handleCopy(item)"
                    v-if="item.status === 'finish'"
                    width="44rpx"
                    height="44rpx"
                    src="/static/chat/copy.svg"
                  />
                </view>
                <view class="action-item" @tap="retryMessage(item)">
                  <LkSvg
                    v-if="item.status === 'finish'"
                    width="44rpx"
                    height="44rpx"
                    color="none"
                    src="/static/chat/refresh.svg"
                  />
                </view>
              </view>

              <view class="action-list">
                <view class="action-item" v-if="!item.isLike || item.isLike === 'like'">
                  <LkSvg
                    @tap="updateFeedback(item, 'like')"
                    v-if="item.status === 'finish'"
                    width="44rpx"
                    height="44rpx"
                    :src="
                      item.isLike === 'like'
                        ? '/static/chat/upvote_fill1.svg'
                        : '/static/chat/like.svg'
                    "
                    color="none"
                    :class="{ 'feedback-active': item.isLike === 'like' }"
                  />
                </view>
                <view
                  class="action-item"
                  v-if="!item.isLike || item.isLike === 'dislike'"
                  @tap="updateFeedback(item, 'dislike')"
                >
                  <LkSvg
                    v-if="item.status === 'finish'"
                    width="44rpx"
                    height="44rpx"
                    color="none"
                    :src="
                      item.isLike === 'dislike'
                        ? '/static/chat/downvote_fill1.svg'
                        : '/static/chat/unlike.svg'
                    "
                    :class="{ 'feedback-active': item.isLike === 'dislike' }"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 文件列表 -->
          <view v-if="hasFiles(item)" class="file-list">
            <view
              v-for="(file, fileIndex) in getDisplayFiles(item)"
              :key="fileIndex"
              class="file-item"
              @tap="openFile(file)"
            >
              <view v-if="file.type === 'image'" class="file-image">
                <image :src="file.url" class="image-preview" mode="aspectFill"></image>
              </view>
              <view v-else class="file-document">
                <view class="file-icon">
                  <text class="document-icon"></text>
                </view>
                <view class="file-name">{{ file.name }}</view>
              </view>
            </view>
            <!-- 展开/收起按钮 -->
            <view
              v-if="getFiles(item).length > 3"
              class="expand-toggle"
              @tap.stop="toggleFileListExpand(item.dataId || '')"
            >
              <text
                >{{ expandedFileListsMap[item.dataId || ''] ? '收起' : '展开' }}
                <LkSvg
                  v-if="expandedFileListsMap[item.dataId || '']"
                  width="24rpx"
                  height="24rpx"
                  src="/static/chat/chevron-down.svg" />
                <LkSvg v-else width="24rpx" height="24rpx" src="/static/chat/chevron-up.svg"
              /></text>
            </view>
          </view>

          <!-- 消息底部操作栏 -->
        </view>
      </template>
    </view>

    <!-- 长按消息弹出层 -->
    <view class="longpress-popup" v-if="showPopup" :style="popupStyle" @tap.stop>
      <view class="popup-item" @tap="handleCopyFromPopup">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/copy_2.svg" />
        <text>复制</text>
      </view>
      <view class="popup-item" @tap="handleEditFromPopup">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/edit.svg" />
        <text>编辑</text>
      </view>
      <view class="popup-item" @tap="handlePlayFromPopup">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/mic.svg" />
        <text>语音播放</text>
      </view>
      <view class="popup-item" @tap="handleDeleteFromPopup">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/delete_2.svg" />
        <text>删除</text>
      </view>
    </view>

    <!-- 遮罩层，点击关闭弹出菜单 -->
    <view class="popup-mask" v-if="showPopup" @tap="closePopup"></view>

    <!-- 消息编辑组件 -->
    <view class="message-edit-container" v-if="showEditModal">
      <MessageEdit
        v-model:visible="showEditModal"
        :content="currentEditContent"
        @confirm="handleEditConfirm"
        @cancel="showEditModal = false"
      />
    </view>

    <!-- 点踩反馈弹窗 -->
    <view v-if="showDislikeModal" class="modal-overlay" @click.self="handleDislikeCancel">
      <view class="dislike-modal-container">
        <view class="dislike-modal-title">
          <LkSvg
            width="36rpx"
            height="36rpx"
            src="/static/chat/feedback-icon.svg"
            color="#4cd964"
          />
          <text style="margin-left: 12rpx">结果反馈</text>
        </view>
        <view class="dislike-modal-content">
          <textarea
            class="dislike-input"
            v-model="dislikeReason"
            placeholder="请输入您觉得回答不满意的地方"
            :focus="showDislikeModal"
            :adjust-position="false"
            maxlength="500"
          ></textarea>
        </view>
        <view class="dislike-modal-actions">
          <button class="dislike-modal-button cancel-button" @click="handleDislikeCancel">
            关闭
          </button>
          <button class="dislike-modal-button confirm-button" @click="handleDislikeConfirm">
            提交反馈
          </button>
        </view>
      </view>
    </view>

    <!-- LkToast component -->
    <LkToast ref="lkToastRef" />
  </view>
</template>

<script setup>
import { ref, computed, watch, reactive, nextTick, onUnmounted } from 'vue';
import { respDims } from '@/utils/respDims';
import LkSvg from '../svg/index.vue';
import cLottie from '@/uni_modules/c-lottie/components/c-lottie/c-lottie.vue';
import { startVoice, stopVoice } from '@/common/ai/voice';
import ai from '@/common/ai';
import MessageEdit from './MessageEdit.vue';
import LKMarkdown from '../LKMarkdown/index.vue';
import MessageThink from './MessageThink.vue';
import LkToast from '../LkToast/index.vue';
import { onLoad } from '@dcloudio/uni-app';
import { ChatRoleEnum } from '@/common/ai/fetch';

// Props定义
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  customStyle: {
    type: Object,
    default: () => ({}),
  },
  welcomeText: {
    type: String,
    default: '',
  },
  appAvatarUrl: {
    type: String,
    default: '',
  },
  isResponseLoading: {
    type: Boolean,
    default: false,
  },
  isParsingBackground: {
    type: Boolean,
    default: false,
  },
  isParsingFiles: {
    type: Boolean,
    default: false,
  },
  // 解析生成图片
  isParsingImage: {
    type: Boolean,
    default: false,
  },
  // 文档解析
  isParsingDocument: {
    type: Boolean,
    default: false,
  },
  finalAppId: {
    type: String,
    default: '',
  },
  resource: {
    type: Object,
    default: () => {},
  },
});

// 在页面加载时获取参数

const cLottieRef = ref();
const lkToastRef = ref();

// 改为使用Map跟踪每个消息的播放状态
const playingMessageMap = reactive({});
const currentPlayingId = ref(null);

console.log('props.resource', props.resource);

const soundlottie = {
  chatting: 'https://huayun-ai-obs-public.huayuntiantu.com/f0352dc439fd1f259c5302f80da2f117.json',
  waveform: 'https://huayun-ai-obs-public.huayuntiantu.com/73a4187f53ef7532280b747b37cd7fe0.json',
};

// 跟踪每个消息的文件列表展开状态
const expandedFileListsMap = reactive({});

// 长按弹窗状态
const showPopup = ref(false);
const popupStyle = ref({});
const currentLongPressMessage = ref(null);

const parsedText = ref([]);

const chatType = ref('');

onLoad(options => {
  console.log('options', options);
  chatType.value = options.chatType || '';
});

watch(
  () => props.welcomeText,
  newText => {
    console.log('newText', newText);
    parsedText.value = parseText(newText);
  },
  { immediate: true }
);

// 编辑消息状态
const showEditModal = ref(false);
const currentEditContent = ref('');

// 点踩反馈状态
const showDislikeModal = ref(false);
const currentDislikeMessage = ref(null);
const dislikeReason = ref('');

// 事件
const emit = defineEmits([
  'updateMessage',
  'retryMessage',
  'updateContent',
  'sendMessage',
  'deleteMessage',
  'clearResource',
]);

// 响应式状态
const activeOptions = ref('');

const getSourceClass = source => {
  return source === 3 ? 'official' : 'school';
};
// 转换归属值为可读文本
const getAttributionText = attribution => {
  switch (attribution) {
    case 1:
      return '个人资源';
    case 2:
      return '校本资源';
    case 3:
      return '官方资源';
    case 4:
      return '第三方资源';
    default:
      return '未知来源';
  }
};
// 计算属性
const sortedList = computed(() => {
  return [...props.list].sort((a, b) => (a.createTimeValue || 0) - (b.createTimeValue || 0));
});

// 判断是否有消息列表，用于控制欢迎消息的位置
const hasMessages = computed(() => {
  return props.list && props.list.length > 0;
});

// 判断单个消息是否应该显示骨架屏
function shouldShowMessageSkeleton(item) {
  // 只有AI消息且状态不是finish时才可能显示骨架屏
  if (item.obj !== ChatRoleEnum.ai || item.status === 'finish') {
    return false;
  }

  // 当正在响应加载且消息没有内容时显示骨架屏
  if (props.isResponseLoading) {
    const hasContent = getMessageContent(item);
    return !hasContent;
  }

  return false;
}

// 方法
function toggleOptions(id) {
  activeOptions.value = activeOptions.value === id ? '' : id;
}

function updateFeedback(message, type) {
  // 获取当前的反馈状态
  const currentFeedback = message.isLike;

  // 如果是点踩操作且当前不是点踩状态，显示输入原因的弹窗
  if (type === 'dislike' && currentFeedback !== 'dislike') {
    currentDislikeMessage.value = message;
    dislikeReason.value = ''; // 清空之前的输入
    showDislikeModal.value = true;
    return;
  }

  let newFeedbackValue;

  if (type === 'like') {
    // 如果当前已经是like状态，则取消；否则设置为like
    newFeedbackValue = currentFeedback === 'like' ? null : 'like';

    // 显示toast提示 - 只有在设置为like时才显示（不是取消时）
    if (newFeedbackValue === 'like') {
      lkToastRef.value?.show({
        message: '很高兴有帮助到您',
        type: 'success',
        duration: 2000,
      });
    }
  } else if (type === 'dislike') {
    // 如果当前已经是dislike状态，则取消
    // 注意：设置为dislike的情况已在上面处理，会显示弹窗
    newFeedbackValue = null;
  }

  emit('updateMessage', {
    dataId: message.dataId,
    isLike: newFeedbackValue,
  });
  activeOptions.value = '';
}

const fileIcon = item => {
  return `/static/fileTypeIcon/${item.format}.svg`;
};

function retryMessage(message) {
  emit('retryMessage', message);
  activeOptions.value = '';
}

function handleLinkClick(link) {
  emit('updateContent', link);
}

function updateContent(content) {
  emit('updateContent', content);
}

function parseText(text) {
  const regex = /\[([^\]]+)\]/g;
  let result;
  let lastIndex = 0;
  const parsed = [];

  while ((result = regex.exec(text)) !== null) {
    if (result.index > lastIndex) {
      parsed.push({
        type: 'text',
        content: text.slice(lastIndex, result.index),
      });
    }
    parsed.push({
      type: 'link',
      content: result[1],
    });
    lastIndex = regex.lastIndex;
  }

  if (lastIndex < text.length) {
    parsed.push({
      type: 'text',
      content: text.slice(lastIndex),
    });
  }

  return parsed;
}

// 格式化更新时间
const formatUpdateTime = time => {
  if (!time) return '';
  // 确保时间格式为 YYYY-MM-DD
  const date = new Date(time);
  if (isNaN(date.getTime())) return String(time);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

function handleSound(message) {
  console.log('handleSound called, message id:', message.dataId);

  // 如果当前消息正在播放，则停止播放
  if (playingMessageMap[message.dataId]) {
    console.log('Stopping TTS...');
    ai.stopTts();
    // 清除播放状态
    playingMessageMap[message.dataId] = false;
    currentPlayingId.value = null;
    console.log('TTS stopped, playingMessageMap reset');
    return;
  }

  // 如果有其他消息正在播放，先停止它
  if (currentPlayingId.value) {
    playingMessageMap[currentPlayingId.value] = false;
    ai.stopTts();
  }

  // 开始播放当前消息
  console.log('Starting TTS for message:', message.dataId);
  currentPlayingId.value = message.dataId;
  playingMessageMap[message.dataId] = true;

  ai.startTts({
    input: getMessageContent(message),
    chatItemId: message.dataId,
    appId: props.finalAppId,
    onStart: () => {
      console.log('语音播放开始');
      playingMessageMap[message.dataId] = true;
    },
    onComplete: () => {
      console.log('语音播放完成');
      playingMessageMap[message.dataId] = false;
      currentPlayingId.value = null;
    },
    onError: error => {
      console.log('语音播放错误', error);
      playingMessageMap[message.dataId] = false;
      currentPlayingId.value = null;
    },
  });
}

function handleCopy(message) {
  uni.setClipboardData({
    data: getMessageContent(message),
    showToast: false,
    success: () => {
      lkToastRef.value?.show({
        message: '复制成功',
        type: 'success',
        duration: 2000,
      });
    },
  });
}

// 长按消息显示操作浮框
async function showMessageActions(message, index, event) {
  currentLongPressMessage.value = message;
  // Make popup visible so we can query its dimensions
  showPopup.value = true;

  // Wait for DOM update
  await nextTick();

  const bubbleId = `msg-bubble-${message.dataId || index}`;

  uni
    .createSelectorQuery()
    .select('.longpress-popup')
    .boundingClientRect()
    .select(`#${bubbleId}`)
    .boundingClientRect()
    .exec(results => {
      const [popupRect, bubbleRect] = results;

      if (!popupRect || !bubbleRect) {
        // Fallback to original logic with a fix for the top boundary
        const touchX = event.touches[0].clientX;
        const touchY = event.touches[0].clientY;
        popupStyle.value = {
          left: `${touchX - 120}px`,
          top: `${Math.max(10, touchY - 100)}px`,
        };
        return;
      }

      const { windowHeight, windowWidth } = uni.getSystemInfoSync();

      // Position popup above the bubble, aligned to the right
      let top = bubbleRect.top - popupRect.height - 8; // 8px margin
      let left = bubbleRect.right - popupRect.width;

      // If not enough space on top, show below
      if (top < 10) {
        // 10px margin from top of screen
        top = bubbleRect.bottom + 8;
      }

      // Adjust if it overflows bottom
      if (top + popupRect.height > windowHeight - 10) {
        top = windowHeight - popupRect.height - 10;
      }

      // Adjust if it overflows left
      if (left < 10) {
        left = 10;
      }

      popupStyle.value = {
        left: `${left}px`,
        top: `${top}px`,
      };
    });
}

// 关闭弹出菜单
function closePopup() {
  showPopup.value = false;
}

// 关闭长按弹窗（供父组件调用）
function closeLongPressPopup() {
  if (showPopup.value) {
    showPopup.value = false;
  }
}

// 处理消息列表点击事件
function handleMessageListTap(event) {
  // 如果长按弹窗显示，则关闭它
  if (showPopup.value) {
    closePopup();
  }
}

// 从弹窗复制消息
function handleCopyFromPopup() {
  if (currentLongPressMessage.value) {
    handleCopy(currentLongPressMessage.value);
    showPopup.value = false;
  }
}

// 从弹窗编辑消息
function handleEditFromPopup() {
  if (currentLongPressMessage.value) {
    currentEditContent.value = getMessageContent(currentLongPressMessage.value);
    showPopup.value = false;
    showEditModal.value = true;
  }
}

// 从弹窗播放语音
function handlePlayFromPopup() {
  if (currentLongPressMessage.value) {
    handleSound(currentLongPressMessage.value);
    closePopup();
  }
}

// 格式化文件大小
const formatFileSize = size => {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
};

// 从弹窗删除消息
function handleDeleteFromPopup() {
  if (currentLongPressMessage.value) {
    emit('deleteMessage', currentLongPressMessage.value);
    showPopup.value = false;
  }
}

// 处理编辑确认
function handleEditConfirm(content) {
  if (currentLongPressMessage.value) {
    const originalContent = getMessageContent(currentLongPressMessage.value);
    emit('retryMessage', {
      ...currentLongPressMessage.value,
      content,
    });
  }
}

// 暴露组件状态和方法，使父组件可以访问
defineExpose({
  showEditModal,
  showDislikeModal,
  closeLongPressPopup,
});

// 获取消息内容
function getMessageContent(item) {
  if (!item || !item.value?.length) return '';

  // 处理不同类型的消息
  let content = '';

  for (const valueItem of item.value) {
    if (valueItem.type === 'text' && valueItem.text?.content) {
      content += valueItem.text.content;
    }
  }

  return content;
}

// 获取思考内容
function getReasoningContent(item) {
  if (!item || !item.value?.length) return '';

  // 查找 value 数组中 type 为 'reasoning' 的元素
  const reasoningItem = item.value.find(valueItem => valueItem.type === 'reasoning');

  if (reasoningItem && reasoningItem.reasoning?.content) {
    return reasoningItem.reasoning.content;
  }

  return '';
}

// 判断是否有文件
function hasFiles(item) {
  if (!item || !item.value?.length) {
    return false;
  }

  return item.value.some(valueItem => valueItem.type === 'file' || valueItem.file);
}

// 获取文件列表
function getFiles(item) {
  if (!item || !item.value?.length) return [];

  return item.value
    .filter(valueItem => valueItem.type === 'file' && valueItem.file)
    .map(valueItem => valueItem.file);
}

// 获取显示的文件列表（考虑展开/收起状态）
function getDisplayFiles(item) {
  const files = getFiles(item);
  const messageId = item.dataId || '';
  // 如果文件数量小于等于3或者该消息的文件列表已展开，则返回所有文件
  if (files.length <= 3 || expandedFileListsMap[messageId]) {
    return files;
  }

  // 否则只返回前3个文件
  return files.slice(0, 3);
}

// 获取显示的图片文件列表
function getDisplayImages(item) {
  const files = getDisplayFiles(item);

  const imageFiles = files.filter(file => {
    return file.type === 'image';
  });

  return imageFiles;
}

// 获取显示的文档文件列表
function getDisplayDocuments(item) {
  const files = getDisplayFiles(item);
  return files.filter(file => file.type !== 'image');
}

// 切换文件列表的展开/收起状态
function toggleFileListExpand(messageId) {
  if (expandedFileListsMap[messageId]) {
    expandedFileListsMap[messageId] = false;
  } else {
    expandedFileListsMap[messageId] = true;
  }
}

// 打开文件
function openFile(file) {
  if (file.type === 'image') {
    uni.previewImage({
      urls: [file.url],
      current: 0,
    });
  } else {
    // 处理其他类型文件
    uni.showLoading({ title: '打开文件中...' });

    uni.downloadFile({
      url: file.url,
      success: res => {
        uni.hideLoading();
        if (res.statusCode === 200) {
          uni.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: function () {
              console.log('打开文档成功');
            },
            fail: function (err) {
              console.error('打开文档失败', err);
              uni.showToast({
                title: '打开文件失败',
                icon: 'none',
              });
            },
          });
        }
      },
      fail: () => {
        uni.hideLoading();
        uni.showToast({
          title: '下载文件失败',
          icon: 'none',
        });
      },
    });
  }
}

// 处理点踩反馈
function handleDislikeCancel() {
  showDislikeModal.value = false;
}

function handleDislikeConfirm() {
  if (currentDislikeMessage.value) {
    emit('updateMessage', {
      dataId: currentDislikeMessage.value.dataId,
      isLike: 'dislike',
      customFeedback: dislikeReason.value,
    });

    // 显示toast提示
    lkToastRef.value?.show({
      message: '您的反馈是我们前进的动力',
      type: 'info',
      duration: 2000,
    });

    // 清理状态
    showDislikeModal.value = false;
    dislikeReason.value = '';
    currentDislikeMessage.value = null;
  }
}
</script>

<style scoped>
.message-list {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
}

.welcome-message {
  display: flex;
  margin-bottom: 24rpx;
  transition: all 0.3s ease-in-out;
}

.centered-welcome {
  height: 100%;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 200rpx;
}

.top-welcome {
  justify-content: flex-start;
  margin-top: 0;
}

.welcome-content {
  background-color: inherit;
  display: inline-block;
  max-width: 100%;
  width: 100%;
  word-wrap: break-word;
}

.welcome-message-content {
  width: 100%;
  border-radius: 10px;
  border: 1px solid var(--Gray-Gray3, #e7e7e7);
  background: #fff;
  padding: 10px;
  box-shadow: 0 0 15.2px 0 rgba(237, 237, 237, 0.62);
}

.resource-details-welcome-message {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  border-bottom: 1px solid #f3f3f3;
}

.app-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-item {
  display: flex;
  margin-bottom: 24rpx;
}

.user-message-item {
  flex-direction: row-reverse;
}

.ai-message-item {
  flex-direction: row;
}

.user-avatar,
.ai-avatar {
  width: 40rpx;
  height: 40rpx;
  flex-shrink: 0;
  margin: 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #3d7fff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
}

.avatar-default {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 用户消息整体包装器 */
.user-message-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-width: 100%;
}

/* 用户消息内容容器 */
.user-message-content {
  display: inline-block;
  margin-bottom: 8rpx;
}

/* 用户文件列表包装器 */
.user-file-list-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
}

/* 用户文件列表容器 */
.user-file-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  position: relative;
}

/* 用户图片网格布局 */
.user-image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 12rpx;
  width: 100%;
  justify-content: flex-start;
}

/* 用户图片项 */
.user-image-item {
  width: 200rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  border: 1px solid #e7e7e7;
  box-shadow: 0px 0px 9.5px 0px rgba(62, 71, 83, 0.12);
  flex-shrink: 0;
}

/* 用户图片预览 */
.user-image-preview {
  width: 200rpx;
  height: 160rpx;
  object-fit: cover;
  border-radius: 12rpx;
  display: block;
}

/* 用户文档列表 */
.user-document-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* AI消息内容容器 */
.ai-message-content {
  /* max-width: 70%; */
  max-width: 100%;
  min-width: 30%;
}

.message-bubble {
  padding: 24rpx;
  border-radius: 40rpx 40rpx 6rpx 40rpx;
  font-size: 28rpx;
  line-height: 1.5;
  position: relative;
  word-break: break-word;
  gap: 10rpx;
}

.user-message {
  display: block;
  background-color: #7d4dff;
  color: white;
}

.ai-message {
  background-color: #f5f5f5;
  color: #1d2129;
  border-radius: 40rpx 40rpx 40rpx 6rpx;
}

.loading-indicator {
  padding: 12rpx 16rpx;
  display: flex;
  align-items: center;
}

.loading-spinner {
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #d0d0d0;
  border-top-color: #3d7fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 骨架屏样式 */
.skeleton-content {
  padding-top: 24rpx;
  .skeleton-line {
    height: 32rpx;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4rpx;
    margin-bottom: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }

    &.skeleton-line-long {
      width: 85%;
    }

    &.skeleton-line-medium {
      width: 70%;
    }

    &.skeleton-line-short {
      width: 45%;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.feedback-options {
  position: absolute;
  top: 8rpx;
  right: -32rpx;
}

.feedback-button {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.more-icon::before {
  content: '⋮';
  font-size: 24rpx;
  color: #666;
}

.options-menu {
  position: absolute;
  top: 28rpx;
  right: 0;
  background-color: white;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  min-width: 120rpx;
  z-index: 10;
}

.option-item {
  padding: 8rpx 12rpx;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.option-item:hover {
  background-color: #f5f5f5;
}

.option-item.selected {
  color: #3d7fff;
}

.option-item text {
  margin-right: 8rpx;
}

.like-icon::before {
  content: '👍';
  font-size: 16rpx;
}

.dislike-icon::before {
  content: '👎';
  font-size: 16rpx;
}

.retry-icon::before {
  content: '↻';
  font-size: 16rpx;
}

.copy-icon::before {
  content: '📋';
  font-size: 16rpx;
}

.file-list {
  flex-wrap: wrap;
  margin-top: 8rpx;
  margin-bottom: 8rpx;
  gap: 8rpx;
  position: relative;
}

.expand-toggle {
  position: absolute;
  bottom: -48rpx;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  cursor: pointer;
  font-size: 24rpx;
  color: #7d4dff;
}

.expand-icon {
  margin-left: 4rpx;
}

.file-item {
  cursor: pointer;
  border-radius: 8rpx;
  overflow: hidden;
}

.file-image {
  width: 184rpx;
  height: 172rpx;
  border-radius: 16rpx;
  border: 1px solid #e7e7e7;
  box-shadow: 0px 0px 9.5px 0px rgba(62, 71, 83, 0.12);
  overflow: hidden;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-document {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  background-color: #f7f7f7;
}

.file-icon {
  margin-right: 8rpx;
}

.document-icon::before {
  content: '📄';
  font-size: 24rpx;
}

.file-name {
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 消息操作栏样式 */
.message-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
  padding: 4rpx 0;
}

.action-list {
  display: flex;
  margin-right: 68rpx;
}
.action-list:last-child {
  margin-right: 0rpx;
}

.action-item {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  line-height: 1;
  height: 44rpx;
}

.action-item:last-child {
  margin-right: 0;
}

.action-item :deep(svg) {
  vertical-align: middle;
}

.feedback-active {
  color: red !important;
}

/* 长按弹出层样式 */
.longpress-popup {
  position: fixed;
  background-color: #ffffff;
  border-radius: 16rpx;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  box-shadow:
    0px 6px 30px 5px rgba(0, 0, 0, 0.15),
    0px 16px 24px 2px rgba(0, 0, 0, 0.04),
    0px 8px 10px -5px rgba(0, 0, 0, 0.04);
}

.popup-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  cursor: pointer;
  border-bottom: 0.5px solid var(--Gray-Gray3, #e7e7e7);
}

.popup-item text {
  margin-left: 12rpx;
  font-size: 28rpx;
  color: #333;
}

/* 遮罩层样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1000;
}
.dynamic-link {
  color: #2450b5;
  cursor: pointer;
  text-decoration: underline;
}
.upload-file-list-item {
  width: 100%;
  margin-bottom: 20rpx;
  justify-content: flex-start;
  align-items: center;
}

.file-card {
  display: flex;
  width: 494rpx;
  height: 122rpx;
  flex-direction: row;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  box-sizing: border-box;
  border: 1px solid #e7e7e7;
  padding: 12rpx 158rpx 12rpx 12rpx;
}

.file-icon-placeholder {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  margin-right: 24rpx;
}

/* 用户文件项样式 */
.user-file-item {
  cursor: pointer;
  border-radius: 8rpx;
  overflow: hidden;
}

/* 用户文件图片样式 */
.user-file-image {
  width: 184rpx;
  height: 172rpx;
  border-radius: 16rpx;
  border: 1px solid #e7e7e7;
  box-shadow: 0px 0px 9.5px 0px rgba(62, 71, 83, 0.12);
  overflow: hidden;
}

/* 用户文件文档样式 */
.user-file-document {
  width: 100%;
  margin-bottom: 8rpx;
  justify-content: flex-start;
  align-items: center;
}

/* 用户文件卡片样式 */
.user-file-card {
  display: flex;
  width: 494rpx;
  height: 122rpx;
  flex-direction: row;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  box-sizing: border-box;
  border: 1px solid #e7e7e7;
}

/* 用户展开/收起按钮 */
.user-expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-top: 8rpx;
  cursor: pointer;
  font-size: 24rpx;
  color: #7d4dff;
  align-self: flex-end;
}

.file-details-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.file-name {
  font-size: 24rpx;
  font-weight: 400;
  color: #1d2129;
  line-height: 1.2;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-info {
  font-size: 24rpx;
  color: #909399;
  font-weight: 400;
  line-height: 1.2;
}
.message-edit-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 99999999;
  /* 解决iOS安全区域覆盖问题 */
  width: 100vw;
  height: 100vh;
  /* 兼容iOS刘海屏和底部Home指示器 */
  padding: constant(safe-area-inset-top) constant(safe-area-inset-right)
    constant(safe-area-inset-bottom) constant(safe-area-inset-left);
  padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom)
    env(safe-area-inset-left);
}

/* 点踩反馈弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.dislike-modal-container {
  background-color: #ffffff;
  border-radius: 12rpx;
  width: 580rpx;
  padding: 32rpx;
  box-shadow: 0px 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.dislike-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.dislike-modal-content {
  width: 100%;
  margin-bottom: 32rpx;
}

.dislike-input {
  width: 100%;
  height: 198rpx;
  padding: 20rpx;
  border: 1px solid #e6e6e6;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #ffffff;
}

.dislike-modal-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 20rpx;
}

.dislike-modal-button {
  width: 160rpx;
  height: 76rpx;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: normal;
  margin: 0;
  padding: 0;
  border: none;
  line-height: normal;
}

.dislike-modal-button::after {
  border: none;
}

.cancel-button {
  background-color: #f2f2f2;
  color: #333333;
}

.confirm-button {
  background-color: #7d4dff;
  color: #ffffff;
}
.resource-details {
  flex: 1;
  padding-bottom: 8px;
}

.resource-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-primary, #303133);
  font-family: 'PingFang SC';
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}

.tags-container {
  display: flex;
  align-items: center;
  margin-top: 8px;
  overflow: hidden;
}

.source-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: #fff;
  margin-right: 8px;
  flex-shrink: 0;
}

.resource-tags {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  .tag {
    display: inline-block;
    padding: 2px 6px;
    background-color: #f5f5f5;
    color: #666;
    font-size: 12px;
    border-radius: 4px;
    margin-right: 4px;
  }
}

.file-info {
  display: flex;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 16px;

  .icon-placeholder {
    font-size: 12px;
    margin-right: 4px;
  }

  .info-text {
    font-size: 12px;
    color: #666;
  }
}
.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;

  .icon-placeholder {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .btn-text {
    font-size: 12px;
    color: #666;
  }
}
.file-icon-welcome-message-icon {
  display: flex;
  width: 24px;
  height: 24px;
  margin-right: 8px;

  image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.resource-meta-bottom {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  .resource-meta {
    display: flex;
    justify-content: space-between;
    width: 100%;
    font-size: 24rpx;
    color: #94a3b8;
    .file-size-container {
      display: flex;
      align-items: center;
      gap: 4px;
      .file-size {
        color: #86909c;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }
    }

    .update-time {
      color: #cbd5e1;
    }

    .file-size-container-right {
      display: flex;
      justify-content: flex-end;
      flex: 1;

      .file-tag-wrapper {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        padding: 9px 0;
        .file-tag {
          display: inline-flex;
          padding: 1px 8px;
          align-items: center;
          gap: 4px;
          border-radius: 999px;
          font-size: 12px;
          line-height: 20px;
          white-space: nowrap;

          &.school {
            background: #e8f7ff;
            color: #3491fa;
          }

          &.official {
            background: #fff7e8;
            color: #ff7d00;
          }
        }
      }
    }
  }
}
.message-content {
  display: flex;
  flex-direction: column;
  margin-top: 8px;
}
</style>
