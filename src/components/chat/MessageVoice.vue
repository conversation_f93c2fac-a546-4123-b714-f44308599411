<template>
  <view class="voice-container">
    <view class="voice-tip" v-show="!isCancel && isRecording">
      <text>松开发送/上滑取消</text>
    </view>
    <view
      class="voice-button"
      :class="{ recording: isRecording, cancel: isCancel }"
      @touchstart="startRecording"
      @touchmove="moveRecording"
      @touchend="endRecording"
    >
      <view v-show="isCancel"> 松手取消 </view>

      <view v-show="!isCancel && isRecording" class="recording-content">
        <view class="recording-dots">
          <span v-for="n in 30" :key="n" class="dot"></span>
        </view>
      </view>

      <view v-show="!isCancel && !isRecording" class="voice-button-content">
        <view @touchstart="prepareSwitchToUpload" @touchend="executeSwitchToUpload">
          <LkSvg width="64rpx" height="64rpx" src="/static/chat/addfile.svg" />
        </view>
        <view class="voice-button-text"> 按住说话 </view>
        <view @touchstart="prepareSwitchToKeyboard" @touchend="executeSwitchToKeyboard">
          <LkSvg width="64rpx" height="64rpx" src="/static/chat/keyboard.svg" />
        </view>
      </view>
    </view>

    <!-- Custom Permission Modal -->
    <PermissionModal
      v-model:show="isShowVoicePermissionModal"
      :title="permissionModalTitle"
      :content="permissionModalContent"
      cancel-text="取消"
      confirm-text="前往设置"
      @cancel="handleModalCancel"
      @confirm="handleModalConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import LkSvg from '../svg/index.vue';
import PermissionModal from '../LKpermissionModal/index.vue';
import { checkPermission } from '@/utils/permission';

// 定义组件名称
defineOptions({
  name: 'MessageVoice',
});

// 定义事件
const emit = defineEmits([
  'start-recording',
  'cancel-recording',
  'finish-recording',
  'switch-to-upload',
  'switch-to-keyboard',
]);

// 响应式状态
const isRecording = ref(false);
const isCancel = ref(false);
const startY = ref(0);
const isSwitchingView = ref(false);
const isShowVoicePermissionModal = ref(false);
const permissionModalTitle = ref('');
const permissionModalContent = ref('');
const permissionOpenSettings = ref<(() => void) | null>(null);

// 检查并请求语音权限
const checkAndRequestVoicePermission = async (): Promise<boolean> => {
  try {
    const result = await checkPermission('voice');

    if (result.granted) {
      return true;
    } else {
      // 权限被拒绝，显示权限弹窗
      if (result.details) {
        permissionModalTitle.value = result.details.deniedTitle;
        permissionModalContent.value = result.details.deniedMessage;
        permissionOpenSettings.value = result.openSettings || null;
        showPermissionModal();
      }
      return false;
    }
  } catch (error) {
    return false;
  }
};

// 显示权限弹窗
const showPermissionModal = () => {
  isShowVoicePermissionModal.value = true;
};

// 权限弹窗的取消操作
const handleModalCancel = () => {
  isShowVoicePermissionModal.value = false;
};

// 权限弹窗的确认操作 (前往设置)
const handleModalConfirm = () => {
  isShowVoicePermissionModal.value = false;
  if (permissionOpenSettings.value) {
    permissionOpenSettings.value();
  }
};

const prepareSwitchBase = () => {
  isSwitchingView.value = true;
};

const prepareSwitchToUpload = () => {
  prepareSwitchBase();
};

const prepareSwitchToKeyboard = () => {
  prepareSwitchBase();
};

const executeSwitchToUpload = () => {
  if (isSwitchingView.value) {
    emit('switch-to-upload');
    isSwitchingView.value = false;
    if (isRecording.value) {
      isRecording.value = false;
      isCancel.value = false;
      emit('cancel-recording');
    }
  }
};

const executeSwitchToKeyboard = () => {
  if (isSwitchingView.value) {
    emit('switch-to-keyboard');
    isSwitchingView.value = false;
    if (isRecording.value) {
      isRecording.value = false;
      isCancel.value = false;
      emit('cancel-recording');
    }
  }
};

// 开始录音
const startRecording = async (event: TouchEvent) => {
  if (isSwitchingView.value) {
    return;
  }

  const hasPermission = await checkAndRequestVoicePermission();
  if (!hasPermission) {
    return;
  }

  setTimeout(() => {
    if (isSwitchingView.value) {
      return;
    }
    isRecording.value = true;
    isCancel.value = false;
    if (event.touches && event.touches.length > 0) {
      startY.value = event.touches[0].clientY;
    }
    emit('start-recording');
  }, 50);
};

// 移动中
const moveRecording = (event: TouchEvent) => {
  if (isSwitchingView.value) return;
  if (!isRecording.value) return;

  event.preventDefault();

  if (event.touches && event.touches.length > 0) {
    const currentY = event.touches[0].clientY;
    const moveDistance = startY.value - currentY;

    if (moveDistance > 50) {
      isCancel.value = true;
    } else {
      isCancel.value = false;
    }
  }
};

// 结束录音
const endRecording = () => {
  if (isSwitchingView.value) {
    isSwitchingView.value = false;
    if (isRecording.value) {
      isRecording.value = false;
      isCancel.value = false;
      emit('cancel-recording');
    }
    return;
  }

  if (isRecording.value) {
    if (isCancel.value) {
      emit('cancel-recording');
    } else {
      emit('finish-recording');
    }
    isRecording.value = false;
    isCancel.value = false;
  }
};
</script>

<style scoped>
.voice-container {
  flex-shrink: 0;
  margin-left: auto;
  margin-right: auto;
}

.voice-button {
  width: 710rpx;
  height: 112rpx;
  border-radius: 100rpx;
  border: 1px solid var(--Gray-Gray1, #f3f3f3);
  background: #fff;
  box-shadow:
    0px 2px 4px 0px rgba(0, 0, 0, 0.05),
    0px 0px 16.7px -4px rgba(0, 0, 0, 0.09);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #1d2129;
  padding: 0 27rpx;
  font-weight: 600;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.voice-button.recording {
  background-color: #1976d2;
  color: white;
  border: none;
}

.voice-button.cancel {
  background-color: #f44336;
  color: white;
  border: none;
}

.recording-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16rpx;
}

.recording-dots {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.voice-tip {
  display: flex;
  width: 280rpx;
  padding: 4rpx 16rpx;
  justify-content: center;
  align-items: center;
  gap: 16rpx;
  border-radius: 100rpx;
  background: rgba(0, 0, 0, 0.6);
  margin: 32rpx auto;
}

.voice-tip text {
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 40rpx; /* 166.667% */
}

.dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  background-color: white;
  border-radius: 50%;
  margin: 0 2px;
}

/* 可以添加一个动画效果使点看起来有波动感 */
@keyframes pulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}

.dot {
  animation: pulse 1.5s infinite;
  animation-delay: calc(var(--n) * 0.05s);
}
.voice-button-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.voice-button-content > view {
  display: flex;
  align-items: center;
  justify-content: center;
}
.voice-button-text {
  font-size: 32rpx;
  color: #1d2129;
  font-weight: 600;
}
</style>
