<template>
  <view class="my-empty">
    <view class="empty-icon" v-if="showImage">
      <image
        class="empty-image"
        :src="imageUrl"
        mode="aspectFit"
        :style="{ width: `${imageWidth}`, height: `${imageHeight}` }"
      ></image>
    </view>
    <text class="empty-text">{{ text }}</text>
    <view class="empty-extra" v-if="$slots.default">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  /**
   * 空状态文本
   */
  text: {
    type: String,
    default: '暂无数据',
  },
  /**
   * 图片URL，默认使用空状态图标
   */
  imageUrl: {
    type: String,
    default: '/static/common/empty.svg',
  },
  /**
   * 图片宽度
   */
  imageWidth: {
    type: [String],
    default: '130',
  },
  /**
   * 图片高度
   */
  imageHeight: {
    type: [String],
    default: '130',
  },
  /**
   * 是否显示图片
   */
  showImage: {
    type: Boolean,
    default: true,
  },
});
</script>

<style lang="scss" scoped>
.my-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  width: 100%;
  box-sizing: border-box;

  .empty-icon {
    margin-bottom: 20rpx;

    .empty-image {
      width: 200rpx;
      height: 200rpx;
    }
  }

  .empty-text {
    font-size: 28rpx;
    color: #4e5969;
    line-height: 1.5;
    text-align: center;
  }

  .empty-extra {
    margin-top: 30rpx;
  }
}
</style>
