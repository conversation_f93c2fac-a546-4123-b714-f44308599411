<template>
  <view class="my-ellipsis-box" :style="ellipsisStyle">
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  lines?: number;
}

const props = withDefaults(defineProps<Props>(), {
  lines: 1,
});

const ellipsisStyle = computed<any>(() => {
  if (props.lines === 1) {
    return {
      overflow: 'hidden',
      'white-space': 'nowrap',
      'text-overflow': 'ellipsis',
      width: '100%',
    };
  } else {
    return {
      overflow: 'hidden',
      display: '-webkit-box',
      '-webkit-box-orient': 'vertical',
      '-webkit-line-clamp': props.lines.toString(),
      'word-break': 'break-all',
      width: '100%',
    };
  }
});
</script>

<style lang="scss" scoped>
.my-ellipsis-box {
  width: 100%;
}
</style>
