<script setup lang="ts">
interface TabItem {
  value: string | number;
  label: string;
  disabled?: boolean;
}

interface Props {
  tabs: TabItem[];
  modelValue: string | number;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue']);

const handleTabChange = (value: string | number) => {
  emit('update:modelValue', value);
};
</script>

<template>
  <view class="lk-tab-group">
    <view
      v-for="item in tabs"
      :key="item.value"
      class="lk-tab-item"
      :class="{ active: modelValue === item.value, disabled: item.disabled }"
      @click="!item.disabled && handleTabChange(item.value)"
    >
      {{ item.label }}
    </view>
  </view>
</template>

<style lang="scss">
.lk-tab-group {
  display: flex;
  width: 100%;
  // background-color: #f2f3f5;
  border-radius: 12rpx;
  padding: 6rpx 0;
}

.lk-tab-item {
  // flex: 1;
  height: 64rpx;
  flex-grow: 0;
  flex-shrink: 0;
  min-width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #4e5969;
  border-radius: 100px;
  padding: 0 24rpx;
  margin-right: 16rpx;
  position: relative;
  transition: all 0.3s;
  background-color: #f3f3f3;
  white-space: nowrap;
  &.active {
    font-weight: bold;
    color: #7d4dff;
    background: linear-gradient(to right, #f1f8ff, #f3efff);
  }

  &.disabled {
    color: #c9cdd4;
    pointer-events: none;
  }
}
</style>
