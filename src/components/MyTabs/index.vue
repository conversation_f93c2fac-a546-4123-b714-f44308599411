<template>
  <view class="my-tabs">
    <scroll-view
      class="tabs-scroll"
      scroll-x
      :scroll-with-animation="scrollAnimation"
      :show-scrollbar="false"
      :scroll-left="scrollLeft"
    >
      <view
        v-for="(item, index) in tabItems"
        :key="index"
        :id="`my-tab-${index}`"
        class="tab-item"
        :class="{ active: currentIndex === index }"
        @tap="onTabClick(index, item)"
      >
        {{ item.label }}
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import {
  ref,
  watch,
  nextTick,
  withDefaults,
  defineProps,
  defineEmits,
  getCurrentInstance,
} from 'vue';

interface TabItem {
  label: string;
  value: string | number;
}

interface Props {
  tabItems: TabItem[];
  currentIndex: number | string;
  scrollAnimation?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  scrollAnimation: true,
});

const scrollLeft = ref(0);
const instance = getCurrentInstance();

const emit = defineEmits<{
  'update:currentIndex': [value: number];
  change: [index: number, item: TabItem];
}>();

const onTabClick = (index: number, item: TabItem) => {
  emit('update:currentIndex', index);
  emit('change', index, item);
};

// 滚动吸附逻辑（基于真实scrollOffset，居中目标tab）
watch(
  () => props.currentIndex,
  async newIndex => {
    await nextTick();
    const idx = Number(newIndex);
    const query = uni.createSelectorQuery().in(instance?.proxy);
    query.select('.tabs-scroll').boundingClientRect();
    // 读取当前scrollLeft，避免使用内部state导致误差
    (query as any).select('.tabs-scroll').scrollOffset();
    query.select(`#my-tab-${idx}`).boundingClientRect();
    query.exec((res: any[]) => {
      const scrollViewRect = res?.[0];
      const scrollOffset = res?.[1];
      const tabRect = res?.[2];
      if (!scrollViewRect || !tabRect) return;
      const current =
        scrollOffset && typeof scrollOffset.scrollLeft === 'number' ? scrollOffset.scrollLeft : 0;
      const scrollViewWidth = scrollViewRect.width;
      const tabWidth = tabRect.width;
      const tabLeftInView = tabRect.left - scrollViewRect.left; // 目标tab相对scroll-view可视区域的左偏移
      const target = current + tabLeftInView - (scrollViewWidth - tabWidth) / 2;
      scrollLeft.value = Math.max(0, target);
    });
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.my-tabs {
  width: 100%;
  background-color: #ffffff;
  padding: 0rpx 20rpx;
}

.tabs-scroll {
  white-space: nowrap;
  // background-color: #ffffff;
  padding: 20rpx 0rpx;
  // border-bottom: 1rpx solid #f0f0f0;
  overflow-x: auto;
  /* 允许状态标签横向滚动 */
}

.tab-item {
  // padding: 12rpx 40rpx;
  margin: 0 15rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  transition: all 0.3s;
  width: 152rpx;
  height: 68rpx;
  text-align: center;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.tab-item.active {
  color: #ffffff;
  background-color: #7d4dff;
  box-shadow: 0 4rpx 8rpx rgba(0, 122, 255, 0.2);
}
</style>
