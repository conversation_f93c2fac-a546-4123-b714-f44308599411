<template>
  <view
    ref="markmapContainer"
    class="markmap-container"
    :class="`markmap-container-${instanceId}`"
    :data="JSON.stringify({ content: data, instanceId: instanceId })"
    :change:data="markmap.init"
    :export-trigger="exportTrigger"
    :change:export-trigger="markmap.handleExport"
    :instance-id="instanceId"
    :change:instance-id="markmap.setInstanceId"
    :style="{ height: height + 'px' }"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <!-- 导出按钮 -->
    <view class="export-btn" @click="exportImage" v-if="data">
      <text class="export-icon">📤</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MarkmapLocal',
  props: {
    data: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
      default: 400,
    },
    instanceId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      exportTrigger: 0,
    };
  },
  methods: {
    exportImage() {
      // 触发 renderjs 中的导出方法
      console.log('exportImage 被点击');
      this.exportTrigger = Date.now();
      console.log('exportTrigger 设置为:', this.exportTrigger);
    },
    onMouseEnter() {
      // 在 uni-app 中，悬停效果通过 CSS 处理
    },
    onMouseLeave() {
      // 在 uni-app 中，悬停效果通过 CSS 处理
    },
  },
};
</script>

<script module="markmap" lang="renderjs">
// 使用本地安装的markmap库
import { Transformer } from 'markmap-lib';
import { Markmap, loadCSS, loadJS } from 'markmap-view';

// 使用 Map 来存储每个实例的状态，避免全局变量污染
const instanceStates = new Map();

// 获取或创建实例状态
function getInstanceState(instanceId) {
  if (!instanceStates.has(instanceId)) {
    instanceStates.set(instanceId, {
      mm: null,
      transformer: null,
      timer: null,
      currentData: ''
    });
  }
  return instanceStates.get(instanceId);
}

export default {
  data() {
    return {
      currentInstanceId: null
    }
  },
  mounted() {
    // 添加全局导出方法供调试使用
    window.markmapExport = () => {
      console.log('全局导出方法被调用');
      this.exportToImage();
    };
  },
  methods: {
    // 设置实例ID
    setInstanceId(instanceId) {
      console.log('设置实例ID:', instanceId);
      this.currentInstanceId = instanceId;
    },

    handleExport(trigger) {
      console.log('handleExport 被调用，trigger:', trigger);
      if (trigger && trigger > 0) {
        console.log('触发导出操作');
        this.exportToImage();
      } else if (trigger && trigger < 0) {
        console.log('触发预览操作');
        this.previewImage();
      }
    },
    init(dataStr) {
      if (!dataStr || typeof dataStr !== 'string') {
        console.warn('Markmap: Invalid data provided');
        return;
      }

      let data, instanceId;

      // 尝试解析JSON格式的数据
      try {
        const parsed = JSON.parse(dataStr);
        if (parsed.content && parsed.instanceId) {
          data = parsed.content;
          instanceId = parsed.instanceId;
          this.currentInstanceId = instanceId;
          console.log('从JSON数据获取到实例ID:', instanceId);
        } else {
          // 兼容旧格式，直接是字符串数据
          data = dataStr;
        }
      } catch (e) {
        // 不是JSON格式，直接使用原始数据
        data = dataStr;
      }

      // 如果还没有实例ID，尝试从DOM获取
      if (!instanceId) {
        instanceId = this.currentInstanceId;

        if (!instanceId) {
          // 从当前执行的容器元素获取
          const containers = document.querySelectorAll('.markmap-container[class*="markmap-container-"]');
          for (const container of containers) {
            const classList = Array.from(container.classList);
            const instanceClass = classList.find(cls => cls.startsWith('markmap-container-'));
            if (instanceClass) {
              const id = instanceClass.replace('markmap-container-', '');
              // 检查这个容器是否还没有被初始化
              const existingSvg = container.querySelector('.markmap-svg');
              if (!existingSvg) {
                instanceId = id;
                this.currentInstanceId = id;
                console.log('从DOM获取到实例ID:', instanceId);
                break;
              }
            }
          }
        }
      }

      if (!instanceId) {
        console.warn('Markmap: No instance ID available');
        return;
      }

      const state = getInstanceState(instanceId);

      // 存储当前数据供导出使用
      state.currentData = data;

      // 清除之前的定时器
      if (state.timer) {
        clearTimeout(state.timer);
      }

      // 延迟执行，确保DOM已渲染
      state.timer = setTimeout(() => {
        this.renderMarkmap(data);
      }, 100);
    },

    async renderMarkmap(data) {
      try {
        // 确保有实例ID
        let instanceId = this.currentInstanceId;
        if (!instanceId) {
          console.error('Markmap: No instance ID available for rendering');
          return;
        }

        const state = getInstanceState(instanceId);

        // 使用实例特定的选择器
        const container = document.querySelector(`.markmap-container-${instanceId}`);
        if (!container) {
          console.error('Markmap container not found for instance:', instanceId);
          return;
        }

        console.log(`Markmap: 开始渲染实例 ${instanceId}`);
        console.log('容器元素:', container);

        // 移除已存在的SVG
        let existingSvg = container.querySelector(".markmap-svg");
        if (existingSvg) {
          container.removeChild(existingSvg);
        }

        // 创建新的SVG元素
        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        svg.setAttribute("class", "markmap-svg");
        svg.style.width = "100%";
        svg.style.height = "100%";
        svg.style.display = "block";
        container.appendChild(svg);

        // 初始化transformer
        if (!state.transformer) {
          state.transformer = new Transformer();
        }

        // 转换markdown数据
        const { root, features } = state.transformer.transform(data);

        // 获取所需的资源
        const assets = state.transformer.getUsedAssets(features);

        // 先加载CSS和JS资源，等待加载完成
        const loadPromises = [];

        if (assets.styles) {
          loadPromises.push(loadCSS(assets.styles));
        }
        if (assets.scripts) {
          loadPromises.push(loadJS(assets.scripts, {
            getMarkmap: () => ({ Markmap, loadCSS, loadJS })
          }));
        }

        // 等待所有资源加载完成
        if (loadPromises.length > 0) {
          await Promise.all(loadPromises);
          // 额外等待一点时间确保资源完全加载
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        // 创建Markmap实例的配置
        const options = {
          duration: 500,
          initialExpandLevel: -1, // -1 表示展开所有层级
          // 确保连接线可见的配置
          paddingX: 8,
          spacingHorizontal: 80,
          spacingVertical: 5,
          // 自定义颜色方案
          color: (node) => {
            const colors = [
              '#007aff', // 蓝色
              '#34c759', // 绿色
              '#ff9500', // 橙色
              '#ff3b30', // 红色
              '#af52de', // 紫色
              '#5ac8fa', // 青色
              '#ffcc00', // 黄色
              '#ff2d92'  // 粉色
            ];
            return colors[node.depth % colors.length];
          },
          // 自定义字体大小
          fontSize: (node) => {
            const baseFontSize = 14;
            const maxFontSize = 20;
            const minFontSize = 12;
            return Math.max(minFontSize, Math.min(maxFontSize, baseFontSize - node.depth * 2));
          }
        };

        // 创建markmap实例
        state.mm = Markmap.create(svg, options, root);

        // 确保渲染完成后适配容器
        setTimeout(() => {
          if (state.mm) {
            state.mm.fit();
          }
        }, 100);

        // 监听容器大小变化
        if (window.ResizeObserver) {
          const resizeObserver = new ResizeObserver(() => {
            if (state.mm) {
              state.mm.fit();
            }
          });
          resizeObserver.observe(container);
        }

        console.log('Markmap rendered successfully');

      } catch (error) {
        console.error('Failed to render markmap:', error);
        this.showError(error.message);
      }
    },

    showError(message) {
      if (!this.currentInstanceId) {
        console.error('Markmap: No instance ID available for showing error');
        return;
      }

      const container = document.querySelector(`.markmap-container-${this.currentInstanceId}`);
      if (container) {
        container.innerHTML = `
          <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: #f5f5f5;
            border-radius: 8px;
            color: #666;
            font-size: 14px;
            text-align: center;
            padding: 20px;
          ">
            <div>
              <div style="font-size: 16px; margin-bottom: 8px;">⚠️ 思维导图渲染失败</div>
              <div style="font-size: 12px; color: #999;">${message}</div>
              <div style="font-size: 12px; color: #999; margin-top: 8px;">请检查数据格式或刷新页面重试</div>
            </div>
          </div>
        `;
      }
    },

    // 计算思维导图尺寸
    calculateDimensions(data) {
      const lines = data.split('\n');
      const fixedWidth = 600;
      const lineHeight = 30;
      const verticalSpacing = 10;

      let totalHeight = 0;
      let headingCount = 0;

      lines.forEach((line) => {
        if (line.trim().startsWith('#')) {
          headingCount++;
          totalHeight += lineHeight + verticalSpacing;
        }
      });

      totalHeight += 50; // 添加边距
      const height = Math.max(300, totalHeight);

      return { width: fixedWidth, height };
    },

    // 捕获完整的SVG
    captureFullSVG() {
      if (!this.currentInstanceId) {
        console.error('Markmap: No instance ID available for capture');
        return null;
      }

      const svg = document.querySelector(`.markmap-container-${this.currentInstanceId} .markmap-svg`);
      if (!svg) {
        console.error('SVG element not found for capture in instance:', this.currentInstanceId);
        return null;
      }

      console.log('SVG尺寸:', svg.clientWidth, 'x', svg.clientHeight);

      // 克隆 SVG 以避免修改原始 SVG
      const clonedSvg = svg.cloneNode(true);

      // 设置明确的尺寸
      clonedSvg.setAttribute('width', svg.clientWidth.toString());
      clonedSvg.setAttribute('height', svg.clientHeight.toString());

      // 捕获并内联所有样式（避免跨域问题）
      let styles = '';

      // 添加基本的 markmap 样式
      styles += `
        .markmap-link {
          fill: none;
          stroke: #ccc;
          stroke-width: 2px;
          stroke-opacity: 0.8;
        }
        .markmap-node > circle {
          fill: #fff;
          stroke-width: 2px;
        }
        .markmap-node > text {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          fill: #333;
          text-anchor: start;
          dominant-baseline: central;
        }
        path {
          vector-effect: non-scaling-stroke;
        }
      `;

      // 尝试安全地获取样式表
      try {
        Array.from(document.styleSheets).forEach((sheet) => {
          try {
            // 只处理同源样式表
            if (sheet.href === null || sheet.href.startsWith(window.location.origin)) {
              Array.from(sheet.cssRules).forEach((rule) => {
                if (rule.cssText.includes('markmap')) {
                  styles += rule.cssText;
                }
              });
            }
          } catch (e) {
            console.warn('跳过无法访问的样式表:', sheet.href);
          }
        });
      } catch (e) {
        console.warn('样式表访问失败，使用默认样式', e);
      }

      const styleElement = document.createElement('style');
      styleElement.textContent = styles;
      clonedSvg.insertBefore(styleElement, clonedSvg.firstChild);

      return new XMLSerializer().serializeToString(clonedSvg);
    },

    // 生成图片
    generateImage(data) {
      return new Promise((resolve, reject) => {
        console.log('开始捕获SVG数据');
        const svgData = this.captureFullSVG();

        if (!svgData) {
          console.error('Failed to capture SVG');
          reject(new Error('Failed to capture SVG'));
          return;
        }

        console.log('SVG数据捕获成功，长度:', svgData.length);

        let { width, height } = this.calculateDimensions(data);
        const minWidth = 500;
        const minHeight = 600;
        width = Math.max(width, minWidth);
        height = Math.max(height, minHeight);

        // 增加画布尺寸，提高分辨率
        const scale = 4;
        const canvas = document.createElement('canvas');
        canvas.width = width * scale;
        canvas.height = height * scale;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        // 设置背景
        ctx.fillStyle = '#fff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 启用图像平滑
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        const img = new Image();
        img.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgData)}`;

        img.onload = () => {
          // 使用缩放绘制图像
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          const jpgDataUrl = canvas.toDataURL('image/jpeg', 1);
          resolve(jpgDataUrl);
        };

        img.onerror = (e) => {
          console.error('Image load error:', e);
          reject(new Error('Failed to load image'));
        };
      });
    },

    // 导出图片
    async exportToImage() {
      try {
        if (!this.currentInstanceId) {
          console.error('Markmap: No instance ID available for export');
          return;
        }

        const state = getInstanceState(this.currentInstanceId);
        console.log('开始导出图片，当前数据:', state.currentData ? '有数据' : '无数据');

        // 使用存储的当前数据
        if (!state.currentData) {
          console.error('No data available for export');
          uni && uni.showToast && uni.showToast({
            title: '暂无数据可导出',
            icon: 'none'
          });
          return;
        }

        // 检查SVG是否存在
        const svg = document.querySelector(`.markmap-container-${this.currentInstanceId} .markmap-svg`);
        if (!svg) {
          console.error('SVG element not found');
          uni && uni.showToast && uni.showToast({
            title: '思维导图未渲染完成',
            icon: 'none'
          });
          return;
        }

        console.log('SVG元素找到，开始生成图片');

        // 显示加载提示
        uni && uni.showLoading && uni.showLoading({
          title: '正在生成图片...'
        });

        const imageUrl = await this.generateImage(state.currentData);

        // 隐藏加载提示
        uni && uni.hideLoading && uni.hideLoading();

        // 创建下载链接
        console.log('创建下载链接，图片URL长度:', imageUrl.length);

        // 检测运行环境
        const isApp = typeof plus !== 'undefined';
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        console.log('运行环境 - App:', isApp, 'Mobile:', isMobile);

        if (isApp && plus) {
          // uni-app App环境，使用plus API
          console.log('使用 plus API 保存图片');
          try {
            // 将base64转换为本地文件
            const base64Data = imageUrl.split(',')[1];
            const fileName = 'markmap_' + new Date().getTime() + '.jpg';

            // 保存到相册（先检查权限）
            this.saveImageWithPermission(imageUrl);

          } catch (error) {
            console.error('plus API 保存失败:', error);
            this.fallbackDownload(imageUrl);
          }
        } else {
          // 浏览器环境或H5
          console.log('使用浏览器下载方式');
          this.browserDownload(imageUrl);
        }
      } catch (error) {
        console.error('Export failed:', error);

        // 隐藏加载提示
        uni && uni.hideLoading && uni.hideLoading();

        // 显示错误提示
        uni && uni.showToast && uni.showToast({
          title: '导出失败',
          icon: 'none'
        });
      }
    },

    // 将 dataURL 转换为 Blob
    dataURLtoBlob(dataURL) {
      const arr = dataURL.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },

    // 保存图片到相册
    saveImageWithPermission(imageUrl) {
      console.log('使用 uni.saveImageToPhotosAlbum 保存图片');

      // 显示保存中提示
      uni.showLoading({
        title: '正在保存到相册...'
      });

      // 使用 uni-app 标准 API 保存图片
      uni.saveImageToPhotosAlbum({
        filePath: imageUrl,
        success: () => {
          console.log('图片已保存到相册');
          uni.hideLoading();
          uni.showToast({
            title: '已保存到相册',
            icon: 'success'
          });
        },
        fail: (error) => {
          console.error('保存图片到相册失败:', error);
          uni.hideLoading();

          // 根据错误信息处理
          if (error.errMsg && error.errMsg.includes('auth')) {
            // 权限相关错误
            uni.showModal({
              title: '保存失败',
              content: '需要相册权限才能保存图片，请在设置中开启相册权限',
              showCancel: true,
              cancelText: '其他方式',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  // 尝试打开设置页面
                  if (typeof plus !== 'undefined') {
                    plus.runtime.openURL('app-settings:');
                  }
                } else {
                  // 使用其他保存方式
                  this.fallbackDownload(imageUrl);
                }
              }
            });
          } else {
            // 其他错误，使用备选方案
            uni.showToast({
              title: '保存失败，尝试其他方式',
              icon: 'none'
            });
            this.fallbackDownload(imageUrl);
          }
        }
      });
    },



    // 浏览器下载方式
    browserDownload(imageUrl) {
      try {
        // 尝试使用 a 标签下载
        const a = document.createElement('a');
        a.href = imageUrl;
        a.download = 'markmap-' + new Date().getTime() + '.jpg';
        a.style.display = 'none';

        document.body.appendChild(a);

        // 尝试多种触发方式
        console.log('尝试触发下载');

        // 方式1: 直接点击
        a.click();

        // 方式2: 模拟鼠标事件
        setTimeout(() => {
          const event = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true
          });
          a.dispatchEvent(event);
        }, 100);

        // 方式3: 如果前两种都失败，打开新窗口
        setTimeout(() => {
          console.log('尝试新窗口方式');
          const newWindow = window.open(imageUrl, '_blank');
          if (newWindow) {
            newWindow.focus();
            uni && uni.showToast && uni.showToast({
              title: '请在新窗口中右键保存图片',
              icon: 'none',
              duration: 3000
            });
          } else {
            // 如果新窗口也被阻止，尝试其他方式
            this.fallbackDownload(imageUrl);
          }
        }, 500);

        setTimeout(() => {
          document.body.removeChild(a);
        }, 1000);

        uni && uni.showToast && uni.showToast({
          title: '正在下载图片...',
          icon: 'loading',
          duration: 2000
        });

      } catch (error) {
        console.error('浏览器下载失败:', error);
        this.fallbackDownload(imageUrl);
      }
    },

    // 保存到文件系统（App环境）
    saveToFileSystem(imageUrl, fileName) {
      try {
        const base64Data = imageUrl.split(',')[1];
        const filePath = plus.io.convertLocalFileSystemURL('_downloads/') + fileName;

        plus.io.requestFileSystem(plus.io.PUBLIC_DOWNLOADS, (fs) => {
          fs.root.getFile(fileName, { create: true }, (fileEntry) => {
            fileEntry.createWriter((writer) => {
              writer.onwrite = () => {
                console.log('文件保存成功:', filePath);
                uni.showToast({
                  title: '图片已保存到下载文件夹',
                  icon: 'success'
                });
              };
              writer.onerror = (error) => {
                console.error('文件写入失败:', error);
                this.fallbackDownload(imageUrl);
              };

              const blob = this.dataURLtoBlob(imageUrl);
              writer.write(blob);
            });
          });
        });
      } catch (error) {
        console.error('文件系统保存失败:', error);
        this.fallbackDownload(imageUrl);
      }
    },

    // 备选下载方案
    fallbackDownload(imageUrl) {
      console.log('使用备选下载方案');

      // 尝试复制到剪贴板
      if (navigator.clipboard && navigator.clipboard.write) {
        try {
          const blob = this.dataURLtoBlob(imageUrl);
          const item = new ClipboardItem({ 'image/jpeg': blob });
          navigator.clipboard.write([item]).then(() => {
            uni && uni.showToast && uni.showToast({
              title: '图片已复制到剪贴板',
              icon: 'success'
            });
          }).catch((error) => {
            console.error('剪贴板复制失败:', error);
            this.showImageInModal(imageUrl);
          });
        } catch (error) {
          console.error('剪贴板操作失败:', error);
          this.showImageInModal(imageUrl);
        }
      } else {
        this.showImageInModal(imageUrl);
      }
    },

    // 在模态框中显示图片
    showImageInModal(imageUrl) {
      console.log('在模态框中显示图片');

      // 创建模态框
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        flex-direction: column;
      `;

      const img = document.createElement('img');
      img.src = imageUrl;
      img.style.cssText = `
        max-width: 90%;
        max-height: 80%;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      `;

      const tip = document.createElement('div');
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

      if (isMobile) {
        if (isIOS) {
          tip.innerHTML = '长按图片选择"存储到照片"<br>点击空白区域关闭';
        } else {
          tip.innerHTML = '长按图片选择"保存图片"<br>点击空白区域关闭';
        }
      } else {
        tip.innerHTML = '右键图片选择"图片另存为"<br>点击空白区域关闭';
      }

      tip.style.cssText = `
        color: white;
        text-align: center;
        margin-top: 20px;
        font-size: 16px;
        line-height: 1.5;
      `;

      modal.appendChild(img);
      modal.appendChild(tip);

      // 点击空白区域关闭
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          document.body.removeChild(modal);
        }
      });

      document.body.appendChild(modal);

      uni && uni.showToast && uni.showToast({
        title: '长按图片保存',
        icon: 'none',
        duration: 2000
      });
    },

    // 预览图片
    async previewImage() {
      try {
        if (!this.currentInstanceId) {
          console.error('Markmap: No instance ID available for preview');
          return;
        }

        const state = getInstanceState(this.currentInstanceId);
        if (!state.currentData) {
          console.error('No data available for preview');
          return;
        }

        console.log('开始生成预览图片');
        const imageUrl = await this.generateImage(state.currentData);

        // 检测环境并选择合适的预览方式
        const isApp = typeof plus !== 'undefined';
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isMobile || isApp) {
          // 移动端使用模态框预览
          this.showImageInModal(imageUrl);
        } else {
          // 桌面端使用新窗口预览
          try {
            const newWindow = window.open('', '_blank', 'width=800,height=600');
            if (newWindow) {
              // 构建 HTML 内容
              newWindow.document.write('<!DOCTYPE html>');
              newWindow.document.write('<html><head><title>Markmap Preview</title>');
              newWindow.document.write('<style>');
              newWindow.document.write('body { margin: 0; padding: 20px; background: #f5f5f5; display: flex; justify-content: center; align-items: center; min-height: 100vh; }');
              newWindow.document.write('img { max-width: 100%; max-height: 100%; box-shadow: 0 4px 12px rgba(0,0,0,0.15); border-radius: 8px; background: white; }');
              newWindow.document.write('.download-btn { position: fixed; top: 20px; right: 20px; padding: 10px 20px; background: #007aff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; }');
              newWindow.document.write('.download-btn:hover { background: #0056b3; }');
              newWindow.document.write('</style></head><body>');
              newWindow.document.write('<img src="' + imageUrl + '" alt="Markmap Preview" />');
              newWindow.document.write('<button class="download-btn" onclick="downloadImage()">下载图片</button>');
              newWindow.document.write('<script>function downloadImage() { const a = document.createElement("a"); a.href = "' + imageUrl + '"; a.download = "markmap-" + new Date().getTime() + ".jpg"; a.click(); }<\/script>');
              newWindow.document.write('<\/body><\/html>');
              newWindow.document.close();
            } else {
              // 新窗口被阻止，使用模态框
              this.showImageInModal(imageUrl);
            }
          } catch (error) {
            console.error('新窗口预览失败:', error);
            this.showImageInModal(imageUrl);
          }
        }

        console.log('预览窗口已打开');

        uni && uni.showToast && uni.showToast({
          title: '预览已打开',
          icon: 'success'
        });

      } catch (error) {
        console.error('Preview failed:', error);
        uni && uni.showToast && uni.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.markmap-container {
  width: 100%;
  border: 1px solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;

  // 悬停时显示导出按钮
  &:hover .export-btn {
    display: flex !important;
  }

  :deep(.markmap-svg) {
    width: 100%;
    height: 100%;
    display: block;

    // 确保SVG内容可见
    * {
      vector-effect: non-scaling-stroke;
    }

    // 连接线样式
    .markmap-link {
      fill: none;
      stroke: #ccc;
      stroke-width: 2px;
      stroke-opacity: 0.8;
    }

    // 节点样式
    .markmap-node {
      cursor: pointer;

      > circle {
        fill: #fff;
        stroke-width: 2px;
      }

      > text {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        fill: #333;
        text-anchor: start;
        dominant-baseline: central;
      }
    }

    // 确保路径元素可见
    path {
      vector-effect: non-scaling-stroke;
    }
  }

  // 加载状态样式
  &::before {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #666;
    font-size: 14px;
    z-index: 1;
    white-space: nowrap;
  }

  // 当有内容时隐藏加载文字
  &:has(.markmap-svg) {
    &::before {
      display: none;
    }
  }

  // 导出按钮样式
  .export-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      transform: translateY(-1px);
    }

    .export-icon {
      font-size: 16px;
      color: #666;
    }
  }

  // 调试按钮样式
  .debug-btn {
    position: absolute;
    top: 10px;
    right: 50px;
    width: 32px;
    height: 32px;
    background: rgba(255, 165, 0, 0.9);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      background: rgba(255, 165, 0, 1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      transform: translateY(-1px);
    }

    .debug-icon {
      font-size: 16px;
      color: #fff;
    }
  }

  // 预览按钮样式
  .preview-btn {
    position: absolute;
    top: 10px;
    right: 90px;
    width: 32px;
    height: 32px;
    background: rgba(34, 139, 34, 0.9);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      background: rgba(34, 139, 34, 1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      transform: translateY(-1px);
    }

    .preview-icon {
      font-size: 16px;
      color: #fff;
    }
  }

  // 测试按钮样式
  .test-btn {
    position: absolute;
    top: 10px;
    right: 130px;
    width: 32px;
    height: 32px;
    background: rgba(138, 43, 226, 0.9);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      background: rgba(138, 43, 226, 1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      transform: translateY(-1px);
    }

    .test-icon {
      font-size: 16px;
      color: #fff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .markmap-container {
    border-radius: 8rpx;
  }
}
</style>
