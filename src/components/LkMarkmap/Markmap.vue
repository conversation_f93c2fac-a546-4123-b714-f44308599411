<template>
  <view
    class="markmap-container"
    :data="markdownData"
    :change:data="minder.init"
    :style="containerStyle"
  >
  </view>
</template>

<script>
export default {
  name: 'Markmap',
  data() {
    return {};
  },
  props: {
    data: {
      type: String,
      default: '',
    },
    height: {
      type: [String, Number],
      default: '400px',
    },
    width: {
      type: [String, Number],
      default: '100%',
    },
  },
  computed: {
    markdownData() {
      return this.data;
    },
    containerStyle() {
      return {
        height: typeof this.height === 'number' ? this.height + 'px' : this.height,
        width: typeof this.width === 'number' ? this.width + 'px' : this.width,
      };
    },
  },
  methods: {
    // 提供外部调用的方法
    refresh() {
      if (this.data) {
        this.$refs.container && this.minder.init(this.data);
      }
    },
  },
};
</script>

<script module="minder" lang="renderjs">
// 动态导入markmap库
let Transformer, Markmap;
let mm;
let transformer;
let timer;
let isLibraryLoaded = false;

// 加载markmap库
const loadMarkmapLibrary = async () => {
  if (isLibraryLoaded) return true;

  try {
    // 在App端，需要确保markmap库已经正确安装
    if (typeof window !== 'undefined') {
      // 检查是否已经加载了markmap库
      if (window.markmap) {
        Transformer = window.markmap.Transformer;
        Markmap = window.markmap.Markmap;
      } else {
        // 动态加载markmap库
        const markmapLib = await import('markmap-lib');
        const markmapView = await import('markmap-view');

        Transformer = markmapLib.Transformer;
        Markmap = markmapView.Markmap;
      }

      transformer = new Transformer();
      isLibraryLoaded = true;
      return true;
    }
  } catch (error) {
    console.error('Failed to load markmap library:', error);
    return false;
  }

  return false;
};

export default {
  data() {
    return {}
  },
  methods: {
    async init(data) {
      if (!data || typeof data !== 'string') {
        console.warn('Markmap: Invalid data provided');
        return;
      }

      // 清除之前的定时器
      if (timer) {
        clearTimeout(timer);
      }

      // 确保markmap库已加载
      const isLoaded = await loadMarkmapLibrary();
      if (!isLoaded) {
        this.showError('Markmap库加载失败');
        return;
      }

      // 延迟执行，确保DOM已渲染
      timer = setTimeout(() => {
        this.renderMarkmap(data);
      }, 100);
    },

    renderMarkmap(data) {
      try {
        const container = document.querySelector(".markmap-container");
        if (!container) {
          console.error('Markmap container not found');
          return;
        }

        // 移除已存在的SVG
        let existingSvg = container.querySelector(".markmap-svg");
        if (existingSvg) {
          container.removeChild(existingSvg);
        }

        // 创建新的SVG元素
        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        svg.setAttribute("class", "markmap-svg");
        svg.style.width = "100%";
        svg.style.height = "100%";
        svg.style.display = "block";
        container.appendChild(svg);

        // 创建Markmap实例
        mm = Markmap.create(svg, {
          duration: 500,
          maxWidth: 300,
          initialExpandLevel: 2,
          // 自定义颜色方案
          color: (node) => {
            const colors = [
              '#007aff', // 蓝色
              '#34c759', // 绿色
              '#ff9500', // 橙色
              '#ff3b30', // 红色
              '#af52de', // 紫色
              '#5ac8fa', // 青色
              '#ffcc00', // 黄色
              '#ff2d92'  // 粉色
            ];
            return colors[node.depth % colors.length];
          },
          // 自定义字体大小
          fontSize: (node) => {
            const baseFontSize = 14;
            const maxFontSize = 20;
            const minFontSize = 12;
            const fontSize = Math.max(minFontSize, Math.min(maxFontSize, baseFontSize - node.depth * 2));
            return fontSize;
          }
        });

        // 更新数据
        this.update(data);

        // 监听容器大小变化
        if (window.ResizeObserver) {
          const resizeObserver = new ResizeObserver(() => {
            if (mm) {
              mm.fit();
            }
          });
          resizeObserver.observe(container);
        }

      } catch (error) {
        console.error('Failed to render markmap:', error);
        this.showError(error.message);
      }
    },

    async update(data) {
      try {
        if (!mm || !data) return;

        // 转换markdown数据
        const { root } = transformer.transform(data);

        // 设置数据并渲染
        await mm.setData(root);
        mm.fit();

      } catch (error) {
        console.error('Failed to update markmap:', error);
        this.showError('数据更新失败');
      }
    },

    showError(message) {
      const container = document.querySelector(".markmap-container");
      if (container) {
        container.innerHTML = `
          <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: #f5f5f5;
            border-radius: 8px;
            color: #666;
            font-size: 14px;
            text-align: center;
            padding: 20px;
          ">
            <div>
              <div style="font-size: 16px; margin-bottom: 8px;">⚠️ 思维导图渲染失败</div>
              <div style="font-size: 12px; color: #999;">${message}</div>
            </div>
          </div>
        `;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.markmap-container {
  width: 100%;
  height: 400px;
  border: 1px solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;

  :deep(.markmap-svg) {
    width: 100%;
    height: 100%;
    display: block;
  }

  // 加载状态样式
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
  }

  // 当有内容时隐藏加载动画
  &:has(.markmap-svg) {
    &::before {
      display: none;
    }
  }
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .markmap-container {
    height: 300px;
  }
}
</style>
