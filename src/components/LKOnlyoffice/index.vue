<template>
  <view class="onlyoffice-container">
    <!-- 文档容器 -->
    <view
      id="placeholder"
      class="document-container"
      :change:prop="renderScript.propChange"
      :prop="renderProp"
      @ontouchstart="handleTouchStart"
    >
      <view v-if="loading" class="loading">
        {{ loadingText }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LKOnlyoffice',
  props: {
    // 文档类型
    fileType: {
      type: String,
      required: true,
    },
    // 文档唯一标识
    key: {
      type: String,
      required: true,
    },
    // 文档标题
    title: {
      type: String,
      required: true,
    },
    // 文档URL
    url: {
      type: String,
      required: true,
    },
    // 回调token
    token: {
      type: String,
      default: '',
    },
    // 是否显示控制面板
    showControls: {
      type: Boolean,
      default: false,
    },
    // 编辑器模式 view/edit
    mode: {
      type: String,
      default: 'view',
    },
    // 语言
    lang: {
      type: String,
      default: 'zh-CN',
    },
  },
  data() {
    return {
      loading: true,
      loadingText: '正在加载文档预览...',
      renderProp: {
        fileType: '',
        key: '',
        title: '',
        url: '',
        token: '',
        mode: 'view',
        lang: 'zh-CN',
      },
    };
  },
  watch: {
    fileType: {
      handler(val) {
        this.renderProp.fileType = val;
        this.updateRenderProp();
      },
      immediate: true,
    },
    key: {
      handler(val) {
        this.renderProp.key = val;
        this.updateRenderProp();
      },
      immediate: true,
    },
    title: {
      handler(val) {
        this.renderProp.title = val;
        this.updateRenderProp();
      },
      immediate: true,
    },
    url: {
      handler(val) {
        this.renderProp.url = val;
        this.updateRenderProp();
      },
      immediate: true,
    },
    token: {
      handler(val) {
        console.log('token4', val);
        this.renderProp.token = val;
        this.updateRenderProp();
      },
      immediate: true,
    },
    mode: {
      handler(val) {
        this.renderProp.mode = val;
        this.updateRenderProp();
      },
      immediate: true,
    },
    lang: {
      handler(val) {
        this.renderProp.lang = val;
        this.updateRenderProp();
      },
      immediate: true,
    },
  },
  mounted() {
    this.initComponent();
  },
  beforeDestroy() {
    this.releaseConnection();
  },
  methods: {
    handleTouchStart() {
      console.log('handleTouchStart');
    },
    initComponent() {
      // 验证必要参数
      if (!this.fileType || !this.key || !this.title || !this.url) {
        this.loading = false;
        this.loadingText = '缺少必要的预览参数';
        return;
      }

      this.updateRenderProp();
    },

    updateRenderProp() {
      this.renderProp = {
        ...this.renderProp,
        fileType: this.fileType,
        key: this.key,
        title: this.title,
        url: this.url,
        token: this.token,
        mode: this.mode,
        lang: this.lang,
        timestamp: Date.now(), // 触发更新
      };
    },

    switchToEdit() {
      this.renderProp.mode = 'edit';
      this.updateRenderProp();
    },

    switchToView() {
      this.renderProp.mode = 'view';
      this.updateRenderProp();
    },

    releaseConnection() {
      // 通过renderjs调用
      this.renderProp.action = 'release';
      this.updateRenderProp();
    },

    reloadDocument() {
      this.loading = true;
      this.loadingText = '正在重新加载文档...';
      this.renderProp.action = 'reload';
      this.updateRenderProp();
    },

    // 处理renderjs的回调
    handleDocumentReady() {
      this.loading = false;
      this.$emit('onDocumentReady');
    },

    handleDocumentError(error) {
      this.loading = false;
      this.loadingText = `文档加载失败: ${error}`;
      this.$emit('onDocumentError', error);
    },
  },
};
</script>

<script module="renderScript" lang="renderjs">
let docEditor = null;
let onlyofficeConfig = null;
let configLoaded = false;
let pendingProps = null;

export default {
  async mounted() {
    // 初始化时加载OnlyOffice配置
    await this.loadOnlyOfficeConfig();
    configLoaded = true;

    // 如果有待处理的props，现在处理它们
    if (pendingProps) {
      this.initEditor(pendingProps.props, pendingProps.ownerInstance);
      pendingProps = null;
    }
  },

  methods: {
    // 属性变化处理
    propChange(newVal, oldVal, ownerInstance, instance) {
      console.log('LKOnlyoffice propChange 被调用:', newVal);

      // 基本验证
      if (!newVal) {
        console.log('newVal 为空，跳过处理');
        return;
      }

      // 如果配置还没有加载完成，保存props等待配置加载完成
      if (!configLoaded) {
        console.log('OnlyOffice配置还未加载完成，等待配置加载...');
        pendingProps = { props: newVal, ownerInstance };
        return;
      }

      // 处理不同的动作
      if (newVal.action === 'release') {
        this.releaseConnection(ownerInstance);
        return;
      }

      if (newVal.action === 'reload') {
        this.reloadDocument(ownerInstance);
        return;
      }

      // 如果编辑器已存在且只是模式切换
      if (docEditor && oldVal && newVal.mode !== oldVal.mode) {
        this.switchMode(newVal.mode);
        return;
      }

      // 验证必要参数（在这里验证，这样可以给出更详细的错误信息）
      if (!newVal.fileType || !newVal.title || !newVal.url) {
        console.error('缺少必要参数:', {
          fileType: newVal.fileType,
          key: newVal.key,
          title: newVal.title,
          url: newVal.url
        });
        ownerInstance.callMethod('handleDocumentError', '缺少必要的文档参数');
        return;
      }

      // 初始化或重新初始化编辑器
      this.initEditor(newVal, ownerInstance);
    },

    // 加载OnlyOffice配置
    async loadOnlyOfficeConfig() {
      try {
        onlyofficeConfig = {
          onlyofficeUrl: 'https://onlyoffice-pre.hwzxs.com'
        };
        console.log('OnlyOffice配置加载完成:', onlyofficeConfig);
      } catch (error) {
        console.error('加载OnlyOffice配置失败:', error);
        throw error;
      }
    },

    // 动态加载OnlyOffice脚本
    async loadOnlyOfficeScript() {
      return new Promise((resolve, reject) => {
        if (typeof DocsAPI !== 'undefined') {
          console.log('OnlyOffice API已存在，无需重复加载');
          resolve();
          return;
        }

        if (!onlyofficeConfig || !onlyofficeConfig.onlyofficeUrl) {
          reject(new Error('OnlyOffice配置未加载或URL未配置'));
          return;
        }

        let scriptUrl = '';
        scriptUrl = `https://onlyoffice-pre.hwzxs.com/web-apps/apps/api/documents/api.js`;

        console.log('开始加载OnlyOffice脚本:', scriptUrl);

        const script = document.createElement('script');
        script.src = scriptUrl;
        script.onload = () => {
          console.log('OnlyOffice脚本加载成功');
          resolve();
        };
        script.onerror = (error) => {
          console.error('OnlyOffice脚本加载失败:', error);
          reject(new Error(`加载OnlyOffice脚本失败: ${scriptUrl}`));
        };
        document.head.appendChild(script);
      });
    },

    // 初始化编辑器
    async initEditor(props, ownerInstance) {
      try {
        console.log('开始初始化OnlyOffice编辑器:', props);

        // 验证必要参数
        if (!props.fileType || !props.title || !props.url) {
          throw new Error('缺少必要的文档参数');
        }

        // 先加载脚本
        await this.loadOnlyOfficeScript();

        // 检查DocsAPI是否可用
        if (typeof DocsAPI === 'undefined') {
          throw new Error('OnlyOffice API未正确加载');
        }
        // 基础配置
        const docConfig = {
          document: {
            fileType: props.fileType,
            key: props.key || `doc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            title: props.title,
            url: props.url
          },
          editorConfig: {
            mode:  'view',
            lang:  'zh-CN',
            user: {
      id: "78e1e841",
      name: "huayun",
    },
            coEditing: {
              mode: "strict", // 使用严格模式
              change: false   // 禁止切换协作模式
            },
            customization: {
              anonymous: true,
              feedback: false,
              comments: false,
              chat: false,
              compactHeader: true,
              compactToolbar: true,
              toolbar: false,
              hideRightMenu: true,
              hideRulers: true,
              toolbarHideFileName: true,
              plugins: false,
              help: false,
              about: false,
              logo: {
                visible: false
              },
              goback: {
                visible: false
              },
              close: {
                visible: false
              },
              callbackUrl: props.token
                ? `${window.location.origin}/api/officeCallback?token=${encodeURIComponent(props.token)}`
                : undefined,
              // 禁用协作功能
              chat: false,
              comments: false,
              review: false,
              reviewDisplay: 'markup',
              trackChanges: false,
              hideRightMenu: true,
              mentionShare: false,
              compatibleFeatures: false,
              macros: false,
              plugins: false,
              spellcheck: false
            },
            // 关闭协同编辑功能
            coEditing: {
              mode: "strict", // 使用严格模式
              change: false   // 禁止切换协作模式
            }
          },
          events: {
            onAppReady: () => {
              console.log('OnlyOffice文档编辑器已准备就绪');
            },
            onDocumentReady: () => {
              console.log('文档已加载完成');
              ownerInstance.callMethod('handleDocumentReady');
            },
            onDocumentClick: (event) => {
              console.log('文档点击事件:', event);
              ownerInstance.callMethod('handleDocumentClick', event);
            },
            onExternalMouseUp: (event) => {
              console.log('外部鼠标释放事件:', event);
              ownerInstance.callMethod('handleExternalMouseUp', event);
            },
            onError: (event) => {
              console.error('OnlyOffice编辑器错误:', event);
              const errorMsg = event.data || event.message || '文档加载失败';
              ownerInstance.callMethod('handleDocumentError', errorMsg);
            },
            onDocumentStateChange: (event) => {
              console.log('文档状态改变:', event.data);
            }
          },
          // 添加界面显示配置
          showHeader: false,
          showMenu: false,
          showFilter: false,
          showSelectorHeader: false,
          showSelectorCancel: false,
          showSettings: false,
          infoPanelVisible: false,
          showTitle: false,
          width: "100%",
          height: "100%"
        };

        console.log('OnlyOffice配置:', docConfig);

        // 清理旧的编辑器
        if (docEditor) {
          try {
            docEditor.destroyEditor();
          } catch (e) {
            console.warn('销毁旧编辑器时出错:', e);
          }
          docEditor = null;
        }

        // 确保容器存在
        const container = document.getElementById('placeholder');
        if (!container) {
          throw new Error('找不到文档容器元素');
        }

        // 创建新的编辑器
        docEditor = new DocsAPI.DocEditor('placeholder', docConfig);
        console.log('OnlyOffice编辑器创建成功');

      } catch (error) {
        console.error('初始化编辑器时发生错误:', error);
        ownerInstance.callMethod('handleDocumentError', error.message || '初始化编辑器失败');
      }
    },

    // 切换模式
    switchMode(mode) {
      if (docEditor) {
        docEditor.setMode(mode);
      }
    },

    // 释放连接
    releaseConnection(ownerInstance) {
      if (docEditor) {
        docEditor.destroyEditor();
        docEditor = null;
        return true;
      } else {
        console.log('没有活动的编辑器实例');
        return false;
      }
    },

    // 重新加载文档
    reloadDocument(ownerInstance) {
      if (docEditor) {
        ownerInstance.callMethod('handleDocumentError', '请先释放当前连接');
      } else {
        // 重新初始化
        const placeholder = document.getElementById('placeholder');
        if (placeholder) {
          placeholder.innerHTML = '<div class="loading">正在重新加载文档...</div>';
        }
        // 触发重新初始化会通过propChange处理
      }
    }
  }
}
</script>

<style scoped>
.onlyoffice-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.control-panel {
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 1000;
  background: white;
  padding: 5px;
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.control-btn {
  padding: 5px 10px;
  border: none;
  border-radius: 3px;
  background-color: #7d4dff;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background-color: #6030e0;
}

.control-btn:active {
  background-color: #5020c0;
}

.document-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: #666;
  text-align: center;
}

/* 全局样式 - 隐藏 OnlyOffice 的工具栏 */
</style>

<style>
/* 隐藏 OnlyOffice 的所有工具栏和界面元素 */
.docEditor .toolbar,
.docEditor .asc-window-toolbar,
.docEditor .header-panel,
.docEditor .statusbar,
.docEditor .left-panel,
.docEditor .right-panel,
.docEditor .toolbar-fullview-panel,
.docEditor .toolbar-mask,
.docEditor .header,
.docEditor .top-toolbar,
.docEditor .bottom-toolbar {
  display: none !important;
  visibility: hidden !important;
}

/* 隐藏右侧面板和侧边栏 */
.docEditor .sdk-content-panel .right-panel,
.docEditor .sdk-content-panel .left-panel,
.docEditor .asc-window.right-panel,
.docEditor .asc-window.left-panel {
  display: none !important;
  width: 0 !important;
}

/* 隐藏所有弹窗 */
.asc-window,
.asc-window-toolbar,
.asc-window-header {
  display: none !important;
}

/* 隐藏标尺 */
.docEditor .rulers {
  display: none !important;
}

/* 隐藏状态栏 */
.docEditor .statusbar,
.docEditor .bottom-toolbar {
  display: none !important;
}

/* 确保文档容器占满整个空间 */
#placeholder {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* 调整文档编辑区域占满全屏 */
.docEditor .sdk-content-panel {
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .control-panel {
    top: 5px;
    left: 5px;
    padding: 3px;
  }

  .control-btn {
    padding: 3px 6px;
    font-size: 10px;
  }
}

/* 确保OnlyOffice界面元素完全隐藏 */
.asc-window-header-title,
.asc-window-header-tools,
.asc-window-header-buttons,
.asc-window-header-icon,
.asc-window-header-caption,
.asc-window-header-back {
  display: none !important;
}

/* 隐藏移动端的菜单按钮和其他控制元素 */
.toolbar-menu-icon,
.toolbar-down-icon,
.toolbar-search-icon,
.toolbar-collaboration-icon,
.toolbar-title,
.toolbar-group,
.toolbar-button {
  display: none !important;
}

/* 确保文档区域占满整个容器 */
.sdk-content {
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* 隐藏协作相关的界面元素 */
.chat-panel,
.comment-panel,
.review-panel,
.collaboration-panel,
.asc-window-users,
.asc-window-comments,
.asc-window-review,
.asc-window-chat,
.asc-window-collaboration,
.toolbar-collaboration,
.toolbar-review,
.toolbar-comments,
.toolbar-chat,
.toolbar-users,
.toolbar-coauthoring {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  visibility: hidden !important;
}
</style>
