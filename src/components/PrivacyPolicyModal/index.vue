<template>
  <uni-popup ref="privacyPopup" type="center" :maskClick="false">
    <view class="privacy-modal">
      <view class="modal-header">
        <text class="title">隐私政策与用户协议</text>
      </view>

      <view class="modal-content">
        <iframe
          src="https://privacy.huayuntiantu.com/aiparent.html"
          class="privacy-iframe"
          frameborder="0"
        ></iframe>
      </view>

      <view class="modal-footer">
        <view class="button-group">
          <u-button
            class="cancel-btn"
            type="default"
            @click="handleCancel"
            :customStyle="cancelButtonStyle"
          >
            不同意
          </u-button>
          <u-button
            class="confirm-btn"
            type="primary"
            @click="handleConfirm"
            :customStyle="confirmButtonStyle"
          >
            同意并继续
          </u-button>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 弹窗引用
const privacyPopup = ref();

// 按钮样式
const cancelButtonStyle = {
  width: '140rpx',
  height: '72rpx',
  borderRadius: '36rpx',
  fontSize: '28rpx',
  border: '2rpx solid #E5E5E5',
  background: '#FFFFFF',
  color: '#666666',
};

const confirmButtonStyle = {
  width: '200rpx',
  height: '72rpx',
  borderRadius: '36rpx',
  fontSize: '28rpx',
  background: '#7D4DFF',
  border: 'none',
};

// 事件定义
const emit = defineEmits<{
  confirm: [];
  cancel: [];
}>();

// 打开弹窗
const open = () => {
  privacyPopup.value?.open();
};

// 关闭弹窗
const close = () => {
  privacyPopup.value?.close();
};

// 处理确认
const handleConfirm = () => {
  close();
  emit('confirm');
};

// 处理取消
const handleCancel = () => {
  close();
  emit('cancel');
};

// 暴露方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.privacy-modal {
  width: 640rpx;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .modal-header {
    padding: 40rpx 40rpx 20rpx;
    text-align: center;
    border-bottom: 2rpx solid #f5f5f5;

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
    }
  }

  .modal-content {
    flex: 1;
    min-height: 0;
    padding: 20rpx;

    .privacy-iframe {
      width: 100%;
      height: 60vh;
      max-height: 800rpx;
      border: none;
      border-radius: 12rpx;
      background: #ffffff;
    }
  }

  .modal-footer {
    padding: 30rpx 40rpx 40rpx;
    border-top: 2rpx solid #f5f5f5;

    .button-group {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 30rpx;
    }
  }
}
</style>
