<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';

interface SelectOption {
  value: string | number;
  text: string;
}

interface Props {
  modelValue?: string | number;
  localdata?: SelectOption[];
  placeholder?: string;
  searchPlaceholder?: string;
  disabled?: boolean;
  clearable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  localdata: () => [],
  placeholder: '请选择',
  searchPlaceholder: '请输入关键词搜索',
  disabled: false,
  clearable: true,
});

const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  change: [value: string | number];
  search: [keyword: string];
}>();

// 响应式数据
const isOpen = ref(false);
const searchKeyword = ref('');
const dropdownRef = ref();
const searchInputRef = ref();

// 计算属性
const selectedOption = computed(() => {
  return props.localdata.find(option => option.value === props.modelValue);
});

const displayText = computed(() => {
  return selectedOption.value?.text || props.placeholder;
});

// 监听搜索关键词变化
watch(searchKeyword, newKeyword => {
  emit('search', newKeyword);
});

// 方法
const toggleDropdown = () => {
  if (props.disabled) return;

  isOpen.value = !isOpen.value;

  if (isOpen.value) {
    // 打开时清空搜索关键词并聚焦输入框
    searchKeyword.value = '';
  }
};

const closeDropdown = () => {
  isOpen.value = false;
  searchKeyword.value = '';
};

const selectOption = (option: SelectOption) => {
  emit('update:modelValue', option.value);
  emit('change', option.value);
  closeDropdown();
};

const clearSelection = (event: Event) => {
  event.stopPropagation();
  emit('update:modelValue', '');
  emit('change', '');
};

// 处理输入框点击事件
const handleInputClick = () => {
  if (props.disabled) return;

  if (!isOpen.value) {
    isOpen.value = true;
    searchKeyword.value = '';
  }

  nextTick(() => {
    setTimeout(() => {
      searchInputRef.value?.focus();
    }, 100);
  });
};

// 处理右侧图标点击事件
const handleArrowClick = (event: Event) => {
  event.stopPropagation();
  if (isOpen.value) {
    closeDropdown();
  } else {
    toggleDropdown();
  }
};

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    closeDropdown();
  }
};

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 暴露方法给父组件
defineExpose({
  selectOption,
  closeDropdown,
});
</script>

<template>
  <view class="lk-dropdown-search" ref="dropdownRef">
    <!-- 下拉框主体（输入框区域） -->
    <view
      class="dropdown-trigger"
      :class="{
        'is-open': isOpen,
        'is-disabled': disabled,
        'has-value': modelValue,
      }"
    >
      <!-- 主输入框 -->
      <input
        ref="searchInputRef"
        v-model="searchKeyword"
        :placeholder="isOpen ? searchPlaceholder : selectedOption?.text ? '' : placeholder"
        :disabled="disabled"
        class="main-input"
        @click="handleInputClick"
      />

      <!-- 显示选中的文本（当未打开且有选中值时） -->
      <text v-if="!isOpen && selectedOption" class="selected-text" @click="handleInputClick">
        {{ selectedOption.text }}
      </text>

      <!-- 清除按钮 -->
      <view v-if="clearable && modelValue && !disabled" class="clear-btn" @click="clearSelection">
        <text class="clear-icon">×</text>
      </view>

      <!-- 下拉箭头 -->
      <view class="arrow-icon" :class="{ 'is-open': isOpen }" @click="handleArrowClick">
        <text class="arrow">▼</text>
      </view>
    </view>

    <!-- 下拉内容区域 -->
    <view v-if="isOpen" class="dropdown-content">
      <!-- 插槽内容 -->
      <view class="slot-container">
        <slot
          :keyword="searchKeyword"
          :selectOption="selectOption"
          :closeDropdown="closeDropdown"
        ></slot>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.lk-dropdown-search {
  position: relative;
  width: 100%;
}

.dropdown-trigger {
  position: relative;
  background: #ffffff;
  padding: 13px 20px;
  border-radius: 50px;
  height: auto;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &.is-disabled {
    background: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.6;
  }

  &.is-open {
    border-color: rgb(125, 77, 255);
    box-shadow: 0 0 0 2px rgba(125, 77, 255, 0.1);
  }
}

.main-input {
  flex: 1;
  font-size: 14px;
  color: rgb(48, 49, 51);
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  height: 20px;
  line-height: 20px;

  &::placeholder {
    color: rgb(174, 174, 178);
  }

  &:disabled {
    cursor: not-allowed;
    color: #999;
  }
}

.selected-text {
  position: absolute;
  left: 20px;
  right: 60px;
  font-size: 14px;
  color: rgb(48, 49, 51);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1;
}

.clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  cursor: pointer;
  border-radius: 50%;
  background: #f0f0f0;
  transition: background 0.2s ease;

  &:hover {
    background: #e0e0e0;
  }

  .clear-icon {
    font-size: 14px;
    color: #999;
    line-height: 1;
  }
}

.arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 20px;
  transition: transform 0.3s ease;

  &.is-open {
    transform: rotate(180deg);
  }

  .arrow {
    font-size: 12px;
    color: #999;
    line-height: 1;
  }
}

.dropdown-content {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
}

.slot-container {
  max-height: 200px;
  overflow-y: auto;
}
</style>
