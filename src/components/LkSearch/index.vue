<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import LkSvg from '@/components/svg/index.vue';

interface Props {
  modelValue?: string;
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean;
  maxlength?: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入关键词搜索',
  disabled: false,
  clearable: true,
  maxlength: -1,
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
  search: [value: string];
  clear: [];
  focus: [];
  blur: [];
}>();

// 内部搜索值
const innerValue = ref(props.modelValue);

// 监听外部值变化
watch(
  () => props.modelValue,
  newValue => {
    innerValue.value = newValue;
  }
);

// 是否显示清除按钮
const showClear = computed(() => {
  return props.clearable && innerValue.value && !props.disabled;
});

// 输入事件
const handleInput = (e: any) => {
  const value = e.detail.value;
  innerValue.value = value;
  emit('update:modelValue', value);
};

// 搜索事件
const handleSearch = () => {
  if (props.disabled) return;
  emit('search', innerValue.value);
};

// 清除事件
const handleClear = () => {
  innerValue.value = '';
  emit('update:modelValue', '');
  emit('clear');
};

// 聚焦事件
const handleFocus = () => {
  emit('focus');
};

// 失焦事件
const handleBlur = () => {
  emit('blur');
};
</script>

<template>
  <view class="lk-search">
    <view class="lk-search__content">
      <view class="lk-search__icon-search">
        <LkSvg width="24px" height="24px" src="/static/search/search2.svg" />
      </view>
      <input
        class="lk-search__input"
        type="text"
        :value="innerValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        confirm-type="search"
        @input="handleInput"
        @confirm="handleSearch"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      <view v-if="showClear" class="lk-search__icon-clear" @click.stop="handleClear">
        <LkSvg width="28rpx" height="28rpx" src="/static/search/clear.svg" color="#C0C4CC" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.lk-search {
  width: 100%;

  &__content {
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 12px;
    background-color: #f6f7f9;
    border-radius: 8px;
    position: relative;
  }

  &__icon-search {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12rpx;
    flex-shrink: 0;
  }

  &__input {
    flex: 1;
    height: 100%;
    font-size: 26rpx;
    color: #1d2129;
    background-color: transparent;
    line-height: 64rpx;

    &::placeholder {
      color: #c0c4cc;
      font-size: 26rpx;
    }

    // 移除输入框默认样式
    &:focus {
      outline: none;
    }
  }

  &__icon-clear {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12rpx;
    padding: 8rpx;
    flex-shrink: 0;

    &:active {
      opacity: 0.6;
    }
  }
}

// 禁用状态
.lk-search--disabled {
  .lk-search__content {
    background-color: #f6f7f9;
    opacity: 0.5;
  }

  .lk-search__input {
    color: #c0c4cc;
    cursor: not-allowed;
    &::placeholder {
      color: #86909c;
      font-size: 16px;
    }
  }
}

// 暗色主题支持（可选）
@media (prefers-color-scheme: dark) {
  .lk-search {
    &__content {
      background-color: #2a2a2a;
    }

    &__input {
      color: #fff;

      &::placeholder {
        color: #666;
      }
    }
  }
}
</style>
