/**
 * MyRecoderPopup 录音工具库
 * 基于 uni-app 的录音管理器 API
 */

import type {
  RecordOptions,
  RecordController,
  RecordResult,
  AudioPlayerOptions,
  AudioPlayerController,
} from './type';

// 当前活动的录音控制器
let activeRecorder: RecordController | null = null;

/**
 * 开始录音
 * @param options 录音选项
 * @returns 录音控制器
 */
export const startRecord = (options: RecordOptions): RecordController => {
  // 创建录音控制器
  const controller: RecordController = {
    state: 'idle',
    stop: () => {},
    pause: () => {},
    resume: () => {},
  };

  // 停止原因
  let stopReason = '';

  // 获取录音管理器实例
  const recorderManager = uni.getRecorderManager();

  // 录音开始事件
  recorderManager.onStart(() => {
    console.log('录音开始');
    controller.state = 'recording';
    options?.onStart?.();
  });

  // 录音暂停事件
  recorderManager.onPause(() => {
    console.log('录音暂停');
    controller.state = 'paused';
  });

  // 录音停止事件
  recorderManager.onStop((res: RecordResult) => {
    console.log('录音结束', stopReason, res);

    // 如果是取消录音
    if (stopReason === 'cancel') {
      controller.state = 'idle';
      options?.onCancel?.();
      return;
    }

    // 如果录音时长太短
    if (res.duration < 500) {
      console.log('录音时长太短，自动取消');
      controller.state = 'idle';
      options?.onCancel?.();
      return;
    }

    // 录音成功
    controller.state = 'idle';
    options?.onResult?.(res.tempFilePath);
  });

  // 录音错误事件
  recorderManager.onError(err => {
    console.error('录音错误', err);
    controller.state = 'idle';

    // 处理常见错误
    if (err.errMsg?.includes('NotFoundError')) {
      uni.showToast({ title: '未检测到麦克风', icon: 'none' });
    } else if (err.errMsg?.includes('not declared in the privacy agreement')) {
      uni.showToast({ title: '未添加录音权限', icon: 'none' });
    } else {
      uni.showToast({ title: '录音错误', icon: 'none' });
    }

    options?.onError?.(err);
  });

  // 录音过程中音量变化事件
  recorderManager.onFrameRecorded(res => {
    const { frameSize, isLastFrame } = res;
    // 计算音量大小，范围 0-100
    const volume = Math.min(100, Math.floor(frameSize / 100));
    options?.onVolumeChange?.(volume);
  });

  // 实现停止录音方法
  controller.stop = (reason?: string) => {
    if (controller.state === 'idle') return;

    stopReason = reason || '';
    recorderManager.stop();

    if (reason === 'cancel') {
      controller.state = 'idle';
    }
  };

  // 实现暂停录音方法
  controller.pause = () => {
    if (controller.state !== 'recording') return;

    recorderManager.pause();
    controller.state = 'paused';
  };

  // 实现继续录音方法
  controller.resume = () => {
    if (controller.state !== 'paused') return;

    recorderManager.resume();
    controller.state = 'recording'; // 手动设置状态为录音中
  };

  // 如果有活动的录音，先停止
  if (activeRecorder && activeRecorder.state !== 'idle') {
    activeRecorder.stop('cancel');
  }

  // 设置当前活动的录音控制器
  activeRecorder = controller;

  // 设置录音参数并开始录音
  const startOptions = {
    duration: 600000, // 最长录音时间，单位ms，默认10分钟
    sampleRate: 16000, // 采样率
    numberOfChannels: 1, // 录音通道数
    encodeBitRate: 96000, // 编码码率
    format: 'mp3', // 音频格式
    frameSize: 50, // 指定帧大小，单位KB
  };

  // 根据平台调整参数
  // #ifdef APP-PLUS
  if (uni.getSystemInfoSync().platform === 'ios') {
    // iOS平台使用mp3格式
    recorderManager.start({
      ...startOptions,
      format: 'mp3',
    });
  } else {
    // 安卓平台使用wav格式
    recorderManager.start({
      ...startOptions,
      format: 'wav',
      sampleRate: 8000, // 安卓平台采样率调低
    });
  }
  // #endif

  // #ifndef APP-PLUS
  // 非App平台使用默认参数
  recorderManager.start(startOptions);
  // #endif

  controller.state = 'recording';
  return controller;
};

/**
 * 停止当前录音
 * @param reason 停止原因，'cancel'表示取消录音
 */
export const stopRecord = (reason = '') => {
  activeRecorder?.stop(reason);
};

/**
 * 创建音频播放器
 * @param filePath 音频文件路径
 * @param options 播放选项
 * @returns 音频播放器控制器
 */
export const createAudioPlayer = (
  filePath: string,
  options?: AudioPlayerOptions
): AudioPlayerController => {
  // 创建内部音频上下文
  const audioContext = uni.createInnerAudioContext();

  // 创建播放器控制器
  const controller: AudioPlayerController = {
    state: 'idle',
    play: () => {},
    pause: () => {},
    stop: () => {},
    seek: () => {},
    destroy: () => {},
    currentTime: 0,
    duration: 0,
  };

  // 设置音频源
  audioContext.src = filePath;
  audioContext.autoplay = false; // 默认不自动播放

  // 播放开始事件
  audioContext.onPlay(() => {
    console.log('音频播放开始');
    controller.state = 'playing';
    options?.onPlay?.();
  });

  // 播放暂停事件
  audioContext.onPause(() => {
    console.log('音频播放暂停');
    controller.state = 'paused';
    options?.onPause?.();
  });

  // 播放停止事件
  audioContext.onStop(() => {
    console.log('音频播放停止');
    controller.state = 'idle';
    options?.onStop?.();
  });

  // 播放结束事件
  audioContext.onEnded(() => {
    console.log('音频播放结束');
    controller.state = 'idle';
    options?.onEnded?.();
  });

  // 播放错误事件
  audioContext.onError(err => {
    console.error('音频播放错误', err);
    controller.state = 'idle';

    // 尝试使用替代方法播放（仅在App环境下）
    // #ifdef APP-PLUS
    try {
      console.log('尝试使用plus.audio播放');
      const plusAudio = plus.audio.createPlayer(filePath);
      plusAudio.play();

      // 监听播放结束
      plusAudio.addEventListener('ended', () => {
        controller.state = 'idle';
        options?.onEnded?.();
      });
    } catch (plusErr) {
      console.error('plus.audio播放失败', plusErr);
      options?.onError?.(err);
    }
    // #endif

    // 非App环境直接报错
    // #ifndef APP-PLUS
    options?.onError?.(err);
    // #endif
  });

  // 播放进度更新事件
  audioContext.onTimeUpdate(() => {
    controller.currentTime = audioContext.currentTime;
    controller.duration = audioContext.duration;
    options?.onTimeUpdate?.(audioContext.currentTime, audioContext.duration);
  });

  // 实现播放方法
  controller.play = () => {
    try {
      // 检查文件是否存在
      uni.getFileInfo({
        filePath,
        success: () => {
          audioContext.play();
          controller.state = 'playing';
        },
        fail: err => {
          console.error('文件不存在', err);

          // 尝试直接播放
          console.log('尝试直接播放');
          audioContext.play();
        },
      });
    } catch (error) {
      console.error('播放错误', error);
      options?.onError?.(error);
    }
  };

  // 实现暂停方法
  controller.pause = () => {
    audioContext.pause();
    controller.state = 'paused';
  };

  // 实现停止方法
  controller.stop = () => {
    audioContext.stop();
    controller.state = 'idle';
  };

  // 实现跳转方法
  controller.seek = (position: number) => {
    audioContext.seek(position);
  };

  // 实现销毁方法
  controller.destroy = () => {
    audioContext.destroy();
    controller.state = 'idle';
  };

  return controller;
};

/**
 * 检查文件是否存在
 * @param filePath 文件路径
 * @returns Promise<boolean>
 */
export const checkFileExists = (filePath: string): Promise<boolean> => {
  return new Promise(resolve => {
    uni.getFileInfo({
      filePath,
      success: () => resolve(true),
      fail: () => resolve(false),
    });
  });
};
