/**
 * MyRecoderPopup 录音工具库类型定义
 */

// 录音配置选项接口
export interface RecordOptions {
  // 录音开始回调
  onStart?: () => void;
  // 录音取消回调
  onCancel?: () => void;
  // 录音完成回调，返回录音文件路径
  onResult?: (filePath: string) => void;
  // 录音错误回调
  onError?: (error: any) => void;
  // 录音过程中音量变化回调
  onVolumeChange?: (volume: number) => void;
}

// 录音控制器接口
export interface RecordController {
  // 录音状态：'idle'(空闲)、'recording'(录音中)、'paused'(暂停)
  state: string;
  // 停止录音
  stop: (reason?: string) => void;
  // 暂停录音
  pause: () => void;
  // 继续录音
  resume: () => void;
}

// 录音结果接口
export interface RecordResult {
  // 录音文件临时路径
  tempFilePath: string;
  // 录音时长，单位：ms
  duration: number;
  // 录音文件大小，单位：Byte
  fileSize: number;
}

/**
 * 播放音频接口
 */
export interface AudioPlayerOptions {
  // 播放开始回调
  onPlay?: () => void;
  // 播放暂停回调
  onPause?: () => void;
  // 播放停止回调
  onStop?: () => void;
  // 播放结束回调
  onEnded?: () => void;
  // 播放错误回调
  onError?: (error: any) => void;
  // 播放进度更新回调，返回当前播放时间(秒)和总时长(秒)
  onTimeUpdate?: (currentTime: number, duration: number) => void;
}

/**
 * 音频播放器控制器接口
 */
export interface AudioPlayerController {
  // 播放状态：'idle'(空闲)、'playing'(播放中)、'paused'(暂停)
  state: string;
  // 播放
  play: () => void;
  // 暂停
  pause: () => void;
  // 停止
  stop: () => void;
  // 跳转到指定位置
  seek: (position: number) => void;
  // 销毁播放器
  destroy: () => void;
  // 当前播放时间(秒)
  currentTime: number;
  // 音频总时长(秒)
  duration: number;
}
