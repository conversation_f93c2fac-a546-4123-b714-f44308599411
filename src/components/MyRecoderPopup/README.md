# MyRecoderPopup 语音录入组件

一个功能完整的录音弹窗组件，适用于Vue3 + UniApp项目，提供录音、播放、暂停等功能，并支持波形动画显示。

## 功能特点

- 🎙️ 提供语音录制功能，最长支持10分钟录音
- ▶️ 支持录音播放、暂停控制
- 📊 实时显示动态波形动画效果
- ⏱️ 准确的录音计时显示
- 📱 适配移动端交互体验
- 🔌 提供丰富的事件回调接口

## 安装和使用

### 导入组件

```js
// 导入组件
import MyRecoderPopup from '@/components/MyRecoderPopup';
```

### 基本用法

```vue
<template>
  <!-- 1. 在模板中引用组件 -->
  <MyRecoderPopup
    ref="recorderRef"
    @record-success="handleRecordSuccess"
    @record-cancel="handleRecordCancel"
  />

  <!-- 2. 添加按钮打开录音弹窗 -->
  <button @click="openRecorder">开始录音</button>
</template>

<script setup>
import { ref } from 'vue';
import MyRecoderPopup from '@/components/MyRecoderPopup';

// 获取组件引用
const recorderRef = ref(null);

// 打开录音弹窗
const openRecorder = () => {
  recorderRef.value.open();
};

// 处理录音成功事件
const handleRecordSuccess = data => {
  console.log('录音成功:', data.path, '时长:', data.duration + '秒');
};

// 处理录音取消事件
const handleRecordCancel = () => {
  console.log('录音已取消');
};
</script>
```

## 组件API

### Props

该组件没有必需的Props。

### 事件

| 事件名         | 说明             | 回调参数                                                    |
| -------------- | ---------------- | ----------------------------------------------------------- |
| record-success | 录音成功时触发   | `{duration: number, path: string}` - 录音时长(秒)和文件路径 |
| record-cancel  | 录音被取消时触发 | -                                                           |
| close          | 弹窗关闭时触发   | -                                                           |

### 方法

| 方法名 | 说明         | 参数 |
| ------ | ------------ | ---- |
| open   | 打开录音弹窗 | -    |
| close  | 关闭录音弹窗 | -    |

## 核心API

组件内部提供了两个核心API，可单独使用：

### 录音API

```js
import { startRecord } from '@/components/MyRecoderPopup/recoder';

// 开始录音
const recorder = startRecord({
  onStart: () => {
    console.log('开始录音');
  },
  onResult: filePath => {
    console.log('录音完成:', filePath);
  },
  onVolumeChange: volume => {
    console.log('音量:', volume);
  },
});

// 停止录音
recorder.stop();
```

### 音频播放API

```js
import { createAudioPlayer } from '@/components/MyRecoderPopup/recoder';

// 创建音频播放器
const player = createAudioPlayer('audio-file-path.mp3', {
  onPlay: () => {
    console.log('开始播放');
  },
  onPause: () => {
    console.log('暂停播放');
  },
  onEnded: () => {
    console.log('播放结束');
  },
});

// 播放控制
player.play(); // 播放
player.pause(); // 暂停
player.stop(); // 停止
player.seek(10); // 跳转到10秒位置
player.destroy(); // 销毁播放器
```

## 类型定义

组件导出以下TypeScript类型：

```ts
// 录音结果
export interface RecordResult {
  tempFilePath: string; // 临时文件路径
  duration: number; // 录音时长(毫秒)
  fileSize: number; // 文件大小(字节)
}

// 录音控制器
export interface RecordController {
  state: string; // 录音状态
  stop: () => void; // 停止录音
  pause: () => void; // 暂停录音
  resume: () => void; // 继续录音
}

// 音频播放器控制器
export interface AudioPlayerController {
  state: string; // 播放状态
  play: () => void; // 播放
  pause: () => void; // 暂停
  stop: () => void; // 停止
  seek: (position: number) => void; // 跳转
  destroy: () => void; // 销毁
  currentTime: number; // 当前播放时间
  duration: number; // 音频总时长
}
```

## 平台兼容性

- 支持微信小程序、APP、H5等UniApp支持的平台
- 不同平台的录音权限需单独处理

## 注意事项

1. 使用前请确保已获取录音权限
2. H5环境下录音功能可能受到浏览器安全策略限制
3. 录音文件为临时文件，如需永久保存需进行上传处理

## 贡献与反馈

如发现问题或有功能建议，请联系开发团队。
