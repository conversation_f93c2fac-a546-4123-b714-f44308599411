<template>
  <uni-popup ref="popupRef" type="bottom" :is-mask-click="false" @change="handleChange">
    <view class="my-recoder-popup">
      <!-- 标题栏 -->
      <view class="popup-title">
        <text class="title-text">语音录入</text>
        <view class="close-icon" @click="handleClose">
          <text class="close-icon-text">×</text>
        </view>
      </view>

      <!-- 初始状态 -->
      <view class="popup-body" v-if="status === 'init'">
        <text class="hint-text">最长十分钟</text>
        <view class="record-btn" @click="handleStartRecord">
          <LkSvg class="icon-white" src="/static/chat/mic.svg" width="60rpx" height="60rpx" />
        </view>
        <text class="record-tip">点击开始录音</text>
      </view>

      <!-- 录音中状态 -->
      <view class="popup-body" v-else-if="status === 'recording'">
        <text class="time-text">{{ formattedTime }}</text>
        <view class="record-btn stop-btn" @click="handleStopRecord">
          <view class="stop-icon"></view>
        </view>
        <text class="recording-tip">正在说话，点击停止</text>
        <view class="wave-container">
          <view
            v-for="(item, index) in waveItems"
            :key="index"
            :class="['wave-item', `wave-item-${index}`]"
            :style="{ height: `${item}rpx` }"
          ></view>
        </view>
      </view>

      <!-- 录音完成状态 -->
      <view class="popup-body" v-else-if="status === 'finished'">
        <text class="time-text">{{ formattedTime }}</text>
        <view class="record-btn play-btn" @click="handlePlayRecord">
          <image
            v-if="isPlaying"
            class="icon-white"
            src="/static/homework/pause_fill.svg"
            mode="aspectFit"
            style="width: 60rpx; height: 60rpx"
          />
          <image
            v-else
            class="icon-white"
            src="/static/homework/play_fill.svg"
            mode="aspectFit"
            style="width: 60rpx; height: 60rpx"
          />
        </view>
        <view class="button-group">
          <button class="btn btn-outline" @click="handleReRecord">重录</button>
          <button class="btn btn-primary" @click="handleSubmit">完成</button>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { LkSvg } from '@/components';
import {
  startRecord,
  createAudioPlayer,
  type RecordController,
  type AudioPlayerController,
} from './recoder';

// 定义组件事件
const emit = defineEmits<{
  // 录音成功事件
  (e: 'record-success', data: { duration: number; path: string }): void;
  // 录音取消事件
  (e: 'record-cancel'): void;
  // 关闭弹窗事件
  (e: 'close'): void;
}>();

// uni-popup 组件引用
const popupRef = ref();

// 录音状态
const status = ref('init'); // init, recording, finished
const recordTime = ref(0); // 录音时长（秒）
const recordPath = ref(''); // 录音文件路径
const timer = ref<number | null>(null);
const recorder = ref<RecordController | null>(null);
const audioPlayer = ref<AudioPlayerController | null>(null);
const waveItems = ref(Array(24).fill(10)); // 初始化音波高度
const isVoiceProcessing = ref(false);
const voiceStartTime = ref(0);
const isPlaying = ref(false); // 是否正在播放
const currentPlayTime = ref(0); // 当前播放时间（秒）
const audioDuration = ref(0); // 音频总时长（秒）

// 格式化时间显示
const formattedTime = computed(() => {
  // 如果正在播放，显示当前播放时间
  const timeToFormat = isPlaying.value ? currentPlayTime.value : recordTime.value;
  const minutes = Math.floor(timeToFormat / 60);
  const seconds = Math.floor(timeToFormat % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
});

// 随机生成波浪动画效果
const updateWaveItems = () => {
  waveItems.value = waveItems.value.map(() => {
    return Math.floor(Math.random() * 25) + 5;
  });
};

// 打开弹窗
const open = () => {
  popupRef.value?.open('bottom');
};

// 关闭弹窗
const close = () => {
  popupRef.value?.close();
};

// 处理弹窗状态变化
const handleChange = (e: { show: boolean }) => {
  if (!e.show) {
    resetRecorder();
    emit('close');
  }
};

// 处理关闭
const handleClose = () => {
  close();
};

// 开始录音
const handleStartRecord = () => {
  status.value = 'recording';
  recordTime.value = 0;
  voiceStartTime.value = Date.now(); // 记录开始时间

  // 使用录音工具库开始录音
  recorder.value = startRecord({
    onStart: () => {
      console.log('录音开始');
    },
    onResult: (filePath: string) => {
      console.log('录音完成，文件路径:', filePath);
      recordPath.value = filePath;
      isVoiceProcessing.value = false;
    },
    onCancel: () => {
      console.log('录音取消');
      status.value = 'init';
      isVoiceProcessing.value = false;
    },
    onError: err => {
      console.error('录音错误:', err);
      status.value = 'init';
      isVoiceProcessing.value = false;
      uni.showToast({
        title: '录音失败',
        icon: 'none',
      });
    },
    onVolumeChange: volume => {
      // 可以根据音量大小动态更新波形
      updateWaveItems();
    },
  });

  // 启动定时器
  timer.value = setInterval(() => {
    recordTime.value++;
    updateWaveItems(); // 更新波形动画

    // 达到最大时间自动停止
    if (recordTime.value >= 600) {
      // 最大10分钟
      handleStopRecord();
    }
  }, 1000) as unknown as number;
};

// 停止录音
const handleStopRecord = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }

  // 判断录音时间是否太短
  const recordingDuration = Date.now() - voiceStartTime.value;
  const minDuration = 500; // 最小录音时长（毫秒）

  if (recordingDuration < minDuration) {
    // 录音时间太短
    uni.showToast({
      title: '说话时间太短',
      icon: 'none',
      duration: 1500,
    });

    if (recorder.value) {
      recorder.value.stop('cancel');
      recorder.value = null;
    }

    status.value = 'init';
    isVoiceProcessing.value = false;
    return;
  }

  // 正常停止录音
  if (recorder.value) {
    recorder.value.stop();
    isVoiceProcessing.value = true; // 开始语音识别，显示loading
  }

  status.value = 'finished';
};

// 播放/暂停录音
const handlePlayRecord = () => {
  console.log('点击播放按钮，当前路径:', recordPath.value);

  if (!recordPath.value) {
    uni.showToast({
      title: '录音文件不存在',
      icon: 'none',
    });
    return;
  }

  try {
    if (isPlaying.value) {
      // 如果正在播放，则暂停
      console.log('暂停播放');
      if (audioPlayer.value) {
        audioPlayer.value.pause();
      }
      isPlaying.value = false;

      // 清除播放时间更新定时器
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
      }
    } else {
      // 如果未播放，则开始播放
      console.log('开始播放:', recordPath.value);

      // 如果播放器已存在，先销毁
      if (audioPlayer.value) {
        audioPlayer.value.destroy();
      }

      // 创建新的音频播放器
      audioPlayer.value = createAudioPlayer(recordPath.value, {
        onPlay: () => {
          console.log('音频开始播放');
          isPlaying.value = true;

          // 设置播放时间更新定时器
          if (timer.value) {
            clearInterval(timer.value);
          }

          // 重置当前播放时间
          currentPlayTime.value = 0;

          timer.value = setInterval(() => {
            if (audioPlayer.value) {
              currentPlayTime.value = audioPlayer.value.currentTime;
              audioDuration.value = audioPlayer.value.duration;

              // 如果播放结束，清除定时器
              if (currentPlayTime.value >= audioDuration.value) {
                clearInterval(timer.value as number);
                timer.value = null;
              }
            }
          }, 100) as unknown as number;
        },
        onPause: () => {
          console.log('音频暂停播放');
          isPlaying.value = false;

          // 清除播放时间更新定时器
          if (timer.value) {
            clearInterval(timer.value);
            timer.value = null;
          }
        },
        onStop: () => {
          console.log('音频停止播放');
          isPlaying.value = false;
          currentPlayTime.value = 0;

          // 清除播放时间更新定时器
          if (timer.value) {
            clearInterval(timer.value);
            timer.value = null;
          }
        },
        onEnded: () => {
          console.log('音频播放结束');
          isPlaying.value = false;
          currentPlayTime.value = 0;

          // 清除播放时间更新定时器
          if (timer.value) {
            clearInterval(timer.value);
            timer.value = null;
          }
        },
        onError: err => {
          console.error('音频播放错误:', err);
          isPlaying.value = false;

          // 清除播放时间更新定时器
          if (timer.value) {
            clearInterval(timer.value);
            timer.value = null;
          }

          uni.showToast({
            title: '播放失败',
            icon: 'none',
          });
        },
        onTimeUpdate: (currentTime, duration) => {
          // 更新播放进度
          currentPlayTime.value = currentTime;
          audioDuration.value = duration;
        },
      });

      // 开始播放
      audioPlayer.value.play();
    }
  } catch (error) {
    console.error('播放错误:', error);
    uni.showToast({
      title: '播放失败',
      icon: 'none',
    });
  }
};

// 重新录制
const handleReRecord = () => {
  // 如果正在播放，先停止播放
  if (isPlaying.value && audioPlayer.value) {
    audioPlayer.value.stop();
    isPlaying.value = false;
  }

  // 清除播放时间更新定时器
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }

  status.value = 'init';
  recordTime.value = 0;
  recordPath.value = '';
  currentPlayTime.value = 0;
};

// 提交录音
const handleSubmit = () => {
  if (recordPath.value) {
    // 如果正在播放，先停止播放
    if (isPlaying.value && audioPlayer.value) {
      audioPlayer.value.stop();
      isPlaying.value = false;
    }

    // 清除播放时间更新定时器
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null;
    }

    emit('record-success', {
      duration: recordTime.value,
      path: recordPath.value,
    });
    resetRecorder();
    close();
  } else {
    uni.showToast({
      title: '录音失败，请重试',
      icon: 'none',
    });
  }
};

// 重置录音器状态
const resetRecorder = () => {
  if (recorder.value && recorder.value.state !== 'idle') {
    recorder.value.stop('cancel');
    recorder.value = null;
  }

  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }

  // 如果正在播放，停止播放
  if (isPlaying.value && audioPlayer.value) {
    audioPlayer.value.stop();
    audioPlayer.value = null;
    isPlaying.value = false;
  }

  status.value = 'init';
  recordTime.value = 0;
  recordPath.value = '';
  currentPlayTime.value = 0;
  audioDuration.value = 0;
};

onMounted(() => {
  // 初始化工作
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }

  if (recorder.value && recorder.value.state !== 'idle') {
    recorder.value.stop('cancel');
  }

  if (audioPlayer.value) {
    audioPlayer.value.destroy();
  }
});

// 向父组件暴露方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.my-recoder-popup {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 32rpx 32rpx;
  position: relative;
}

.popup-title {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 52rpx;
  margin-bottom: 48rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1d2129;
  line-height: 52rpx;
}

.close-icon {
  position: absolute;
  right: 0;
  top: 0;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  &-text {
    font-size: 48rpx;
    color: #1d2129;
  }
}

.popup-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0rpx 40rpx 0rpx;
  background-color: #fff;
  border-radius: 24rpx;
  min-height: 500rpx;
}

.hint-text,
.recording-tip {
  font-size: 28rpx;
  color: #4e5969;
  font-weight: 500;
  margin-top: 20rpx;
}

.time-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #4e5969;
  margin: 40rpx 0;
}

.record-btn {
  width: 130rpx;
  height: 130rpx;
  border-radius: 999rpx;
  background: linear-gradient(180deg, #4da3ff 0%, #7d4dff 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40rpx 0;
  box-shadow:
    0 10rpx 10rpx -6rpx rgba(96, 87, 182, 0.1),
    0 16rpx 20rpx 2rpx rgba(96, 87, 182, 0.06),
    0 6rpx 28rpx 4rpx rgba(96, 87, 182, 0.19);
}

.icon-white {
  /* 使用CSS滤镜将图标变成白色 */
  filter: brightness(0) invert(1);
}

.stop-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
}

.record-tip {
  font-size: 28rpx;
  color: #4e5969;
  margin-top: 12rpx;
}

.wave-container {
  display: flex;
  align-items: center;
  margin-top: 40rpx;
  height: 54rpx;
  gap: 10rpx;
}

.wave-item {
  width: 8rpx;
  background-color: #5e85ff;
  border-radius: 100rpx;
  transition: height 0.2s ease;
}

.button-group {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 40rpx 32rpx 0;
  gap: 20rpx;
  border-top: 1px solid #dcdcdc;
}

.btn {
  flex: 1;
  height: 96rpx;
  border-radius: 200rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;

  &-outline {
    border-radius: 100rpx;
    border: 1px solid #dcdcdc;
    background: #fff;
  }

  &-primary {
    background-color: #7d4dff;
    color: #ffffff;
    font-weight: 600;
  }
}
</style>
