<template>
  <view
    v-if="showPopup"
    class="lk-popup"
    :class="[
      popupClass,
      isDesktop ? 'lk-popup--desktop' : '',
      { 'lk-popup--constrained': constrainToContainer },
    ]"
    :style="{ zIndex: zIndex }"
  >
    <!-- 遮罩层 - 根据模式调整位置 -->
    <view
      v-if="overlay"
      class="lk-popup__overlay"
      :class="[
        `lk-popup__overlay--${mode}`,
        { 'lk-popup__overlay--show': showTrans },
        { 'lk-popup__overlay--follow': overlayFollowContent },
      ]"
      :style="overlayStyles"
      @click="onOverlayClick"
    />

    <!-- 弹窗内容 -->
    <view
      class="lk-popup__content"
      :class="[
        `lk-popup__content--${mode}`,
        { 'lk-popup__content--show': showTrans },
        {
          'lk-popup__content--round':
            Number(round) > 0 && ['top', 'bottom', 'center'].includes(mode),
        },
      ]"
      :style="contentStyles"
      @click.stop
    >
      <!-- 关闭按钮 -->
      <view
        v-if="closeable"
        class="lk-popup__close"
        :class="`lk-popup__close--${closeIconPos}`"
        @click="close"
      >
        <text class="lk-popup__close-icon">×</text>
      </view>

      <!-- 主要内容 -->
      <slot />

      <!-- 底部追加内容 -->
      <slot name="bottom" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, defineOptions } from 'vue';

defineOptions({
  name: 'LkPopup',
});

// Props 定义
interface Props {
  show?: boolean;
  overlay?: boolean;
  mode?: 'top' | 'right' | 'bottom' | 'left' | 'center';
  duration?: number | string;
  closeable?: boolean;
  overlayStyle?: Record<string, any> | string;
  overlayOpacity?: number | string;
  closeOnClickOverlay?: boolean;
  zIndex?: number | string;
  safeAreaInsetBottom?: boolean;
  safeAreaInsetTop?: boolean;
  closeIconPos?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  round?: number | string;
  zoom?: boolean;
  bgColor?: string;
  customStyle?: Record<string, any> | string;
  overlayFollowContent?: boolean; // 新增：遮罩层是否跟随内容位置
  constrainToContainer?: boolean; // 新增：是否约束在容器内
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  overlay: true,
  mode: 'bottom',
  duration: 300,
  closeable: false,
  overlayStyle: () => ({}),
  overlayOpacity: 0.5,
  closeOnClickOverlay: true,
  zIndex: 10075,
  safeAreaInsetBottom: true,
  safeAreaInsetTop: false,
  closeIconPos: 'top-right',
  round: 0,
  zoom: true,
  bgColor: '',
  customStyle: () => ({}),
  overlayFollowContent: true, // 默认开启遮罩跟随内容
  constrainToContainer: true, // 默认约束在容器内
});

// Emits 定义
const emit = defineEmits<{
  open: [];
  close: [];
  'update:show': [value: boolean];
}>();

// 支持 v-model:show (暂时保留，可能在未来版本中使用)
// const modelValue = computed({
//   get: () => props.show,
//   set: (value: boolean) => emit('update:show', value)
// });

// 响应式数据
const showPopup = ref(false);
const showTrans = ref(false);
const isDesktop = ref(false);

// 计算属性
const popupClass = computed(() => {
  return `lk-popup--${props.mode}`;
});

const overlayStyles = computed(() => {
  const styles: Record<string, any> = {
    opacity: props.overlayOpacity,
    transition: `opacity ${props.duration}ms ease-in-out`,
  };

  if (typeof props.overlayStyle === 'string') {
    return { ...styles, cssText: props.overlayStyle };
  }

  return { ...styles, ...props.overlayStyle };
});

const contentStyles = computed(() => {
  const styles: Record<string, any> = {
    transition: `transform ${props.duration}ms ease-in-out, opacity ${props.duration}ms ease-in-out`,
  };

  // 设置圆角
  if (Number(props.round) > 0 && ['top', 'bottom', 'center'].includes(props.mode)) {
    if (props.mode === 'top') {
      styles.borderBottomLeftRadius = `${props.round}px`;
      styles.borderBottomRightRadius = `${props.round}px`;
    } else if (props.mode === 'bottom') {
      styles.borderTopLeftRadius = `${props.round}px`;
      styles.borderTopRightRadius = `${props.round}px`;
    } else if (props.mode === 'center') {
      styles.borderRadius = `${props.round}px`;
    }
  }

  // 设置背景色
  if (props.bgColor) {
    styles.backgroundColor = props.bgColor;
  }

  // 安全区域
  if (props.safeAreaInsetBottom && ['bottom', 'center'].includes(props.mode)) {
    styles.paddingBottom = 'env(safe-area-inset-bottom)';
  }

  if (props.safeAreaInsetTop && ['top', 'center'].includes(props.mode)) {
    styles.paddingTop = 'env(safe-area-inset-top)';
  }

  // 自定义样式
  if (typeof props.customStyle === 'string') {
    return { ...styles, cssText: props.customStyle };
  }

  return { ...styles, ...props.customStyle };
});

// 方法
const open = () => {
  showPopup.value = true;
  nextTick(() => {
    showTrans.value = true;
    emit('open');
  });
};

const close = () => {
  showTrans.value = false;
  emit('close');
  emit('update:show', false);

  setTimeout(() => {
    showPopup.value = false;
  }, Number(props.duration));
};

const onOverlayClick = () => {
  if (props.closeOnClickOverlay) {
    close();
  }
};

// 监听 show 变化
watch(
  () => props.show,
  newVal => {
    if (newVal) {
      open();
    } else {
      close();
    }
  },
  { immediate: true }
);

// 检测设备类型
// #ifdef H5
isDesktop.value = window.innerWidth > 768;
// #endif

// 暴露方法给父组件
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.lk-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  // 约束在容器内的样式
  &--constrained {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &__overlay {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    z-index: 1;

    &--show {
      opacity: 1;
    }

    // 在约束模式下使用绝对定位
    .lk-popup--constrained & {
      position: absolute;
    }

    // 默认全屏遮罩
    &--top,
    &--bottom,
    &--left,
    &--right,
    &--center {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    // 遮罩层跟随弹窗内容的位置（当 overlayFollowContent 为 true 时）
    &--follow {
      &.lk-popup__overlay--center {
        // 中心弹窗：保持全屏遮罩
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }
    }
  }

  &__content {
    position: absolute;
    background-color: #fff;
    z-index: 2;

    &--top {
      top: 0;
      left: 0;
      right: 0;
      transform: translateY(-100%);

      &.lk-popup__content--show {
        transform: translateY(0);
      }
    }

    &--bottom {
      bottom: 0;
      left: 0;
      right: 0;
      transform: translateY(100%);

      &.lk-popup__content--show {
        transform: translateY(0);
      }
    }

    &--left {
      top: 0;
      bottom: 0;
      left: 0;
      transform: translateX(-100%);

      &.lk-popup__content--show {
        transform: translateX(0);
      }
    }

    &--right {
      top: 0;
      bottom: 0;
      right: 0;
      transform: translateX(100%);

      &.lk-popup__content--show {
        transform: translateX(0);
      }
    }

    &--center {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.8);
      opacity: 0;
      max-width: 90vw;
      max-height: 90vh;

      &.lk-popup__content--show {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
      }
    }
  }

  &__close {
    position: absolute;
    width: 44rpx;
    height: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    z-index: 3;

    &--top-left {
      top: 20rpx;
      left: 20rpx;
    }

    &--top-right {
      top: 20rpx;
      right: 20rpx;
    }

    &--bottom-left {
      bottom: 20rpx;
      left: 20rpx;
    }

    &--bottom-right {
      bottom: 20rpx;
      right: 20rpx;
    }

    &-icon {
      color: #fff;
      font-size: 32rpx;
      line-height: 1;
    }
  }

  &--desktop {
    .lk-popup__content--center {
      max-width: 80vw;
      max-height: 80vh;
    }
  }
}
</style>
