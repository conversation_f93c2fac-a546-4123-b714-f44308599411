<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import LkSvg from '@/components/svg/index.vue';
import {
  getGradesList,
  getTextbookVersionsList,
  getStudentGradeAndTextbook,
  getSubjects,
} from '@/api/students/resourceCenter';

// Props
interface Props {
  show: boolean;
  currentSubject?: string; // 当前选中的学科ID
  value?: {
    gradeName: string;
    gradeId: number;
    versionName: string;
    versionId: number;
    semesterId: number;
    textbookVersionId: number;
    textbookVolumeId: number;
    textbookVolumeCategory: number;
  };
}

// Emits
interface Emits {
  (e: 'close'): void;
  (e: 'selectSubject', subjectId: string): void; // 学科选择事件
  (
    e: 'confirm',
    obj: {
      gradeName: string;
      versionName: string;
      semesterId: number;
      textbookVersionId: number;
      textbookVolumeId: number;
      gradeId: number;
      textbookVolumeCategory: number;
    }
  ): void; // 年级名称，版本名称，学期id，册数id，版本id，册数id
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const gradeName = ref(props.value?.gradeName || '');
const versionName = ref(props.value?.versionName || '');
const selectedGrade = ref(props.value?.gradeId || 0);
const selectedSemester = ref(props.value?.semesterId || 1); // 默认选中上册
const selectedVersion = ref(props.value?.versionId || 0);
const selectedVolume = ref(props.value?.textbookVolumeId || 0);

// 模拟年级数据
const gradeList = ref();

const semesterList = ref([
  {
    id: 1,
    name: '上册',
  },
  {
    id: 2,
    name: '下册',
  },
]);

const versionList = ref();
const subjectList = ref();
const selectedSubject = ref(props.currentSubject || ''); // 当前选中的学科ID

// 监听外部传入的学科变化
watch(
  () => props.currentSubject,
  async newSubject => {
    if (newSubject) {
      selectedSubject.value = newSubject;
      // 学科变化时重新获取对应的教材版本
      await getTextbookVersions();
    }
  },
  { immediate: true }
);

// 监听弹窗显示状态，每次打开时重新请求版本列表
watch(
  () => props.show,
  async newShow => {
    if (newShow) {
      console.log('弹窗打开，重新请求版本列表');
      await getTextbookVersions();
    }
  }
);

onMounted(async () => {
  await getGradeList();
  await loadStudentDefaults();
  await getSubjectsList();
});
const getSubjectsList = async () => {
  try {
    const res = await getSubjects({ gradeId: selectedGrade.value });
    subjectList.value = res.subjects;
  } catch (error) {
    console.error('获取学科列表失败:', error);
  }
};
// 方法
// 加载学生默认年级和教材版本信息
const loadStudentDefaults = async () => {
  try {
    // 如果props中已经有值，则不需要加载默认值
    if (props.value?.gradeId && props.value?.textbookVersionId) {
      console.log('使用props传入的默认值');
      return;
    }

    const studentInfo = uni.getStorageSync('defultgrade');
    console.log('获取到学生默认信息:', studentInfo);

    if (studentInfo) {
      // 设置默认年级
      selectedGrade.value = studentInfo.gradeId;
      gradeName.value = studentInfo.gradeName;

      // 设置默认教材版本
      selectedVersion.value = studentInfo.textbookVersionId;
      versionName.value = studentInfo.textbookVersionName;

      // 默认选择上册（id: 1）
      selectedSemester.value = 1;

      // 获取对应的教材版本列表
      await getTextbookVersions();

      console.log('学生默认信息已设置:', {
        gradeId: selectedGrade.value,
        gradeName: gradeName.value,
        textbookVersionId: selectedVersion.value,
        textbookVersionName: versionName.value,
        semesterId: selectedSemester.value,
      });
    }
  } catch (error) {
    console.warn('获取学生默认年级和教材版本失败:', error);
    // 如果获取失败，保持原有的默认值
  }
};

// 处理学科选择（这里实际上是学科，不是年级）
const handleSelectSubject = (subject: any) => {
  console.log('选择学科:', subject);
  selectedSubject.value = subject.id.toString();
  // 通知父组件学科选择变化
  emit('selectSubject', subject.id.toString());
  // 根据选中的学科获取对应的教材版本
  getTextbookVersions();
};

const getGradeList = async () => {
  const res = await getGradesList();
  gradeList.value = res.grades;
};
const getTextbookVersions = async () => {
  try {
    // 获取当前年级的所有教材版本
    const res = await getTextbookVersionsList({
      gradeId: selectedGrade.value,
    });
    console.log('获取到的教材版本列表:', res);

    // 如果有选中的学科，筛选出该学科对应的教材版本
    if (selectedSubject.value && selectedSubject.value !== '全部') {
      const subjectId = parseInt(selectedSubject.value);
      versionList.value = res.filter((version: any) => version.subjectId === subjectId);
      console.log(`筛选学科ID ${subjectId} 的教材版本:`, versionList.value);
    } else {
      // 如果没有选中学科或选中"全部"，显示所有版本
      versionList.value = res;
    }
  } catch (error) {
    console.error('获取教材版本失败:', error);
    versionList.value = [];
  }
};

const handleSelectSemester = (semester: number) => {
  selectedSemester.value = semester;
  getTextbookVersions();
};

const handleSelectVersion = (version: any) => {
  selectedVersion.value = version.id;
  versionName.value = version.name;
};

const handleSaveGradeSelection = () => {
  emit('confirm', {
    gradeName: gradeName.value,
    gradeId: selectedGrade.value,
    versionName: versionName.value,
    semesterId: selectedSemester.value,
    textbookVersionId: selectedVersion.value,
    textbookVolumeId: selectedVolume.value,
    textbookVolumeCategory: selectedSemester.value,
  });
  emit('close');
};

const handleCloseGradePopup = () => {
  emit('close');
};

// // 监听弹窗显示状态，重置选择
// watch(() => props.show, (newShow) => {
//   if (newShow) {
//     // 弹窗打开时重置为默认值
//     selectedGrade.value = '七年级';
//     selectedSemester.value = '上册';
//     selectedVersion.value = '人教版';
//   }
// });
</script>

<template>
  <u-popup
    mode="bottom"
    :round="24"
    :safeAreaInsetBottom="true"
    :show="show"
    @close="handleCloseGradePopup"
  >
    <view class="grade-popup">
      <view class="popup-header">
        <view class="popup-title">设置学科及教材</view>
        <view class="popup-close" @click="handleCloseGradePopup">
          <LkSvg width="24px" height="24px" src="/static/icons/close.svg" />
        </view>
      </view>

      <view class="grade-selection-content">
        <!-- 左侧学科列表 -->
        <scroll-view
          class="grade-list-section"
          scroll-y
          :show-scrollbar="false"
          :enhanced="true"
          :bounces="false"
        >
          <view
            v-for="subject in subjectList"
            :key="subject.id"
            class="grade-item"
            :class="{ active: selectedSubject === subject.id.toString() }"
            @click="handleSelectSubject(subject)"
          >
            {{ subject.name }}
          </view>
        </scroll-view>

        <!-- 右侧选择区域 -->
        <view class="selection-section">
          <!-- 上下册选择 -->
          <!-- <view class="semester-selection">
            <view
              v-for="semester in semesterList"
              :key="semester.id"
              class="semester-item"
              :class="{ active: selectedSemester === semester.id }"
              @click="handleSelectSemester(semester.id)"
            >
              {{ semester.name }}
            </view>
          </view> -->

          <!-- 选择教材标题 -->
          <view class="selection-title">选择教材</view>

          <!-- 版本选择列表 -->
          <scroll-view
            class="version-list"
            scroll-y
            :show-scrollbar="false"
            :enhanced="true"
            :bounces="false"
          >
            <view
              v-for="version in versionList"
              :key="version"
              class="version-item"
              :class="{ active: selectedVersion === version.id }"
              @click="handleSelectVersion(version)"
            >
              {{ version.name }}
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="save-button" @click="handleSaveGradeSelection"> 保存 </view>
    </view>
  </u-popup>
</template>

<style lang="scss" scoped>
.grade-popup {
  padding: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  height: 656px;
  overflow: hidden;
  position: relative;
  /* 优化移动端触摸滚动 */
  -webkit-overflow-scrolling: touch;
  touch-action: manipulation;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 32rpx 24rpx 32rpx;
  position: relative;
  background-color: #f7f7fd;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.popup-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
}

.grade-selection-content {
  display: flex;
  height: calc(656px - 120rpx - 120rpx);
  /* 总高度减去头部和底部按钮的高度 */
  /* 确保内容区域可以正常滚动 */
  overflow: hidden;
}

.grade-list-section {
  width: 240rpx;
  background: #f7f7fd;
  height: 100%;
  /* 移除overflow属性，由scroll-view组件处理 */
}

.grade-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;

  &.active {
    color: #7d4dff;
    font-weight: 600;
    position: relative;
  }
}

.selection-section {
  flex: 1;
  padding: 32rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  /* 移除overflow属性，由内部scroll-view处理 */
}

.semester-selection {
  display: flex;
  gap: 8rpx;
  margin-bottom: 32rpx;
  background: #f3f3f3;
  border-radius: 12rpx;
  padding: 4rpx;
}

.semester-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56rpx;
  background: transparent;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #86909c;
  font-weight: 400;
  transition: all 0.2s ease;
  cursor: pointer;

  &.active {
    background: #fff;
    color: #7d4dff;
    font-weight: 500;
  }

  &:hover:not(.active) {
    background: rgba(125, 77, 255, 0.1);
    color: #7d4dff;
  }
}

.selection-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 24rpx;
}

.version-list {
  flex: 1;
  height: 0;
  /* 配合flex: 1 使用，确保高度计算正确 */
  /* scroll-view组件会处理滚动 */
}

.version-item {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding: 0 24rpx;
  background: var(--Gray-Gray1, #f3f3f3);
  border-radius: 8px;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    background: var(--Brand-Brand1, #f3ecff);
    color: #7d4dff;
    font-weight: 600;

    &::after {
      content: '';
      position: absolute;
      right: 24rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 16rpx;
      height: 16rpx;
      background: #7d4dff;
      border-radius: 50%;
    }
  }
}

.save-button {
  position: absolute;
  bottom: 32rpx;
  left: 32rpx;
  right: 32rpx;
  height: 88rpx;
  background: #7d4dff;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}
</style>
