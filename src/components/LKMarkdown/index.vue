<!-- uniapp vue3 markdown解析 -->
<template>
  <view class="ua__markdown">
    <mp-html
      :content="htmlString"
      :selectable="true"
      :tag-style="tagStyle"
      @linktap="handleLinkTap"
    ></mp-html>

    <!-- 思维导图渲染 -->
    <view v-if="mindmapData" class="mindmap-wrapper">
      <MarkmapLocal
        :key="instanceId + '-' + (mindmapData?.id || 0)"
        :data="mindmapData.markdown"
        :height="mindmapData.height"
        :instance-id="instanceId"
      />
    </view>
  </view>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import MarkdownIt from './lib/markdown-it.min.js';
import hljs from './lib/highlight/uni-highlight.min.js';
import './lib/highlight/atom-one-dark.css';
import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue';
import MarkmapLocal from '@/components/LkMarkmap/MarkmapLocal.vue';

// 添加Array.prototype.at()的polyfill，解决Android WebView兼容性问题
if (!Array.prototype.at) {
  Array.prototype.at = function (index) {
    // 将负数索引转换为正数索引
    const len = this.length;
    const relativeIndex = index < 0 ? len + index : index;

    // 检查索引是否在有效范围内
    if (relativeIndex < 0 || relativeIndex >= len) {
      return undefined;
    }

    return this[relativeIndex];
  };
}

// 导入Katex相关依赖
import katex from 'katex';
import 'katex/dist/katex.min.css';
import katexPlugin from '@vscode/markdown-it-katex';

const props = defineProps({
  // 解析内容
  source: String,
  showLine: {
    type: [Boolean, String],
    default: true,
  },
});

// 为每个实例创建唯一ID，用于区分不同实例
const instanceId = Date.now() + '-' + Math.floor(Math.random() * 10000);
// 每个实例都有自己的数据
const copyCodeData = ref([]);
const mindmapData = ref(null);

// 检测思维导图的函数
const detectMindmaps = content => {
  const mindmaps = [];

  // 1. 检测代码块格式的思维导图 (支持 ``` 和 ~~~ 两种格式)
  const codeBlockRegex =
    /(?:```|~~~)(?:mindmap|mermaid-mindmap|markmap)\s*\n([\s\S]*?)\n(?:```|~~~)/g;
  let match;

  while ((match = codeBlockRegex.exec(content)) !== null) {
    const markdown = match[1].trim();
    if (markdown) {
      mindmaps.push({
        markdown,
        height: 400,
        fallbackHtml: generateFallbackHtml(markdown),
        type: 'codeblock',
        id: Date.now() + Math.random(), // 添加唯一ID
      });
    }
  }

  // 2. 检测直接的markdown层级结构（思维导图格式）
  // 匹配标题后面跟着层级结构的内容，更宽松的匹配
  const mindmapStructureRegex =
    /^(#+\s*.*(?:思维导图|脑图|mindmap|导图|大纲|计划|管理|学习|项目|知识|技能|方案|流程|架构|体系).*)\n((?:^#+.*\n(?:^-.*\n)*)*)/gim;

  while ((match = mindmapStructureRegex.exec(content)) !== null) {
    const title = match[1].trim();
    const structure = match[2].trim();

    if (structure && isMindmapStructure(title + '\n' + structure)) {
      const markdown = title + '\n' + structure;
      mindmaps.push({
        markdown,
        height: 400,
        fallbackHtml: generateFallbackHtml(markdown),
        type: 'structure',
        id: Date.now() + Math.random(), // 添加唯一ID
      });
    }
  }

  // 3. 如果没有找到特定关键词的思维导图，检测通用的层级结构
  if (mindmaps.length === 0) {
    const generalStructureRegex = /^(#+\s*.*)\n((?:^#+.*\n(?:^-.*\n)*)+)/gim;
    while ((match = generalStructureRegex.exec(content)) !== null) {
      const title = match[1].trim();
      const structure = match[2].trim();

      if (structure && isMindmapStructure(title + '\n' + structure)) {
        const markdown = title + '\n' + structure;
        mindmaps.push({
          markdown,
          height: 400,
          fallbackHtml: generateFallbackHtml(markdown),
          type: 'general',
          id: Date.now() + Math.random(), // 添加唯一ID
        });
        break; // 只取第一个匹配的通用结构
      }
    }
  }

  return mindmaps;
};

// 判断是否为思维导图结构
const isMindmapStructure = content => {
  const lines = content.split('\n').filter(line => line.trim());

  // 检查是否有标题层级结构
  let hasTitle = false;
  let hasSubtitles = false;
  let hasListItems = false;

  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed.match(/^#+\s/)) {
      if (trimmed.match(/^#\s/)) {
        hasTitle = true;
      } else {
        hasSubtitles = true;
      }
    } else if (trimmed.match(/^-\s/)) {
      hasListItems = true;
    }
  }

  // 降低要求：有标题和子标题即可，或者有标题和列表项
  return hasTitle && (hasSubtitles || hasListItems) && lines.length >= 3;
};

// 生成小程序端的降级HTML
const generateFallbackHtml = markdown => {
  const lines = markdown.split('\n');
  let html = '<div class="mindmap-fallback-content">';

  lines.forEach(line => {
    const trimmed = line.trim();
    if (trimmed) {
      const level = (line.match(/^#+/) || [''])[0].length;
      const text = trimmed.replace(/^#+\s*/, '');
      const indent = Math.max(0, (level - 1) * 20);
      html += `<div style="margin-left: ${indent}px; margin: 5px 0; padding: 5px; border-left: 2px solid #007aff;">
        <span style="font-weight: ${level <= 2 ? 'bold' : 'normal'}; font-size: ${Math.max(14, 18 - level)}px;">${text}</span>
      </div>`;
    }
  });

  html += '</div>';
  return html;
};

// 创建markdown-it实例并配置Katex支持
const createMarkdownInstance = () => {
  const md = MarkdownIt({
    html: true,
    highlight: function (str, lang) {
      let preCode = '';
      try {
        preCode = hljs.highlightAuto(str).value;
      } catch (err) {
        preCode = md.utils.escapeHtml(str);
      }
      const lines = preCode.split(/\n/).slice(0, -1);
      // 添加自定义行号
      let html = lines
        .map((item, index) => {
          if (item == '') {
            return '';
          }
          return (
            '<li><span class="line-num" data-line="' + (index + 1) + '"></span>' + item + '</li>'
          );
        })
        .join('');
      if (props.showLine) {
        html = '<ol style="padding: 0px 30px;">' + html + '</ol>';
      } else {
        html = '<ol style="padding: 0px 7px;list-style:none;">' + html + '</ol>';
      }
      copyCodeData.value.push(str);
      let htmlCode = `<div class="markdown-wrap">`;
      // #ifndef MP-WEIXIN
      htmlCode += `<div style="color: #aaa;text-align: right;font-size: 12px;padding:8px;">`;
      htmlCode += `${lang}<a class="copy-btn" href="copy:${copyCodeData.value.length - 1}" style="margin-left: 8px;cursor:pointer;color:#007aff;">复制代码</a>`;
      htmlCode += `</div>`;
      // #endif
      htmlCode += `<pre class="hljs" style="padding:10px 8px 0;margin-bottom:5px;overflow: auto;display: block;border-radius: 5px;"><code>${html}</code></pre>`;
      htmlCode += '</div>';
      return htmlCode;
    },
  });

  // 添加Katex支持
  try {
    md.use(katexPlugin, {
      throwOnError: false,
      errorColor: '#cc0000',
      strict: false,
      trust: false,
      output: 'html',
      fleqn: false,
      leqno: false,
      macros: {
        // 数集符号
        '\\RR': '\\mathbb{R}',
        '\\NN': '\\mathbb{N}',
        '\\ZZ': '\\mathbb{Z}',
        '\\QQ': '\\mathbb{Q}',
        '\\CC': '\\mathbb{C}',
        '\\PP': '\\mathbb{P}',
        // 常用符号
        '\\eps': '\\varepsilon',
        '\\phi': '\\varphi',
        '\\implies': '\\Rightarrow',
        '\\iff': '\\Leftrightarrow',
        // 三角函数
        '\\tg': '\\tan',
        '\\ctg': '\\cot',
        '\\arctg': '\\arctan',
        '\\arcctg': '\\arccot',
        // 中文数学符号
        '\\lt': '<',
        '\\gt': '>',
        '\\le': '\\leq',
        '\\ge': '\\geq',
      },
    });
  } catch (err) {
    console.warn('Katex not available:', err);
  }

  return md;
};

const markdown = createMarkdownInstance();

const htmlString = computed(() => {
  let value = props.source;
  if (!value) {
    mindmapData.value = null;
    return '';
  }

  // 调试：检查是否包含数学公式
  const hasInlineMath = /\$[^$]+\$/.test(value);
  const hasDisplayMath = /\$\$[\s\S]+?\$\$/.test(value);
  const hasLatexBrackets = /\\[\[\(][\s\S]*?\\[\]\)]/.test(value);

  // 检测并提取思维导图
  const detectedMindmaps = detectMindmaps(value);

  mindmapData.value = detectedMindmaps.length > 0 ? detectedMindmaps[0] : null;

  // 移除思维导图内容，避免重复渲染
  // 1. 移除代码块格式的思维导图 (支持 ``` 和 ~~~ 两种格式)
  value = value.replace(
    /(?:```|~~~)(?:mindmap|mermaid-mindmap|markmap)\s*\n([\s\S]*?)\n(?:```|~~~)/g,
    ''
  );

  // 2. 移除直接的markdown层级结构思维导图
  value = value.replace(
    /^(#+\s*.*(?:思维导图|脑图|mindmap|导图|大纲).*)\n((?:^#+.*\n(?:^-.*\n)*)*)/gim,
    ''
  );

  // 解析<br />到\n
  value = value.replace(/<br>|<br\/>|<br \/>/g, '\n');
  value = value.replace(/&nbsp;/g, ' ');

  // 预处理LaTeX公式，确保正确识别
  // 处理行内公式：将 \(...\) 转换为 $...$
  value = value.replace(/\\\\?\(/g, '$').replace(/\\\\?\)/g, '$');

  // 处理块级公式：将 \[...\] 转换为 $$...$$
  value = value.replace(/\\\\?\[/g, '$$').replace(/\\\\?\]/g, '$$');

  // 确保公式前后有适当的空格
  value = value.replace(/([^$])\$([^$\s])/g, '$1 $$$2');
  value = value.replace(/([^$\s])\$([^$])/g, '$1$$ $2');

  let result = '';
  if (value.split('```').length % 2) {
    let mdtext = value;
    if (mdtext[mdtext.length - 1] != '\n') {
      mdtext += '\n';
    }
    result = markdown.render(mdtext);
  } else {
    result = markdown.render(value);
  }
  // 解决表格边框型失效问题
  result = result.replace(/<table/g, `<table class="table"`);
  result = result.replace(/<tr/g, `<tr class="tr"`);
  result = result.replace(/<th>/g, `<th class="th">`);
  result = result.replace(/<td/g, `<td class="td"`);
  result = result.replace(/<hr>|<hr\/>|<hr \/>/g, `<hr class="hr">`);

  // 处理图片样式，确保在安卓端也能正常工作
  result = result.replace(/<img([^>]*?)>/g, (match, attrs) => {
    // 定义图片的基础样式
    const imgStyles = ['width: 100%', 'height: auto', 'display: block', 'border-radius: 8px'];

    // 检查是否已经有 style 属性
    if (attrs.includes('style=')) {
      // 如果已有 style，在现有 style 中添加样式
      return match.replace(/style="([^"]*?)"/g, (styleMatch, styleContent) => {
        let newStyles = styleContent;

        // 添加缺失的样式
        imgStyles.forEach(style => {
          const property = style.split(':')[0].trim();
          if (!newStyles.includes(property)) {
            newStyles += newStyles.endsWith(';') ? ` ${style};` : `; ${style};`;
          }
        });

        return `style="${newStyles}"`;
      });
    } else {
      // 如果没有 style 属性，直接添加
      return `<img${attrs} style="${imgStyles.join('; ')};">`;
    }
  });

  // 处理Katex数学公式在不支持的环境中的降级显示
  // 检查是否在小程序环境或其他不完全支持Katex的环境
  // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || MP-KUAISHOU
  // 将Katex公式转换为普通文本显示
  result = result.replace(
    /<span class="katex-display"[^>]*>([\s\S]*?)<\/span>/g,
    (match, content) => {
      // 尝试多种方式提取LaTeX源码
      let latex = '';

      // 方法1: 从data-latex属性提取
      const latexMatch = content.match(/data-latex="([^"]*?)"/);
      if (latexMatch) {
        latex = latexMatch[1];
      } else {
        // 方法2: 从annotation标签提取
        const annotationMatch = content.match(
          /<annotation[^>]*encoding="application\/x-tex"[^>]*>([\s\S]*?)<\/annotation>/
        );
        if (annotationMatch) {
          latex = annotationMatch[1].trim();
        } else {
          // 方法3: 尝试从原始内容中提取
          const textContent = content.replace(/<[^>]*>/g, '').trim();
          latex = textContent || '数学公式';
        }
      }

      return `<div class="math-formula-fallback display-math">$$${latex}$$</div>`;
    }
  );

  result = result.replace(/<span class="katex"[^>]*>([\s\S]*?)<\/span>/g, (match, content) => {
    // 尝试多种方式提取LaTeX源码
    let latex = '';

    // 方法1: 从data-latex属性提取
    const latexMatch = content.match(/data-latex="([^"]*?)"/);
    if (latexMatch) {
      latex = latexMatch[1];
    } else {
      // 方法2: 从annotation标签提取
      const annotationMatch = content.match(
        /<annotation[^>]*encoding="application\/x-tex"[^>]*>([\s\S]*?)<\/annotation>/
      );
      if (annotationMatch) {
        latex = annotationMatch[1].trim();
      } else {
        // 方法3: 尝试从原始内容中提取
        const textContent = content.replace(/<[^>]*>/g, '').trim();
        latex = textContent || '数学公式';
      }
    }

    return `<span class="math-formula-fallback inline-math">$${latex}$</span>`;
  });
  // #endif

  return result;
});

const tagStyle = {
  //...其他标签样式
  table: 'width:100%;border-collapse:collapse;border:1px solid #ebeef5;',
  th: 'border:1px solid #ebeef5;padding:8px;background-color:#f5f7fa;',
  td: 'border:1px solid #ebeef5;padding:8px;',
  hr: 'border: 0;border-top: 1px solid #e5e5e5;margin:20px 0;',
  p: 'margin-bottom:10px;',
  h1: 'font-size:36px;margin:20px 0 10px;',
  h2: 'font-size:30px;margin:20px 0 10px;',
  h3: 'font-size:24px;margin:20px 0 10px;',
  h4: 'font-size:18px;margin:10px 0;',
  h5: 'font-size:14px;margin:10px 0;',
  h6: 'font-size:12px;margin:10px 0;',
};

const handleLinkTap = attrs => {
  const { href } = attrs;
  if (href && href.startsWith('copy:')) {
    const index = parseInt(href.replace('copy:', ''));
    const code = copyCodeData.value[index];
    uni.setClipboardData({
      data: code,
      showToast: false,
      success() {
        uni.showToast({
          title: '复制成功',
          icon: 'none',
        });
      },
    });
  }
};
</script>

<style lang="scss" scoped>
.ua__markdown {
  font-size: 32rpx;
  line-height: 1.5;
  word-break: break-all;
}

/* 思维导图容器样式 */
.mindmap-wrapper {
  margin: 20rpx 0;
}

/* 思维导图占位符样式 */
:deep(.mindmap-placeholder) {
  text-align: center;
  padding: 20rpx;
  color: #666;
  font-size: 28rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

/* 小程序端降级样式 */
.mindmap-fallback {
  margin: 20rpx 0;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4px solid #007aff;
}

.mindmap-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.mindmap-title::before {
  content: '🧠';
  margin-right: 10rpx;
}

.mindmap-tip {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: #fff3cd;
  border-radius: 8rpx;
  border: 1px solid #ffeaa7;
}

.mindmap-fallback-content {
  background: white;
  border-radius: 8rpx;
  padding: 20rpx;
}

/* Katex数学公式样式 */
:deep(.katex) {
  font-size: 1.1em;
  line-height: 1.2;
}

:deep(.katex-display) {
  margin: 1em 0;
  text-align: center;
  overflow-x: auto;
  overflow-y: hidden;
}

:deep(.katex-display > .katex) {
  display: inline-block;
  white-space: nowrap;
}

/* 行内公式样式 */
:deep(.katex-inline) {
  display: inline;
  white-space: nowrap;
}

/* 错误处理样式 */
:deep(.katex-error) {
  color: #cc0000;
  background-color: #ffeeee;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
}

/* 响应式处理 */
@media (max-width: 768px) {
  :deep(.katex-display) {
    font-size: 0.9em;
  }

  :deep(.katex) {
    font-size: 1em;
  }
}

/* 小程序端数学公式降级样式 */
/* #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || MP-KUAISHOU */
:deep(.math-formula-fallback) {
  font-family: 'Times New Roman', serif;
  color: #333;
  background-color: #f8f9fa;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  border: 1px solid #e9ecef;
}

:deep(.display-math) {
  display: block;
  text-align: center;
  margin: 20rpx 0;
  padding: 20rpx;
  font-size: 32rpx;
  line-height: 1.5;
}

:deep(.inline-math) {
  display: inline;
  font-size: 28rpx;
  margin: 0 4rpx;
}

/* 数学公式提示 */
:deep(.math-formula-fallback::before) {
  content: '📐 ';
  opacity: 0.6;
}
/* #endif */
</style>
