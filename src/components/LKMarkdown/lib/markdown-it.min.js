function e(e) {
  if (e.__esModule) return e;
  var r = Object.defineProperty({}, '__esModule', { value: !0 });
  return (
    Object.keys(e).forEach(function (t) {
      var n = Object.getOwnPropertyDescriptor(e, t);
      Object.defineProperty(
        r,
        t,
        n.get
          ? n
          : {
              enumerable: !0,
              get: function () {
                return e[t];
              },
            }
      );
    }),
    r
  );
}
var r = {},
  t = {
    Aacute: 'Á',
    aacute: 'á',
    Abreve: 'Ă',
    abreve: 'ă',
    ac: '∾',
    acd: '∿',
    acE: '∾̳',
    Acirc: 'Â',
    acirc: 'â',
    acute: '´',
    Acy: 'А',
    acy: 'а',
    AElig: 'Æ',
    aelig: 'æ',
    af: '⁡',
    Afr: '𝔄',
    afr: '𝔞',
    Agrave: 'À',
    agrave: 'à',
    alefsym: 'ℵ',
    aleph: 'ℵ',
    Alpha: 'Α',
    alpha: 'α',
    Amacr: 'Ā',
    amacr: 'ā',
    amalg: '⨿',
    amp: '&',
    AMP: '&',
    andand: '⩕',
    And: '⩓',
    and: '∧',
    andd: '⩜',
    andslope: '⩘',
    andv: '⩚',
    ang: '∠',
    ange: '⦤',
    angle: '∠',
    angmsdaa: '⦨',
    angmsdab: '⦩',
    angmsdac: '⦪',
    angmsdad: '⦫',
    angmsdae: '⦬',
    angmsdaf: '⦭',
    angmsdag: '⦮',
    angmsdah: '⦯',
    angmsd: '∡',
    angrt: '∟',
    angrtvb: '⊾',
    angrtvbd: '⦝',
    angsph: '∢',
    angst: 'Å',
    angzarr: '⍼',
    Aogon: 'Ą',
    aogon: 'ą',
    Aopf: '𝔸',
    aopf: '𝕒',
    apacir: '⩯',
    ap: '≈',
    apE: '⩰',
    ape: '≊',
    apid: '≋',
    apos: "'",
    ApplyFunction: '⁡',
    approx: '≈',
    approxeq: '≊',
    Aring: 'Å',
    aring: 'å',
    Ascr: '𝒜',
    ascr: '𝒶',
    Assign: '≔',
    ast: '*',
    asymp: '≈',
    asympeq: '≍',
    Atilde: 'Ã',
    atilde: 'ã',
    Auml: 'Ä',
    auml: 'ä',
    awconint: '∳',
    awint: '⨑',
    backcong: '≌',
    backepsilon: '϶',
    backprime: '‵',
    backsim: '∽',
    backsimeq: '⋍',
    Backslash: '∖',
    Barv: '⫧',
    barvee: '⊽',
    barwed: '⌅',
    Barwed: '⌆',
    barwedge: '⌅',
    bbrk: '⎵',
    bbrktbrk: '⎶',
    bcong: '≌',
    Bcy: 'Б',
    bcy: 'б',
    bdquo: '„',
    becaus: '∵',
    because: '∵',
    Because: '∵',
    bemptyv: '⦰',
    bepsi: '϶',
    bernou: 'ℬ',
    Bernoullis: 'ℬ',
    Beta: 'Β',
    beta: 'β',
    beth: 'ℶ',
    between: '≬',
    Bfr: '𝔅',
    bfr: '𝔟',
    bigcap: '⋂',
    bigcirc: '◯',
    bigcup: '⋃',
    bigodot: '⨀',
    bigoplus: '⨁',
    bigotimes: '⨂',
    bigsqcup: '⨆',
    bigstar: '★',
    bigtriangledown: '▽',
    bigtriangleup: '△',
    biguplus: '⨄',
    bigvee: '⋁',
    bigwedge: '⋀',
    bkarow: '⤍',
    blacklozenge: '⧫',
    blacksquare: '▪',
    blacktriangle: '▴',
    blacktriangledown: '▾',
    blacktriangleleft: '◂',
    blacktriangleright: '▸',
    blank: '␣',
    blk12: '▒',
    blk14: '░',
    blk34: '▓',
    block: '█',
    bne: '=⃥',
    bnequiv: '≡⃥',
    bNot: '⫭',
    bnot: '⌐',
    Bopf: '𝔹',
    bopf: '𝕓',
    bot: '⊥',
    bottom: '⊥',
    bowtie: '⋈',
    boxbox: '⧉',
    boxdl: '┐',
    boxdL: '╕',
    boxDl: '╖',
    boxDL: '╗',
    boxdr: '┌',
    boxdR: '╒',
    boxDr: '╓',
    boxDR: '╔',
    boxh: '─',
    boxH: '═',
    boxhd: '┬',
    boxHd: '╤',
    boxhD: '╥',
    boxHD: '╦',
    boxhu: '┴',
    boxHu: '╧',
    boxhU: '╨',
    boxHU: '╩',
    boxminus: '⊟',
    boxplus: '⊞',
    boxtimes: '⊠',
    boxul: '┘',
    boxuL: '╛',
    boxUl: '╜',
    boxUL: '╝',
    boxur: '└',
    boxuR: '╘',
    boxUr: '╙',
    boxUR: '╚',
    boxv: '│',
    boxV: '║',
    boxvh: '┼',
    boxvH: '╪',
    boxVh: '╫',
    boxVH: '╬',
    boxvl: '┤',
    boxvL: '╡',
    boxVl: '╢',
    boxVL: '╣',
    boxvr: '├',
    boxvR: '╞',
    boxVr: '╟',
    boxVR: '╠',
    bprime: '‵',
    breve: '˘',
    Breve: '˘',
    brvbar: '¦',
    bscr: '𝒷',
    Bscr: 'ℬ',
    bsemi: '⁏',
    bsim: '∽',
    bsime: '⋍',
    bsolb: '⧅',
    bsol: '\\',
    bsolhsub: '⟈',
    bull: '•',
    bullet: '•',
    bump: '≎',
    bumpE: '⪮',
    bumpe: '≏',
    Bumpeq: '≎',
    bumpeq: '≏',
    Cacute: 'Ć',
    cacute: 'ć',
    capand: '⩄',
    capbrcup: '⩉',
    capcap: '⩋',
    cap: '∩',
    Cap: '⋒',
    capcup: '⩇',
    capdot: '⩀',
    CapitalDifferentialD: 'ⅅ',
    caps: '∩︀',
    caret: '⁁',
    caron: 'ˇ',
    Cayleys: 'ℭ',
    ccaps: '⩍',
    Ccaron: 'Č',
    ccaron: 'č',
    Ccedil: 'Ç',
    ccedil: 'ç',
    Ccirc: 'Ĉ',
    ccirc: 'ĉ',
    Cconint: '∰',
    ccups: '⩌',
    ccupssm: '⩐',
    Cdot: 'Ċ',
    cdot: 'ċ',
    cedil: '¸',
    Cedilla: '¸',
    cemptyv: '⦲',
    cent: '¢',
    centerdot: '·',
    CenterDot: '·',
    cfr: '𝔠',
    Cfr: 'ℭ',
    CHcy: 'Ч',
    chcy: 'ч',
    check: '✓',
    checkmark: '✓',
    Chi: 'Χ',
    chi: 'χ',
    circ: 'ˆ',
    circeq: '≗',
    circlearrowleft: '↺',
    circlearrowright: '↻',
    circledast: '⊛',
    circledcirc: '⊚',
    circleddash: '⊝',
    CircleDot: '⊙',
    circledR: '®',
    circledS: 'Ⓢ',
    CircleMinus: '⊖',
    CirclePlus: '⊕',
    CircleTimes: '⊗',
    cir: '○',
    cirE: '⧃',
    cire: '≗',
    cirfnint: '⨐',
    cirmid: '⫯',
    cirscir: '⧂',
    ClockwiseContourIntegral: '∲',
    CloseCurlyDoubleQuote: '”',
    CloseCurlyQuote: '’',
    clubs: '♣',
    clubsuit: '♣',
    colon: ':',
    Colon: '∷',
    Colone: '⩴',
    colone: '≔',
    coloneq: '≔',
    comma: ',',
    commat: '@',
    comp: '∁',
    compfn: '∘',
    complement: '∁',
    complexes: 'ℂ',
    cong: '≅',
    congdot: '⩭',
    Congruent: '≡',
    conint: '∮',
    Conint: '∯',
    ContourIntegral: '∮',
    copf: '𝕔',
    Copf: 'ℂ',
    coprod: '∐',
    Coproduct: '∐',
    copy: '©',
    COPY: '©',
    copysr: '℗',
    CounterClockwiseContourIntegral: '∳',
    crarr: '↵',
    cross: '✗',
    Cross: '⨯',
    Cscr: '𝒞',
    cscr: '𝒸',
    csub: '⫏',
    csube: '⫑',
    csup: '⫐',
    csupe: '⫒',
    ctdot: '⋯',
    cudarrl: '⤸',
    cudarrr: '⤵',
    cuepr: '⋞',
    cuesc: '⋟',
    cularr: '↶',
    cularrp: '⤽',
    cupbrcap: '⩈',
    cupcap: '⩆',
    CupCap: '≍',
    cup: '∪',
    Cup: '⋓',
    cupcup: '⩊',
    cupdot: '⊍',
    cupor: '⩅',
    cups: '∪︀',
    curarr: '↷',
    curarrm: '⤼',
    curlyeqprec: '⋞',
    curlyeqsucc: '⋟',
    curlyvee: '⋎',
    curlywedge: '⋏',
    curren: '¤',
    curvearrowleft: '↶',
    curvearrowright: '↷',
    cuvee: '⋎',
    cuwed: '⋏',
    cwconint: '∲',
    cwint: '∱',
    cylcty: '⌭',
    dagger: '†',
    Dagger: '‡',
    daleth: 'ℸ',
    darr: '↓',
    Darr: '↡',
    dArr: '⇓',
    dash: '‐',
    Dashv: '⫤',
    dashv: '⊣',
    dbkarow: '⤏',
    dblac: '˝',
    Dcaron: 'Ď',
    dcaron: 'ď',
    Dcy: 'Д',
    dcy: 'д',
    ddagger: '‡',
    ddarr: '⇊',
    DD: 'ⅅ',
    dd: 'ⅆ',
    DDotrahd: '⤑',
    ddotseq: '⩷',
    deg: '°',
    Del: '∇',
    Delta: 'Δ',
    delta: 'δ',
    demptyv: '⦱',
    dfisht: '⥿',
    Dfr: '𝔇',
    dfr: '𝔡',
    dHar: '⥥',
    dharl: '⇃',
    dharr: '⇂',
    DiacriticalAcute: '´',
    DiacriticalDot: '˙',
    DiacriticalDoubleAcute: '˝',
    DiacriticalGrave: '`',
    DiacriticalTilde: '˜',
    diam: '⋄',
    diamond: '⋄',
    Diamond: '⋄',
    diamondsuit: '♦',
    diams: '♦',
    die: '¨',
    DifferentialD: 'ⅆ',
    digamma: 'ϝ',
    disin: '⋲',
    div: '÷',
    divide: '÷',
    divideontimes: '⋇',
    divonx: '⋇',
    DJcy: 'Ђ',
    djcy: 'ђ',
    dlcorn: '⌞',
    dlcrop: '⌍',
    dollar: '$',
    Dopf: '𝔻',
    dopf: '𝕕',
    Dot: '¨',
    dot: '˙',
    DotDot: '⃜',
    doteq: '≐',
    doteqdot: '≑',
    DotEqual: '≐',
    dotminus: '∸',
    dotplus: '∔',
    dotsquare: '⊡',
    doublebarwedge: '⌆',
    DoubleContourIntegral: '∯',
    DoubleDot: '¨',
    DoubleDownArrow: '⇓',
    DoubleLeftArrow: '⇐',
    DoubleLeftRightArrow: '⇔',
    DoubleLeftTee: '⫤',
    DoubleLongLeftArrow: '⟸',
    DoubleLongLeftRightArrow: '⟺',
    DoubleLongRightArrow: '⟹',
    DoubleRightArrow: '⇒',
    DoubleRightTee: '⊨',
    DoubleUpArrow: '⇑',
    DoubleUpDownArrow: '⇕',
    DoubleVerticalBar: '∥',
    DownArrowBar: '⤓',
    downarrow: '↓',
    DownArrow: '↓',
    Downarrow: '⇓',
    DownArrowUpArrow: '⇵',
    DownBreve: '̑',
    downdownarrows: '⇊',
    downharpoonleft: '⇃',
    downharpoonright: '⇂',
    DownLeftRightVector: '⥐',
    DownLeftTeeVector: '⥞',
    DownLeftVectorBar: '⥖',
    DownLeftVector: '↽',
    DownRightTeeVector: '⥟',
    DownRightVectorBar: '⥗',
    DownRightVector: '⇁',
    DownTeeArrow: '↧',
    DownTee: '⊤',
    drbkarow: '⤐',
    drcorn: '⌟',
    drcrop: '⌌',
    Dscr: '𝒟',
    dscr: '𝒹',
    DScy: 'Ѕ',
    dscy: 'ѕ',
    dsol: '⧶',
    Dstrok: 'Đ',
    dstrok: 'đ',
    dtdot: '⋱',
    dtri: '▿',
    dtrif: '▾',
    duarr: '⇵',
    duhar: '⥯',
    dwangle: '⦦',
    DZcy: 'Џ',
    dzcy: 'џ',
    dzigrarr: '⟿',
    Eacute: 'É',
    eacute: 'é',
    easter: '⩮',
    Ecaron: 'Ě',
    ecaron: 'ě',
    Ecirc: 'Ê',
    ecirc: 'ê',
    ecir: '≖',
    ecolon: '≕',
    Ecy: 'Э',
    ecy: 'э',
    eDDot: '⩷',
    Edot: 'Ė',
    edot: 'ė',
    eDot: '≑',
    ee: 'ⅇ',
    efDot: '≒',
    Efr: '𝔈',
    efr: '𝔢',
    eg: '⪚',
    Egrave: 'È',
    egrave: 'è',
    egs: '⪖',
    egsdot: '⪘',
    el: '⪙',
    Element: '∈',
    elinters: '⏧',
    ell: 'ℓ',
    els: '⪕',
    elsdot: '⪗',
    Emacr: 'Ē',
    emacr: 'ē',
    empty: '∅',
    emptyset: '∅',
    EmptySmallSquare: '◻',
    emptyv: '∅',
    EmptyVerySmallSquare: '▫',
    emsp13: ' ',
    emsp14: ' ',
    emsp: ' ',
    ENG: 'Ŋ',
    eng: 'ŋ',
    ensp: ' ',
    Eogon: 'Ę',
    eogon: 'ę',
    Eopf: '𝔼',
    eopf: '𝕖',
    epar: '⋕',
    eparsl: '⧣',
    eplus: '⩱',
    epsi: 'ε',
    Epsilon: 'Ε',
    epsilon: 'ε',
    epsiv: 'ϵ',
    eqcirc: '≖',
    eqcolon: '≕',
    eqsim: '≂',
    eqslantgtr: '⪖',
    eqslantless: '⪕',
    Equal: '⩵',
    equals: '=',
    EqualTilde: '≂',
    equest: '≟',
    Equilibrium: '⇌',
    equiv: '≡',
    equivDD: '⩸',
    eqvparsl: '⧥',
    erarr: '⥱',
    erDot: '≓',
    escr: 'ℯ',
    Escr: 'ℰ',
    esdot: '≐',
    Esim: '⩳',
    esim: '≂',
    Eta: 'Η',
    eta: 'η',
    ETH: 'Ð',
    eth: 'ð',
    Euml: 'Ë',
    euml: 'ë',
    euro: '€',
    excl: '!',
    exist: '∃',
    Exists: '∃',
    expectation: 'ℰ',
    exponentiale: 'ⅇ',
    ExponentialE: 'ⅇ',
    fallingdotseq: '≒',
    Fcy: 'Ф',
    fcy: 'ф',
    female: '♀',
    ffilig: 'ﬃ',
    fflig: 'ﬀ',
    ffllig: 'ﬄ',
    Ffr: '𝔉',
    ffr: '𝔣',
    filig: 'ﬁ',
    FilledSmallSquare: '◼',
    FilledVerySmallSquare: '▪',
    fjlig: 'fj',
    flat: '♭',
    fllig: 'ﬂ',
    fltns: '▱',
    fnof: 'ƒ',
    Fopf: '𝔽',
    fopf: '𝕗',
    forall: '∀',
    ForAll: '∀',
    fork: '⋔',
    forkv: '⫙',
    Fouriertrf: 'ℱ',
    fpartint: '⨍',
    frac12: '½',
    frac13: '⅓',
    frac14: '¼',
    frac15: '⅕',
    frac16: '⅙',
    frac18: '⅛',
    frac23: '⅔',
    frac25: '⅖',
    frac34: '¾',
    frac35: '⅗',
    frac38: '⅜',
    frac45: '⅘',
    frac56: '⅚',
    frac58: '⅝',
    frac78: '⅞',
    frasl: '⁄',
    frown: '⌢',
    fscr: '𝒻',
    Fscr: 'ℱ',
    gacute: 'ǵ',
    Gamma: 'Γ',
    gamma: 'γ',
    Gammad: 'Ϝ',
    gammad: 'ϝ',
    gap: '⪆',
    Gbreve: 'Ğ',
    gbreve: 'ğ',
    Gcedil: 'Ģ',
    Gcirc: 'Ĝ',
    gcirc: 'ĝ',
    Gcy: 'Г',
    gcy: 'г',
    Gdot: 'Ġ',
    gdot: 'ġ',
    ge: '≥',
    gE: '≧',
    gEl: '⪌',
    gel: '⋛',
    geq: '≥',
    geqq: '≧',
    geqslant: '⩾',
    gescc: '⪩',
    ges: '⩾',
    gesdot: '⪀',
    gesdoto: '⪂',
    gesdotol: '⪄',
    gesl: '⋛︀',
    gesles: '⪔',
    Gfr: '𝔊',
    gfr: '𝔤',
    gg: '≫',
    Gg: '⋙',
    ggg: '⋙',
    gimel: 'ℷ',
    GJcy: 'Ѓ',
    gjcy: 'ѓ',
    gla: '⪥',
    gl: '≷',
    glE: '⪒',
    glj: '⪤',
    gnap: '⪊',
    gnapprox: '⪊',
    gne: '⪈',
    gnE: '≩',
    gneq: '⪈',
    gneqq: '≩',
    gnsim: '⋧',
    Gopf: '𝔾',
    gopf: '𝕘',
    grave: '`',
    GreaterEqual: '≥',
    GreaterEqualLess: '⋛',
    GreaterFullEqual: '≧',
    GreaterGreater: '⪢',
    GreaterLess: '≷',
    GreaterSlantEqual: '⩾',
    GreaterTilde: '≳',
    Gscr: '𝒢',
    gscr: 'ℊ',
    gsim: '≳',
    gsime: '⪎',
    gsiml: '⪐',
    gtcc: '⪧',
    gtcir: '⩺',
    gt: '>',
    GT: '>',
    Gt: '≫',
    gtdot: '⋗',
    gtlPar: '⦕',
    gtquest: '⩼',
    gtrapprox: '⪆',
    gtrarr: '⥸',
    gtrdot: '⋗',
    gtreqless: '⋛',
    gtreqqless: '⪌',
    gtrless: '≷',
    gtrsim: '≳',
    gvertneqq: '≩︀',
    gvnE: '≩︀',
    Hacek: 'ˇ',
    hairsp: ' ',
    half: '½',
    hamilt: 'ℋ',
    HARDcy: 'Ъ',
    hardcy: 'ъ',
    harrcir: '⥈',
    harr: '↔',
    hArr: '⇔',
    harrw: '↭',
    Hat: '^',
    hbar: 'ℏ',
    Hcirc: 'Ĥ',
    hcirc: 'ĥ',
    hearts: '♥',
    heartsuit: '♥',
    hellip: '…',
    hercon: '⊹',
    hfr: '𝔥',
    Hfr: 'ℌ',
    HilbertSpace: 'ℋ',
    hksearow: '⤥',
    hkswarow: '⤦',
    hoarr: '⇿',
    homtht: '∻',
    hookleftarrow: '↩',
    hookrightarrow: '↪',
    hopf: '𝕙',
    Hopf: 'ℍ',
    horbar: '―',
    HorizontalLine: '─',
    hscr: '𝒽',
    Hscr: 'ℋ',
    hslash: 'ℏ',
    Hstrok: 'Ħ',
    hstrok: 'ħ',
    HumpDownHump: '≎',
    HumpEqual: '≏',
    hybull: '⁃',
    hyphen: '‐',
    Iacute: 'Í',
    iacute: 'í',
    ic: '⁣',
    Icirc: 'Î',
    icirc: 'î',
    Icy: 'И',
    icy: 'и',
    Idot: 'İ',
    IEcy: 'Е',
    iecy: 'е',
    iexcl: '¡',
    iff: '⇔',
    ifr: '𝔦',
    Ifr: 'ℑ',
    Igrave: 'Ì',
    igrave: 'ì',
    ii: 'ⅈ',
    iiiint: '⨌',
    iiint: '∭',
    iinfin: '⧜',
    iiota: '℩',
    IJlig: 'Ĳ',
    ijlig: 'ĳ',
    Imacr: 'Ī',
    imacr: 'ī',
    image: 'ℑ',
    ImaginaryI: 'ⅈ',
    imagline: 'ℐ',
    imagpart: 'ℑ',
    imath: 'ı',
    Im: 'ℑ',
    imof: '⊷',
    imped: 'Ƶ',
    Implies: '⇒',
    incare: '℅',
    in: '∈',
    infin: '∞',
    infintie: '⧝',
    inodot: 'ı',
    intcal: '⊺',
    int: '∫',
    Int: '∬',
    integers: 'ℤ',
    Integral: '∫',
    intercal: '⊺',
    Intersection: '⋂',
    intlarhk: '⨗',
    intprod: '⨼',
    InvisibleComma: '⁣',
    InvisibleTimes: '⁢',
    IOcy: 'Ё',
    iocy: 'ё',
    Iogon: 'Į',
    iogon: 'į',
    Iopf: '𝕀',
    iopf: '𝕚',
    Iota: 'Ι',
    iota: 'ι',
    iprod: '⨼',
    iquest: '¿',
    iscr: '𝒾',
    Iscr: 'ℐ',
    isin: '∈',
    isindot: '⋵',
    isinE: '⋹',
    isins: '⋴',
    isinsv: '⋳',
    isinv: '∈',
    it: '⁢',
    Itilde: 'Ĩ',
    itilde: 'ĩ',
    Iukcy: 'І',
    iukcy: 'і',
    Iuml: 'Ï',
    iuml: 'ï',
    Jcirc: 'Ĵ',
    jcirc: 'ĵ',
    Jcy: 'Й',
    jcy: 'й',
    Jfr: '𝔍',
    jfr: '𝔧',
    jmath: 'ȷ',
    Jopf: '𝕁',
    jopf: '𝕛',
    Jscr: '𝒥',
    jscr: '𝒿',
    Jsercy: 'Ј',
    jsercy: 'ј',
    Jukcy: 'Є',
    jukcy: 'є',
    Kappa: 'Κ',
    kappa: 'κ',
    kappav: 'ϰ',
    Kcedil: 'Ķ',
    kcedil: 'ķ',
    Kcy: 'К',
    kcy: 'к',
    Kfr: '𝔎',
    kfr: '𝔨',
    kgreen: 'ĸ',
    KHcy: 'Х',
    khcy: 'х',
    KJcy: 'Ќ',
    kjcy: 'ќ',
    Kopf: '𝕂',
    kopf: '𝕜',
    Kscr: '𝒦',
    kscr: '𝓀',
    lAarr: '⇚',
    Lacute: 'Ĺ',
    lacute: 'ĺ',
    laemptyv: '⦴',
    lagran: 'ℒ',
    Lambda: 'Λ',
    lambda: 'λ',
    lang: '⟨',
    Lang: '⟪',
    langd: '⦑',
    langle: '⟨',
    lap: '⪅',
    Laplacetrf: 'ℒ',
    laquo: '«',
    larrb: '⇤',
    larrbfs: '⤟',
    larr: '←',
    Larr: '↞',
    lArr: '⇐',
    larrfs: '⤝',
    larrhk: '↩',
    larrlp: '↫',
    larrpl: '⤹',
    larrsim: '⥳',
    larrtl: '↢',
    latail: '⤙',
    lAtail: '⤛',
    lat: '⪫',
    late: '⪭',
    lates: '⪭︀',
    lbarr: '⤌',
    lBarr: '⤎',
    lbbrk: '❲',
    lbrace: '{',
    lbrack: '[',
    lbrke: '⦋',
    lbrksld: '⦏',
    lbrkslu: '⦍',
    Lcaron: 'Ľ',
    lcaron: 'ľ',
    Lcedil: 'Ļ',
    lcedil: 'ļ',
    lceil: '⌈',
    lcub: '{',
    Lcy: 'Л',
    lcy: 'л',
    ldca: '⤶',
    ldquo: '“',
    ldquor: '„',
    ldrdhar: '⥧',
    ldrushar: '⥋',
    ldsh: '↲',
    le: '≤',
    lE: '≦',
    LeftAngleBracket: '⟨',
    LeftArrowBar: '⇤',
    leftarrow: '←',
    LeftArrow: '←',
    Leftarrow: '⇐',
    LeftArrowRightArrow: '⇆',
    leftarrowtail: '↢',
    LeftCeiling: '⌈',
    LeftDoubleBracket: '⟦',
    LeftDownTeeVector: '⥡',
    LeftDownVectorBar: '⥙',
    LeftDownVector: '⇃',
    LeftFloor: '⌊',
    leftharpoondown: '↽',
    leftharpoonup: '↼',
    leftleftarrows: '⇇',
    leftrightarrow: '↔',
    LeftRightArrow: '↔',
    Leftrightarrow: '⇔',
    leftrightarrows: '⇆',
    leftrightharpoons: '⇋',
    leftrightsquigarrow: '↭',
    LeftRightVector: '⥎',
    LeftTeeArrow: '↤',
    LeftTee: '⊣',
    LeftTeeVector: '⥚',
    leftthreetimes: '⋋',
    LeftTriangleBar: '⧏',
    LeftTriangle: '⊲',
    LeftTriangleEqual: '⊴',
    LeftUpDownVector: '⥑',
    LeftUpTeeVector: '⥠',
    LeftUpVectorBar: '⥘',
    LeftUpVector: '↿',
    LeftVectorBar: '⥒',
    LeftVector: '↼',
    lEg: '⪋',
    leg: '⋚',
    leq: '≤',
    leqq: '≦',
    leqslant: '⩽',
    lescc: '⪨',
    les: '⩽',
    lesdot: '⩿',
    lesdoto: '⪁',
    lesdotor: '⪃',
    lesg: '⋚︀',
    lesges: '⪓',
    lessapprox: '⪅',
    lessdot: '⋖',
    lesseqgtr: '⋚',
    lesseqqgtr: '⪋',
    LessEqualGreater: '⋚',
    LessFullEqual: '≦',
    LessGreater: '≶',
    lessgtr: '≶',
    LessLess: '⪡',
    lesssim: '≲',
    LessSlantEqual: '⩽',
    LessTilde: '≲',
    lfisht: '⥼',
    lfloor: '⌊',
    Lfr: '𝔏',
    lfr: '𝔩',
    lg: '≶',
    lgE: '⪑',
    lHar: '⥢',
    lhard: '↽',
    lharu: '↼',
    lharul: '⥪',
    lhblk: '▄',
    LJcy: 'Љ',
    ljcy: 'љ',
    llarr: '⇇',
    ll: '≪',
    Ll: '⋘',
    llcorner: '⌞',
    Lleftarrow: '⇚',
    llhard: '⥫',
    lltri: '◺',
    Lmidot: 'Ŀ',
    lmidot: 'ŀ',
    lmoustache: '⎰',
    lmoust: '⎰',
    lnap: '⪉',
    lnapprox: '⪉',
    lne: '⪇',
    lnE: '≨',
    lneq: '⪇',
    lneqq: '≨',
    lnsim: '⋦',
    loang: '⟬',
    loarr: '⇽',
    lobrk: '⟦',
    longleftarrow: '⟵',
    LongLeftArrow: '⟵',
    Longleftarrow: '⟸',
    longleftrightarrow: '⟷',
    LongLeftRightArrow: '⟷',
    Longleftrightarrow: '⟺',
    longmapsto: '⟼',
    longrightarrow: '⟶',
    LongRightArrow: '⟶',
    Longrightarrow: '⟹',
    looparrowleft: '↫',
    looparrowright: '↬',
    lopar: '⦅',
    Lopf: '𝕃',
    lopf: '𝕝',
    loplus: '⨭',
    lotimes: '⨴',
    lowast: '∗',
    lowbar: '_',
    LowerLeftArrow: '↙',
    LowerRightArrow: '↘',
    loz: '◊',
    lozenge: '◊',
    lozf: '⧫',
    lpar: '(',
    lparlt: '⦓',
    lrarr: '⇆',
    lrcorner: '⌟',
    lrhar: '⇋',
    lrhard: '⥭',
    lrm: '‎',
    lrtri: '⊿',
    lsaquo: '‹',
    lscr: '𝓁',
    Lscr: 'ℒ',
    lsh: '↰',
    Lsh: '↰',
    lsim: '≲',
    lsime: '⪍',
    lsimg: '⪏',
    lsqb: '[',
    lsquo: '‘',
    lsquor: '‚',
    Lstrok: 'Ł',
    lstrok: 'ł',
    ltcc: '⪦',
    ltcir: '⩹',
    lt: '<',
    LT: '<',
    Lt: '≪',
    ltdot: '⋖',
    lthree: '⋋',
    ltimes: '⋉',
    ltlarr: '⥶',
    ltquest: '⩻',
    ltri: '◃',
    ltrie: '⊴',
    ltrif: '◂',
    ltrPar: '⦖',
    lurdshar: '⥊',
    luruhar: '⥦',
    lvertneqq: '≨︀',
    lvnE: '≨︀',
    macr: '¯',
    male: '♂',
    malt: '✠',
    maltese: '✠',
    Map: '⤅',
    map: '↦',
    mapsto: '↦',
    mapstodown: '↧',
    mapstoleft: '↤',
    mapstoup: '↥',
    marker: '▮',
    mcomma: '⨩',
    Mcy: 'М',
    mcy: 'м',
    mdash: '—',
    mDDot: '∺',
    measuredangle: '∡',
    MediumSpace: ' ',
    Mellintrf: 'ℳ',
    Mfr: '𝔐',
    mfr: '𝔪',
    mho: '℧',
    micro: 'µ',
    midast: '*',
    midcir: '⫰',
    mid: '∣',
    middot: '·',
    minusb: '⊟',
    minus: '−',
    minusd: '∸',
    minusdu: '⨪',
    MinusPlus: '∓',
    mlcp: '⫛',
    mldr: '…',
    mnplus: '∓',
    models: '⊧',
    Mopf: '𝕄',
    mopf: '𝕞',
    mp: '∓',
    mscr: '𝓂',
    Mscr: 'ℳ',
    mstpos: '∾',
    Mu: 'Μ',
    mu: 'μ',
    multimap: '⊸',
    mumap: '⊸',
    nabla: '∇',
    Nacute: 'Ń',
    nacute: 'ń',
    nang: '∠⃒',
    nap: '≉',
    napE: '⩰̸',
    napid: '≋̸',
    napos: 'ŉ',
    napprox: '≉',
    natural: '♮',
    naturals: 'ℕ',
    natur: '♮',
    nbsp: ' ',
    nbump: '≎̸',
    nbumpe: '≏̸',
    ncap: '⩃',
    Ncaron: 'Ň',
    ncaron: 'ň',
    Ncedil: 'Ņ',
    ncedil: 'ņ',
    ncong: '≇',
    ncongdot: '⩭̸',
    ncup: '⩂',
    Ncy: 'Н',
    ncy: 'н',
    ndash: '–',
    nearhk: '⤤',
    nearr: '↗',
    neArr: '⇗',
    nearrow: '↗',
    ne: '≠',
    nedot: '≐̸',
    NegativeMediumSpace: '​',
    NegativeThickSpace: '​',
    NegativeThinSpace: '​',
    NegativeVeryThinSpace: '​',
    nequiv: '≢',
    nesear: '⤨',
    nesim: '≂̸',
    NestedGreaterGreater: '≫',
    NestedLessLess: '≪',
    NewLine: '\n',
    nexist: '∄',
    nexists: '∄',
    Nfr: '𝔑',
    nfr: '𝔫',
    ngE: '≧̸',
    nge: '≱',
    ngeq: '≱',
    ngeqq: '≧̸',
    ngeqslant: '⩾̸',
    nges: '⩾̸',
    nGg: '⋙̸',
    ngsim: '≵',
    nGt: '≫⃒',
    ngt: '≯',
    ngtr: '≯',
    nGtv: '≫̸',
    nharr: '↮',
    nhArr: '⇎',
    nhpar: '⫲',
    ni: '∋',
    nis: '⋼',
    nisd: '⋺',
    niv: '∋',
    NJcy: 'Њ',
    njcy: 'њ',
    nlarr: '↚',
    nlArr: '⇍',
    nldr: '‥',
    nlE: '≦̸',
    nle: '≰',
    nleftarrow: '↚',
    nLeftarrow: '⇍',
    nleftrightarrow: '↮',
    nLeftrightarrow: '⇎',
    nleq: '≰',
    nleqq: '≦̸',
    nleqslant: '⩽̸',
    nles: '⩽̸',
    nless: '≮',
    nLl: '⋘̸',
    nlsim: '≴',
    nLt: '≪⃒',
    nlt: '≮',
    nltri: '⋪',
    nltrie: '⋬',
    nLtv: '≪̸',
    nmid: '∤',
    NoBreak: '⁠',
    NonBreakingSpace: ' ',
    nopf: '𝕟',
    Nopf: 'ℕ',
    Not: '⫬',
    not: '¬',
    NotCongruent: '≢',
    NotCupCap: '≭',
    NotDoubleVerticalBar: '∦',
    NotElement: '∉',
    NotEqual: '≠',
    NotEqualTilde: '≂̸',
    NotExists: '∄',
    NotGreater: '≯',
    NotGreaterEqual: '≱',
    NotGreaterFullEqual: '≧̸',
    NotGreaterGreater: '≫̸',
    NotGreaterLess: '≹',
    NotGreaterSlantEqual: '⩾̸',
    NotGreaterTilde: '≵',
    NotHumpDownHump: '≎̸',
    NotHumpEqual: '≏̸',
    notin: '∉',
    notindot: '⋵̸',
    notinE: '⋹̸',
    notinva: '∉',
    notinvb: '⋷',
    notinvc: '⋶',
    NotLeftTriangleBar: '⧏̸',
    NotLeftTriangle: '⋪',
    NotLeftTriangleEqual: '⋬',
    NotLess: '≮',
    NotLessEqual: '≰',
    NotLessGreater: '≸',
    NotLessLess: '≪̸',
    NotLessSlantEqual: '⩽̸',
    NotLessTilde: '≴',
    NotNestedGreaterGreater: '⪢̸',
    NotNestedLessLess: '⪡̸',
    notni: '∌',
    notniva: '∌',
    notnivb: '⋾',
    notnivc: '⋽',
    NotPrecedes: '⊀',
    NotPrecedesEqual: '⪯̸',
    NotPrecedesSlantEqual: '⋠',
    NotReverseElement: '∌',
    NotRightTriangleBar: '⧐̸',
    NotRightTriangle: '⋫',
    NotRightTriangleEqual: '⋭',
    NotSquareSubset: '⊏̸',
    NotSquareSubsetEqual: '⋢',
    NotSquareSuperset: '⊐̸',
    NotSquareSupersetEqual: '⋣',
    NotSubset: '⊂⃒',
    NotSubsetEqual: '⊈',
    NotSucceeds: '⊁',
    NotSucceedsEqual: '⪰̸',
    NotSucceedsSlantEqual: '⋡',
    NotSucceedsTilde: '≿̸',
    NotSuperset: '⊃⃒',
    NotSupersetEqual: '⊉',
    NotTilde: '≁',
    NotTildeEqual: '≄',
    NotTildeFullEqual: '≇',
    NotTildeTilde: '≉',
    NotVerticalBar: '∤',
    nparallel: '∦',
    npar: '∦',
    nparsl: '⫽⃥',
    npart: '∂̸',
    npolint: '⨔',
    npr: '⊀',
    nprcue: '⋠',
    nprec: '⊀',
    npreceq: '⪯̸',
    npre: '⪯̸',
    nrarrc: '⤳̸',
    nrarr: '↛',
    nrArr: '⇏',
    nrarrw: '↝̸',
    nrightarrow: '↛',
    nRightarrow: '⇏',
    nrtri: '⋫',
    nrtrie: '⋭',
    nsc: '⊁',
    nsccue: '⋡',
    nsce: '⪰̸',
    Nscr: '𝒩',
    nscr: '𝓃',
    nshortmid: '∤',
    nshortparallel: '∦',
    nsim: '≁',
    nsime: '≄',
    nsimeq: '≄',
    nsmid: '∤',
    nspar: '∦',
    nsqsube: '⋢',
    nsqsupe: '⋣',
    nsub: '⊄',
    nsubE: '⫅̸',
    nsube: '⊈',
    nsubset: '⊂⃒',
    nsubseteq: '⊈',
    nsubseteqq: '⫅̸',
    nsucc: '⊁',
    nsucceq: '⪰̸',
    nsup: '⊅',
    nsupE: '⫆̸',
    nsupe: '⊉',
    nsupset: '⊃⃒',
    nsupseteq: '⊉',
    nsupseteqq: '⫆̸',
    ntgl: '≹',
    Ntilde: 'Ñ',
    ntilde: 'ñ',
    ntlg: '≸',
    ntriangleleft: '⋪',
    ntrianglelefteq: '⋬',
    ntriangleright: '⋫',
    ntrianglerighteq: '⋭',
    Nu: 'Ν',
    nu: 'ν',
    num: '#',
    numero: '№',
    numsp: ' ',
    nvap: '≍⃒',
    nvdash: '⊬',
    nvDash: '⊭',
    nVdash: '⊮',
    nVDash: '⊯',
    nvge: '≥⃒',
    nvgt: '>⃒',
    nvHarr: '⤄',
    nvinfin: '⧞',
    nvlArr: '⤂',
    nvle: '≤⃒',
    nvlt: '<⃒',
    nvltrie: '⊴⃒',
    nvrArr: '⤃',
    nvrtrie: '⊵⃒',
    nvsim: '∼⃒',
    nwarhk: '⤣',
    nwarr: '↖',
    nwArr: '⇖',
    nwarrow: '↖',
    nwnear: '⤧',
    Oacute: 'Ó',
    oacute: 'ó',
    oast: '⊛',
    Ocirc: 'Ô',
    ocirc: 'ô',
    ocir: '⊚',
    Ocy: 'О',
    ocy: 'о',
    odash: '⊝',
    Odblac: 'Ő',
    odblac: 'ő',
    odiv: '⨸',
    odot: '⊙',
    odsold: '⦼',
    OElig: 'Œ',
    oelig: 'œ',
    ofcir: '⦿',
    Ofr: '𝔒',
    ofr: '𝔬',
    ogon: '˛',
    Ograve: 'Ò',
    ograve: 'ò',
    ogt: '⧁',
    ohbar: '⦵',
    ohm: 'Ω',
    oint: '∮',
    olarr: '↺',
    olcir: '⦾',
    olcross: '⦻',
    oline: '‾',
    olt: '⧀',
    Omacr: 'Ō',
    omacr: 'ō',
    Omega: 'Ω',
    omega: 'ω',
    Omicron: 'Ο',
    omicron: 'ο',
    omid: '⦶',
    ominus: '⊖',
    Oopf: '𝕆',
    oopf: '𝕠',
    opar: '⦷',
    OpenCurlyDoubleQuote: '“',
    OpenCurlyQuote: '‘',
    operp: '⦹',
    oplus: '⊕',
    orarr: '↻',
    Or: '⩔',
    or: '∨',
    ord: '⩝',
    order: 'ℴ',
    orderof: 'ℴ',
    ordf: 'ª',
    ordm: 'º',
    origof: '⊶',
    oror: '⩖',
    orslope: '⩗',
    orv: '⩛',
    oS: 'Ⓢ',
    Oscr: '𝒪',
    oscr: 'ℴ',
    Oslash: 'Ø',
    oslash: 'ø',
    osol: '⊘',
    Otilde: 'Õ',
    otilde: 'õ',
    otimesas: '⨶',
    Otimes: '⨷',
    otimes: '⊗',
    Ouml: 'Ö',
    ouml: 'ö',
    ovbar: '⌽',
    OverBar: '‾',
    OverBrace: '⏞',
    OverBracket: '⎴',
    OverParenthesis: '⏜',
    para: '¶',
    parallel: '∥',
    par: '∥',
    parsim: '⫳',
    parsl: '⫽',
    part: '∂',
    PartialD: '∂',
    Pcy: 'П',
    pcy: 'п',
    percnt: '%',
    period: '.',
    permil: '‰',
    perp: '⊥',
    pertenk: '‱',
    Pfr: '𝔓',
    pfr: '𝔭',
    Phi: 'Φ',
    phi: 'φ',
    phiv: 'ϕ',
    phmmat: 'ℳ',
    phone: '☎',
    Pi: 'Π',
    pi: 'π',
    pitchfork: '⋔',
    piv: 'ϖ',
    planck: 'ℏ',
    planckh: 'ℎ',
    plankv: 'ℏ',
    plusacir: '⨣',
    plusb: '⊞',
    pluscir: '⨢',
    plus: '+',
    plusdo: '∔',
    plusdu: '⨥',
    pluse: '⩲',
    PlusMinus: '±',
    plusmn: '±',
    plussim: '⨦',
    plustwo: '⨧',
    pm: '±',
    Poincareplane: 'ℌ',
    pointint: '⨕',
    popf: '𝕡',
    Popf: 'ℙ',
    pound: '£',
    prap: '⪷',
    Pr: '⪻',
    pr: '≺',
    prcue: '≼',
    precapprox: '⪷',
    prec: '≺',
    preccurlyeq: '≼',
    Precedes: '≺',
    PrecedesEqual: '⪯',
    PrecedesSlantEqual: '≼',
    PrecedesTilde: '≾',
    preceq: '⪯',
    precnapprox: '⪹',
    precneqq: '⪵',
    precnsim: '⋨',
    pre: '⪯',
    prE: '⪳',
    precsim: '≾',
    prime: '′',
    Prime: '″',
    primes: 'ℙ',
    prnap: '⪹',
    prnE: '⪵',
    prnsim: '⋨',
    prod: '∏',
    Product: '∏',
    profalar: '⌮',
    profline: '⌒',
    profsurf: '⌓',
    prop: '∝',
    Proportional: '∝',
    Proportion: '∷',
    propto: '∝',
    prsim: '≾',
    prurel: '⊰',
    Pscr: '𝒫',
    pscr: '𝓅',
    Psi: 'Ψ',
    psi: 'ψ',
    puncsp: ' ',
    Qfr: '𝔔',
    qfr: '𝔮',
    qint: '⨌',
    qopf: '𝕢',
    Qopf: 'ℚ',
    qprime: '⁗',
    Qscr: '𝒬',
    qscr: '𝓆',
    quaternions: 'ℍ',
    quatint: '⨖',
    quest: '?',
    questeq: '≟',
    quot: '"',
    QUOT: '"',
    rAarr: '⇛',
    race: '∽̱',
    Racute: 'Ŕ',
    racute: 'ŕ',
    radic: '√',
    raemptyv: '⦳',
    rang: '⟩',
    Rang: '⟫',
    rangd: '⦒',
    range: '⦥',
    rangle: '⟩',
    raquo: '»',
    rarrap: '⥵',
    rarrb: '⇥',
    rarrbfs: '⤠',
    rarrc: '⤳',
    rarr: '→',
    Rarr: '↠',
    rArr: '⇒',
    rarrfs: '⤞',
    rarrhk: '↪',
    rarrlp: '↬',
    rarrpl: '⥅',
    rarrsim: '⥴',
    Rarrtl: '⤖',
    rarrtl: '↣',
    rarrw: '↝',
    ratail: '⤚',
    rAtail: '⤜',
    ratio: '∶',
    rationals: 'ℚ',
    rbarr: '⤍',
    rBarr: '⤏',
    RBarr: '⤐',
    rbbrk: '❳',
    rbrace: '}',
    rbrack: ']',
    rbrke: '⦌',
    rbrksld: '⦎',
    rbrkslu: '⦐',
    Rcaron: 'Ř',
    rcaron: 'ř',
    Rcedil: 'Ŗ',
    rcedil: 'ŗ',
    rceil: '⌉',
    rcub: '}',
    Rcy: 'Р',
    rcy: 'р',
    rdca: '⤷',
    rdldhar: '⥩',
    rdquo: '”',
    rdquor: '”',
    rdsh: '↳',
    real: 'ℜ',
    realine: 'ℛ',
    realpart: 'ℜ',
    reals: 'ℝ',
    Re: 'ℜ',
    rect: '▭',
    reg: '®',
    REG: '®',
    ReverseElement: '∋',
    ReverseEquilibrium: '⇋',
    ReverseUpEquilibrium: '⥯',
    rfisht: '⥽',
    rfloor: '⌋',
    rfr: '𝔯',
    Rfr: 'ℜ',
    rHar: '⥤',
    rhard: '⇁',
    rharu: '⇀',
    rharul: '⥬',
    Rho: 'Ρ',
    rho: 'ρ',
    rhov: 'ϱ',
    RightAngleBracket: '⟩',
    RightArrowBar: '⇥',
    rightarrow: '→',
    RightArrow: '→',
    Rightarrow: '⇒',
    RightArrowLeftArrow: '⇄',
    rightarrowtail: '↣',
    RightCeiling: '⌉',
    RightDoubleBracket: '⟧',
    RightDownTeeVector: '⥝',
    RightDownVectorBar: '⥕',
    RightDownVector: '⇂',
    RightFloor: '⌋',
    rightharpoondown: '⇁',
    rightharpoonup: '⇀',
    rightleftarrows: '⇄',
    rightleftharpoons: '⇌',
    rightrightarrows: '⇉',
    rightsquigarrow: '↝',
    RightTeeArrow: '↦',
    RightTee: '⊢',
    RightTeeVector: '⥛',
    rightthreetimes: '⋌',
    RightTriangleBar: '⧐',
    RightTriangle: '⊳',
    RightTriangleEqual: '⊵',
    RightUpDownVector: '⥏',
    RightUpTeeVector: '⥜',
    RightUpVectorBar: '⥔',
    RightUpVector: '↾',
    RightVectorBar: '⥓',
    RightVector: '⇀',
    ring: '˚',
    risingdotseq: '≓',
    rlarr: '⇄',
    rlhar: '⇌',
    rlm: '‏',
    rmoustache: '⎱',
    rmoust: '⎱',
    rnmid: '⫮',
    roang: '⟭',
    roarr: '⇾',
    robrk: '⟧',
    ropar: '⦆',
    ropf: '𝕣',
    Ropf: 'ℝ',
    roplus: '⨮',
    rotimes: '⨵',
    RoundImplies: '⥰',
    rpar: ')',
    rpargt: '⦔',
    rppolint: '⨒',
    rrarr: '⇉',
    Rrightarrow: '⇛',
    rsaquo: '›',
    rscr: '𝓇',
    Rscr: 'ℛ',
    rsh: '↱',
    Rsh: '↱',
    rsqb: ']',
    rsquo: '’',
    rsquor: '’',
    rthree: '⋌',
    rtimes: '⋊',
    rtri: '▹',
    rtrie: '⊵',
    rtrif: '▸',
    rtriltri: '⧎',
    RuleDelayed: '⧴',
    ruluhar: '⥨',
    rx: '℞',
    Sacute: 'Ś',
    sacute: 'ś',
    sbquo: '‚',
    scap: '⪸',
    Scaron: 'Š',
    scaron: 'š',
    Sc: '⪼',
    sc: '≻',
    sccue: '≽',
    sce: '⪰',
    scE: '⪴',
    Scedil: 'Ş',
    scedil: 'ş',
    Scirc: 'Ŝ',
    scirc: 'ŝ',
    scnap: '⪺',
    scnE: '⪶',
    scnsim: '⋩',
    scpolint: '⨓',
    scsim: '≿',
    Scy: 'С',
    scy: 'с',
    sdotb: '⊡',
    sdot: '⋅',
    sdote: '⩦',
    searhk: '⤥',
    searr: '↘',
    seArr: '⇘',
    searrow: '↘',
    sect: '§',
    semi: ';',
    seswar: '⤩',
    setminus: '∖',
    setmn: '∖',
    sext: '✶',
    Sfr: '𝔖',
    sfr: '𝔰',
    sfrown: '⌢',
    sharp: '♯',
    SHCHcy: 'Щ',
    shchcy: 'щ',
    SHcy: 'Ш',
    shcy: 'ш',
    ShortDownArrow: '↓',
    ShortLeftArrow: '←',
    shortmid: '∣',
    shortparallel: '∥',
    ShortRightArrow: '→',
    ShortUpArrow: '↑',
    shy: '­',
    Sigma: 'Σ',
    sigma: 'σ',
    sigmaf: 'ς',
    sigmav: 'ς',
    sim: '∼',
    simdot: '⩪',
    sime: '≃',
    simeq: '≃',
    simg: '⪞',
    simgE: '⪠',
    siml: '⪝',
    simlE: '⪟',
    simne: '≆',
    simplus: '⨤',
    simrarr: '⥲',
    slarr: '←',
    SmallCircle: '∘',
    smallsetminus: '∖',
    smashp: '⨳',
    smeparsl: '⧤',
    smid: '∣',
    smile: '⌣',
    smt: '⪪',
    smte: '⪬',
    smtes: '⪬︀',
    SOFTcy: 'Ь',
    softcy: 'ь',
    solbar: '⌿',
    solb: '⧄',
    sol: '/',
    Sopf: '𝕊',
    sopf: '𝕤',
    spades: '♠',
    spadesuit: '♠',
    spar: '∥',
    sqcap: '⊓',
    sqcaps: '⊓︀',
    sqcup: '⊔',
    sqcups: '⊔︀',
    Sqrt: '√',
    sqsub: '⊏',
    sqsube: '⊑',
    sqsubset: '⊏',
    sqsubseteq: '⊑',
    sqsup: '⊐',
    sqsupe: '⊒',
    sqsupset: '⊐',
    sqsupseteq: '⊒',
    square: '□',
    Square: '□',
    SquareIntersection: '⊓',
    SquareSubset: '⊏',
    SquareSubsetEqual: '⊑',
    SquareSuperset: '⊐',
    SquareSupersetEqual: '⊒',
    SquareUnion: '⊔',
    squarf: '▪',
    squ: '□',
    squf: '▪',
    srarr: '→',
    Sscr: '𝒮',
    sscr: '𝓈',
    ssetmn: '∖',
    ssmile: '⌣',
    sstarf: '⋆',
    Star: '⋆',
    star: '☆',
    starf: '★',
    straightepsilon: 'ϵ',
    straightphi: 'ϕ',
    strns: '¯',
    sub: '⊂',
    Sub: '⋐',
    subdot: '⪽',
    subE: '⫅',
    sube: '⊆',
    subedot: '⫃',
    submult: '⫁',
    subnE: '⫋',
    subne: '⊊',
    subplus: '⪿',
    subrarr: '⥹',
    subset: '⊂',
    Subset: '⋐',
    subseteq: '⊆',
    subseteqq: '⫅',
    SubsetEqual: '⊆',
    subsetneq: '⊊',
    subsetneqq: '⫋',
    subsim: '⫇',
    subsub: '⫕',
    subsup: '⫓',
    succapprox: '⪸',
    succ: '≻',
    succcurlyeq: '≽',
    Succeeds: '≻',
    SucceedsEqual: '⪰',
    SucceedsSlantEqual: '≽',
    SucceedsTilde: '≿',
    succeq: '⪰',
    succnapprox: '⪺',
    succneqq: '⪶',
    succnsim: '⋩',
    succsim: '≿',
    SuchThat: '∋',
    sum: '∑',
    Sum: '∑',
    sung: '♪',
    sup1: '¹',
    sup2: '²',
    sup3: '³',
    sup: '⊃',
    Sup: '⋑',
    supdot: '⪾',
    supdsub: '⫘',
    supE: '⫆',
    supe: '⊇',
    supedot: '⫄',
    Superset: '⊃',
    SupersetEqual: '⊇',
    suphsol: '⟉',
    suphsub: '⫗',
    suplarr: '⥻',
    supmult: '⫂',
    supnE: '⫌',
    supne: '⊋',
    supplus: '⫀',
    supset: '⊃',
    Supset: '⋑',
    supseteq: '⊇',
    supseteqq: '⫆',
    supsetneq: '⊋',
    supsetneqq: '⫌',
    supsim: '⫈',
    supsub: '⫔',
    supsup: '⫖',
    swarhk: '⤦',
    swarr: '↙',
    swArr: '⇙',
    swarrow: '↙',
    swnwar: '⤪',
    szlig: 'ß',
    Tab: '\t',
    target: '⌖',
    Tau: 'Τ',
    tau: 'τ',
    tbrk: '⎴',
    Tcaron: 'Ť',
    tcaron: 'ť',
    Tcedil: 'Ţ',
    tcedil: 'ţ',
    Tcy: 'Т',
    tcy: 'т',
    tdot: '⃛',
    telrec: '⌕',
    Tfr: '𝔗',
    tfr: '𝔱',
    there4: '∴',
    therefore: '∴',
    Therefore: '∴',
    Theta: 'Θ',
    theta: 'θ',
    thetasym: 'ϑ',
    thetav: 'ϑ',
    thickapprox: '≈',
    thicksim: '∼',
    ThickSpace: '  ',
    ThinSpace: ' ',
    thinsp: ' ',
    thkap: '≈',
    thksim: '∼',
    THORN: 'Þ',
    thorn: 'þ',
    tilde: '˜',
    Tilde: '∼',
    TildeEqual: '≃',
    TildeFullEqual: '≅',
    TildeTilde: '≈',
    timesbar: '⨱',
    timesb: '⊠',
    times: '×',
    timesd: '⨰',
    tint: '∭',
    toea: '⤨',
    topbot: '⌶',
    topcir: '⫱',
    top: '⊤',
    Topf: '𝕋',
    topf: '𝕥',
    topfork: '⫚',
    tosa: '⤩',
    tprime: '‴',
    trade: '™',
    TRADE: '™',
    triangle: '▵',
    triangledown: '▿',
    triangleleft: '◃',
    trianglelefteq: '⊴',
    triangleq: '≜',
    triangleright: '▹',
    trianglerighteq: '⊵',
    tridot: '◬',
    trie: '≜',
    triminus: '⨺',
    TripleDot: '⃛',
    triplus: '⨹',
    trisb: '⧍',
    tritime: '⨻',
    trpezium: '⏢',
    Tscr: '𝒯',
    tscr: '𝓉',
    TScy: 'Ц',
    tscy: 'ц',
    TSHcy: 'Ћ',
    tshcy: 'ћ',
    Tstrok: 'Ŧ',
    tstrok: 'ŧ',
    twixt: '≬',
    twoheadleftarrow: '↞',
    twoheadrightarrow: '↠',
    Uacute: 'Ú',
    uacute: 'ú',
    uarr: '↑',
    Uarr: '↟',
    uArr: '⇑',
    Uarrocir: '⥉',
    Ubrcy: 'Ў',
    ubrcy: 'ў',
    Ubreve: 'Ŭ',
    ubreve: 'ŭ',
    Ucirc: 'Û',
    ucirc: 'û',
    Ucy: 'У',
    ucy: 'у',
    udarr: '⇅',
    Udblac: 'Ű',
    udblac: 'ű',
    udhar: '⥮',
    ufisht: '⥾',
    Ufr: '𝔘',
    ufr: '𝔲',
    Ugrave: 'Ù',
    ugrave: 'ù',
    uHar: '⥣',
    uharl: '↿',
    uharr: '↾',
    uhblk: '▀',
    ulcorn: '⌜',
    ulcorner: '⌜',
    ulcrop: '⌏',
    ultri: '◸',
    Umacr: 'Ū',
    umacr: 'ū',
    uml: '¨',
    UnderBar: '_',
    UnderBrace: '⏟',
    UnderBracket: '⎵',
    UnderParenthesis: '⏝',
    Union: '⋃',
    UnionPlus: '⊎',
    Uogon: 'Ų',
    uogon: 'ų',
    Uopf: '𝕌',
    uopf: '𝕦',
    UpArrowBar: '⤒',
    uparrow: '↑',
    UpArrow: '↑',
    Uparrow: '⇑',
    UpArrowDownArrow: '⇅',
    updownarrow: '↕',
    UpDownArrow: '↕',
    Updownarrow: '⇕',
    UpEquilibrium: '⥮',
    upharpoonleft: '↿',
    upharpoonright: '↾',
    uplus: '⊎',
    UpperLeftArrow: '↖',
    UpperRightArrow: '↗',
    upsi: 'υ',
    Upsi: 'ϒ',
    upsih: 'ϒ',
    Upsilon: 'Υ',
    upsilon: 'υ',
    UpTeeArrow: '↥',
    UpTee: '⊥',
    upuparrows: '⇈',
    urcorn: '⌝',
    urcorner: '⌝',
    urcrop: '⌎',
    Uring: 'Ů',
    uring: 'ů',
    urtri: '◹',
    Uscr: '𝒰',
    uscr: '𝓊',
    utdot: '⋰',
    Utilde: 'Ũ',
    utilde: 'ũ',
    utri: '▵',
    utrif: '▴',
    uuarr: '⇈',
    Uuml: 'Ü',
    uuml: 'ü',
    uwangle: '⦧',
    vangrt: '⦜',
    varepsilon: 'ϵ',
    varkappa: 'ϰ',
    varnothing: '∅',
    varphi: 'ϕ',
    varpi: 'ϖ',
    varpropto: '∝',
    varr: '↕',
    vArr: '⇕',
    varrho: 'ϱ',
    varsigma: 'ς',
    varsubsetneq: '⊊︀',
    varsubsetneqq: '⫋︀',
    varsupsetneq: '⊋︀',
    varsupsetneqq: '⫌︀',
    vartheta: 'ϑ',
    vartriangleleft: '⊲',
    vartriangleright: '⊳',
    vBar: '⫨',
    Vbar: '⫫',
    vBarv: '⫩',
    Vcy: 'В',
    vcy: 'в',
    vdash: '⊢',
    vDash: '⊨',
    Vdash: '⊩',
    VDash: '⊫',
    Vdashl: '⫦',
    veebar: '⊻',
    vee: '∨',
    Vee: '⋁',
    veeeq: '≚',
    vellip: '⋮',
    verbar: '|',
    Verbar: '‖',
    vert: '|',
    Vert: '‖',
    VerticalBar: '∣',
    VerticalLine: '|',
    VerticalSeparator: '❘',
    VerticalTilde: '≀',
    VeryThinSpace: ' ',
    Vfr: '𝔙',
    vfr: '𝔳',
    vltri: '⊲',
    vnsub: '⊂⃒',
    vnsup: '⊃⃒',
    Vopf: '𝕍',
    vopf: '𝕧',
    vprop: '∝',
    vrtri: '⊳',
    Vscr: '𝒱',
    vscr: '𝓋',
    vsubnE: '⫋︀',
    vsubne: '⊊︀',
    vsupnE: '⫌︀',
    vsupne: '⊋︀',
    Vvdash: '⊪',
    vzigzag: '⦚',
    Wcirc: 'Ŵ',
    wcirc: 'ŵ',
    wedbar: '⩟',
    wedge: '∧',
    Wedge: '⋀',
    wedgeq: '≙',
    weierp: '℘',
    Wfr: '𝔚',
    wfr: '𝔴',
    Wopf: '𝕎',
    wopf: '𝕨',
    wp: '℘',
    wr: '≀',
    wreath: '≀',
    Wscr: '𝒲',
    wscr: '𝓌',
    xcap: '⋂',
    xcirc: '◯',
    xcup: '⋃',
    xdtri: '▽',
    Xfr: '𝔛',
    xfr: '𝔵',
    xharr: '⟷',
    xhArr: '⟺',
    Xi: 'Ξ',
    xi: 'ξ',
    xlarr: '⟵',
    xlArr: '⟸',
    xmap: '⟼',
    xnis: '⋻',
    xodot: '⨀',
    Xopf: '𝕏',
    xopf: '𝕩',
    xoplus: '⨁',
    xotime: '⨂',
    xrarr: '⟶',
    xrArr: '⟹',
    Xscr: '𝒳',
    xscr: '𝓍',
    xsqcup: '⨆',
    xuplus: '⨄',
    xutri: '△',
    xvee: '⋁',
    xwedge: '⋀',
    Yacute: 'Ý',
    yacute: 'ý',
    YAcy: 'Я',
    yacy: 'я',
    Ycirc: 'Ŷ',
    ycirc: 'ŷ',
    Ycy: 'Ы',
    ycy: 'ы',
    yen: '¥',
    Yfr: '𝔜',
    yfr: '𝔶',
    YIcy: 'Ї',
    yicy: 'ї',
    Yopf: '𝕐',
    yopf: '𝕪',
    Yscr: '𝒴',
    yscr: '𝓎',
    YUcy: 'Ю',
    yucy: 'ю',
    yuml: 'ÿ',
    Yuml: 'Ÿ',
    Zacute: 'Ź',
    zacute: 'ź',
    Zcaron: 'Ž',
    zcaron: 'ž',
    Zcy: 'З',
    zcy: 'з',
    Zdot: 'Ż',
    zdot: 'ż',
    zeetrf: 'ℨ',
    ZeroWidthSpace: '​',
    Zeta: 'Ζ',
    zeta: 'ζ',
    zfr: '𝔷',
    Zfr: 'ℨ',
    ZHcy: 'Ж',
    zhcy: 'ж',
    zigrarr: '⇝',
    zopf: '𝕫',
    Zopf: 'ℤ',
    Zscr: '𝒵',
    zscr: '𝓏',
    zwj: '‍',
    zwnj: '‌',
  },
  n =
    /[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4E\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDF55-\uDF59]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDF3C-\uDF3E]|\uD806[\uDC3B\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,
  s = {},
  o = {};
function i(e, r, t) {
  var n,
    s,
    a,
    c,
    l,
    u = '';
  for (
    'string' != typeof r && ((t = r), (r = i.defaultChars)),
      void 0 === t && (t = !0),
      l = (function (e) {
        var r,
          t,
          n = o[e];
        if (n) return n;
        for (n = o[e] = [], r = 0; r < 128; r++)
          (t = String.fromCharCode(r)),
            /^[0-9a-z]$/i.test(t)
              ? n.push(t)
              : n.push('%' + ('0' + r.toString(16).toUpperCase()).slice(-2));
        for (r = 0; r < e.length; r++) n[e.charCodeAt(r)] = e[r];
        return n;
      })(r),
      n = 0,
      s = e.length;
    n < s;
    n++
  )
    if (
      ((a = e.charCodeAt(n)),
      t && 37 === a && n + 2 < s && /^[0-9a-f]{2}$/i.test(e.slice(n + 1, n + 3)))
    )
      (u += e.slice(n, n + 3)), (n += 2);
    else if (a < 128) u += l[a];
    else if (a >= 55296 && a <= 57343) {
      if (
        a >= 55296 &&
        a <= 56319 &&
        n + 1 < s &&
        (c = e.charCodeAt(n + 1)) >= 56320 &&
        c <= 57343
      ) {
        (u += encodeURIComponent(e[n] + e[n + 1])), n++;
        continue;
      }
      u += '%EF%BF%BD';
    } else u += encodeURIComponent(e[n]);
  return u;
}
(i.defaultChars = ";/?:@&=+$,-_.!~*'()#"), (i.componentChars = "-_.!~*'()");
var a = i,
  c = {};
function l(e, r) {
  var t;
  return (
    'string' != typeof r && (r = l.defaultChars),
    (t = (function (e) {
      var r,
        t,
        n = c[e];
      if (n) return n;
      for (n = c[e] = [], r = 0; r < 128; r++) (t = String.fromCharCode(r)), n.push(t);
      for (r = 0; r < e.length; r++)
        n[(t = e.charCodeAt(r))] = '%' + ('0' + t.toString(16).toUpperCase()).slice(-2);
      return n;
    })(r)),
    e.replace(/(%[a-f0-9]{2})+/gi, function (e) {
      var r,
        n,
        s,
        o,
        i,
        a,
        c,
        l = '';
      for (r = 0, n = e.length; r < n; r += 3)
        (s = parseInt(e.slice(r + 1, r + 3), 16)) < 128
          ? (l += t[s])
          : 192 == (224 & s) &&
              r + 3 < n &&
              128 == (192 & (o = parseInt(e.slice(r + 4, r + 6), 16)))
            ? ((l += (c = ((s << 6) & 1984) | (63 & o)) < 128 ? '��' : String.fromCharCode(c)),
              (r += 3))
            : 224 == (240 & s) &&
                r + 6 < n &&
                ((o = parseInt(e.slice(r + 4, r + 6), 16)),
                (i = parseInt(e.slice(r + 7, r + 9), 16)),
                128 == (192 & o) && 128 == (192 & i))
              ? ((l +=
                  (c = ((s << 12) & 61440) | ((o << 6) & 4032) | (63 & i)) < 2048 ||
                  (c >= 55296 && c <= 57343)
                    ? '���'
                    : String.fromCharCode(c)),
                (r += 6))
              : 240 == (248 & s) &&
                  r + 9 < n &&
                  ((o = parseInt(e.slice(r + 4, r + 6), 16)),
                  (i = parseInt(e.slice(r + 7, r + 9), 16)),
                  (a = parseInt(e.slice(r + 10, r + 12), 16)),
                  128 == (192 & o) && 128 == (192 & i) && 128 == (192 & a))
                ? ((c =
                    ((s << 18) & 1835008) | ((o << 12) & 258048) | ((i << 6) & 4032) | (63 & a)) <
                    65536 || c > 1114111
                    ? (l += '����')
                    : ((c -= 65536),
                      (l += String.fromCharCode(55296 + (c >> 10), 56320 + (1023 & c)))),
                  (r += 9))
                : (l += '�');
      return l;
    })
  );
}
(l.defaultChars = ';/?:@&=+$,#'), (l.componentChars = '');
var u = l;
function p() {
  (this.protocol = null),
    (this.slashes = null),
    (this.auth = null),
    (this.port = null),
    (this.hostname = null),
    (this.hash = null),
    (this.search = null),
    (this.pathname = null);
}
var h = /^([a-z0-9.+-]+:)/i,
  f = /:[0-9]*$/,
  d = /^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,
  m = ['{', '}', '|', '\\', '^', '`'].concat(['<', '>', '"', '`', ' ', '\r', '\n', '\t']),
  g = ["'"].concat(m),
  _ = ['%', '/', '?', ';', '#'].concat(g),
  k = ['/', '?', '#'],
  b = /^[+a-z0-9A-Z_-]{0,63}$/,
  v = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,
  C = { javascript: !0, 'javascript:': !0 },
  y = {
    http: !0,
    https: !0,
    ftp: !0,
    gopher: !0,
    file: !0,
    'http:': !0,
    'https:': !0,
    'ftp:': !0,
    'gopher:': !0,
    'file:': !0,
  };
(p.prototype.parse = function (e, r) {
  var t,
    n,
    s,
    o,
    i,
    a = e;
  if (((a = a.trim()), !r && 1 === e.split('#').length)) {
    var c = d.exec(a);
    if (c) return (this.pathname = c[1]), c[2] && (this.search = c[2]), this;
  }
  var l = h.exec(a);
  if (
    (l && ((s = (l = l[0]).toLowerCase()), (this.protocol = l), (a = a.substr(l.length))),
    (r || l || a.match(/^\/\/[^@\/]+@[^@\/]+/)) &&
      (!(i = '//' === a.substr(0, 2)) || (l && C[l]) || ((a = a.substr(2)), (this.slashes = !0))),
    !C[l] && (i || (l && !y[l])))
  ) {
    var u,
      p,
      f = -1;
    for (t = 0; t < k.length; t++) -1 !== (o = a.indexOf(k[t])) && (-1 === f || o < f) && (f = o);
    for (
      -1 !== (p = -1 === f ? a.lastIndexOf('@') : a.lastIndexOf('@', f)) &&
        ((u = a.slice(0, p)), (a = a.slice(p + 1)), (this.auth = u)),
        f = -1,
        t = 0;
      t < _.length;
      t++
    )
      -1 !== (o = a.indexOf(_[t])) && (-1 === f || o < f) && (f = o);
    -1 === f && (f = a.length), ':' === a[f - 1] && f--;
    var m = a.slice(0, f);
    (a = a.slice(f)), this.parseHost(m), (this.hostname = this.hostname || '');
    var g = '[' === this.hostname[0] && ']' === this.hostname[this.hostname.length - 1];
    if (!g) {
      var A = this.hostname.split(/\./);
      for (t = 0, n = A.length; t < n; t++) {
        var x = A[t];
        if (x && !x.match(b)) {
          for (var D = '', w = 0, E = x.length; w < E; w++)
            x.charCodeAt(w) > 127 ? (D += 'x') : (D += x[w]);
          if (!D.match(b)) {
            var q = A.slice(0, t),
              S = A.slice(t + 1),
              F = x.match(v);
            F && (q.push(F[1]), S.unshift(F[2])),
              S.length && (a = S.join('.') + a),
              (this.hostname = q.join('.'));
            break;
          }
        }
      }
    }
    this.hostname.length > 255 && (this.hostname = ''),
      g && (this.hostname = this.hostname.substr(1, this.hostname.length - 2));
  }
  var L = a.indexOf('#');
  -1 !== L && ((this.hash = a.substr(L)), (a = a.slice(0, L)));
  var z = a.indexOf('?');
  return (
    -1 !== z && ((this.search = a.substr(z)), (a = a.slice(0, z))),
    a && (this.pathname = a),
    y[s] && this.hostname && !this.pathname && (this.pathname = ''),
    this
  );
}),
  (p.prototype.parseHost = function (e) {
    var r = f.exec(e);
    r && (':' !== (r = r[0]) && (this.port = r.substr(1)), (e = e.substr(0, e.length - r.length))),
      e && (this.hostname = e);
  });
var A = function (e, r) {
  if (e && e instanceof p) return e;
  var t = new p();
  return t.parse(e, r), t;
};
(s.encode = a),
  (s.decode = u),
  (s.format = function (e) {
    var r = '';
    return (
      (r += e.protocol || ''),
      (r += e.slashes ? '//' : ''),
      (r += e.auth ? e.auth + '@' : ''),
      e.hostname && -1 !== e.hostname.indexOf(':')
        ? (r += '[' + e.hostname + ']')
        : (r += e.hostname || ''),
      (r += e.port ? ':' + e.port : ''),
      (r += e.pathname || ''),
      (r += e.search || ''),
      (r += e.hash || '')
    );
  }),
  (s.parse = A);
var x = {},
  D =
    /[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,
  w = /[\0-\x1F\x7F-\x9F]/,
  E = /[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/;
(x.Any = D),
  (x.Cc = w),
  (x.Cf =
    /[\xAD\u0600-\u0605\u061C\u06DD\u070F\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/),
  (x.P = n),
  (x.Z = E),
  (function (e) {
    var r = Object.prototype.hasOwnProperty;
    function o(e, t) {
      return r.call(e, t);
    }
    function i(e) {
      return (
        !(e >= 55296 && e <= 57343) &&
        !(e >= 64976 && e <= 65007) &&
        65535 != (65535 & e) &&
        65534 != (65535 & e) &&
        !(e >= 0 && e <= 8) &&
        11 !== e &&
        !(e >= 14 && e <= 31) &&
        !(e >= 127 && e <= 159) &&
        !(e > 1114111)
      );
    }
    function a(e) {
      if (e > 65535) {
        var r = 55296 + ((e -= 65536) >> 10),
          t = 56320 + (1023 & e);
        return String.fromCharCode(r, t);
      }
      return String.fromCharCode(e);
    }
    var c = /\\([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g,
      l = new RegExp(c.source + '|' + /&([a-z#][a-z0-9]{1,31});/gi.source, 'gi'),
      u = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i,
      p = t;
    var h = /[&<>"]/,
      f = /[&<>"]/g,
      d = { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;' };
    function m(e) {
      return d[e];
    }
    var g = /[.?*+^$[\]\\(){}|-]/g;
    var _ = n;
    (e.lib = {}),
      (e.lib.mdurl = s),
      (e.lib.ucmicro = x),
      (e.assign = function (e) {
        var r = Array.prototype.slice.call(arguments, 1);
        return (
          r.forEach(function (r) {
            if (r) {
              if ('object' != typeof r) throw new TypeError(r + 'must be object');
              Object.keys(r).forEach(function (t) {
                e[t] = r[t];
              });
            }
          }),
          e
        );
      }),
      (e.isString = function (e) {
        return (
          '[object String]' ===
          (function (e) {
            return Object.prototype.toString.call(e);
          })(e)
        );
      }),
      (e.has = o),
      (e.unescapeMd = function (e) {
        return e.indexOf('\\') < 0 ? e : e.replace(c, '$1');
      }),
      (e.unescapeAll = function (e) {
        return e.indexOf('\\') < 0 && e.indexOf('&') < 0
          ? e
          : e.replace(l, function (e, r, t) {
              return (
                r ||
                (function (e, r) {
                  var t = 0;
                  return o(p, r)
                    ? p[r]
                    : 35 === r.charCodeAt(0) &&
                        u.test(r) &&
                        i(
                          (t =
                            'x' === r[1].toLowerCase()
                              ? parseInt(r.slice(2), 16)
                              : parseInt(r.slice(1), 10))
                        )
                      ? a(t)
                      : e;
                })(e, t)
              );
            });
      }),
      (e.isValidEntityCode = i),
      (e.fromCodePoint = a),
      (e.escapeHtml = function (e) {
        return h.test(e) ? e.replace(f, m) : e;
      }),
      (e.arrayReplaceAt = function (e, r, t) {
        return [].concat(e.slice(0, r), t, e.slice(r + 1));
      }),
      (e.isSpace = function (e) {
        switch (e) {
          case 9:
          case 32:
            return !0;
        }
        return !1;
      }),
      (e.isWhiteSpace = function (e) {
        if (e >= 8192 && e <= 8202) return !0;
        switch (e) {
          case 9:
          case 10:
          case 11:
          case 12:
          case 13:
          case 32:
          case 160:
          case 5760:
          case 8239:
          case 8287:
          case 12288:
            return !0;
        }
        return !1;
      }),
      (e.isMdAsciiPunct = function (e) {
        switch (e) {
          case 33:
          case 34:
          case 35:
          case 36:
          case 37:
          case 38:
          case 39:
          case 40:
          case 41:
          case 42:
          case 43:
          case 44:
          case 45:
          case 46:
          case 47:
          case 58:
          case 59:
          case 60:
          case 61:
          case 62:
          case 63:
          case 64:
          case 91:
          case 92:
          case 93:
          case 94:
          case 95:
          case 96:
          case 123:
          case 124:
          case 125:
          case 126:
            return !0;
          default:
            return !1;
        }
      }),
      (e.isPunctChar = function (e) {
        return _.test(e);
      }),
      (e.escapeRE = function (e) {
        return e.replace(g, '\\$&');
      }),
      (e.normalizeReference = function (e) {
        return (
          (e = e.trim().replace(/\s+/g, ' ')),
          'Ṿ' === 'ẞ'.toLowerCase() && (e = e.replace(/ẞ/g, 'ß')),
          e.toLowerCase().toUpperCase()
        );
      });
  })(r);
var q = {},
  S = r.unescapeAll,
  F = r.unescapeAll;
(q.parseLinkLabel = function (e, r, t) {
  var n,
    s,
    o,
    i,
    a = -1,
    c = e.posMax,
    l = e.pos;
  for (e.pos = r + 1, n = 1; e.pos < c; ) {
    if (93 === (o = e.src.charCodeAt(e.pos)) && 0 === --n) {
      s = !0;
      break;
    }
    if (((i = e.pos), e.md.inline.skipToken(e), 91 === o))
      if (i === e.pos - 1) n++;
      else if (t) return (e.pos = l), -1;
  }
  return s && (a = e.pos), (e.pos = l), a;
}),
  (q.parseLinkDestination = function (e, r, t) {
    var n,
      s,
      o = r,
      i = { ok: !1, pos: 0, lines: 0, str: '' };
    if (60 === e.charCodeAt(r)) {
      for (r++; r < t; ) {
        if (10 === (n = e.charCodeAt(r))) return i;
        if (60 === n) return i;
        if (62 === n) return (i.pos = r + 1), (i.str = S(e.slice(o + 1, r))), (i.ok = !0), i;
        92 === n && r + 1 < t ? (r += 2) : r++;
      }
      return i;
    }
    for (s = 0; r < t && 32 !== (n = e.charCodeAt(r)) && !(n < 32 || 127 === n); )
      if (92 === n && r + 1 < t) {
        if (32 === e.charCodeAt(r + 1)) break;
        r += 2;
      } else {
        if (40 === n && ++s > 32) return i;
        if (41 === n) {
          if (0 === s) break;
          s--;
        }
        r++;
      }
    return (
      o === r || 0 !== s || ((i.str = S(e.slice(o, r))), (i.lines = 0), (i.pos = r), (i.ok = !0)), i
    );
  }),
  (q.parseLinkTitle = function (e, r, t) {
    var n,
      s,
      o = 0,
      i = r,
      a = { ok: !1, pos: 0, lines: 0, str: '' };
    if (r >= t) return a;
    if (34 !== (s = e.charCodeAt(r)) && 39 !== s && 40 !== s) return a;
    for (r++, 40 === s && (s = 41); r < t; ) {
      if ((n = e.charCodeAt(r)) === s)
        return (a.pos = r + 1), (a.lines = o), (a.str = F(e.slice(i + 1, r))), (a.ok = !0), a;
      if (40 === n && 41 === s) return a;
      10 === n ? o++ : 92 === n && r + 1 < t && (r++, 10 === e.charCodeAt(r) && o++), r++;
    }
    return a;
  });
var L = r.assign,
  z = r.unescapeAll,
  T = r.escapeHtml,
  I = {};
function M() {
  this.rules = L({}, I);
}
(I.code_inline = function (e, r, t, n, s) {
  var o = e[r];
  return '<code' + s.renderAttrs(o) + '>' + T(e[r].content) + '</code>';
}),
  (I.code_block = function (e, r, t, n, s) {
    var o = e[r];
    return '<pre' + s.renderAttrs(o) + '><code>' + T(e[r].content) + '</code></pre>\n';
  }),
  (I.fence = function (e, r, t, n, s) {
    var o,
      i,
      a,
      c,
      l,
      u = e[r],
      p = u.info ? z(u.info).trim() : '',
      h = '',
      f = '';
    return (
      p && ((h = (a = p.split(/(\s+)/g))[0]), (f = a.slice(2).join(''))),
      0 === (o = (t.highlight && t.highlight(u.content, h, f)) || T(u.content)).indexOf('<pre')
        ? o + '\n'
        : p
          ? ((i = u.attrIndex('class')),
            (c = u.attrs ? u.attrs.slice() : []),
            i < 0
              ? c.push(['class', t.langPrefix + h])
              : ((c[i] = c[i].slice()), (c[i][1] += ' ' + t.langPrefix + h)),
            (l = { attrs: c }),
            '<pre><code' + s.renderAttrs(l) + '>' + o + '</code></pre>\n')
          : '<pre><code' + s.renderAttrs(u) + '>' + o + '</code></pre>\n'
    );
  }),
  (I.image = function (e, r, t, n, s) {
    var o = e[r];
    return (
      (o.attrs[o.attrIndex('alt')][1] = s.renderInlineAsText(o.children, t, n)),
      s.renderToken(e, r, t)
    );
  }),
  (I.hardbreak = function (e, r, t) {
    return t.xhtmlOut ? '<br />\n' : '<br>\n';
  }),
  (I.softbreak = function (e, r, t) {
    return t.breaks ? (t.xhtmlOut ? '<br />\n' : '<br>\n') : '\n';
  }),
  (I.text = function (e, r) {
    return T(e[r].content);
  }),
  (I.html_block = function (e, r) {
    return e[r].content;
  }),
  (I.html_inline = function (e, r) {
    return e[r].content;
  }),
  (M.prototype.renderAttrs = function (e) {
    var r, t, n;
    if (!e.attrs) return '';
    for (n = '', r = 0, t = e.attrs.length; r < t; r++)
      n += ' ' + T(e.attrs[r][0]) + '="' + T(e.attrs[r][1]) + '"';
    return n;
  }),
  (M.prototype.renderToken = function (e, r, t) {
    var n,
      s = '',
      o = !1,
      i = e[r];
    return i.hidden
      ? ''
      : (i.block && -1 !== i.nesting && r && e[r - 1].hidden && (s += '\n'),
        (s += (-1 === i.nesting ? '</' : '<') + i.tag),
        (s += this.renderAttrs(i)),
        0 === i.nesting && t.xhtmlOut && (s += ' /'),
        i.block &&
          ((o = !0),
          1 === i.nesting &&
            r + 1 < e.length &&
            ('inline' === (n = e[r + 1]).type ||
              n.hidden ||
              (-1 === n.nesting && n.tag === i.tag)) &&
            (o = !1)),
        (s += o ? '>\n' : '>'));
  }),
  (M.prototype.renderInline = function (e, r, t) {
    for (var n, s = '', o = this.rules, i = 0, a = e.length; i < a; i++)
      void 0 !== o[(n = e[i].type)]
        ? (s += o[n](e, i, r, t, this))
        : (s += this.renderToken(e, i, r));
    return s;
  }),
  (M.prototype.renderInlineAsText = function (e, r, t) {
    for (var n = '', s = 0, o = e.length; s < o; s++)
      'text' === e[s].type
        ? (n += e[s].content)
        : 'image' === e[s].type
          ? (n += this.renderInlineAsText(e[s].children, r, t))
          : 'softbreak' === e[s].type && (n += '\n');
    return n;
  }),
  (M.prototype.render = function (e, r, t) {
    var n,
      s,
      o,
      i = '',
      a = this.rules;
    for (n = 0, s = e.length; n < s; n++)
      'inline' === (o = e[n].type)
        ? (i += this.renderInline(e[n].children, r, t))
        : void 0 !== a[o]
          ? (i += a[e[n].type](e, n, r, t, this))
          : (i += this.renderToken(e, n, r, t));
    return i;
  });
var R = M;
function B() {
  (this.__rules__ = []), (this.__cache__ = null);
}
(B.prototype.__find__ = function (e) {
  for (var r = 0; r < this.__rules__.length; r++) if (this.__rules__[r].name === e) return r;
  return -1;
}),
  (B.prototype.__compile__ = function () {
    var e = this,
      r = [''];
    e.__rules__.forEach(function (e) {
      e.enabled &&
        e.alt.forEach(function (e) {
          r.indexOf(e) < 0 && r.push(e);
        });
    }),
      (e.__cache__ = {}),
      r.forEach(function (r) {
        (e.__cache__[r] = []),
          e.__rules__.forEach(function (t) {
            t.enabled && ((r && t.alt.indexOf(r) < 0) || e.__cache__[r].push(t.fn));
          });
      });
  }),
  (B.prototype.at = function (e, r, t) {
    var n = this.__find__(e),
      s = t || {};
    if (-1 === n) throw new Error('Parser rule not found: ' + e);
    (this.__rules__[n].fn = r), (this.__rules__[n].alt = s.alt || []), (this.__cache__ = null);
  }),
  (B.prototype.before = function (e, r, t, n) {
    var s = this.__find__(e),
      o = n || {};
    if (-1 === s) throw new Error('Parser rule not found: ' + e);
    this.__rules__.splice(s, 0, { name: r, enabled: !0, fn: t, alt: o.alt || [] }),
      (this.__cache__ = null);
  }),
  (B.prototype.after = function (e, r, t, n) {
    var s = this.__find__(e),
      o = n || {};
    if (-1 === s) throw new Error('Parser rule not found: ' + e);
    this.__rules__.splice(s + 1, 0, { name: r, enabled: !0, fn: t, alt: o.alt || [] }),
      (this.__cache__ = null);
  }),
  (B.prototype.push = function (e, r, t) {
    var n = t || {};
    this.__rules__.push({ name: e, enabled: !0, fn: r, alt: n.alt || [] }), (this.__cache__ = null);
  }),
  (B.prototype.enable = function (e, r) {
    Array.isArray(e) || (e = [e]);
    var t = [];
    return (
      e.forEach(function (e) {
        var n = this.__find__(e);
        if (n < 0) {
          if (r) return;
          throw new Error('Rules manager: invalid rule name ' + e);
        }
        (this.__rules__[n].enabled = !0), t.push(e);
      }, this),
      (this.__cache__ = null),
      t
    );
  }),
  (B.prototype.enableOnly = function (e, r) {
    Array.isArray(e) || (e = [e]),
      this.__rules__.forEach(function (e) {
        e.enabled = !1;
      }),
      this.enable(e, r);
  }),
  (B.prototype.disable = function (e, r) {
    Array.isArray(e) || (e = [e]);
    var t = [];
    return (
      e.forEach(function (e) {
        var n = this.__find__(e);
        if (n < 0) {
          if (r) return;
          throw new Error('Rules manager: invalid rule name ' + e);
        }
        (this.__rules__[n].enabled = !1), t.push(e);
      }, this),
      (this.__cache__ = null),
      t
    );
  }),
  (B.prototype.getRules = function (e) {
    return null === this.__cache__ && this.__compile__(), this.__cache__[e] || [];
  });
var N = B,
  O = /\r\n?|\n/g,
  P = /\0/g,
  j = r.arrayReplaceAt;
function U(e) {
  return /^<\/a\s*>/i.test(e);
}
var V = /\+-|\.\.|\?\?\?\?|!!!!|,,|--/,
  Z = /\((c|tm|r)\)/i,
  $ = /\((c|tm|r)\)/gi,
  G = { c: '©', r: '®', tm: '™' };
function H(e, r) {
  return G[r.toLowerCase()];
}
function J(e) {
  var r,
    t,
    n = 0;
  for (r = e.length - 1; r >= 0; r--)
    'text' !== (t = e[r]).type || n || (t.content = t.content.replace($, H)),
      'link_open' === t.type && 'auto' === t.info && n--,
      'link_close' === t.type && 'auto' === t.info && n++;
}
function W(e) {
  var r,
    t,
    n = 0;
  for (r = e.length - 1; r >= 0; r--)
    'text' !== (t = e[r]).type ||
      n ||
      (V.test(t.content) &&
        (t.content = t.content
          .replace(/\+-/g, '±')
          .replace(/\.{2,}/g, '…')
          .replace(/([?!])…/g, '$1..')
          .replace(/([?!]){4,}/g, '$1$1$1')
          .replace(/,{2,}/g, ',')
          .replace(/(^|[^-])---(?=[^-]|$)/gm, '$1—')
          .replace(/(^|\s)--(?=\s|$)/gm, '$1–')
          .replace(/(^|[^-\s])--(?=[^-\s]|$)/gm, '$1–'))),
      'link_open' === t.type && 'auto' === t.info && n--,
      'link_close' === t.type && 'auto' === t.info && n++;
}
var Y = r.isWhiteSpace,
  K = r.isPunctChar,
  Q = r.isMdAsciiPunct,
  X = /['"]/,
  ee = /['"]/g;
function re(e, r, t) {
  return e.slice(0, r) + t + e.slice(r + 1);
}
function te(e, r) {
  var t, n, s, o, i, a, c, l, u, p, h, f, d, m, g, _, k, b, v, C, y;
  for (v = [], t = 0; t < e.length; t++) {
    for (n = e[t], c = e[t].level, k = v.length - 1; k >= 0 && !(v[k].level <= c); k--);
    if (((v.length = k + 1), 'text' === n.type)) {
      (i = 0), (a = (s = n.content).length);
      e: for (; i < a && ((ee.lastIndex = i), (o = ee.exec(s))); ) {
        if (((g = _ = !0), (i = o.index + 1), (b = "'" === o[0]), (u = 32), o.index - 1 >= 0))
          u = s.charCodeAt(o.index - 1);
        else
          for (k = t - 1; k >= 0 && 'softbreak' !== e[k].type && 'hardbreak' !== e[k].type; k--)
            if (e[k].content) {
              u = e[k].content.charCodeAt(e[k].content.length - 1);
              break;
            }
        if (((p = 32), i < a)) p = s.charCodeAt(i);
        else
          for (
            k = t + 1;
            k < e.length && 'softbreak' !== e[k].type && 'hardbreak' !== e[k].type;
            k++
          )
            if (e[k].content) {
              p = e[k].content.charCodeAt(0);
              break;
            }
        if (
          ((h = Q(u) || K(String.fromCharCode(u))),
          (f = Q(p) || K(String.fromCharCode(p))),
          (d = Y(u)),
          (m = Y(p)) ? (g = !1) : f && (d || h || (g = !1)),
          d ? (_ = !1) : h && (m || f || (_ = !1)),
          34 === p && '"' === o[0] && u >= 48 && u <= 57 && (_ = g = !1),
          g && _ && ((g = h), (_ = f)),
          g || _)
        ) {
          if (_)
            for (k = v.length - 1; k >= 0 && ((l = v[k]), !(v[k].level < c)); k--)
              if (l.single === b && v[k].level === c) {
                (l = v[k]),
                  b
                    ? ((C = r.md.options.quotes[2]), (y = r.md.options.quotes[3]))
                    : ((C = r.md.options.quotes[0]), (y = r.md.options.quotes[1])),
                  (n.content = re(n.content, o.index, y)),
                  (e[l.token].content = re(e[l.token].content, l.pos, C)),
                  (i += y.length - 1),
                  l.token === t && (i += C.length - 1),
                  (a = (s = n.content).length),
                  (v.length = k);
                continue e;
              }
          g
            ? v.push({ token: t, pos: o.index, single: b, level: c })
            : _ && b && (n.content = re(n.content, o.index, '’'));
        } else b && (n.content = re(n.content, o.index, '’'));
      }
    }
  }
}
function ne(e, r, t) {
  (this.type = e),
    (this.tag = r),
    (this.attrs = null),
    (this.map = null),
    (this.nesting = t),
    (this.level = 0),
    (this.children = null),
    (this.content = ''),
    (this.markup = ''),
    (this.info = ''),
    (this.meta = null),
    (this.block = !1),
    (this.hidden = !1);
}
(ne.prototype.attrIndex = function (e) {
  var r, t, n;
  if (!this.attrs) return -1;
  for (t = 0, n = (r = this.attrs).length; t < n; t++) if (r[t][0] === e) return t;
  return -1;
}),
  (ne.prototype.attrPush = function (e) {
    this.attrs ? this.attrs.push(e) : (this.attrs = [e]);
  }),
  (ne.prototype.attrSet = function (e, r) {
    var t = this.attrIndex(e),
      n = [e, r];
    t < 0 ? this.attrPush(n) : (this.attrs[t] = n);
  }),
  (ne.prototype.attrGet = function (e) {
    var r = this.attrIndex(e),
      t = null;
    return r >= 0 && (t = this.attrs[r][1]), t;
  }),
  (ne.prototype.attrJoin = function (e, r) {
    var t = this.attrIndex(e);
    t < 0 ? this.attrPush([e, r]) : (this.attrs[t][1] = this.attrs[t][1] + ' ' + r);
  });
var se = ne,
  oe = se;
function ie(e, r, t) {
  (this.src = e), (this.env = t), (this.tokens = []), (this.inlineMode = !1), (this.md = r);
}
ie.prototype.Token = oe;
var ae = ie,
  ce = N,
  le = [
    [
      'normalize',
      function (e) {
        var r;
        (r = (r = e.src.replace(O, '\n')).replace(P, '�')), (e.src = r);
      },
    ],
    [
      'block',
      function (e) {
        var r;
        e.inlineMode
          ? (((r = new e.Token('inline', '', 0)).content = e.src),
            (r.map = [0, 1]),
            (r.children = []),
            e.tokens.push(r))
          : e.md.block.parse(e.src, e.md, e.env, e.tokens);
      },
    ],
    [
      'inline',
      function (e) {
        var r,
          t,
          n,
          s = e.tokens;
        for (t = 0, n = s.length; t < n; t++)
          'inline' === (r = s[t]).type && e.md.inline.parse(r.content, e.md, e.env, r.children);
      },
    ],
    [
      'linkify',
      function (e) {
        var r,
          t,
          n,
          s,
          o,
          i,
          a,
          c,
          l,
          u,
          p,
          h,
          f,
          d,
          m,
          g,
          _,
          k,
          b = e.tokens;
        if (e.md.options.linkify)
          for (t = 0, n = b.length; t < n; t++)
            if ('inline' === b[t].type && e.md.linkify.pretest(b[t].content))
              for (f = 0, r = (s = b[t].children).length - 1; r >= 0; r--)
                if ('link_close' !== (i = s[r]).type) {
                  if (
                    ('html_inline' === i.type &&
                      ((k = i.content), /^<a[>\s]/i.test(k) && f > 0 && f--, U(i.content) && f++),
                    !(f > 0) && 'text' === i.type && e.md.linkify.test(i.content))
                  ) {
                    for (
                      l = i.content,
                        _ = e.md.linkify.match(l),
                        a = [],
                        h = i.level,
                        p = 0,
                        _.length > 0 &&
                          0 === _[0].index &&
                          r > 0 &&
                          'text_special' === s[r - 1].type &&
                          (_ = _.slice(1)),
                        c = 0;
                      c < _.length;
                      c++
                    )
                      (d = _[c].url),
                        (m = e.md.normalizeLink(d)),
                        e.md.validateLink(m) &&
                          ((g = _[c].text),
                          (g = _[c].schema
                            ? 'mailto:' !== _[c].schema || /^mailto:/i.test(g)
                              ? e.md.normalizeLinkText(g)
                              : e.md.normalizeLinkText('mailto:' + g).replace(/^mailto:/, '')
                            : e.md.normalizeLinkText('http://' + g).replace(/^http:\/\//, '')),
                          (u = _[c].index) > p &&
                            (((o = new e.Token('text', '', 0)).content = l.slice(p, u)),
                            (o.level = h),
                            a.push(o)),
                          ((o = new e.Token('link_open', 'a', 1)).attrs = [['href', m]]),
                          (o.level = h++),
                          (o.markup = 'linkify'),
                          (o.info = 'auto'),
                          a.push(o),
                          ((o = new e.Token('text', '', 0)).content = g),
                          (o.level = h),
                          a.push(o),
                          ((o = new e.Token('link_close', 'a', -1)).level = --h),
                          (o.markup = 'linkify'),
                          (o.info = 'auto'),
                          a.push(o),
                          (p = _[c].lastIndex));
                    p < l.length &&
                      (((o = new e.Token('text', '', 0)).content = l.slice(p)),
                      (o.level = h),
                      a.push(o)),
                      (b[t].children = s = j(s, r, a));
                  }
                } else for (r--; s[r].level !== i.level && 'link_open' !== s[r].type; ) r--;
      },
    ],
    [
      'replacements',
      function (e) {
        var r;
        if (e.md.options.typographer)
          for (r = e.tokens.length - 1; r >= 0; r--)
            'inline' === e.tokens[r].type &&
              (Z.test(e.tokens[r].content) && J(e.tokens[r].children),
              V.test(e.tokens[r].content) && W(e.tokens[r].children));
      },
    ],
    [
      'smartquotes',
      function (e) {
        var r;
        if (e.md.options.typographer)
          for (r = e.tokens.length - 1; r >= 0; r--)
            'inline' === e.tokens[r].type &&
              X.test(e.tokens[r].content) &&
              te(e.tokens[r].children, e);
      },
    ],
    [
      'text_join',
      function (e) {
        var r,
          t,
          n,
          s,
          o,
          i,
          a = e.tokens;
        for (r = 0, t = a.length; r < t; r++)
          if ('inline' === a[r].type) {
            for (o = (n = a[r].children).length, s = 0; s < o; s++)
              'text_special' === n[s].type && (n[s].type = 'text');
            for (s = i = 0; s < o; s++)
              'text' === n[s].type && s + 1 < o && 'text' === n[s + 1].type
                ? (n[s + 1].content = n[s].content + n[s + 1].content)
                : (s !== i && (n[i] = n[s]), i++);
            s !== i && (n.length = i);
          }
      },
    ],
  ];
function ue() {
  this.ruler = new ce();
  for (var e = 0; e < le.length; e++) this.ruler.push(le[e][0], le[e][1]);
}
(ue.prototype.process = function (e) {
  var r, t, n;
  for (r = 0, t = (n = this.ruler.getRules('')).length; r < t; r++) n[r](e);
}),
  (ue.prototype.State = ae);
var pe = ue,
  he = r.isSpace;
function fe(e, r) {
  var t = e.bMarks[r] + e.tShift[r],
    n = e.eMarks[r];
  return e.src.slice(t, n);
}
function de(e) {
  var r,
    t = [],
    n = 0,
    s = e.length,
    o = !1,
    i = 0,
    a = '';
  for (r = e.charCodeAt(n); n < s; )
    124 === r &&
      (o
        ? ((a += e.substring(i, n - 1)), (i = n))
        : (t.push(a + e.substring(i, n)), (a = ''), (i = n + 1))),
      (o = 92 === r),
      n++,
      (r = e.charCodeAt(n));
  return t.push(a + e.substring(i)), t;
}
var me = r.isSpace,
  ge = r.isSpace,
  _e = r.isSpace;
function ke(e, r) {
  var t, n, s, o;
  return (
    (n = e.bMarks[r] + e.tShift[r]),
    (s = e.eMarks[r]),
    (42 !== (t = e.src.charCodeAt(n++)) && 45 !== t && 43 !== t) ||
    (n < s && ((o = e.src.charCodeAt(n)), !_e(o)))
      ? -1
      : n
  );
}
function be(e, r) {
  var t,
    n = e.bMarks[r] + e.tShift[r],
    s = n,
    o = e.eMarks[r];
  if (s + 1 >= o) return -1;
  if ((t = e.src.charCodeAt(s++)) < 48 || t > 57) return -1;
  for (;;) {
    if (s >= o) return -1;
    if (!((t = e.src.charCodeAt(s++)) >= 48 && t <= 57)) {
      if (41 === t || 46 === t) break;
      return -1;
    }
    if (s - n >= 10) return -1;
  }
  return s < o && ((t = e.src.charCodeAt(s)), !_e(t)) ? -1 : s;
}
var ve = r.normalizeReference,
  Ce = r.isSpace,
  ye = {},
  Ae =
    '<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^"\'=<>`\\x00-\\x20]+|\'[^\']*\'|"[^"]*"))?)*\\s*\\/?>',
  xe = '<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>',
  De = new RegExp(
    '^(?:' +
      Ae +
      '|' +
      xe +
      '|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)'
  ),
  we = new RegExp('^(?:' + Ae + '|' + xe + ')');
(ye.HTML_TAG_RE = De), (ye.HTML_OPEN_CLOSE_TAG_RE = we);
var Ee = [
    'address',
    'article',
    'aside',
    'base',
    'basefont',
    'blockquote',
    'body',
    'caption',
    'center',
    'col',
    'colgroup',
    'dd',
    'details',
    'dialog',
    'dir',
    'div',
    'dl',
    'dt',
    'fieldset',
    'figcaption',
    'figure',
    'footer',
    'form',
    'frame',
    'frameset',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'head',
    'header',
    'hr',
    'html',
    'iframe',
    'legend',
    'li',
    'link',
    'main',
    'menu',
    'menuitem',
    'nav',
    'noframes',
    'ol',
    'optgroup',
    'option',
    'p',
    'param',
    'section',
    'source',
    'summary',
    'table',
    'tbody',
    'td',
    'tfoot',
    'th',
    'thead',
    'title',
    'tr',
    'track',
    'ul',
  ],
  qe = ye.HTML_OPEN_CLOSE_TAG_RE,
  Se = [
    [/^<(script|pre|style|textarea)(?=(\s|>|$))/i, /<\/(script|pre|style|textarea)>/i, !0],
    [/^<!--/, /-->/, !0],
    [/^<\?/, /\?>/, !0],
    [/^<![A-Z]/, />/, !0],
    [/^<!\[CDATA\[/, /\]\]>/, !0],
    [new RegExp('^</?(' + Ee.join('|') + ')(?=(\\s|/?>|$))', 'i'), /^$/, !0],
    [new RegExp(qe.source + '\\s*$'), /^$/, !1],
  ],
  Fe = r.isSpace,
  Le = se,
  ze = r.isSpace;
function Te(e, r, t, n) {
  var s, o, i, a, c, l, u, p;
  for (
    this.src = e,
      this.md = r,
      this.env = t,
      this.tokens = n,
      this.bMarks = [],
      this.eMarks = [],
      this.tShift = [],
      this.sCount = [],
      this.bsCount = [],
      this.blkIndent = 0,
      this.line = 0,
      this.lineMax = 0,
      this.tight = !1,
      this.ddIndent = -1,
      this.listIndent = -1,
      this.parentType = 'root',
      this.level = 0,
      this.result = '',
      p = !1,
      i = a = l = u = 0,
      c = (o = this.src).length;
    a < c;
    a++
  ) {
    if (((s = o.charCodeAt(a)), !p)) {
      if (ze(s)) {
        l++, 9 === s ? (u += 4 - (u % 4)) : u++;
        continue;
      }
      p = !0;
    }
    (10 !== s && a !== c - 1) ||
      (10 !== s && a++,
      this.bMarks.push(i),
      this.eMarks.push(a),
      this.tShift.push(l),
      this.sCount.push(u),
      this.bsCount.push(0),
      (p = !1),
      (l = 0),
      (u = 0),
      (i = a + 1));
  }
  this.bMarks.push(o.length),
    this.eMarks.push(o.length),
    this.tShift.push(0),
    this.sCount.push(0),
    this.bsCount.push(0),
    (this.lineMax = this.bMarks.length - 1);
}
(Te.prototype.push = function (e, r, t) {
  var n = new Le(e, r, t);
  return (
    (n.block = !0),
    t < 0 && this.level--,
    (n.level = this.level),
    t > 0 && this.level++,
    this.tokens.push(n),
    n
  );
}),
  (Te.prototype.isEmpty = function (e) {
    return this.bMarks[e] + this.tShift[e] >= this.eMarks[e];
  }),
  (Te.prototype.skipEmptyLines = function (e) {
    for (var r = this.lineMax; e < r && !(this.bMarks[e] + this.tShift[e] < this.eMarks[e]); e++);
    return e;
  }),
  (Te.prototype.skipSpaces = function (e) {
    for (var r, t = this.src.length; e < t && ((r = this.src.charCodeAt(e)), ze(r)); e++);
    return e;
  }),
  (Te.prototype.skipSpacesBack = function (e, r) {
    if (e <= r) return e;
    for (; e > r; ) if (!ze(this.src.charCodeAt(--e))) return e + 1;
    return e;
  }),
  (Te.prototype.skipChars = function (e, r) {
    for (var t = this.src.length; e < t && this.src.charCodeAt(e) === r; e++);
    return e;
  }),
  (Te.prototype.skipCharsBack = function (e, r, t) {
    if (e <= t) return e;
    for (; e > t; ) if (r !== this.src.charCodeAt(--e)) return e + 1;
    return e;
  }),
  (Te.prototype.getLines = function (e, r, t, n) {
    var s,
      o,
      i,
      a,
      c,
      l,
      u,
      p = e;
    if (e >= r) return '';
    for (l = new Array(r - e), s = 0; p < r; p++, s++) {
      for (
        o = 0, u = a = this.bMarks[p], c = p + 1 < r || n ? this.eMarks[p] + 1 : this.eMarks[p];
        a < c && o < t;

      ) {
        if (((i = this.src.charCodeAt(a)), ze(i)))
          9 === i ? (o += 4 - ((o + this.bsCount[p]) % 4)) : o++;
        else {
          if (!(a - u < this.tShift[p])) break;
          o++;
        }
        a++;
      }
      l[s] = o > t ? new Array(o - t + 1).join(' ') + this.src.slice(a, c) : this.src.slice(a, c);
    }
    return l.join('');
  }),
  (Te.prototype.Token = Le);
var Ie = Te,
  Me = N,
  Re = [
    [
      'table',
      function (e, r, t, n) {
        var s, o, i, a, c, l, u, p, h, f, d, m, g, _, k, b, v, C;
        if (r + 2 > t) return !1;
        if (((l = r + 1), e.sCount[l] < e.blkIndent)) return !1;
        if (e.sCount[l] - e.blkIndent >= 4) return !1;
        if ((i = e.bMarks[l] + e.tShift[l]) >= e.eMarks[l]) return !1;
        if (124 !== (v = e.src.charCodeAt(i++)) && 45 !== v && 58 !== v) return !1;
        if (i >= e.eMarks[l]) return !1;
        if (124 !== (C = e.src.charCodeAt(i++)) && 45 !== C && 58 !== C && !he(C)) return !1;
        if (45 === v && he(C)) return !1;
        for (; i < e.eMarks[l]; ) {
          if (124 !== (s = e.src.charCodeAt(i)) && 45 !== s && 58 !== s && !he(s)) return !1;
          i++;
        }
        for (u = (o = fe(e, r + 1)).split('|'), f = [], a = 0; a < u.length; a++) {
          if (!(d = u[a].trim())) {
            if (0 === a || a === u.length - 1) continue;
            return !1;
          }
          if (!/^:?-+:?$/.test(d)) return !1;
          58 === d.charCodeAt(d.length - 1)
            ? f.push(58 === d.charCodeAt(0) ? 'center' : 'right')
            : 58 === d.charCodeAt(0)
              ? f.push('left')
              : f.push('');
        }
        if (-1 === (o = fe(e, r).trim()).indexOf('|')) return !1;
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        if (
          ((u = de(o)).length && '' === u[0] && u.shift(),
          u.length && '' === u[u.length - 1] && u.pop(),
          0 === (p = u.length) || p !== f.length)
        )
          return !1;
        if (n) return !0;
        for (
          _ = e.parentType,
            e.parentType = 'table',
            b = e.md.block.ruler.getRules('blockquote'),
            (h = e.push('table_open', 'table', 1)).map = m = [r, 0],
            (h = e.push('thead_open', 'thead', 1)).map = [r, r + 1],
            (h = e.push('tr_open', 'tr', 1)).map = [r, r + 1],
            a = 0;
          a < u.length;
          a++
        )
          (h = e.push('th_open', 'th', 1)),
            f[a] && (h.attrs = [['style', 'text-align:' + f[a]]]),
            ((h = e.push('inline', '', 0)).content = u[a].trim()),
            (h.children = []),
            (h = e.push('th_close', 'th', -1));
        for (
          h = e.push('tr_close', 'tr', -1), h = e.push('thead_close', 'thead', -1), l = r + 2;
          l < t && !(e.sCount[l] < e.blkIndent);
          l++
        ) {
          for (k = !1, a = 0, c = b.length; a < c; a++)
            if (b[a](e, l, t, !0)) {
              k = !0;
              break;
            }
          if (k) break;
          if (!(o = fe(e, l).trim())) break;
          if (e.sCount[l] - e.blkIndent >= 4) break;
          for (
            (u = de(o)).length && '' === u[0] && u.shift(),
              u.length && '' === u[u.length - 1] && u.pop(),
              l === r + 2 && ((h = e.push('tbody_open', 'tbody', 1)).map = g = [r + 2, 0]),
              (h = e.push('tr_open', 'tr', 1)).map = [l, l + 1],
              a = 0;
            a < p;
            a++
          )
            (h = e.push('td_open', 'td', 1)),
              f[a] && (h.attrs = [['style', 'text-align:' + f[a]]]),
              ((h = e.push('inline', '', 0)).content = u[a] ? u[a].trim() : ''),
              (h.children = []),
              (h = e.push('td_close', 'td', -1));
          h = e.push('tr_close', 'tr', -1);
        }
        return (
          g && ((h = e.push('tbody_close', 'tbody', -1)), (g[1] = l)),
          (h = e.push('table_close', 'table', -1)),
          (m[1] = l),
          (e.parentType = _),
          (e.line = l),
          !0
        );
      },
      ['paragraph', 'reference'],
    ],
    [
      'code',
      function (e, r, t) {
        var n, s, o;
        if (e.sCount[r] - e.blkIndent < 4) return !1;
        for (s = n = r + 1; n < t; )
          if (e.isEmpty(n)) n++;
          else {
            if (!(e.sCount[n] - e.blkIndent >= 4)) break;
            s = ++n;
          }
        return (
          (e.line = s),
          ((o = e.push('code_block', 'code', 0)).content =
            e.getLines(r, s, 4 + e.blkIndent, !1) + '\n'),
          (o.map = [r, e.line]),
          !0
        );
      },
    ],
    [
      'fence',
      function (e, r, t, n) {
        var s,
          o,
          i,
          a,
          c,
          l,
          u,
          p = !1,
          h = e.bMarks[r] + e.tShift[r],
          f = e.eMarks[r];
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        if (h + 3 > f) return !1;
        if (126 !== (s = e.src.charCodeAt(h)) && 96 !== s) return !1;
        if (((c = h), (o = (h = e.skipChars(h, s)) - c) < 3)) return !1;
        if (
          ((u = e.src.slice(c, h)),
          (i = e.src.slice(h, f)),
          96 === s && i.indexOf(String.fromCharCode(s)) >= 0)
        )
          return !1;
        if (n) return !0;
        for (
          a = r;
          !(++a >= t) &&
          !((h = c = e.bMarks[a] + e.tShift[a]) < (f = e.eMarks[a]) && e.sCount[a] < e.blkIndent);

        )
          if (
            e.src.charCodeAt(h) === s &&
            !(
              e.sCount[a] - e.blkIndent >= 4 ||
              (h = e.skipChars(h, s)) - c < o ||
              (h = e.skipSpaces(h)) < f
            )
          ) {
            p = !0;
            break;
          }
        return (
          (o = e.sCount[r]),
          (e.line = a + (p ? 1 : 0)),
          ((l = e.push('fence', 'code', 0)).info = i),
          (l.content = e.getLines(r + 1, a, o, !0)),
          (l.markup = u),
          (l.map = [r, e.line]),
          !0
        );
      },
      ['paragraph', 'reference', 'blockquote', 'list'],
    ],
    [
      'blockquote',
      function (e, r, t, n) {
        var s,
          o,
          i,
          a,
          c,
          l,
          u,
          p,
          h,
          f,
          d,
          m,
          g,
          _,
          k,
          b,
          v,
          C,
          y,
          A,
          x = e.lineMax,
          D = e.bMarks[r] + e.tShift[r],
          w = e.eMarks[r];
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        if (62 !== e.src.charCodeAt(D++)) return !1;
        if (n) return !0;
        for (
          a = h = e.sCount[r] + 1,
            32 === e.src.charCodeAt(D)
              ? (D++, a++, h++, (s = !1), (b = !0))
              : 9 === e.src.charCodeAt(D)
                ? ((b = !0), (e.bsCount[r] + h) % 4 == 3 ? (D++, a++, h++, (s = !1)) : (s = !0))
                : (b = !1),
            f = [e.bMarks[r]],
            e.bMarks[r] = D;
          D < w && ((o = e.src.charCodeAt(D)), me(o));

        )
          9 === o ? (h += 4 - ((h + e.bsCount[r] + (s ? 1 : 0)) % 4)) : h++, D++;
        for (
          d = [e.bsCount[r]],
            e.bsCount[r] = e.sCount[r] + 1 + (b ? 1 : 0),
            l = D >= w,
            _ = [e.sCount[r]],
            e.sCount[r] = h - a,
            k = [e.tShift[r]],
            e.tShift[r] = D - e.bMarks[r],
            C = e.md.block.ruler.getRules('blockquote'),
            g = e.parentType,
            e.parentType = 'blockquote',
            p = r + 1;
          p < t &&
          ((A = e.sCount[p] < e.blkIndent),
          !((D = e.bMarks[p] + e.tShift[p]) >= (w = e.eMarks[p])));
          p++
        )
          if (62 !== e.src.charCodeAt(D++) || A) {
            if (l) break;
            for (v = !1, i = 0, c = C.length; i < c; i++)
              if (C[i](e, p, t, !0)) {
                v = !0;
                break;
              }
            if (v) {
              (e.lineMax = p),
                0 !== e.blkIndent &&
                  (f.push(e.bMarks[p]),
                  d.push(e.bsCount[p]),
                  k.push(e.tShift[p]),
                  _.push(e.sCount[p]),
                  (e.sCount[p] -= e.blkIndent));
              break;
            }
            f.push(e.bMarks[p]),
              d.push(e.bsCount[p]),
              k.push(e.tShift[p]),
              _.push(e.sCount[p]),
              (e.sCount[p] = -1);
          } else {
            for (
              a = h = e.sCount[p] + 1,
                32 === e.src.charCodeAt(D)
                  ? (D++, a++, h++, (s = !1), (b = !0))
                  : 9 === e.src.charCodeAt(D)
                    ? ((b = !0), (e.bsCount[p] + h) % 4 == 3 ? (D++, a++, h++, (s = !1)) : (s = !0))
                    : (b = !1),
                f.push(e.bMarks[p]),
                e.bMarks[p] = D;
              D < w && ((o = e.src.charCodeAt(D)), me(o));

            )
              9 === o ? (h += 4 - ((h + e.bsCount[p] + (s ? 1 : 0)) % 4)) : h++, D++;
            (l = D >= w),
              d.push(e.bsCount[p]),
              (e.bsCount[p] = e.sCount[p] + 1 + (b ? 1 : 0)),
              _.push(e.sCount[p]),
              (e.sCount[p] = h - a),
              k.push(e.tShift[p]),
              (e.tShift[p] = D - e.bMarks[p]);
          }
        for (
          m = e.blkIndent,
            e.blkIndent = 0,
            (y = e.push('blockquote_open', 'blockquote', 1)).markup = '>',
            y.map = u = [r, 0],
            e.md.block.tokenize(e, r, p),
            (y = e.push('blockquote_close', 'blockquote', -1)).markup = '>',
            e.lineMax = x,
            e.parentType = g,
            u[1] = e.line,
            i = 0;
          i < k.length;
          i++
        )
          (e.bMarks[i + r] = f[i]),
            (e.tShift[i + r] = k[i]),
            (e.sCount[i + r] = _[i]),
            (e.bsCount[i + r] = d[i]);
        return (e.blkIndent = m), !0;
      },
      ['paragraph', 'reference', 'blockquote', 'list'],
    ],
    [
      'hr',
      function (e, r, t, n) {
        var s,
          o,
          i,
          a,
          c = e.bMarks[r] + e.tShift[r],
          l = e.eMarks[r];
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        if (42 !== (s = e.src.charCodeAt(c++)) && 45 !== s && 95 !== s) return !1;
        for (o = 1; c < l; ) {
          if ((i = e.src.charCodeAt(c++)) !== s && !ge(i)) return !1;
          i === s && o++;
        }
        return (
          !(o < 3) &&
          (n ||
            ((e.line = r + 1),
            ((a = e.push('hr', 'hr', 0)).map = [r, e.line]),
            (a.markup = Array(o + 1).join(String.fromCharCode(s)))),
          !0)
        );
      },
      ['paragraph', 'reference', 'blockquote', 'list'],
    ],
    [
      'list',
      function (e, r, t, n) {
        var s,
          o,
          i,
          a,
          c,
          l,
          u,
          p,
          h,
          f,
          d,
          m,
          g,
          _,
          k,
          b,
          v,
          C,
          y,
          A,
          x,
          D,
          w,
          E,
          q,
          S,
          F,
          L,
          z = !1,
          T = !0;
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        if (e.listIndent >= 0 && e.sCount[r] - e.listIndent >= 4 && e.sCount[r] < e.blkIndent)
          return !1;
        if (
          (n && 'paragraph' === e.parentType && e.sCount[r] >= e.blkIndent && (z = !0),
          (w = be(e, r)) >= 0)
        ) {
          if (
            ((u = !0),
            (q = e.bMarks[r] + e.tShift[r]),
            (g = Number(e.src.slice(q, w - 1))),
            z && 1 !== g)
          )
            return !1;
        } else {
          if (!((w = ke(e, r)) >= 0)) return !1;
          u = !1;
        }
        if (z && e.skipSpaces(w) >= e.eMarks[r]) return !1;
        if (((m = e.src.charCodeAt(w - 1)), n)) return !0;
        for (
          d = e.tokens.length,
            u
              ? ((L = e.push('ordered_list_open', 'ol', 1)), 1 !== g && (L.attrs = [['start', g]]))
              : (L = e.push('bullet_list_open', 'ul', 1)),
            L.map = f = [r, 0],
            L.markup = String.fromCharCode(m),
            k = r,
            E = !1,
            F = e.md.block.ruler.getRules('list'),
            C = e.parentType,
            e.parentType = 'list';
          k < t;

        ) {
          for (
            D = w, _ = e.eMarks[k], l = b = e.sCount[k] + w - (e.bMarks[r] + e.tShift[r]);
            D < _;

          ) {
            if (9 === (s = e.src.charCodeAt(D))) b += 4 - ((b + e.bsCount[k]) % 4);
            else {
              if (32 !== s) break;
              b++;
            }
            D++;
          }
          if (
            ((c = (o = D) >= _ ? 1 : b - l) > 4 && (c = 1),
            (a = l + c),
            ((L = e.push('list_item_open', 'li', 1)).markup = String.fromCharCode(m)),
            (L.map = p = [r, 0]),
            u && (L.info = e.src.slice(q, w - 1)),
            (x = e.tight),
            (A = e.tShift[r]),
            (y = e.sCount[r]),
            (v = e.listIndent),
            (e.listIndent = e.blkIndent),
            (e.blkIndent = a),
            (e.tight = !0),
            (e.tShift[r] = o - e.bMarks[r]),
            (e.sCount[r] = b),
            o >= _ && e.isEmpty(r + 1)
              ? (e.line = Math.min(e.line + 2, t))
              : e.md.block.tokenize(e, r, t, !0),
            (e.tight && !E) || (T = !1),
            (E = e.line - r > 1 && e.isEmpty(e.line - 1)),
            (e.blkIndent = e.listIndent),
            (e.listIndent = v),
            (e.tShift[r] = A),
            (e.sCount[r] = y),
            (e.tight = x),
            ((L = e.push('list_item_close', 'li', -1)).markup = String.fromCharCode(m)),
            (k = r = e.line),
            (p[1] = k),
            (o = e.bMarks[r]),
            k >= t)
          )
            break;
          if (e.sCount[k] < e.blkIndent) break;
          if (e.sCount[r] - e.blkIndent >= 4) break;
          for (S = !1, i = 0, h = F.length; i < h; i++)
            if (F[i](e, k, t, !0)) {
              S = !0;
              break;
            }
          if (S) break;
          if (u) {
            if ((w = be(e, k)) < 0) break;
            q = e.bMarks[k] + e.tShift[k];
          } else if ((w = ke(e, k)) < 0) break;
          if (m !== e.src.charCodeAt(w - 1)) break;
        }
        return (
          ((L = u
            ? e.push('ordered_list_close', 'ol', -1)
            : e.push('bullet_list_close', 'ul', -1)).markup = String.fromCharCode(m)),
          (f[1] = k),
          (e.line = k),
          (e.parentType = C),
          T &&
            (function (e, r) {
              var t,
                n,
                s = e.level + 2;
              for (t = r + 2, n = e.tokens.length - 2; t < n; t++)
                e.tokens[t].level === s &&
                  'paragraph_open' === e.tokens[t].type &&
                  ((e.tokens[t + 2].hidden = !0), (e.tokens[t].hidden = !0), (t += 2));
            })(e, d),
          !0
        );
      },
      ['paragraph', 'reference', 'blockquote'],
    ],
    [
      'reference',
      function (e, r, t, n) {
        var s,
          o,
          i,
          a,
          c,
          l,
          u,
          p,
          h,
          f,
          d,
          m,
          g,
          _,
          k,
          b,
          v = 0,
          C = e.bMarks[r] + e.tShift[r],
          y = e.eMarks[r],
          A = r + 1;
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        if (91 !== e.src.charCodeAt(C)) return !1;
        for (; ++C < y; )
          if (93 === e.src.charCodeAt(C) && 92 !== e.src.charCodeAt(C - 1)) {
            if (C + 1 === y) return !1;
            if (58 !== e.src.charCodeAt(C + 1)) return !1;
            break;
          }
        for (
          a = e.lineMax,
            k = e.md.block.ruler.getRules('reference'),
            f = e.parentType,
            e.parentType = 'reference';
          A < a && !e.isEmpty(A);
          A++
        )
          if (!(e.sCount[A] - e.blkIndent > 3 || e.sCount[A] < 0)) {
            for (_ = !1, l = 0, u = k.length; l < u; l++)
              if (k[l](e, A, a, !0)) {
                _ = !0;
                break;
              }
            if (_) break;
          }
        for (y = (g = e.getLines(r, A, e.blkIndent, !1).trim()).length, C = 1; C < y; C++) {
          if (91 === (s = g.charCodeAt(C))) return !1;
          if (93 === s) {
            h = C;
            break;
          }
          (10 === s || (92 === s && ++C < y && 10 === g.charCodeAt(C))) && v++;
        }
        if (h < 0 || 58 !== g.charCodeAt(h + 1)) return !1;
        for (C = h + 2; C < y; C++)
          if (10 === (s = g.charCodeAt(C))) v++;
          else if (!Ce(s)) break;
        if (!(d = e.md.helpers.parseLinkDestination(g, C, y)).ok) return !1;
        if (((c = e.md.normalizeLink(d.str)), !e.md.validateLink(c))) return !1;
        for (o = C = d.pos, i = v += d.lines, m = C; C < y; C++)
          if (10 === (s = g.charCodeAt(C))) v++;
          else if (!Ce(s)) break;
        for (
          d = e.md.helpers.parseLinkTitle(g, C, y),
            C < y && m !== C && d.ok
              ? ((b = d.str), (C = d.pos), (v += d.lines))
              : ((b = ''), (C = o), (v = i));
          C < y && ((s = g.charCodeAt(C)), Ce(s));

        )
          C++;
        if (C < y && 10 !== g.charCodeAt(C) && b)
          for (b = '', C = o, v = i; C < y && ((s = g.charCodeAt(C)), Ce(s)); ) C++;
        return (
          !(C < y && 10 !== g.charCodeAt(C)) &&
          !!(p = ve(g.slice(1, h))) &&
          (n ||
            (void 0 === e.env.references && (e.env.references = {}),
            void 0 === e.env.references[p] && (e.env.references[p] = { title: b, href: c }),
            (e.parentType = f),
            (e.line = r + v + 1)),
          !0)
        );
      },
    ],
    [
      'html_block',
      function (e, r, t, n) {
        var s,
          o,
          i,
          a,
          c = e.bMarks[r] + e.tShift[r],
          l = e.eMarks[r];
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        if (!e.md.options.html) return !1;
        if (60 !== e.src.charCodeAt(c)) return !1;
        for (a = e.src.slice(c, l), s = 0; s < Se.length && !Se[s][0].test(a); s++);
        if (s === Se.length) return !1;
        if (n) return Se[s][2];
        if (((o = r + 1), !Se[s][1].test(a)))
          for (; o < t && !(e.sCount[o] < e.blkIndent); o++)
            if (
              ((c = e.bMarks[o] + e.tShift[o]),
              (l = e.eMarks[o]),
              (a = e.src.slice(c, l)),
              Se[s][1].test(a))
            ) {
              0 !== a.length && o++;
              break;
            }
        return (
          (e.line = o),
          ((i = e.push('html_block', '', 0)).map = [r, o]),
          (i.content = e.getLines(r, o, e.blkIndent, !0)),
          !0
        );
      },
      ['paragraph', 'reference', 'blockquote'],
    ],
    [
      'heading',
      function (e, r, t, n) {
        var s,
          o,
          i,
          a,
          c = e.bMarks[r] + e.tShift[r],
          l = e.eMarks[r];
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        if (35 !== (s = e.src.charCodeAt(c)) || c >= l) return !1;
        for (o = 1, s = e.src.charCodeAt(++c); 35 === s && c < l && o <= 6; )
          o++, (s = e.src.charCodeAt(++c));
        return (
          !(o > 6 || (c < l && !Fe(s))) &&
          (n ||
            ((l = e.skipSpacesBack(l, c)),
            (i = e.skipCharsBack(l, 35, c)) > c && Fe(e.src.charCodeAt(i - 1)) && (l = i),
            (e.line = r + 1),
            ((a = e.push('heading_open', 'h' + String(o), 1)).markup = '########'.slice(0, o)),
            (a.map = [r, e.line]),
            ((a = e.push('inline', '', 0)).content = e.src.slice(c, l).trim()),
            (a.map = [r, e.line]),
            (a.children = []),
            ((a = e.push('heading_close', 'h' + String(o), -1)).markup = '########'.slice(0, o))),
          !0)
        );
      },
      ['paragraph', 'reference', 'blockquote'],
    ],
    [
      'lheading',
      function (e, r, t) {
        var n,
          s,
          o,
          i,
          a,
          c,
          l,
          u,
          p,
          h,
          f = r + 1,
          d = e.md.block.ruler.getRules('paragraph');
        if (e.sCount[r] - e.blkIndent >= 4) return !1;
        for (h = e.parentType, e.parentType = 'paragraph'; f < t && !e.isEmpty(f); f++)
          if (!(e.sCount[f] - e.blkIndent > 3)) {
            if (
              e.sCount[f] >= e.blkIndent &&
              (c = e.bMarks[f] + e.tShift[f]) < (l = e.eMarks[f]) &&
              (45 === (p = e.src.charCodeAt(c)) || 61 === p) &&
              ((c = e.skipChars(c, p)), (c = e.skipSpaces(c)) >= l)
            ) {
              u = 61 === p ? 1 : 2;
              break;
            }
            if (!(e.sCount[f] < 0)) {
              for (s = !1, o = 0, i = d.length; o < i; o++)
                if (d[o](e, f, t, !0)) {
                  s = !0;
                  break;
                }
              if (s) break;
            }
          }
        return (
          !!u &&
          ((n = e.getLines(r, f, e.blkIndent, !1).trim()),
          (e.line = f + 1),
          ((a = e.push('heading_open', 'h' + String(u), 1)).markup = String.fromCharCode(p)),
          (a.map = [r, e.line]),
          ((a = e.push('inline', '', 0)).content = n),
          (a.map = [r, e.line - 1]),
          (a.children = []),
          ((a = e.push('heading_close', 'h' + String(u), -1)).markup = String.fromCharCode(p)),
          (e.parentType = h),
          !0)
        );
      },
    ],
    [
      'paragraph',
      function (e, r) {
        var t,
          n,
          s,
          o,
          i,
          a,
          c = r + 1,
          l = e.md.block.ruler.getRules('paragraph'),
          u = e.lineMax;
        for (a = e.parentType, e.parentType = 'paragraph'; c < u && !e.isEmpty(c); c++)
          if (!(e.sCount[c] - e.blkIndent > 3 || e.sCount[c] < 0)) {
            for (n = !1, s = 0, o = l.length; s < o; s++)
              if (l[s](e, c, u, !0)) {
                n = !0;
                break;
              }
            if (n) break;
          }
        return (
          (t = e.getLines(r, c, e.blkIndent, !1).trim()),
          (e.line = c),
          ((i = e.push('paragraph_open', 'p', 1)).map = [r, e.line]),
          ((i = e.push('inline', '', 0)).content = t),
          (i.map = [r, e.line]),
          (i.children = []),
          (i = e.push('paragraph_close', 'p', -1)),
          (e.parentType = a),
          !0
        );
      },
    ],
  ];
function Be() {
  this.ruler = new Me();
  for (var e = 0; e < Re.length; e++)
    this.ruler.push(Re[e][0], Re[e][1], { alt: (Re[e][2] || []).slice() });
}
(Be.prototype.tokenize = function (e, r, t) {
  for (
    var n, s = this.ruler.getRules(''), o = s.length, i = r, a = !1, c = e.md.options.maxNesting;
    i < t && ((e.line = i = e.skipEmptyLines(i)), !(i >= t)) && !(e.sCount[i] < e.blkIndent);

  ) {
    if (e.level >= c) {
      e.line = t;
      break;
    }
    for (n = 0; n < o && !s[n](e, i, t, !1); n++);
    (e.tight = !a),
      e.isEmpty(e.line - 1) && (a = !0),
      (i = e.line) < t && e.isEmpty(i) && ((a = !0), i++, (e.line = i));
  }
}),
  (Be.prototype.parse = function (e, r, t, n) {
    var s;
    e && ((s = new this.State(e, r, t, n)), this.tokenize(s, s.line, s.lineMax));
  }),
  (Be.prototype.State = Ie);
var Ne = Be;
function Oe(e) {
  switch (e) {
    case 10:
    case 33:
    case 35:
    case 36:
    case 37:
    case 38:
    case 42:
    case 43:
    case 45:
    case 58:
    case 60:
    case 61:
    case 62:
    case 64:
    case 91:
    case 92:
    case 93:
    case 94:
    case 95:
    case 96:
    case 123:
    case 125:
    case 126:
      return !0;
    default:
      return !1;
  }
}
for (
  var Pe = /(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i,
    je = r.isSpace,
    Ue = r.isSpace,
    Ve = [],
    Ze = 0;
  Ze < 256;
  Ze++
)
  Ve.push(0);
'\\!"#$%&\'()*+,./:;<=>?@[]^_`{|}~-'.split('').forEach(function (e) {
  Ve[e.charCodeAt(0)] = 1;
});
var $e = {};
function Ge(e, r) {
  var t,
    n,
    s,
    o,
    i,
    a = [],
    c = r.length;
  for (t = 0; t < c; t++)
    126 === (s = r[t]).marker &&
      -1 !== s.end &&
      ((o = r[s.end]),
      ((i = e.tokens[s.token]).type = 's_open'),
      (i.tag = 's'),
      (i.nesting = 1),
      (i.markup = '~~'),
      (i.content = ''),
      ((i = e.tokens[o.token]).type = 's_close'),
      (i.tag = 's'),
      (i.nesting = -1),
      (i.markup = '~~'),
      (i.content = ''),
      'text' === e.tokens[o.token - 1].type &&
        '~' === e.tokens[o.token - 1].content &&
        a.push(o.token - 1));
  for (; a.length; ) {
    for (n = (t = a.pop()) + 1; n < e.tokens.length && 's_close' === e.tokens[n].type; ) n++;
    t !== --n && ((i = e.tokens[n]), (e.tokens[n] = e.tokens[t]), (e.tokens[t] = i));
  }
}
($e.tokenize = function (e, r) {
  var t,
    n,
    s,
    o,
    i = e.pos,
    a = e.src.charCodeAt(i);
  if (r) return !1;
  if (126 !== a) return !1;
  if (((s = (n = e.scanDelims(e.pos, !0)).length), (o = String.fromCharCode(a)), s < 2)) return !1;
  for (s % 2 && ((e.push('text', '', 0).content = o), s--), t = 0; t < s; t += 2)
    (e.push('text', '', 0).content = o + o),
      e.delimiters.push({
        marker: a,
        length: 0,
        token: e.tokens.length - 1,
        end: -1,
        open: n.can_open,
        close: n.can_close,
      });
  return (e.pos += n.length), !0;
}),
  ($e.postProcess = function (e) {
    var r,
      t = e.tokens_meta,
      n = e.tokens_meta.length;
    for (Ge(e, e.delimiters), r = 0; r < n; r++) t[r] && t[r].delimiters && Ge(e, t[r].delimiters);
  });
var He = {};
function Je(e, r) {
  var t, n, s, o, i, a;
  for (t = r.length - 1; t >= 0; t--)
    (95 !== (n = r[t]).marker && 42 !== n.marker) ||
      (-1 !== n.end &&
        ((s = r[n.end]),
        (a =
          t > 0 &&
          r[t - 1].end === n.end + 1 &&
          r[t - 1].marker === n.marker &&
          r[t - 1].token === n.token - 1 &&
          r[n.end + 1].token === s.token + 1),
        (i = String.fromCharCode(n.marker)),
        ((o = e.tokens[n.token]).type = a ? 'strong_open' : 'em_open'),
        (o.tag = a ? 'strong' : 'em'),
        (o.nesting = 1),
        (o.markup = a ? i + i : i),
        (o.content = ''),
        ((o = e.tokens[s.token]).type = a ? 'strong_close' : 'em_close'),
        (o.tag = a ? 'strong' : 'em'),
        (o.nesting = -1),
        (o.markup = a ? i + i : i),
        (o.content = ''),
        a &&
          ((e.tokens[r[t - 1].token].content = ''),
          (e.tokens[r[n.end + 1].token].content = ''),
          t--)));
}
(He.tokenize = function (e, r) {
  var t,
    n,
    s = e.pos,
    o = e.src.charCodeAt(s);
  if (r) return !1;
  if (95 !== o && 42 !== o) return !1;
  for (n = e.scanDelims(e.pos, 42 === o), t = 0; t < n.length; t++)
    (e.push('text', '', 0).content = String.fromCharCode(o)),
      e.delimiters.push({
        marker: o,
        length: n.length,
        token: e.tokens.length - 1,
        end: -1,
        open: n.can_open,
        close: n.can_close,
      });
  return (e.pos += n.length), !0;
}),
  (He.postProcess = function (e) {
    var r,
      t = e.tokens_meta,
      n = e.tokens_meta.length;
    for (Je(e, e.delimiters), r = 0; r < n; r++) t[r] && t[r].delimiters && Je(e, t[r].delimiters);
  });
var We = r.normalizeReference,
  Ye = r.isSpace,
  Ke = r.normalizeReference,
  Qe = r.isSpace,
  Xe =
    /^([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,
  er = /^([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)$/,
  rr = ye.HTML_TAG_RE;
var tr = t,
  nr = r.has,
  sr = r.isValidEntityCode,
  or = r.fromCodePoint,
  ir = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,
  ar = /^&([a-z][a-z0-9]{1,31});/i;
function cr(e, r) {
  var t,
    n,
    s,
    o,
    i,
    a,
    c,
    l,
    u = {},
    p = r.length;
  if (p) {
    var h = 0,
      f = -2,
      d = [];
    for (t = 0; t < p; t++)
      if (
        ((s = r[t]),
        d.push(0),
        (r[h].marker === s.marker && f === s.token - 1) || (h = t),
        (f = s.token),
        (s.length = s.length || 0),
        s.close)
      ) {
        for (
          u.hasOwnProperty(s.marker) || (u[s.marker] = [-1, -1, -1, -1, -1, -1]),
            i = u[s.marker][(s.open ? 3 : 0) + (s.length % 3)],
            a = n = h - d[h] - 1;
          n > i;
          n -= d[n] + 1
        )
          if (
            (o = r[n]).marker === s.marker &&
            o.open &&
            o.end < 0 &&
            ((c = !1),
            (o.close || s.open) &&
              (o.length + s.length) % 3 == 0 &&
              ((o.length % 3 == 0 && s.length % 3 == 0) || (c = !0)),
            !c)
          ) {
            (l = n > 0 && !r[n - 1].open ? d[n - 1] + 1 : 0),
              (d[t] = t - n + l),
              (d[n] = l),
              (s.open = !1),
              (o.end = t),
              (o.close = !1),
              (a = -1),
              (f = -2);
            break;
          }
        -1 !== a && (u[s.marker][(s.open ? 3 : 0) + ((s.length || 0) % 3)] = a);
      }
  }
}
var lr = se,
  ur = r.isWhiteSpace,
  pr = r.isPunctChar,
  hr = r.isMdAsciiPunct;
function fr(e, r, t, n) {
  (this.src = e),
    (this.env = t),
    (this.md = r),
    (this.tokens = n),
    (this.tokens_meta = Array(n.length)),
    (this.pos = 0),
    (this.posMax = this.src.length),
    (this.level = 0),
    (this.pending = ''),
    (this.pendingLevel = 0),
    (this.cache = {}),
    (this.delimiters = []),
    (this._prev_delimiters = []),
    (this.backticks = {}),
    (this.backticksScanned = !1),
    (this.linkLevel = 0);
}
(fr.prototype.pushPending = function () {
  var e = new lr('text', '', 0);
  return (
    (e.content = this.pending),
    (e.level = this.pendingLevel),
    this.tokens.push(e),
    (this.pending = ''),
    e
  );
}),
  (fr.prototype.push = function (e, r, t) {
    this.pending && this.pushPending();
    var n = new lr(e, r, t),
      s = null;
    return (
      t < 0 && (this.level--, (this.delimiters = this._prev_delimiters.pop())),
      (n.level = this.level),
      t > 0 &&
        (this.level++,
        this._prev_delimiters.push(this.delimiters),
        (this.delimiters = []),
        (s = { delimiters: this.delimiters })),
      (this.pendingLevel = this.level),
      this.tokens.push(n),
      this.tokens_meta.push(s),
      n
    );
  }),
  (fr.prototype.scanDelims = function (e, r) {
    var t,
      n,
      s,
      o,
      i,
      a,
      c,
      l,
      u,
      p = e,
      h = !0,
      f = !0,
      d = this.posMax,
      m = this.src.charCodeAt(e);
    for (t = e > 0 ? this.src.charCodeAt(e - 1) : 32; p < d && this.src.charCodeAt(p) === m; ) p++;
    return (
      (s = p - e),
      (n = p < d ? this.src.charCodeAt(p) : 32),
      (c = hr(t) || pr(String.fromCharCode(t))),
      (u = hr(n) || pr(String.fromCharCode(n))),
      (a = ur(t)),
      (l = ur(n)) ? (h = !1) : u && (a || c || (h = !1)),
      a ? (f = !1) : c && (l || u || (f = !1)),
      r ? ((o = h), (i = f)) : ((o = h && (!f || c)), (i = f && (!h || u))),
      { can_open: o, can_close: i, length: s }
    );
  }),
  (fr.prototype.Token = lr);
var dr = fr,
  mr = N,
  gr = [
    [
      'text',
      function (e, r) {
        for (var t = e.pos; t < e.posMax && !Oe(e.src.charCodeAt(t)); ) t++;
        return t !== e.pos && (r || (e.pending += e.src.slice(e.pos, t)), (e.pos = t), !0);
      },
    ],
    [
      'linkify',
      function (e, r) {
        var t, n, s, o, i, a, c;
        return (
          !!e.md.options.linkify &&
          !(e.linkLevel > 0) &&
          !((t = e.pos) + 3 > e.posMax) &&
          58 === e.src.charCodeAt(t) &&
          47 === e.src.charCodeAt(t + 1) &&
          47 === e.src.charCodeAt(t + 2) &&
          !!(n = e.pending.match(Pe)) &&
          ((s = n[1]),
          !!(o = e.md.linkify.matchAtStart(e.src.slice(t - s.length))) &&
            ((i = (i = o.url).replace(/\*+$/, '')),
            (a = e.md.normalizeLink(i)),
            !!e.md.validateLink(a) &&
              (r ||
                ((e.pending = e.pending.slice(0, -s.length)),
                ((c = e.push('link_open', 'a', 1)).attrs = [['href', a]]),
                (c.markup = 'linkify'),
                (c.info = 'auto'),
                ((c = e.push('text', '', 0)).content = e.md.normalizeLinkText(i)),
                ((c = e.push('link_close', 'a', -1)).markup = 'linkify'),
                (c.info = 'auto')),
              (e.pos += i.length - s.length),
              !0)))
        );
      },
    ],
    [
      'newline',
      function (e, r) {
        var t,
          n,
          s,
          o = e.pos;
        if (10 !== e.src.charCodeAt(o)) return !1;
        if (((t = e.pending.length - 1), (n = e.posMax), !r))
          if (t >= 0 && 32 === e.pending.charCodeAt(t))
            if (t >= 1 && 32 === e.pending.charCodeAt(t - 1)) {
              for (s = t - 1; s >= 1 && 32 === e.pending.charCodeAt(s - 1); ) s--;
              (e.pending = e.pending.slice(0, s)), e.push('hardbreak', 'br', 0);
            } else (e.pending = e.pending.slice(0, -1)), e.push('softbreak', 'br', 0);
          else e.push('softbreak', 'br', 0);
        for (o++; o < n && je(e.src.charCodeAt(o)); ) o++;
        return (e.pos = o), !0;
      },
    ],
    [
      'escape',
      function (e, r) {
        var t,
          n,
          s,
          o,
          i,
          a = e.pos,
          c = e.posMax;
        if (92 !== e.src.charCodeAt(a)) return !1;
        if (++a >= c) return !1;
        if (10 === (t = e.src.charCodeAt(a))) {
          for (
            r || e.push('hardbreak', 'br', 0), a++;
            a < c && ((t = e.src.charCodeAt(a)), Ue(t));

          )
            a++;
          return (e.pos = a), !0;
        }
        return (
          (o = e.src[a]),
          t >= 55296 &&
            t <= 56319 &&
            a + 1 < c &&
            (n = e.src.charCodeAt(a + 1)) >= 56320 &&
            n <= 57343 &&
            ((o += e.src[a + 1]), a++),
          (s = '\\' + o),
          r ||
            ((i = e.push('text_special', '', 0)),
            t < 256 && 0 !== Ve[t] ? (i.content = o) : (i.content = s),
            (i.markup = s),
            (i.info = 'escape')),
          (e.pos = a + 1),
          !0
        );
      },
    ],
    [
      'backticks',
      function (e, r) {
        var t,
          n,
          s,
          o,
          i,
          a,
          c,
          l,
          u = e.pos;
        if (96 !== e.src.charCodeAt(u)) return !1;
        for (t = u, u++, n = e.posMax; u < n && 96 === e.src.charCodeAt(u); ) u++;
        if (
          ((c = (s = e.src.slice(t, u)).length), e.backticksScanned && (e.backticks[c] || 0) <= t)
        )
          return r || (e.pending += s), (e.pos += c), !0;
        for (i = a = u; -1 !== (i = e.src.indexOf('`', a)); ) {
          for (a = i + 1; a < n && 96 === e.src.charCodeAt(a); ) a++;
          if ((l = a - i) === c)
            return (
              r ||
                (((o = e.push('code_inline', 'code', 0)).markup = s),
                (o.content = e.src
                  .slice(u, i)
                  .replace(/\n/g, ' ')
                  .replace(/^ (.+) $/, '$1'))),
              (e.pos = a),
              !0
            );
          e.backticks[l] = i;
        }
        return (e.backticksScanned = !0), r || (e.pending += s), (e.pos += c), !0;
      },
    ],
    ['strikethrough', $e.tokenize],
    ['emphasis', He.tokenize],
    [
      'link',
      function (e, r) {
        var t,
          n,
          s,
          o,
          i,
          a,
          c,
          l,
          u = '',
          p = '',
          h = e.pos,
          f = e.posMax,
          d = e.pos,
          m = !0;
        if (91 !== e.src.charCodeAt(e.pos)) return !1;
        if (((i = e.pos + 1), (o = e.md.helpers.parseLinkLabel(e, e.pos, !0)) < 0)) return !1;
        if ((a = o + 1) < f && 40 === e.src.charCodeAt(a)) {
          for (m = !1, a++; a < f && ((n = e.src.charCodeAt(a)), Ye(n) || 10 === n); a++);
          if (a >= f) return !1;
          if (((d = a), (c = e.md.helpers.parseLinkDestination(e.src, a, e.posMax)).ok)) {
            for (
              u = e.md.normalizeLink(c.str), e.md.validateLink(u) ? (a = c.pos) : (u = ''), d = a;
              a < f && ((n = e.src.charCodeAt(a)), Ye(n) || 10 === n);
              a++
            );
            if (((c = e.md.helpers.parseLinkTitle(e.src, a, e.posMax)), a < f && d !== a && c.ok))
              for (
                p = c.str, a = c.pos;
                a < f && ((n = e.src.charCodeAt(a)), Ye(n) || 10 === n);
                a++
              );
          }
          (a >= f || 41 !== e.src.charCodeAt(a)) && (m = !0), a++;
        }
        if (m) {
          if (void 0 === e.env.references) return !1;
          if (
            (a < f && 91 === e.src.charCodeAt(a)
              ? ((d = a + 1),
                (a = e.md.helpers.parseLinkLabel(e, a)) >= 0
                  ? (s = e.src.slice(d, a++))
                  : (a = o + 1))
              : (a = o + 1),
            s || (s = e.src.slice(i, o)),
            !(l = e.env.references[We(s)]))
          )
            return (e.pos = h), !1;
          (u = l.href), (p = l.title);
        }
        return (
          r ||
            ((e.pos = i),
            (e.posMax = o),
            (e.push('link_open', 'a', 1).attrs = t = [['href', u]]),
            p && t.push(['title', p]),
            e.linkLevel++,
            e.md.inline.tokenize(e),
            e.linkLevel--,
            e.push('link_close', 'a', -1)),
          (e.pos = a),
          (e.posMax = f),
          !0
        );
      },
    ],
    [
      'image',
      function (e, r) {
        var t,
          n,
          s,
          o,
          i,
          a,
          c,
          l,
          u,
          p,
          h,
          f,
          d,
          m = '',
          g = e.pos,
          _ = e.posMax;
        if (33 !== e.src.charCodeAt(e.pos)) return !1;
        if (91 !== e.src.charCodeAt(e.pos + 1)) return !1;
        if (((a = e.pos + 2), (i = e.md.helpers.parseLinkLabel(e, e.pos + 1, !1)) < 0)) return !1;
        if ((c = i + 1) < _ && 40 === e.src.charCodeAt(c)) {
          for (c++; c < _ && ((n = e.src.charCodeAt(c)), Qe(n) || 10 === n); c++);
          if (c >= _) return !1;
          for (
            d = c,
              (u = e.md.helpers.parseLinkDestination(e.src, c, e.posMax)).ok &&
                ((m = e.md.normalizeLink(u.str)), e.md.validateLink(m) ? (c = u.pos) : (m = '')),
              d = c;
            c < _ && ((n = e.src.charCodeAt(c)), Qe(n) || 10 === n);
            c++
          );
          if (((u = e.md.helpers.parseLinkTitle(e.src, c, e.posMax)), c < _ && d !== c && u.ok))
            for (
              p = u.str, c = u.pos;
              c < _ && ((n = e.src.charCodeAt(c)), Qe(n) || 10 === n);
              c++
            );
          else p = '';
          if (c >= _ || 41 !== e.src.charCodeAt(c)) return (e.pos = g), !1;
          c++;
        } else {
          if (void 0 === e.env.references) return !1;
          if (
            (c < _ && 91 === e.src.charCodeAt(c)
              ? ((d = c + 1),
                (c = e.md.helpers.parseLinkLabel(e, c)) >= 0
                  ? (o = e.src.slice(d, c++))
                  : (c = i + 1))
              : (c = i + 1),
            o || (o = e.src.slice(a, i)),
            !(l = e.env.references[Ke(o)]))
          )
            return (e.pos = g), !1;
          (m = l.href), (p = l.title);
        }
        return (
          r ||
            ((s = e.src.slice(a, i)),
            e.md.inline.parse(s, e.md, e.env, (f = [])),
            ((h = e.push('image', 'img', 0)).attrs = t =
              [
                ['src', m],
                ['alt', ''],
              ]),
            (h.children = f),
            (h.content = s),
            p && t.push(['title', p])),
          (e.pos = c),
          (e.posMax = _),
          !0
        );
      },
    ],
    [
      'autolink',
      function (e, r) {
        var t,
          n,
          s,
          o,
          i,
          a,
          c = e.pos;
        if (60 !== e.src.charCodeAt(c)) return !1;
        for (i = e.pos, a = e.posMax; ; ) {
          if (++c >= a) return !1;
          if (60 === (o = e.src.charCodeAt(c))) return !1;
          if (62 === o) break;
        }
        return (
          (t = e.src.slice(i + 1, c)),
          er.test(t)
            ? ((n = e.md.normalizeLink(t)),
              !!e.md.validateLink(n) &&
                (r ||
                  (((s = e.push('link_open', 'a', 1)).attrs = [['href', n]]),
                  (s.markup = 'autolink'),
                  (s.info = 'auto'),
                  ((s = e.push('text', '', 0)).content = e.md.normalizeLinkText(t)),
                  ((s = e.push('link_close', 'a', -1)).markup = 'autolink'),
                  (s.info = 'auto')),
                (e.pos += t.length + 2),
                !0))
            : !!Xe.test(t) &&
              ((n = e.md.normalizeLink('mailto:' + t)),
              !!e.md.validateLink(n) &&
                (r ||
                  (((s = e.push('link_open', 'a', 1)).attrs = [['href', n]]),
                  (s.markup = 'autolink'),
                  (s.info = 'auto'),
                  ((s = e.push('text', '', 0)).content = e.md.normalizeLinkText(t)),
                  ((s = e.push('link_close', 'a', -1)).markup = 'autolink'),
                  (s.info = 'auto')),
                (e.pos += t.length + 2),
                !0))
        );
      },
    ],
    [
      'html_inline',
      function (e, r) {
        var t,
          n,
          s,
          o,
          i,
          a = e.pos;
        return (
          !!e.md.options.html &&
          ((s = e.posMax),
          !(60 !== e.src.charCodeAt(a) || a + 2 >= s) &&
            !(
              33 !== (t = e.src.charCodeAt(a + 1)) &&
              63 !== t &&
              47 !== t &&
              !(function (e) {
                var r = 32 | e;
                return r >= 97 && r <= 122;
              })(t)
            ) &&
            !!(n = e.src.slice(a).match(rr)) &&
            (r ||
              (((o = e.push('html_inline', '', 0)).content = e.src.slice(a, a + n[0].length)),
              (i = o.content),
              /^<a[>\s]/i.test(i) && e.linkLevel++,
              (function (e) {
                return /^<\/a\s*>/i.test(e);
              })(o.content) && e.linkLevel--),
            (e.pos += n[0].length),
            !0))
        );
      },
    ],
    [
      'entity',
      function (e, r) {
        var t,
          n,
          s,
          o = e.pos,
          i = e.posMax;
        if (38 !== e.src.charCodeAt(o)) return !1;
        if (o + 1 >= i) return !1;
        if (35 === e.src.charCodeAt(o + 1)) {
          if ((n = e.src.slice(o).match(ir)))
            return (
              r ||
                ((t =
                  'x' === n[1][0].toLowerCase() ? parseInt(n[1].slice(1), 16) : parseInt(n[1], 10)),
                ((s = e.push('text_special', '', 0)).content = sr(t) ? or(t) : or(65533)),
                (s.markup = n[0]),
                (s.info = 'entity')),
              (e.pos += n[0].length),
              !0
            );
        } else if ((n = e.src.slice(o).match(ar)) && nr(tr, n[1]))
          return (
            r ||
              (((s = e.push('text_special', '', 0)).content = tr[n[1]]),
              (s.markup = n[0]),
              (s.info = 'entity')),
            (e.pos += n[0].length),
            !0
          );
        return !1;
      },
    ],
  ],
  _r = [
    [
      'balance_pairs',
      function (e) {
        var r,
          t = e.tokens_meta,
          n = e.tokens_meta.length;
        for (cr(0, e.delimiters), r = 0; r < n; r++)
          t[r] && t[r].delimiters && cr(0, t[r].delimiters);
      },
    ],
    ['strikethrough', $e.postProcess],
    ['emphasis', He.postProcess],
    [
      'fragments_join',
      function (e) {
        var r,
          t,
          n = 0,
          s = e.tokens,
          o = e.tokens.length;
        for (r = t = 0; r < o; r++)
          s[r].nesting < 0 && n--,
            (s[r].level = n),
            s[r].nesting > 0 && n++,
            'text' === s[r].type && r + 1 < o && 'text' === s[r + 1].type
              ? (s[r + 1].content = s[r].content + s[r + 1].content)
              : (r !== t && (s[t] = s[r]), t++);
        r !== t && (s.length = t);
      },
    ],
  ];
function kr() {
  var e;
  for (this.ruler = new mr(), e = 0; e < gr.length; e++) this.ruler.push(gr[e][0], gr[e][1]);
  for (this.ruler2 = new mr(), e = 0; e < _r.length; e++) this.ruler2.push(_r[e][0], _r[e][1]);
}
(kr.prototype.skipToken = function (e) {
  var r,
    t,
    n = e.pos,
    s = this.ruler.getRules(''),
    o = s.length,
    i = e.md.options.maxNesting,
    a = e.cache;
  if (void 0 === a[n]) {
    if (e.level < i) for (t = 0; t < o && (e.level++, (r = s[t](e, !0)), e.level--, !r); t++);
    else e.pos = e.posMax;
    r || e.pos++, (a[n] = e.pos);
  } else e.pos = a[n];
}),
  (kr.prototype.tokenize = function (e) {
    for (
      var r,
        t,
        n = this.ruler.getRules(''),
        s = n.length,
        o = e.posMax,
        i = e.md.options.maxNesting;
      e.pos < o;

    ) {
      if (e.level < i) for (t = 0; t < s && !(r = n[t](e, !1)); t++);
      if (r) {
        if (e.pos >= o) break;
      } else e.pending += e.src[e.pos++];
    }
    e.pending && e.pushPending();
  }),
  (kr.prototype.parse = function (e, r, t, n) {
    var s,
      o,
      i,
      a = new this.State(e, r, t, n);
    for (this.tokenize(a), i = (o = this.ruler2.getRules('')).length, s = 0; s < i; s++) o[s](a);
  }),
  (kr.prototype.State = dr);
var br = kr;
function vr(e) {
  var r = Array.prototype.slice.call(arguments, 1);
  return (
    r.forEach(function (r) {
      r &&
        Object.keys(r).forEach(function (t) {
          e[t] = r[t];
        });
    }),
    e
  );
}
function Cr(e) {
  return Object.prototype.toString.call(e);
}
function yr(e) {
  return '[object Function]' === Cr(e);
}
function Ar(e) {
  return e.replace(/[.?*+^$[\]\\(){}|-]/g, '\\$&');
}
var xr = { fuzzyLink: !0, fuzzyEmail: !0, fuzzyIP: !1 };
var Dr = {
    'http:': {
      validate: function (e, r, t) {
        var n = e.slice(r);
        return (
          t.re.http ||
            (t.re.http = new RegExp(
              '^\\/\\/' + t.re.src_auth + t.re.src_host_port_strict + t.re.src_path,
              'i'
            )),
          t.re.http.test(n) ? n.match(t.re.http)[0].length : 0
        );
      },
    },
    'https:': 'http:',
    'ftp:': 'http:',
    '//': {
      validate: function (e, r, t) {
        var n = e.slice(r);
        return (
          t.re.no_http ||
            (t.re.no_http = new RegExp(
              '^' +
                t.re.src_auth +
                '(?:localhost|(?:(?:' +
                t.re.src_domain +
                ')\\.)+' +
                t.re.src_domain_root +
                ')' +
                t.re.src_port +
                t.re.src_host_terminator +
                t.re.src_path,
              'i'
            )),
          t.re.no_http.test(n)
            ? (r >= 3 && ':' === e[r - 3]) || (r >= 3 && '/' === e[r - 3])
              ? 0
              : n.match(t.re.no_http)[0].length
            : 0
        );
      },
    },
    'mailto:': {
      validate: function (e, r, t) {
        var n = e.slice(r);
        return (
          t.re.mailto ||
            (t.re.mailto = new RegExp('^' + t.re.src_email_name + '@' + t.re.src_host_strict, 'i')),
          t.re.mailto.test(n) ? n.match(t.re.mailto)[0].length : 0
        );
      },
    },
  },
  wr = 'biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф'.split('|');
function Er(e) {
  var r = (e.re = (function (e) {
      var r = {};
      return (
        (e = e || {}),
        (r.src_Any = D.source),
        (r.src_Cc = w.source),
        (r.src_Z = E.source),
        (r.src_P = n.source),
        (r.src_ZPCc = [r.src_Z, r.src_P, r.src_Cc].join('|')),
        (r.src_ZCc = [r.src_Z, r.src_Cc].join('|')),
        (r.src_pseudo_letter = '(?:(?![><｜]|' + r.src_ZPCc + ')' + r.src_Any + ')'),
        (r.src_ip4 =
          '(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)'),
        (r.src_auth = '(?:(?:(?!' + r.src_ZCc + '|[@/\\[\\]()]).)+@)?'),
        (r.src_port =
          '(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?'),
        (r.src_host_terminator =
          '(?=$|[><｜]|' +
          r.src_ZPCc +
          ')(?!' +
          (e['---'] ? '-(?!--)|' : '-|') +
          '_|:\\d|\\.-|\\.(?!$|' +
          r.src_ZPCc +
          '))'),
        (r.src_path =
          '(?:[/?#](?:(?!' +
          r.src_ZCc +
          '|[><｜]|[()[\\]{}.,"\'?!\\-;]).|\\[(?:(?!' +
          r.src_ZCc +
          '|\\]).)*\\]|\\((?:(?!' +
          r.src_ZCc +
          '|[)]).)*\\)|\\{(?:(?!' +
          r.src_ZCc +
          '|[}]).)*\\}|\\"(?:(?!' +
          r.src_ZCc +
          '|["]).)+\\"|\\\'(?:(?!' +
          r.src_ZCc +
          "|[']).)+\\'|\\'(?=" +
          r.src_pseudo_letter +
          '|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!' +
          r.src_ZCc +
          '|[.]|$)|' +
          (e['---'] ? '\\-(?!--(?:[^-]|$))(?:-*)|' : '\\-+|') +
          ',(?!' +
          r.src_ZCc +
          '|$)|;(?!' +
          r.src_ZCc +
          '|$)|\\!+(?!' +
          r.src_ZCc +
          '|[!]|$)|\\?(?!' +
          r.src_ZCc +
          '|[?]|$))+|\\/)?'),
        (r.src_email_name = '[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*'),
        (r.src_xn = 'xn--[a-z0-9\\-]{1,59}'),
        (r.src_domain_root = '(?:' + r.src_xn + '|' + r.src_pseudo_letter + '{1,63})'),
        (r.src_domain =
          '(?:' +
          r.src_xn +
          '|(?:' +
          r.src_pseudo_letter +
          ')|(?:' +
          r.src_pseudo_letter +
          '(?:-|' +
          r.src_pseudo_letter +
          '){0,61}' +
          r.src_pseudo_letter +
          '))'),
        (r.src_host = '(?:(?:(?:(?:' + r.src_domain + ')\\.)*' + r.src_domain + '))'),
        (r.tpl_host_fuzzy = '(?:' + r.src_ip4 + '|(?:(?:(?:' + r.src_domain + ')\\.)+(?:%TLDS%)))'),
        (r.tpl_host_no_ip_fuzzy = '(?:(?:(?:' + r.src_domain + ')\\.)+(?:%TLDS%))'),
        (r.src_host_strict = r.src_host + r.src_host_terminator),
        (r.tpl_host_fuzzy_strict = r.tpl_host_fuzzy + r.src_host_terminator),
        (r.src_host_port_strict = r.src_host + r.src_port + r.src_host_terminator),
        (r.tpl_host_port_fuzzy_strict = r.tpl_host_fuzzy + r.src_port + r.src_host_terminator),
        (r.tpl_host_port_no_ip_fuzzy_strict =
          r.tpl_host_no_ip_fuzzy + r.src_port + r.src_host_terminator),
        (r.tpl_host_fuzzy_test =
          'localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:' + r.src_ZPCc + '|>|$))'),
        (r.tpl_email_fuzzy =
          '(^|[><｜]|"|\\(|' +
          r.src_ZCc +
          ')(' +
          r.src_email_name +
          '@' +
          r.tpl_host_fuzzy_strict +
          ')'),
        (r.tpl_link_fuzzy =
          '(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|' +
          r.src_ZPCc +
          '))((?![$+<=>^`|｜])' +
          r.tpl_host_port_fuzzy_strict +
          r.src_path +
          ')'),
        (r.tpl_link_no_ip_fuzzy =
          '(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|' +
          r.src_ZPCc +
          '))((?![$+<=>^`|｜])' +
          r.tpl_host_port_no_ip_fuzzy_strict +
          r.src_path +
          ')'),
        r
      );
    })(e.__opts__)),
    t = e.__tlds__.slice();
  function s(e) {
    return e.replace('%TLDS%', r.src_tlds);
  }
  e.onCompile(),
    e.__tlds_replaced__ ||
      t.push(
        'a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]'
      ),
    t.push(r.src_xn),
    (r.src_tlds = t.join('|')),
    (r.email_fuzzy = RegExp(s(r.tpl_email_fuzzy), 'i')),
    (r.link_fuzzy = RegExp(s(r.tpl_link_fuzzy), 'i')),
    (r.link_no_ip_fuzzy = RegExp(s(r.tpl_link_no_ip_fuzzy), 'i')),
    (r.host_fuzzy_test = RegExp(s(r.tpl_host_fuzzy_test), 'i'));
  var o = [];
  function i(e, r) {
    throw new Error('(LinkifyIt) Invalid schema "' + e + '": ' + r);
  }
  (e.__compiled__ = {}),
    Object.keys(e.__schemas__).forEach(function (r) {
      var t = e.__schemas__[r];
      if (null !== t) {
        var n = { validate: null, link: null };
        if (((e.__compiled__[r] = n), '[object Object]' === Cr(t)))
          return (
            !(function (e) {
              return '[object RegExp]' === Cr(e);
            })(t.validate)
              ? yr(t.validate)
                ? (n.validate = t.validate)
                : i(r, t)
              : (n.validate = (function (e) {
                  return function (r, t) {
                    var n = r.slice(t);
                    return e.test(n) ? n.match(e)[0].length : 0;
                  };
                })(t.validate)),
            void (yr(t.normalize)
              ? (n.normalize = t.normalize)
              : t.normalize
                ? i(r, t)
                : (n.normalize = function (e, r) {
                    r.normalize(e);
                  }))
          );
        !(function (e) {
          return '[object String]' === Cr(e);
        })(t)
          ? i(r, t)
          : o.push(r);
      }
    }),
    o.forEach(function (r) {
      e.__compiled__[e.__schemas__[r]] &&
        ((e.__compiled__[r].validate = e.__compiled__[e.__schemas__[r]].validate),
        (e.__compiled__[r].normalize = e.__compiled__[e.__schemas__[r]].normalize));
    }),
    (e.__compiled__[''] = {
      validate: null,
      normalize: function (e, r) {
        r.normalize(e);
      },
    });
  var a = Object.keys(e.__compiled__)
    .filter(function (r) {
      return r.length > 0 && e.__compiled__[r];
    })
    .map(Ar)
    .join('|');
  (e.re.schema_test = RegExp('(^|(?!_)(?:[><｜]|' + r.src_ZPCc + '))(' + a + ')', 'i')),
    (e.re.schema_search = RegExp('(^|(?!_)(?:[><｜]|' + r.src_ZPCc + '))(' + a + ')', 'ig')),
    (e.re.schema_at_start = RegExp('^' + e.re.schema_search.source, 'i')),
    (e.re.pretest = RegExp(
      '(' + e.re.schema_test.source + ')|(' + e.re.host_fuzzy_test.source + ')|@',
      'i'
    )),
    (function (e) {
      (e.__index__ = -1), (e.__text_cache__ = '');
    })(e);
}
function qr(e, r) {
  var t = e.__index__,
    n = e.__last_index__,
    s = e.__text_cache__.slice(t, n);
  (this.schema = e.__schema__.toLowerCase()),
    (this.index = t + r),
    (this.lastIndex = n + r),
    (this.raw = s),
    (this.text = s),
    (this.url = s);
}
function Sr(e, r) {
  var t = new qr(e, r);
  return e.__compiled__[t.schema].normalize(t, e), t;
}
function Fr(e, r) {
  if (!(this instanceof Fr)) return new Fr(e, r);
  var t;
  r ||
    ((t = e),
    Object.keys(t || {}).reduce(function (e, r) {
      return e || xr.hasOwnProperty(r);
    }, !1) && ((r = e), (e = {}))),
    (this.__opts__ = vr({}, xr, r)),
    (this.__index__ = -1),
    (this.__last_index__ = -1),
    (this.__schema__ = ''),
    (this.__text_cache__ = ''),
    (this.__schemas__ = vr({}, Dr, e)),
    (this.__compiled__ = {}),
    (this.__tlds__ = wr),
    (this.__tlds_replaced__ = !1),
    (this.re = {}),
    Er(this);
}
(Fr.prototype.add = function (e, r) {
  return (this.__schemas__[e] = r), Er(this), this;
}),
  (Fr.prototype.set = function (e) {
    return (this.__opts__ = vr(this.__opts__, e)), this;
  }),
  (Fr.prototype.test = function (e) {
    if (((this.__text_cache__ = e), (this.__index__ = -1), !e.length)) return !1;
    var r, t, n, s, o, i, a, c;
    if (this.re.schema_test.test(e))
      for ((a = this.re.schema_search).lastIndex = 0; null !== (r = a.exec(e)); )
        if ((s = this.testSchemaAt(e, r[2], a.lastIndex))) {
          (this.__schema__ = r[2]),
            (this.__index__ = r.index + r[1].length),
            (this.__last_index__ = r.index + r[0].length + s);
          break;
        }
    return (
      this.__opts__.fuzzyLink &&
        this.__compiled__['http:'] &&
        (c = e.search(this.re.host_fuzzy_test)) >= 0 &&
        (this.__index__ < 0 || c < this.__index__) &&
        null !==
          (t = e.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) &&
        ((o = t.index + t[1].length),
        (this.__index__ < 0 || o < this.__index__) &&
          ((this.__schema__ = ''),
          (this.__index__ = o),
          (this.__last_index__ = t.index + t[0].length))),
      this.__opts__.fuzzyEmail &&
        this.__compiled__['mailto:'] &&
        e.indexOf('@') >= 0 &&
        null !== (n = e.match(this.re.email_fuzzy)) &&
        ((o = n.index + n[1].length),
        (i = n.index + n[0].length),
        (this.__index__ < 0 ||
          o < this.__index__ ||
          (o === this.__index__ && i > this.__last_index__)) &&
          ((this.__schema__ = 'mailto:'), (this.__index__ = o), (this.__last_index__ = i))),
      this.__index__ >= 0
    );
  }),
  (Fr.prototype.pretest = function (e) {
    return this.re.pretest.test(e);
  }),
  (Fr.prototype.testSchemaAt = function (e, r, t) {
    return this.__compiled__[r.toLowerCase()]
      ? this.__compiled__[r.toLowerCase()].validate(e, t, this)
      : 0;
  }),
  (Fr.prototype.match = function (e) {
    var r = 0,
      t = [];
    this.__index__ >= 0 &&
      this.__text_cache__ === e &&
      (t.push(Sr(this, r)), (r = this.__last_index__));
    for (var n = r ? e.slice(r) : e; this.test(n); )
      t.push(Sr(this, r)), (n = n.slice(this.__last_index__)), (r += this.__last_index__);
    return t.length ? t : null;
  }),
  (Fr.prototype.matchAtStart = function (e) {
    if (((this.__text_cache__ = e), (this.__index__ = -1), !e.length)) return null;
    var r = this.re.schema_at_start.exec(e);
    if (!r) return null;
    var t = this.testSchemaAt(e, r[2], r[0].length);
    return t
      ? ((this.__schema__ = r[2]),
        (this.__index__ = r.index + r[1].length),
        (this.__last_index__ = r.index + r[0].length + t),
        Sr(this, 0))
      : null;
  }),
  (Fr.prototype.tlds = function (e, r) {
    return (
      (e = Array.isArray(e) ? e : [e]),
      r
        ? ((this.__tlds__ = this.__tlds__
            .concat(e)
            .sort()
            .filter(function (e, r, t) {
              return e !== t[r - 1];
            })
            .reverse()),
          Er(this),
          this)
        : ((this.__tlds__ = e.slice()), (this.__tlds_replaced__ = !0), Er(this), this)
    );
  }),
  (Fr.prototype.normalize = function (e) {
    e.schema || (e.url = 'http://' + e.url),
      'mailto:' !== e.schema || /^mailto:/i.test(e.url) || (e.url = 'mailto:' + e.url);
  }),
  (Fr.prototype.onCompile = function () {});
var Lr = Fr,
  zr = 2147483647,
  Tr = /^xn--/,
  Ir = /[^\x20-\x7E]/,
  Mr = /[\x2E\u3002\uFF0E\uFF61]/g,
  Rr = {
    overflow: 'Overflow: input needs wider integers to process',
    'not-basic': 'Illegal input >= 0x80 (not a basic code point)',
    'invalid-input': 'Invalid input',
  },
  Br = Math.floor,
  Nr = String.fromCharCode;
/*! https://mths.be/punycode v1.4.1 by @mathias */ function Or(e) {
  throw new RangeError(Rr[e]);
}
function Pr(e, r) {
  for (var t = e.length, n = []; t--; ) n[t] = r(e[t]);
  return n;
}
function jr(e, r) {
  var t = e.split('@'),
    n = '';
  return (
    t.length > 1 && ((n = t[0] + '@'), (e = t[1])),
    n + Pr((e = e.replace(Mr, '.')).split('.'), r).join('.')
  );
}
function Ur(e) {
  for (var r, t, n = [], s = 0, o = e.length; s < o; )
    (r = e.charCodeAt(s++)) >= 55296 && r <= 56319 && s < o
      ? 56320 == (64512 & (t = e.charCodeAt(s++)))
        ? n.push(((1023 & r) << 10) + (1023 & t) + 65536)
        : (n.push(r), s--)
      : n.push(r);
  return n;
}
function Vr(e) {
  return Pr(e, function (e) {
    var r = '';
    return (
      e > 65535 && ((r += Nr((((e -= 65536) >>> 10) & 1023) | 55296)), (e = 56320 | (1023 & e))),
      (r += Nr(e))
    );
  }).join('');
}
function Zr(e, r) {
  return e + 22 + 75 * (e < 26) - ((0 != r) << 5);
}
function $r(e, r, t) {
  var n = 0;
  for (e = t ? Br(e / 700) : e >> 1, e += Br(e / r); e > 455; n += 36) e = Br(e / 35);
  return Br(n + (36 * e) / (e + 38));
}
function Gr(e) {
  var r,
    t,
    n,
    s,
    o,
    i,
    a,
    c,
    l,
    u,
    p,
    h = [],
    f = e.length,
    d = 0,
    m = 128,
    g = 72;
  for ((t = e.lastIndexOf('-')) < 0 && (t = 0), n = 0; n < t; ++n)
    e.charCodeAt(n) >= 128 && Or('not-basic'), h.push(e.charCodeAt(n));
  for (s = t > 0 ? t + 1 : 0; s < f; ) {
    for (
      o = d, i = 1, a = 36;
      s >= f && Or('invalid-input'),
        ((c =
          (p = e.charCodeAt(s++)) - 48 < 10
            ? p - 22
            : p - 65 < 26
              ? p - 65
              : p - 97 < 26
                ? p - 97
                : 36) >= 36 ||
          c > Br((zr - d) / i)) &&
          Or('overflow'),
        (d += c * i),
        !(c < (l = a <= g ? 1 : a >= g + 26 ? 26 : a - g));
      a += 36
    )
      i > Br(zr / (u = 36 - l)) && Or('overflow'), (i *= u);
    (g = $r(d - o, (r = h.length + 1), 0 == o)),
      Br(d / r) > zr - m && Or('overflow'),
      (m += Br(d / r)),
      (d %= r),
      h.splice(d++, 0, m);
  }
  return Vr(h);
}
function Hr(e) {
  var r,
    t,
    n,
    s,
    o,
    i,
    a,
    c,
    l,
    u,
    p,
    h,
    f,
    d,
    m,
    g = [];
  for (h = (e = Ur(e)).length, r = 128, t = 0, o = 72, i = 0; i < h; ++i)
    (p = e[i]) < 128 && g.push(Nr(p));
  for (n = s = g.length, s && g.push('-'); n < h; ) {
    for (a = zr, i = 0; i < h; ++i) (p = e[i]) >= r && p < a && (a = p);
    for (
      a - r > Br((zr - t) / (f = n + 1)) && Or('overflow'), t += (a - r) * f, r = a, i = 0;
      i < h;
      ++i
    )
      if (((p = e[i]) < r && ++t > zr && Or('overflow'), p == r)) {
        for (c = t, l = 36; !(c < (u = l <= o ? 1 : l >= o + 26 ? 26 : l - o)); l += 36)
          (m = c - u), (d = 36 - u), g.push(Nr(Zr(u + (m % d), 0))), (c = Br(m / d));
        g.push(Nr(Zr(c, 0))), (o = $r(t, f, n == s)), (t = 0), ++n;
      }
    ++t, ++r;
  }
  return g.join('');
}
function Jr(e) {
  return jr(e, function (e) {
    return Tr.test(e) ? Gr(e.slice(4).toLowerCase()) : e;
  });
}
function Wr(e) {
  return jr(e, function (e) {
    return Ir.test(e) ? 'xn--' + Hr(e) : e;
  });
}
var Yr = { decode: Ur, encode: Vr },
  Kr = { version: '1.4.1', ucs2: Yr, toASCII: Wr, toUnicode: Jr, encode: Hr, decode: Gr },
  Qr = r,
  Xr = q,
  et = R,
  rt = pe,
  tt = Ne,
  nt = br,
  st = Lr,
  ot = s,
  it = e(
    Object.freeze({
      __proto__: null,
      decode: Gr,
      encode: Hr,
      toUnicode: Jr,
      toASCII: Wr,
      version: '1.4.1',
      ucs2: Yr,
      default: Kr,
    })
  ),
  at = {
    default: {
      options: {
        html: !1,
        xhtmlOut: !1,
        breaks: !1,
        langPrefix: 'language-',
        linkify: !1,
        typographer: !1,
        quotes: '“”‘’',
        highlight: null,
        maxNesting: 100,
      },
      components: { core: {}, block: {}, inline: {} },
    },
    zero: {
      options: {
        html: !1,
        xhtmlOut: !1,
        breaks: !1,
        langPrefix: 'language-',
        linkify: !1,
        typographer: !1,
        quotes: '“”‘’',
        highlight: null,
        maxNesting: 20,
      },
      components: {
        core: { rules: ['normalize', 'block', 'inline', 'text_join'] },
        block: { rules: ['paragraph'] },
        inline: { rules: ['text'], rules2: ['balance_pairs', 'fragments_join'] },
      },
    },
    commonmark: {
      options: {
        html: !0,
        xhtmlOut: !0,
        breaks: !1,
        langPrefix: 'language-',
        linkify: !1,
        typographer: !1,
        quotes: '“”‘’',
        highlight: null,
        maxNesting: 20,
      },
      components: {
        core: { rules: ['normalize', 'block', 'inline', 'text_join'] },
        block: {
          rules: [
            'blockquote',
            'code',
            'fence',
            'heading',
            'hr',
            'html_block',
            'lheading',
            'list',
            'reference',
            'paragraph',
          ],
        },
        inline: {
          rules: [
            'autolink',
            'backticks',
            'emphasis',
            'entity',
            'escape',
            'html_inline',
            'image',
            'link',
            'newline',
            'text',
          ],
          rules2: ['balance_pairs', 'emphasis', 'fragments_join'],
        },
      },
    },
  },
  ct = /^(vbscript|javascript|file|data):/,
  lt = /^data:image\/(gif|png|jpeg|webp);/;
function ut(e) {
  var r = e.trim().toLowerCase();
  return !ct.test(r) || !!lt.test(r);
}
var pt = ['http:', 'https:', 'mailto:'];
function ht(e) {
  var r = ot.parse(e, !0);
  if (r.hostname && (!r.protocol || pt.indexOf(r.protocol) >= 0))
    try {
      r.hostname = it.toASCII(r.hostname);
    } catch (e) {}
  return ot.encode(ot.format(r));
}
function ft(e) {
  var r = ot.parse(e, !0);
  if (r.hostname && (!r.protocol || pt.indexOf(r.protocol) >= 0))
    try {
      r.hostname = it.toUnicode(r.hostname);
    } catch (e) {}
  return ot.decode(ot.format(r), ot.decode.defaultChars + '%');
}
function dt(e, r) {
  if (!(this instanceof dt)) return new dt(e, r);
  r || Qr.isString(e) || ((r = e || {}), (e = 'default')),
    (this.inline = new nt()),
    (this.block = new tt()),
    (this.core = new rt()),
    (this.renderer = new et()),
    (this.linkify = new st()),
    (this.validateLink = ut),
    (this.normalizeLink = ht),
    (this.normalizeLinkText = ft),
    (this.utils = Qr),
    (this.helpers = Qr.assign({}, Xr)),
    (this.options = {}),
    this.configure(e),
    r && this.set(r);
}
(dt.prototype.set = function (e) {
  return Qr.assign(this.options, e), this;
}),
  (dt.prototype.configure = function (e) {
    var r,
      t = this;
    if (Qr.isString(e) && !(e = at[(r = e)]))
      throw new Error('Wrong `markdown-it` preset "' + r + '", check name');
    if (!e) throw new Error("Wrong `markdown-it` preset, can't be empty");
    return (
      e.options && t.set(e.options),
      e.components &&
        Object.keys(e.components).forEach(function (r) {
          e.components[r].rules && t[r].ruler.enableOnly(e.components[r].rules),
            e.components[r].rules2 && t[r].ruler2.enableOnly(e.components[r].rules2);
        }),
      this
    );
  }),
  (dt.prototype.enable = function (e, r) {
    var t = [];
    Array.isArray(e) || (e = [e]),
      ['core', 'block', 'inline'].forEach(function (r) {
        t = t.concat(this[r].ruler.enable(e, !0));
      }, this),
      (t = t.concat(this.inline.ruler2.enable(e, !0)));
    var n = e.filter(function (e) {
      return t.indexOf(e) < 0;
    });
    if (n.length && !r) throw new Error('MarkdownIt. Failed to enable unknown rule(s): ' + n);
    return this;
  }),
  (dt.prototype.disable = function (e, r) {
    var t = [];
    Array.isArray(e) || (e = [e]),
      ['core', 'block', 'inline'].forEach(function (r) {
        t = t.concat(this[r].ruler.disable(e, !0));
      }, this),
      (t = t.concat(this.inline.ruler2.disable(e, !0)));
    var n = e.filter(function (e) {
      return t.indexOf(e) < 0;
    });
    if (n.length && !r) throw new Error('MarkdownIt. Failed to disable unknown rule(s): ' + n);
    return this;
  }),
  (dt.prototype.use = function (e) {
    var r = [this].concat(Array.prototype.slice.call(arguments, 1));
    return e.apply(e, r), this;
  }),
  (dt.prototype.parse = function (e, r) {
    if ('string' != typeof e) throw new Error('Input data should be a String');
    var t = new this.core.State(e, this, r);
    return this.core.process(t), t.tokens;
  }),
  (dt.prototype.render = function (e, r) {
    return (r = r || {}), this.renderer.render(this.parse(e, r), this.options, r);
  }),
  (dt.prototype.parseInline = function (e, r) {
    var t = new this.core.State(e, this, r);
    return (t.inlineMode = !0), this.core.process(t), t.tokens;
  }),
  (dt.prototype.renderInline = function (e, r) {
    return (r = r || {}), this.renderer.render(this.parseInline(e, r), this.options, r);
  });
var mt = dt;
export default mt;
