# MyBasicUpload 组件

这是一个无UI的基础上传组件，提供相机、相册和本地文件上传功能，不包含任何UI元素（除了必要的权限申请弹窗和文件选择器）。

## 功能特点

- 拍照功能（支持权限检查和申请）
- 相册选择功能（支持权限检查和申请）
- 本地文件选择功能
- 自动上传到服务器
- 通过事件返回上传结果
- 支持APP和H5环境

## 使用方法

### 基本引入

```vue
<script setup>
import { ref } from 'vue';
import MyBasicUpload from '@/components/MyBasicUpload';

// 创建引用以调用组件方法
const myBasicUploadRef = ref(null);

// 处理上传成功事件
function handleBeforeUpload(files, type) {
  console.log('准备上传:', { files, type });
  // 可以在这里进行上传前的预处理
  // 例如：显示自定义加载状态、验证文件等
}

function handleUploadSuccess(fileData) {
  console.log('上传成功:', fileData);
  // fileData 包含：fileUrl, fileName, fileKey, id, fileType, fileSize, type, uploadTime 等信息
}

// 手动上传示例
function handleManualUpload() {
  // 假设您有一些文件路径需要上传
  const files = ['/path/to/file1.jpg', '/path/to/file2.png'];

  // 手动调用上传方法
  myBasicUploadRef.value.uploadToServer(files, 'image_url');
}

// 相册选择示例
function handleCustomAlbum() {
  // 选择最多3张图片，只选择原图
  myBasicUploadRef.value.openAlbum({
    count: 3,
    sizeType: ['original'],
  });
}

function handleCompressedAlbum() {
  // 选择1张图片，只选择压缩图
  myBasicUploadRef.value.openAlbum({
    count: 1,
    sizeType: ['compressed'],
  });
}
</script>

<template>
  <!-- 引入组件 -->
  <MyBasicUpload
    ref="myBasicUploadRef"
    @before-upload="handleBeforeUpload"
    @upload-success="handleUploadSuccess"
  />

  <!-- 自定义UI -->
  <button @tap="myBasicUploadRef.openCamera()">拍照</button>
  <button @tap="myBasicUploadRef.openAlbum()">相册(默认)</button>
  <button @tap="handleCustomAlbum()">相册(3张原图)</button>
  <button @tap="handleCompressedAlbum()">相册(1张压缩)</button>
  <button @tap="myBasicUploadRef.openFilePicker()">文件</button>
  <button @tap="myBasicUploadRef.openFilePicker({ extension: ['.zip', '.doc'], count: 6 })">
    限制文件类型
  </button>
  <button @tap="handleManualUpload()">手动上传</button>
</template>
```

### 可用方法

通过 ref 引用可以调用以下方法：

- `openCamera()`: 打开相机进行拍照
- `openAlbum(options?)`: 打开相册选择图片
  - `options` (可选): 相册选择配置对象
    - `count`: 选择图片的数量，默认为 9
    - `sizeType`: 图片大小类型，默认为 `['original', 'compressed']`
- `openFilePicker(options?)`: 打开文件选择器
  - `options` (可选): 文件选择器配置对象
    - `extension`: 文件扩展名限制，例如 `['.zip', '.doc', '.pdf']`
    - `count`: 选择文件的数量，默认为 9
    - `sizeType`: 图片大小类型（仅对图片有效），默认为 `['original', 'compressed']`
    - `sourceType`: 来源类型（仅对图片有效），默认为 `['album', 'camera']`
  - **注意**: 配置的 `extension` 和 `count` 将在文件选择后进行验证，不符合要求的文件选择会触发 `file-fail` 事件
- `uploadToServer(files, type)`: 手动上传文件到服务器
  - `files`: 要上传的文件数组
  - `type`: 文件类型，可选值为 `'image_url'` 或 `'file'`

### 事件

组件提供以下事件：

- `before-upload`: 文件开始上传前触发，返回文件数组和上传类型，可用于上传前的预处理
- `upload-success`: 文件上传成功时触发，返回文件信息
- `upload-fail`: 文件上传失败时触发，返回错误信息
- `capture-success`: 拍照或相册选择成功时触发，返回选择的文件信息
- `capture-fail`: 拍照或相册选择失败时触发，返回错误信息
- `file-success`: 文件选择成功时触发，返回选择的文件信息
- `file-fail`: 文件选择失败或验证失败时触发，返回错误信息
  - `error`: 原始错误数据
  - `errorMsg`: 错误消息（如：文件类型不支持、数量超限等）
  - `errorType`: 错误类型（如：`'fileType'`、`'fileCount'`）

### 手动上传详细说明

`uploadToServer` 方法允许您手动上传文件到服务器，适用于以下场景：

1. **已有文件路径需要上传**
2. **从其他来源获取的文件**
3. **需要在特定时机触发上传**

**参数说明：**

- `files` (UploadFiles): 文件数组，格式取决于 `type` 参数
  - 当 `type` 为 `'image_url'` 时：直接传入文件路径字符串数组 (`string[]`)
  - 当 `type` 为 `'file'` 时：传入包含 `tempFilePath` 和可选 `name`、`fileType` 属性的对象数组 (`FileItem[]`)
- `type` (UploadType): 文件类型标识
  - `UploadType.IMAGE_URL` 或 `'image_url'`: 图片文件
  - `UploadType.FILE` 或 `'file'`: 普通文件

**TypeScript 类型定义：**

```typescript
import {
  UploadType,
  type FileItem,
  type UploadFiles,
  type AlbumOptions,
} from '@/components/MyBasicUpload/types';

// 图片文件类型
const imageFiles: string[] = ['/temp/image1.jpg', '/temp/image2.png'];

// 普通文件类型
const normalFiles: FileItem[] = [
  { tempFilePath: '/temp/document.pdf', name: '文档.pdf', fileType: 'file' },
  { tempFilePath: '/temp/archive.zip', name: '压缩包.zip', fileType: 'file' },
];

// 相册选择配置
const albumOptions: AlbumOptions = {
  count: 5,
  sizeType: ['original', 'compressed'],
};
```

**使用示例：**

```javascript
// 上传图片文件
const imageFiles = ['/temp/image1.jpg', '/temp/image2.png'];
myBasicUploadRef.value.uploadToServer(imageFiles, UploadType.IMAGE_URL);

// 上传普通文件
const normalFiles = [
  { tempFilePath: '/temp/document.pdf', name: '文档.pdf', fileType: 'file' },
  { tempFilePath: '/temp/archive.zip', name: '压缩包.zip', fileType: 'file' },
];
myBasicUploadRef.value.uploadToServer(normalFiles, UploadType.FILE);
```

### 错误处理示例

```javascript
// 监听文件选择失败事件
const handleFileFail = (error) => {
  console.log('文件选择失败:', error);

  switch (error.errorType) {
    case 'fileType':
      uni.showToast({ title: error.errorMsg, icon: 'none' });
      break;
    case 'fileCount':
      uni.showToast({ title: error.errorMsg, icon: 'none' });
      break;
    default:
      uni.showToast({ title: '文件选择失败', icon: 'none' });
  }
};

// 在模板中绑定事件
<MyBasicUpload @file-fail="handleFileFail" />
```

## 完整示例

请参考 `example.vue` 文件，了解组件的完整使用方法。

## 类型定义

组件提供了完整的 TypeScript 类型支持，所有类型定义都在 `types.ts` 文件中：

- `UploadType`: 上传类型枚举
- `FileItem`: 文件项接口
- `UploadFiles`: 上传文件联合类型
- `AlbumOptions`: 相册选择配置接口
- `FilePickerOptions`: 文件选择器配置接口
- `UploadFileData`: 上传成功返回数据
- `UploadErrorData`: 上传失败错误数据
- `CaptureSuccessData`/`CaptureErrorData`: 拍照选择结果数据
- `MyBasicUploadMethods`: 组件方法类型
- `MyBasicUploadEvents`: 组件事件类型

## 注意事项

1. 组件内部已处理权限申请逻辑，包括相机和相册权限
2. 组件会自动上传选择的文件到服务器
3. 组件没有内置UI，需要自行设计界面
4. 组件依赖 `xe-upload` 和 `LKpermissionModal` 组件
5. **完整的 TypeScript 支持**：所有方法和事件都有完整的类型定义
