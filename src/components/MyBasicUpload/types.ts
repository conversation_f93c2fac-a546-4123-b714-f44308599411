/**
 * MyBasicUpload 组件相关类型定义
 */

// 上传文件类型枚举
export enum UploadType {
  /** 图片URL类型 - 直接传入文件路径字符串 */
  IMAGE_URL = 'image_url',
  /** 文件类型 - 传入包含文件信息的对象 */
  FILE = 'file',
}

// 图片文件项类型（当 type 为 'image_url' 时）
export type ImageFileItem = string;

// 普通文件项类型（当 type 为 'file' 时）
export interface FileItem {
  /** 临时文件路径 */
  tempFilePath: string;
  /** 文件名（可选）*/
  name?: string;
  /** 文件类型标识（可选）*/
  fileType?: 'file' | string;
  /** 文件大小（可选）*/
  size?: number;
  /** 其他扩展属性 */
  [key: string]: any;
}

// uploadToServer 方法的文件参数类型
export type UploadFiles = ImageFileItem[] | FileItem[];

// uploadToServer 方法参数类型
export interface UploadToServerParams {
  /** 要上传的文件数组 */
  files: UploadFiles;
  /** 上传类型 */
  type: UploadType | 'image_url' | 'file';
}

// 上传成功返回的文件数据
export interface UploadFileData {
  /** 文件访问URL */
  fileUrl: string;
  /** 文件名 */
  fileName: string;
  /** 文件唯一标识 */
  fileKey: string;
  /** 文件ID */
  id: string;
  /** 文件类型 */
  fileType: string;
  /** 文件大小 */
  fileSize: number;
  /** 上传类型标识 */
  type: string;
  /** 上传时间戳 */
  uploadTime: number;
}

// 上传失败错误数据
export interface UploadErrorData {
  /** 错误数据 */
  errorData?: any;
  /** 错误代码（可选）*/
  code?: number;
  /** 原始错误数据（可选）*/
  raw?: any;
  /** 错误详情（可选）*/
  details?: any;
  /** 错误消息（可选）*/
  errorMsg?: string;
  /** 错误类型（可选）*/
  errorType?: string;
}

// 拍照/相册选择成功数据
export interface CaptureSuccessData {
  /** 来源类型 */
  type: 'camera' | 'album';
  /** 选择的文件路径数组 */
  files: string[];
}

// 拍照/相册选择失败数据
export interface CaptureErrorData {
  /** 来源类型 */
  type: 'camera' | 'album';
  /** 错误信息 */
  error: any;
}

// 相册选择配置
export interface AlbumOptions {
  /** 选择图片的数量，默认9 */
  count?: number;
  /** 图片大小类型，默认['original', 'compressed'] */
  sizeType?: ('original' | 'compressed')[];
}

// 文件选择器配置
export interface FilePickerOptions {
  /** 文件扩展名限制，例如 ['.zip', '.doc', '.pdf'] */
  extension?: string[];
  /** 选择文件的数量，默认9 */
  count?: number;
  /** 图片大小类型（仅对图片有效），默认['original', 'compressed'] */
  sizeType?: ('original' | 'compressed')[];
  /** 来源类型（仅对图片有效），默认['album', 'camera'] */
  sourceType?: ('album' | 'camera')[];
}

// MyBasicUpload 组件实例方法类型
export interface MyBasicUploadMethods {
  /** 打开相机拍照 */
  openCamera: () => Promise<void>;
  /** 打开相册选择 */
  openAlbum: (options?: AlbumOptions) => Promise<void>;
  /** 打开文件选择器 */
  openFilePicker: (options?: FilePickerOptions) => void;
  /** 手动上传文件到服务器 */
  uploadToServer: (files: UploadFiles, type: UploadType | 'image_url' | 'file') => void;
}

// MyBasicUpload 组件事件类型
export interface MyBasicUploadEvents {
  /** 上传成功事件 */
  'upload-success': (data: UploadFileData) => void;
  /** 上传失败事件 */
  'upload-fail': (error: UploadErrorData) => void;
  /** 拍照/相册选择成功事件 */
  'capture-success': (data: CaptureSuccessData) => void;
  /** 拍照/相册选择失败事件 */
  'capture-fail': (error: CaptureErrorData) => void;
  /** 文件选择成功事件 */
  'file-success': (data: any) => void;
  /** 文件选择失败事件 */
  'file-fail': (error: UploadErrorData) => void;
}
