<script setup lang="ts">
import { ref } from 'vue';
import { getBaseUrl } from '@/common/ai/url';
import { checkPermission } from '@/utils/permission';
import LKpermissionModal from '@/components/LKpermissionModal/index.vue';
import {
  type UploadFiles,
  UploadType,
  type UploadFileData,
  type UploadErrorData,
  type CaptureSuccessData,
  type CaptureErrorData,
  type AlbumOptions,
  type FilePickerOptions,
} from './types';

// 权限弹窗相关状态
const permissionModalShow = ref(false);
const permissionModalTitle = ref('');
const permissionModalContent = ref('');

// 文件选择器配置存储
const filePickerOptions = ref<FilePickerOptions | null>(null);
const permissionOpenSettings = ref<(() => void) | null>(null);

const isUploading = ref(false);

const props = defineProps({
  // 可以传入一些配置选项
  options: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  (e: 'before-upload', files: UploadFiles, type: UploadType): void;
  (e: 'upload-success', data: UploadFileData): void;
  (e: 'upload-fail', error: UploadErrorData): void;
  (e: 'capture-success', data: CaptureSuccessData): void;
  (e: 'capture-fail', error: CaptureErrorData): void;
}>();

const xeUploadRef = ref<any>(null);
const uploadOptions = ref({});

// 上传文件到服务器
function uploadToServer(files: UploadFiles, type: UploadType) {
  // 触发上传前回调
  emit('before-upload', files, type);

  isUploading.value = true;
  uni.showLoading({
    title: '上传中...',
  });

  files.forEach((fileItem, index) => {
    const filePath = type === 'image_url' ? (fileItem as string) : (fileItem as any).tempFilePath;

    const filenameQueryParam =
      typeof (fileItem as any).fileType !== 'undefined' &&
      (fileItem as any).fileType === 'file' &&
      (fileItem as any).name
        ? `?filename=${(fileItem as any).name}`
        : '';
    const baseUrl = getBaseUrl();
    uni.uploadFile({
      url: `${baseUrl}/huayun-ai/system/file/upload${filenameQueryParam}`,
      header: {
        Authorization: uni.getStorageSync('token'),
      },
      filePath: filePath,
      name: 'file',
      success: uploadResult => {
        isUploading.value = false;
        let data;
        try {
          data = JSON.parse(uploadResult.data);
        } catch (e) {
          console.error('解析服务器响应失败:', uploadResult.data, e);
          if (index === files.length - 1) {
            uni.hideLoading();
          }
          // uni.showToast({ title: '服务器响应格式错误', icon: 'none' });
          emit('upload-fail', { errorMsg: '服务器响应格式错误', errorData: uploadResult.data });
          return;
        }

        if (data.code == 200) {
          console.log('上传成功:', data);
          const fileData = {
            fileUrl: data.data.fileUrl,
            fileName: data.data.fileName,
            fileKey: data.data.fileKey,
            id: data.data.id,
            fileType: data.data.fileType,
            fileSize: data.data.fileSize,
            type: type,
            uploadTime: Date.now(),
          };

          emit('upload-success', fileData);
        } else {
          console.error('上传失败:', data);
          // uni.showToast({ title: data.message || `上传失败 (${data.code})`, icon: 'none' });
          emit('upload-fail', {
            errorMsg: data.message || `上传失败 (${data.code})`,
            code: data.code,
          });
        }

        if (index === files.length - 1) {
          uni.hideLoading();
        }
      },
      fail: err => {
        isUploading.value = false;
        console.error('上传请求失败:', err);
        uni.hideLoading();
        // uni.showToast({ title: '上传请求失败', icon: 'none' });
        emit('upload-fail', { errorMsg: '上传请求失败', errorData: err });
      },
    });
  });
}

// 检查文件是否符合配置要求
function checkFileValidation(files: any[]): { valid: boolean; error?: string; errorType?: string } {
  if (!filePickerOptions.value) {
    // 如果没有配置，直接通过
    return { valid: true };
  }

  const options = filePickerOptions.value;

  // 检查文件数量
  if (options.count && files.length > options.count) {
    return {
      valid: false,
      error: `最多只能选择${options.count}个文件，当前选择了${files.length}个`,
      errorType: 'fileCount',
    };
  }

  // 检查文件扩展名
  if (options.extension && options.extension.length > 0) {
    for (const file of files) {
      const fileName = file.name || file.tempFilePath || '';
      const fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();

      if (!options.extension.some(ext => ext.toLowerCase() === fileExtension)) {
        return {
          valid: false,
          error: `不支持的文件类型：${fileExtension}，仅支持：${options.extension.join(', ')}`,
          errorType: 'fileType',
        };
      }
    }
  }

  return { valid: true };
}

// 处理文件上传回调
function handleUploadCallback(e: any) {
  if (e.type === 'choose') {
    console.log('选择的文件:', e.data);

    // 如果是通过openFilePicker选择的文件，需要进行验证
    if (filePickerOptions.value) {
      const validation = checkFileValidation(e.data);
      if (!validation.valid) {
        console.error('文件检查失败:', validation.error);
        emit('upload-fail', {
          errorData: e.data,
          errorMsg: validation.error,
          errorType: validation.errorType,
        });
        // 清除配置
        filePickerOptions.value = null;
        return;
      }
    }

    uploadToServer(e.data, UploadType.FILE);

    // 清除配置
    filePickerOptions.value = null;
  } else if (e.type === 'warning') {
    console.error('文件上传警告:', e.data);
    emit('upload-fail', { errorData: e.data, errorType: 'warning' });
    // 清除配置
    filePickerOptions.value = null;
  }
}

// 打开相册
async function openAlbum(options?: AlbumOptions) {
  const permissionResult = await checkPermission('album');
  console.log('albumpermissionResult', permissionResult);

  // 合并默认配置和传入的配置
  const config = {
    count: options?.count ?? 9,
    sizeType: options?.sizeType ?? ['original', 'compressed'],
  };

  if (permissionResult.granted) {
    uni.chooseImage({
      count: config.count,
      sizeType: config.sizeType,
      sourceType: ['album'], //从相册选择
      success: function (res) {
        const tempFilePaths = res.tempFilePaths;
        uploadToServer(tempFilePaths as string[], UploadType.IMAGE_URL);
        emit('capture-success', { type: 'album', files: tempFilePaths } as CaptureSuccessData);
      },
      fail: function (err) {
        console.error('选择相册图片失败:', err);
        emit('capture-fail', { type: 'album', error: err });
      },
    });
  } else {
    // 显示权限弹窗
    if (permissionResult.details) {
      permissionModalTitle.value = permissionResult.details.deniedTitle;
      permissionModalContent.value = permissionResult.details.deniedMessage;
      permissionOpenSettings.value = permissionResult.openSettings || null;
      permissionModalShow.value = true;
    }
  }
}

// 打开相机
async function openCamera() {
  // #ifdef APP-PLUS
  let hasPermission = false;

  // 根据平台选择检查权限的方法
  if (plus.os.name === 'iOS') {
    try {
      const AVCaptureDevice = (plus.ios as any).import('AVCaptureDevice');
      const AVMediaTypeVideo = 'vide'; // iOS中AVMediaTypeVideo的实际值
      const authStatus = AVCaptureDevice.authorizationStatusForMediaType(AVMediaTypeVideo);
      console.log('iOS相机权限状态:', authStatus);

      if (authStatus === 0) {
        // 未决定
        // 请求权限
        console.log('请求iOS相机权限');
        // 使用Promise包装原生回调
        hasPermission = await new Promise(resolve => {
          // 尝试多种可能的方法名
          try {
            if (typeof AVCaptureDevice.requestAccessForMediaType === 'function') {
              console.log('使用requestAccessForMediaType方法');
              AVCaptureDevice.requestAccessForMediaType(AVMediaTypeVideo, (granted: boolean) => {
                console.log('iOS相机权限请求结果:', granted);
                (plus.ios as any).deleteObject(AVCaptureDevice);
                resolve(granted);
              });
            } else if (typeof AVCaptureDevice.requestAccess === 'function') {
              console.log('使用requestAccess方法');
              AVCaptureDevice.requestAccess(AVMediaTypeVideo, (granted: boolean) => {
                console.log('iOS相机权限请求结果:', granted);
                (plus.ios as any).deleteObject(AVCaptureDevice);
                resolve(granted);
              });
            } else {
              console.log('没有找到请求权限的方法，尝试使用uni.authorize');
              (plus.ios as any).deleteObject(AVCaptureDevice);
              uni.authorize({
                scope: 'scope.camera',
                success: () => resolve(true),
                fail: () => resolve(false),
              });
            }
          } catch (innerError) {
            console.error('iOS相机权限请求内部错误:', innerError);
            plus.ios.deleteObject(AVCaptureDevice);
            // 使用uni.chooseImage作为最后的尝试
            uni.chooseImage({
              count: 1,
              sourceType: ['camera'],
              success: () => resolve(true),
              fail: () => resolve(false),
            });
          }
        });
      } else if (authStatus === 3) {
        // 已授权
        hasPermission = true;
        plus.ios.deleteObject(AVCaptureDevice);
      } else {
        // 已拒绝或受限
        hasPermission = false;
        plus.ios.deleteObject(AVCaptureDevice);
      }
    } catch (e) {
      console.error('iOS相机权限检查错误:', e);
      // 如果原生API失败，回退到checkPermission
      const permissionResult = await checkPermission('camera');
      hasPermission = permissionResult.granted;
    }
  } else {
    // Android
    try {
      // 直接请求Android相机权限
      const status = await new Promise(resolve => {
        plus.android.requestPermissions(
          ['android.permission.CAMERA'],
          result => {
            console.log('Android相机权限结果:', JSON.stringify(result));
            if (result.granted && result.granted.length > 0) {
              resolve(true);
            } else {
              resolve(false);
            }
          },
          () => resolve(false)
        );
      });
      hasPermission = !!status;
    } catch (e) {
      console.error('Android相机权限检查错误:', e);
      // 如果原生API失败，回退到checkPermission
      const permissionResult = await checkPermission('camera');
      hasPermission = permissionResult.granted;
    }
  }

  console.log('相机权限状态:', hasPermission);

  if (hasPermission) {
    try {
      // 获取摄像头对象
      const camera = plus.camera.getCamera();

      // 使用plus.camera进行拍照
      camera.captureImage(
        capturedFile => {
          console.log('拍照成功:', capturedFile);
          // 将拍照结果转换为与uni.chooseImage相同的格式
          const tempFilePaths = [capturedFile];
          uploadToServer(tempFilePaths as string[], UploadType.IMAGE_URL);
          emit('capture-success', { type: 'camera', files: tempFilePaths } as CaptureSuccessData);
        },
        error => {
          console.error('拍照失败:', error);
          // uni.showToast({
          //   title: error.message || '拍照失败',
          //   icon: 'none',
          // });
          emit('capture-fail', { type: 'camera', error: error });
        },
        {
          // 拍照参数配置
          format: 'jpg',
          optimize: true,
          filename: '_doc/camera/',
        }
      );
    } catch (error) {
      console.error('获取摄像头失败:', error);
      // uni.showToast({
      //   title: '获取摄像头失败',
      //   icon: 'none',
      // });
      emit('capture-fail', { type: 'camera', error: error });
    }
  } else {
    // 显示权限弹窗
    const permissionResult = await checkPermission('camera');
    if (permissionResult.details) {
      permissionModalTitle.value = permissionResult.details.deniedTitle;
      permissionModalContent.value = permissionResult.details.deniedMessage;
      permissionOpenSettings.value = permissionResult.openSettings || null;
      permissionModalShow.value = true;
    }
  }
  // #endif

  // #ifndef APP-PLUS
  // 非APP环境
  const permissionResult = await checkPermission('camera');
  if (permissionResult.granted) {
    uni.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['camera'],
      success: res => {
        const tempFilePaths = res.tempFilePaths;
        uploadToServer(tempFilePaths as string[], UploadType.IMAGE_URL);
        emit('capture-success', { type: 'camera', files: tempFilePaths } as CaptureSuccessData);
      },
      fail: error => {
        console.error('拍照失败:', error);
        // uni.showToast({
        //   title: '拍照失败',
        //   icon: 'none',
        // });
        emit('capture-fail', { type: 'camera', error: error });
      },
    });
  } else {
    // 显示权限弹窗
    if (permissionResult.details) {
      permissionModalTitle.value = permissionResult.details.deniedTitle;
      permissionModalContent.value = permissionResult.details.deniedMessage;
      permissionOpenSettings.value = permissionResult.openSettings || null;
      permissionModalShow.value = true;
    }
  }
  // #endif
}

// 打开文件选择器
function openFilePicker(options?: FilePickerOptions) {
  // 存储配置供后续检查使用
  filePickerOptions.value = options || null;

  console.log('打开文件选择器，存储配置:', filePickerOptions.value);

  // 调用xe-upload组件的文件选择
  xeUploadRef.value?.upload('file', {});
}

// 权限弹窗事件处理
function handlePermissionCancel() {
  permissionModalShow.value = false;
}

function handlePermissionConfirm() {
  permissionModalShow.value = false;
  console.log('前往设置, permissionOpenSettings:', permissionOpenSettings.value);

  if (permissionOpenSettings.value && typeof permissionOpenSettings.value === 'function') {
    // 直接调用permissionOpenSettings函数
    try {
      permissionOpenSettings.value();
    } catch (e) {
      console.error('调用permissionOpenSettings错误:', e);
      // 如果调用失败，尝试直接调用系统API
      try {
        // #ifdef APP-PLUS
        const isIOS = plus.os.name === 'iOS';
        if (isIOS) {
          try {
            // 对于iOS 10+，使用新的API
            const UIApplication = (plus.ios as any).import('UIApplication');
            const application = UIApplication.sharedApplication();
            const NSURL = (plus.ios as any).import('NSURL');
            const settingURL = NSURL.URLWithString('app-settings:');

            // 检查iOS版本
            const iosVersion = plus.os.version || '9.0';
            if (parseInt(iosVersion) >= 10) {
              // iOS 10+ 使用新方法
              console.log('使用iOS 10+方法打开设置');
              const options = plus.ios.newObject('NSDictionary');
              application.openURL_options_completionHandler(settingURL, options, null);
              plus.ios.deleteObject(options);
            } else {
              // iOS 9及以下使用旧方法
              application.openURL(settingURL);
            }

            plus.ios.deleteObject(settingURL);
            plus.ios.deleteObject(NSURL);
            plus.ios.deleteObject(application);
          } catch (iosError) {
            console.error('iOS原生方法打开设置失败:', iosError);
            // 备选方案
            plus.runtime.openURL('app-settings:');
          }
        } else {
          // Android
          plus.runtime.openURL(`package:${plus.runtime.appid}`);
        }
        // #endif

        // #ifndef APP-PLUS
        uni.openSetting({
          success: res => {
            console.log('打开设置成功:', res);
          },
          fail: err => {
            console.error('打开设置失败:', err);
            uni.showToast({ title: '无法打开设置页面', icon: 'none' });
          },
        });
        // #endif
      } catch (e2) {
        console.error('备选打开设置方式错误:', e2);
        uni.showToast({ title: '无法打开设置页面', icon: 'none' });
      }
    }
  } else {
    console.error('permissionOpenSettings不是函数或为null');
    // 如果permissionOpenSettings不是函数，直接尝试打开设置
    try {
      // #ifdef APP-PLUS
      const isIOS = plus.os.name === 'iOS';
      if (isIOS) {
        try {
          // 对于iOS 10+，使用新的API
          const UIApplication = (plus.ios as any).import('UIApplication');
          const application = UIApplication.sharedApplication();
          const NSURL = (plus.ios as any).import('NSURL');
          const settingURL = NSURL.URLWithString('app-settings:');

          // 检查iOS版本
          const iosVersion = plus.os.version || '9.0';
          if (parseInt(iosVersion) >= 10) {
            // iOS 10+ 使用新方法
            console.log('使用iOS 10+方法打开设置');
            const options = plus.ios.newObject('NSDictionary');
            application.openURL_options_completionHandler(settingURL, options, null);
            plus.ios.deleteObject(options);
          } else {
            // iOS 9及以下使用旧方法
            application.openURL(settingURL);
          }

          plus.ios.deleteObject(settingURL);
          plus.ios.deleteObject(NSURL);
          plus.ios.deleteObject(application);
        } catch (iosError) {
          console.error('iOS原生方法打开设置失败:', iosError);
          // 备选方案
          plus.runtime.openURL('app-settings:');
        }
      } else {
        // Android
        plus.runtime.openURL(`package:${plus.runtime.appid}`);
      }
      // #endif

      // #ifndef APP-PLUS
      uni.openSetting({
        success: res => {
          console.log('打开设置成功:', res);
        },
        fail: err => {
          console.error('打开设置失败:', err);
          uni.showToast({ title: '无法打开设置页面', icon: 'none' });
        },
      });
      // #endif
    } catch (e) {
      console.error('直接打开设置页面错误:', e);
      uni.showToast({ title: '无法打开设置页面', icon: 'none' });
    }
  }
}

// 对外暴露的方法
defineExpose({
  openCamera,
  openAlbum,
  openFilePicker,
  uploadToServer,
});
</script>

<template>
  <!-- 只保留必要的组件，不包含UI -->
  <view style="display: none">
    <!-- xe-upload组件 -->
    <xe-upload
      ref="xeUploadRef"
      :options="uploadOptions"
      @callback="handleUploadCallback"
    ></xe-upload>

    <!-- 权限弹窗组件 -->
    <LKpermissionModal
      v-model:show="permissionModalShow"
      :title="permissionModalTitle"
      :content="permissionModalContent"
      cancel-text="取消"
      confirm-text="前往设置"
      @cancel="handlePermissionCancel"
      @confirm="handlePermissionConfirm"
    />
  </view>
</template>
