<template>
  <view class="lk-date-range-picker">
    <!-- 日期区间选择器弹框 -->
    <uni-popup ref="popup" type="bottom" @close="handleCancel" background-color="#fff">
      <view class="date-range-popup">
        <!-- 弹框标题 -->
        <view class="popup-header">
          <text class="popup-title">{{ title }}</text>
          <!-- <view class="close-btn" @tap="handleCancel">
            <LkServiceImage name="close" class="close-icon" />
          </view> -->
        </view>

        <!-- 日期选择内容 -->
        <view class="date-range-content">
          <!-- 快捷选择按钮 -->
          <view class="quick-select-section">
            <view class="section-title">快捷选择</view>
            <view class="quick-buttons">
              <view
                v-for="(quick, index) in quickOptions"
                :key="index"
                class="quick-btn"
                @tap="handleQuickSelect(quick)"
              >
                <text class="quick-btn-text">{{ quick.label }}</text>
              </view>
            </view>
          </view>

          <!-- 日期选择区域 -->
          <view class="date-select-section">
            <view class="section-title">自定义日期区间</view>

            <!-- 开始日期 -->
            <view class="date-item">
              <view class="date-label">
                <text class="label-text">开始日期</text>
              </view>
              <picker
                mode="date"
                :value="internalStartDate"
                :start="minDate"
                :end="maxDate"
                @change="handleStartDateChange"
                class="date-picker"
              >
                <view class="picker-display">
                  <text class="date-text">{{ formatDisplayDate(internalStartDate) }}</text>
                  <LkServiceImage name="selectDownArrow" class="arrow-icon" />
                </view>
              </picker>
            </view>

            <!-- 结束日期 -->
            <view class="date-item">
              <view class="date-label">
                <text class="label-text">结束日期</text>
              </view>
              <picker
                mode="date"
                :value="internalEndDate"
                :start="minDate"
                :end="maxDate"
                @change="handleEndDateChange"
                class="date-picker"
              >
                <view class="picker-display">
                  <text class="date-text">{{ formatDisplayDate(internalEndDate) }}</text>
                  <LkServiceImage name="selectDownArrow" class="arrow-icon" />
                </view>
              </picker>
            </view>
          </view>

          <!-- 错误提示 -->
          <view v-if="errorMessage" class="error-message">
            <text class="error-text">{{ errorMessage }}</text>
          </view>
        </view>

        <!-- 底部操作按钮 -->
        <view class="popup-footer">
          <LkButton type="plain" size="large" shape="round" style="flex: 1" @click="handleCancel">
            取消
          </LkButton>
          <LkButton
            type="primary"
            size="large"
            shape="round"
            style="flex: 1"
            @click="handleConfirm"
          >
            确定
          </LkButton>
        </view>
      </view>
    </uni-popup>

    <!-- 全局提示组件 -->
    <LkToast ref="toastRef" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import LkServiceImage from '@/components/LkServiceImage/index.vue';
import LkToast from '@/components/LkToast/index.vue';
import LkButton from '@/components/LkButton/index.vue';

// TypeScript接口定义
interface DateRangeResult {
  startDate: string;
  endDate: string;
}

interface QuickSelectOption {
  label: string;
  startDate: string;
  endDate: string;
}

interface Props {
  startDate?: string; // 默认开始日期 YYYY-MM-DD
  endDate?: string; // 默认结束日期 YYYY-MM-DD
  minDate?: string; // 最小可选日期
  maxDate?: string; // 最大可选日期
  title?: string; // 弹框标题
}

const props = withDefaults(defineProps<Props>(), {
  startDate: '',
  endDate: '',
  minDate: '2020-01-01',
  maxDate: '2030-12-31',
  title: '选择日期区间',
});

const emit = defineEmits<{
  confirm: [result: DateRangeResult];
  cancel: [];
}>();

// 组件引用
const popup = ref();
const toastRef = ref();

// 响应式数据
const internalStartDate = ref('');
const internalEndDate = ref('');
const errorMessage = ref('');

// 日期格式化工具函数
const formatDisplayDate = (dateStr: string): string => {
  if (!dateStr) return '请选择日期';
  try {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}.${month}.${day}`;
  } catch (error) {
    return '请选择日期';
  }
};

// 将任意日期格式转换为 picker 需要的 YYYY-MM-DD 格式
const formatDateForPicker = (dateStr: string): string => {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    // 检查日期是否有效
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.warn('日期格式转换失败:', dateStr, error);
    return '';
  }
};

// 获取当前日期字符串
const getCurrentDate = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 获取指定天数前的日期
const getDateBefore = (days: number): string => {
  const date = new Date();
  date.setDate(date.getDate() - days);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 获取本月第一天
const getFirstDayOfMonth = (): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}-01`;
};

// 获取当前学期开始日期（简单实现：本学年的9月1日或2月1日）
const getCurrentSemesterStart = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;

  // 如果是9月到次年1月，学期开始是9月1日
  // 如果是2月到8月，学期开始是2月1日
  if (month >= 9 || month <= 1) {
    return `${month >= 9 ? year : year - 1}-09-01`;
  } else {
    return `${year}-02-01`;
  }
};

// 快捷选择选项
const quickOptions = computed<QuickSelectOption[]>(() => [
  {
    label: '本周',
    startDate: getDateBefore(7),
    endDate: getCurrentDate(),
  },
  {
    label: '本月',
    startDate: getFirstDayOfMonth(),
    endDate: getCurrentDate(),
  },
  // {
  //   label: '本学期',
  //   startDate: getCurrentSemesterStart(),
  //   endDate: getCurrentDate(),
  // },
]);

// 日期校验
const validateDates = (): boolean => {
  errorMessage.value = '';

  if (!internalStartDate.value || !internalEndDate.value) {
    errorMessage.value = '请选择完整的日期区间';
    return false;
  }

  const startTime = new Date(internalStartDate.value).getTime();
  const endTime = new Date(internalEndDate.value).getTime();

  if (startTime > endTime) {
    errorMessage.value = '结束日期不能早于开始日期';
    return false;
  }

  return true;
};

// 事件处理
const handleStartDateChange = (e: any) => {
  internalStartDate.value = e.detail.value;
  validateDates();
};

const handleEndDateChange = (e: any) => {
  internalEndDate.value = e.detail.value;
  validateDates();
};

const handleQuickSelect = (option: QuickSelectOption) => {
  internalStartDate.value = option.startDate;
  internalEndDate.value = option.endDate;
  validateDates();

  toastRef.value?.show({
    message: `已选择${option.label}`,
    type: 'success',
  });
};

const handleConfirm = () => {
  if (validateDates()) {
    emit('confirm', {
      startDate: internalStartDate.value,
      endDate: internalEndDate.value,
    });
    popup.value?.close();
  }
};

const handleCancel = () => {
  emit('cancel');
  popup.value?.close();
};

// 组件方法
const open = () => {
  // 初始化日期值，确保格式正确
  internalStartDate.value =
    formatDateForPicker(props.startDate) || formatDateForPicker(getCurrentSemesterStart());
  internalEndDate.value =
    formatDateForPicker(props.endDate) || formatDateForPicker(getCurrentDate());
  errorMessage.value = '';
  popup.value?.open();
};

const close = () => {
  popup.value?.close();
};

// 暴露方法给父组件
defineExpose({
  open,
  close,
});

// 监听props变化
watch(
  () => [props.startDate, props.endDate],
  ([newStartDate, newEndDate]) => {
    if (newStartDate) internalStartDate.value = formatDateForPicker(newStartDate);
    if (newEndDate) internalEndDate.value = formatDateForPicker(newEndDate);
  }
);

onMounted(() => {
  console.log('LkDateRangePicker组件已加载');
});
</script>

<style scoped lang="scss">
.lk-date-range-picker {
  // 组件根容器样式预留
}

.date-range-popup {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 32rpx 40rpx 16rpx;
  border-bottom: 1rpx solid #f1f5f9;

  .popup-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #1e293b;
  }

  .close-btn {
    position: absolute;
    right: 40rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:active {
      background: #e2e8f0;
      transform: translateY(-50%) scale(0.95);
    }

    .close-icon {
      width: 24rpx;
      height: 24rpx;
    }
  }
}

.popup-footer {
  display: flex;
  padding: 24rpx 40rpx 40rpx;
  gap: 24rpx;
  border-top: 1rpx solid #f1f5f9;
}

.date-range-content {
  flex: 1;
  padding: 32rpx 40rpx;
  background: #ffffff;
  overflow-y: auto;
}

.quick-select-section,
.date-select-section {
  margin-bottom: 48rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 24rpx;
}

.quick-buttons {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.quick-btn {
  padding: 16rpx 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    background: #e2e8f0;
  }

  .quick-btn-text {
    font-size: 28rpx;
    color: #475569;
  }
}

.date-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.date-label {
  flex-shrink: 0;
  width: 160rpx;

  .label-text {
    font-size: 30rpx;
    color: #374151;
    font-weight: 500;
  }
}

.date-picker {
  flex: 1;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  transition: all 0.2s ease;

  &:active {
    border-color: #7d4dff;
    background: #f0f5ff;
  }

  .date-text {
    font-size: 30rpx;
    color: #1e293b;
  }

  .arrow-icon {
    width: 32rpx;
    height: 32rpx;
    flex-shrink: 0;
  }
}

.error-message {
  margin-top: 16rpx;
  padding: 16rpx 20rpx;
  background: #fef2f2;
  border: 2rpx solid #fecaca;
  border-radius: 12rpx;

  .error-text {
    font-size: 26rpx;
    color: #dc2626;
  }
}

/* 多端适配优化 */
@media screen and (min-width: 768px) {
  .date-range-content {
    padding: 48rpx;
  }

  .section-title {
    font-size: 36rpx;
  }

  .quick-btn {
    padding: 20rpx 32rpx;

    .quick-btn-text {
      font-size: 32rpx;
    }
  }

  .date-label {
    width: 180rpx;

    .label-text {
      font-size: 34rpx;
    }
  }

  .picker-display {
    padding: 24rpx 28rpx;

    .date-text {
      font-size: 34rpx;
    }

    .arrow-icon {
      width: 36rpx;
      height: 36rpx;
    }
  }
}
</style>
