<script setup lang="ts">
import { ref, reactive } from 'vue';

// Props定义
interface Props {
  threshold?: number; // 滑动阈值，单位px
  velocity?: number; // 最小滑动速度
  disabled?: boolean; // 是否禁用触摸
}

const props = withDefaults(defineProps<Props>(), {
  threshold: 50,
  velocity: 0.3,
  disabled: false,
});

// Events定义
interface Emits {
  slideLeft: [];
  slideRight: [];
  touchStart: [event: TouchEvent];
  touchEnd: [event: TouchEvent];
  sliding: [data: { deltaX: number; progress: number; direction: 'left' | 'right' | 'none' }];
}

const emit = defineEmits<Emits>();

// 触摸状态管理
const touchState = reactive({
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
  startTime: 0,
  isMoving: false,
  isTouching: false,
});

// 防抖标记
const isProcessing = ref(false);

// 触摸开始事件处理
const handleTouchStart = (event: TouchEvent) => {
  if (props.disabled || isProcessing.value) return;

  const touch = event.touches[0];
  touchState.startX = touch.clientX;
  touchState.startY = touch.clientY;
  touchState.startTime = Date.now();
  touchState.isMoving = false;
  touchState.isTouching = true;

  // 发射触摸开始事件
  emit('touchStart', event);
};

// 触摸移动事件处理
const handleTouchMove = (event: TouchEvent) => {
  if (props.disabled || !touchState.isTouching || isProcessing.value) return;

  const touch = event.touches[0];
  touchState.endX = touch.clientX;
  touchState.endY = touch.clientY;
  touchState.isMoving = true;

  // 计算移动距离
  const deltaX = touchState.endX - touchState.startX;
  const deltaY = touchState.endY - touchState.startY;
  const absDeltaX = Math.abs(deltaX);
  const absDeltaY = Math.abs(deltaY);

  // 如果水平移动距离大于垂直移动距离，阻止默认滚动行为
  if (absDeltaX > absDeltaY && absDeltaX > 10) {
    event.preventDefault();

    // 计算滑动进度 (0-1之间)
    const progress = Math.min(absDeltaX / (props.threshold * 3), 1);

    // 判断滑动方向
    let direction: 'left' | 'right' | 'none' = 'none';
    if (absDeltaX > 5) {
      direction = deltaX > 0 ? 'right' : 'left';
    }

    // 发射实时滑动状态
    emit('sliding', {
      deltaX,
      progress,
      direction,
    });
  }
};

// 触摸结束事件处理
const handleTouchEnd = (event: TouchEvent) => {
  if (props.disabled || !touchState.isTouching || isProcessing.value) return;

  touchState.isTouching = false;

  // 如果没有移动，直接退出
  if (!touchState.isMoving) {
    emit('touchEnd', event);
    return;
  }

  // 计算滑动距离和时间
  const deltaX = touchState.endX - touchState.startX;
  const deltaY = touchState.endY - touchState.startY;
  const deltaTime = Date.now() - touchState.startTime;
  const absDeltaX = Math.abs(deltaX);
  const absDeltaY = Math.abs(deltaY);

  // 计算滑动速度 (px/ms)
  const velocity = absDeltaX / deltaTime;

  // 判断是否为有效的水平滑动
  const isHorizontalSlide =
    absDeltaX > absDeltaY && absDeltaX > props.threshold && velocity > props.velocity;

  if (isHorizontalSlide) {
    // 防抖处理
    isProcessing.value = true;

    // 判断滑动方向并发射相应事件
    if (deltaX > 0) {
      emit('slideRight');
      console.log('检测到右滑手势');
    } else {
      emit('slideLeft');
      console.log('检测到左滑手势');
    }

    // 重置防抖标记
    setTimeout(() => {
      isProcessing.value = false;
    }, 300);
  }

  // 发射触摸结束事件
  emit('touchEnd', event);

  // 重置滑动状态
  emit('sliding', {
    deltaX: 0,
    progress: 0,
    direction: 'none',
  });

  // 重置触摸状态
  touchState.isMoving = false;
};

// 触摸取消事件处理
const handleTouchCancel = (event: TouchEvent) => {
  touchState.isTouching = false;
  touchState.isMoving = false;

  // 重置滑动状态
  emit('sliding', {
    deltaX: 0,
    progress: 0,
    direction: 'none',
  });

  emit('touchEnd', event);
};
</script>

<template>
  <view
    class="lk-touch-container"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchCancel"
  >
    <slot />
  </view>
</template>

<style scoped lang="scss">
.lk-touch-container {
  width: 100%;
  height: 100%;
  position: relative;
  touch-action: pan-y; /* 允许垂直滚动，限制水平滚动 */

  /* 硬件加速优化 */
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  -webkit-backface-visibility: hidden;

  /* 禁用文本选择和长按菜单 */
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

/* H5端特定样式 */
/* #ifdef H5 */
.lk-touch-container {
  touch-action: pan-y pinch-zoom; /* H5端支持缩放 */
}
/* #endif */

/* 小程序端特定样式 */
/* #ifdef MP */
.lk-touch-container {
  touch-action: pan-y; /* 小程序端限制滑动 */
}
/* #endif */

/* APP端特定样式 */
/* #ifdef APP-PLUS */
.lk-touch-container {
  touch-action: manipulation; /* APP端优化触摸响应 */
}
/* #endif */
</style>
