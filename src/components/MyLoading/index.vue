<template>
  <view
    v-if="show"
    class="my-loading"
    :class="[
      `size-${size}`,
      `type-${type}`,
      {
        fullscreen: fullscreen,
        overlay: overlay,
      },
    ]"
    :style="customStyle"
  >
    <!-- 加载动画 -->
    <view class="loading-content">
      <!-- 旋转动画 -->
      <view v-if="type === 'spinner'" class="loading-spinner"></view>

      <!-- 点状动画 -->
      <view v-else-if="type === 'dots'" class="loading-dots">
        <view class="dot" v-for="i in 3" :key="i"></view>
      </view>

      <!-- 脉冲动画 -->
      <view v-else-if="type === 'pulse'" class="loading-pulse"></view>

      <!-- 默认旋转动画 -->
      <view v-else class="loading-spinner"></view>

      <!-- 加载文字 -->
      <text v-if="text" class="loading-text">{{ text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { CSSProperties } from 'vue';

// 定义组件属性
interface Props {
  // 是否显示加载状态
  show?: boolean;
  // 加载提示文字
  text?: string;
  // 加载动画类型：spinner(旋转)、dots(点状)、pulse(脉冲)
  type?: 'spinner' | 'dots' | 'pulse';
  // 尺寸：small、medium、large
  size?: 'small' | 'medium' | 'large';
  // 是否全屏显示
  fullscreen?: boolean;
  // 是否显示遮罩层
  overlay?: boolean;
  // 自定义样式
  customStyle?: CSSProperties;
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  show: true,
  text: '加载中...',
  type: 'spinner',
  size: 'medium',
  fullscreen: false,
  overlay: false,
  customStyle: () => ({}),
});
</script>

<style lang="scss" scoped>
.my-loading {
  display: flex;
  align-items: center;
  justify-content: center;

  // 全屏模式
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
  }

  // 遮罩层模式
  &.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  // 尺寸设置
  &.size-small {
    .loading-spinner {
      width: 40rpx;
      height: 40rpx;
      border-width: 4rpx;
    }

    .loading-dots .dot {
      width: 12rpx;
      height: 12rpx;
    }

    .loading-pulse {
      width: 40rpx;
      height: 40rpx;
    }

    .loading-text {
      font-size: 24rpx;
      margin-top: 16rpx;
    }
  }

  &.size-medium {
    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border-width: 6rpx;
    }

    .loading-dots .dot {
      width: 16rpx;
      height: 16rpx;
    }

    .loading-pulse {
      width: 60rpx;
      height: 60rpx;
    }

    .loading-text {
      font-size: 28rpx;
      margin-top: 20rpx;
    }
  }

  &.size-large {
    .loading-spinner {
      width: 80rpx;
      height: 80rpx;
      border-width: 8rpx;
    }

    .loading-dots .dot {
      width: 20rpx;
      height: 20rpx;
    }

    .loading-pulse {
      width: 80rpx;
      height: 80rpx;
    }

    .loading-text {
      font-size: 32rpx;
      margin-top: 24rpx;
    }
  }

  // 旋转动画
  .loading-spinner {
    border: 6rpx solid rgba(125, 77, 255, 0.2);
    border-top: 6rpx solid #7d4dff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  // 点状动画
  .loading-dots {
    display: flex;
    align-items: center;
    gap: 8rpx;

    .dot {
      width: 16rpx;
      height: 16rpx;
      background-color: #7d4dff;
      border-radius: 50%;
      animation: dots 1.4s ease-in-out infinite both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }

      &:nth-child(3) {
        animation-delay: 0s;
      }
    }
  }

  // 脉冲动画
  .loading-pulse {
    width: 60rpx;
    height: 60rpx;
    background-color: #7d4dff;
    border-radius: 50%;
    animation: pulse 1.2s ease-in-out infinite;
  }

  // 加载文字
  .loading-text {
    font-size: 28rpx;
    color: #86909c;
    text-align: center;
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 点状动画
@keyframes dots {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}
</style>
