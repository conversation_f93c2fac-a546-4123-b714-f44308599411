<script setup lang="ts">
import { computed, inject } from 'vue';
import type { PropType, Ref } from 'vue';
import type { TreeItem } from './types';
import LkSvg from '@/components/svg/index.vue';

// 为组件命名，以便在模板中进行递归调用
defineOptions({
  name: 'LkTreeNode',
});

const props = defineProps({
  node: {
    type: Object as PropType<TreeItem>,
    required: true,
  },
  level: {
    type: Number,
    default: 0,
  },
});

// --- 注入来自父组件的状态 ---
interface TreeState {
  selectedKey: Ref<string | number | null>;
  expandedKeys: Ref<Set<string | number>>;
  onNodeSelect: (item: TreeItem, level: number) => void;
  onNodeToggle: (item: TreeItem) => void;
}

// 从上层LkTree组件注入状态和方法
const treeState = inject<TreeState>('treeState');

if (!treeState) {
  throw new Error('LkTreeNode 必须是 LkTree 的后代组件');
}

const { selectedKey, expandedKeys, onNodeSelect, onNodeToggle } = treeState;

const handleSelect = () => {
  onNodeSelect(props.node, props.level);
};

const handleToggle = () => {
  onNodeToggle(props.node);
};

// --- 计算属性 ---
const isSelected = computed(() => selectedKey?.value === props.node.id);
const isExpanded = computed(() => expandedKeys?.value.has(props.node.id));
const isExpandable = computed(() => !props.node.isLeaf);

const indentationStyle = computed(() => ({
  // 基础内边距 + 层级缩进
  paddingLeft: `${props.level * 24 + 16}px`,
}));
</script>

<template>
  <div class="lk-tree-node-wrapper">
    <div
      class="node-item"
      :class="{ selected: isSelected }"
      :style="indentationStyle"
      @click="handleSelect"
    >
      <div class="node-content">
        <span
          class="toggle-arrow"
          :class="{ 'is-expanded': isExpanded, 'is-visible': isExpandable }"
          @click.stop="handleToggle"
        >
          <LkSvg src="/static/database/tree_down.svg" width="16px" height="16px"></LkSvg>
        </span>
        <span class="node-icon">
          <!-- 节点图标 -->
          <LkSvg
            v-if="node.fileType === 2"
            src="/static/database/tree_folder.svg"
            width="22px"
            height="22px"
          ></LkSvg>
          <LkSvg v-else src="/static/database/tree_space.svg" width="22px" height="22px"></LkSvg>
        </span>
        <span class="node-name">{{ node.name }}</span>
      </div>
    </div>

    <!-- 递归渲染子节点 -->
    <template v-if="isExpanded && isExpandable">
      <LkTreeNode
        v-for="childNode in node.children"
        :key="childNode.id"
        :node="childNode"
        :level="level + 1"
      />
    </template>
  </div>
</template>

<style lang="scss" scoped>
.node-item {
  display: flex;
  align-items: center;
  height: 48px;
  cursor: pointer;
  color: #4e5969;
  font-size: 14px;
  transition: background-color 0.2s ease;
  padding-right: 16px;
  border-radius: 8px;
  &:hover {
    background-color: #f7f8fa;
  }

  &.selected {
    background-color: #f2f3f5;
    color: #1d2129;
    font-weight: 500;
  }
}

.node-content {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.toggle-arrow {
  position: relative;
  width: 12px;
  height: 12px;
  margin-right: 8px;
  flex-shrink: 0;
  transform: rotate(-90deg); /* 默认状态：箭头向右（折叠） */
  transition: transform 0.2s ease-in-out;
  visibility: hidden; // 默认隐藏

  // 只有可展开的节点才显示箭头
  &.is-visible {
    visibility: visible;
  }

  // 展开时旋转箭头
  &.is-expanded {
    transform: rotate(0deg); /* 展开状态：箭头向下 */
  }
}

.node-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  svg {
    // 调整图标颜色以匹配设计
    rect {
      fill: #e8eaff;
      stroke: #a0b5ff;
    }
  }
}
.node-name {
  font-size: 16px;
}
</style>
