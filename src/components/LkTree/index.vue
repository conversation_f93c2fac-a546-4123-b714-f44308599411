<script setup lang="ts">
import { ref, provide, watch, computed } from 'vue';
import type { PropType } from 'vue';
import LkTreeNode from './LkTreeNode.vue';
import LkButton from '@/components/LkButton/index.vue';
import type { TreeItem } from './types';

const props = defineProps({
  /**
   * 控制弹窗显示
   */
  show: {
    type: Boolean,
    default: false,
  },
  /**
   * 树形结构数据
   */
  data: {
    type: Array as PropType<TreeItem[]>,
    default: () => [],
  },
  /**
   * 组件标题
   */
  title: {
    type: String,
    default: '学校数据空间',
  },
  /**
   * 异步加载子节点的回调函数
   */
  onLoadChildren: {
    type: Function as PropType<(node: TreeItem) => Promise<void>>,
  },
  /**
   * 是否显示保存按钮
   */
  isSaveBtn: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:show', 'select', 'save']);

// --- 状态管理 ---
const selectedKey = ref<string | number | null>(null);
const expandedKeys = ref<Set<string | number>>(new Set());
const selectedNodeLevel = ref<number | null>(null);

// --- 监听弹窗显示状态，重置内部状态 ---
watch(
  () => props.show,
  newVal => {
    if (newVal) {
      // 弹窗打开时，重置展开、选中状态
      expandedKeys.value.clear();
      selectedKey.value = null;
      selectedNodeLevel.value = null;
    }
  }
);

// --- 计算属性 ---
const isSaveDisabled = computed(() => {
  if (selectedKey.value === null) return true; // 未选择任何节点时禁用
  if (selectedNodeLevel.value === null) return false;
  return selectedNodeLevel.value >= 3;
});

const allRootsHaveNoChildren = computed(() => {
  if (!props.data || props.data.length === 0) {
    return true;
  }
  // 如果一个节点没有被明确标记为 isLeaf: true，我们就假定它可以有子节点。
  return props.data.every(node => node.isLeaf);
});

// --- 事件处理 ---
const handleNodeSelect = (item: TreeItem, level: number) => {
  selectedKey.value = item.id;
  selectedNodeLevel.value = level;
  emit('select', item);
  if (!props.isSaveBtn && item.isLeaf) {
    handleClose();
  }
};

const handleNodeToggle = async (item: TreeItem) => {
  if (expandedKeys.value.has(item.id)) {
    expandedKeys.value.delete(item.id);
  } else {
    // 如果是可展开节点且子节点未加载
    if (!item.isLeaf && !item.childrenLoaded) {
      if (props.onLoadChildren) {
        await props.onLoadChildren(item);
      }
    }
    expandedKeys.value.add(item.id);
  }
};

const handleClose = () => {
  emit('update:show', false);
};

const handleSave = () => {
  if (isSaveDisabled.value) {
    if (selectedKey.value === null) {
      uni.showToast({
        title: '非空间文件不允许恢复到根级目录上',
        icon: 'none',
      });
    } else {
      uni.showToast({
        title: '不能保存至超过三级的空间',
        icon: 'none',
      });
    }
    return;
  }
  emit('save'); // 触发保存事件
  handleClose(); // 关闭弹窗
};

// --- 依赖注入 ---
provide('treeState', {
  selectedKey,
  expandedKeys,
  onNodeSelect: handleNodeSelect,
  onNodeToggle: handleNodeToggle,
});
</script>

<template>
  <u-popup :show="props.show" @close="handleClose" mode="bottom" :round="12">
    <view class="lk-tree-wrapper">
      <view class="lk-tree-header">
        <text class="title">{{ props.title }}</text>
        <LkSvg src="/static/database/close.svg" width="24px" height="24px" @click="handleClose" />
      </view>
      <scroll-view
        scroll-y
        class="lk-tree-body"
        :class="{ 'all-roots-no-children': allRootsHaveNoChildren }"
      >
        <LkTreeNode v-for="node in props.data" :key="node.id" :node="node" :level="0" />
      </scroll-view>
      <view v-if="props.isSaveBtn" class="lk-tree-footer">
        <LkButton
          shape="round"
          class="save-btn"
          :class="{ disabled: isSaveDisabled }"
          @click="handleSave"
          >保存当前位置</LkButton
        >
      </view>
    </view>
  </u-popup>
</template>

<style lang="scss" scoped>
.lk-tree-wrapper {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 70vh; /* 使用视窗高度，使其在不同设备上表现一致 */
  border-radius: 12px 12px 0px 0px;
}

.lk-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
  }
}

.lk-tree-body {
  flex-grow: 1;
  overflow-y: auto;
  padding: 8px 16px;
  &.all-roots-no-children {
    margin-left: -36px;
  }
}

.lk-tree-footer {
  padding: 0 16px;
  .save-btn {
    width: 100%;
    margin: 14px 0;
    flex-shrink: 0;
    height: 48px;
    &.disabled {
      opacity: 0.5;
    }
  }
}
</style>
