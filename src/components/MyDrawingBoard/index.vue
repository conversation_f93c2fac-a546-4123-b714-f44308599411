<template>
  <view
    class="my-drawing-board"
    :style="{ width: props.customWidth || '750rpx', height: props.customHeight || '100vh' }"
  >
    <!-- 顶部工具栏 -->
    <view class="header">
      <view class="header-left">
        <text class="save-btn" @tap="save">保存</text>
        <view class="header-divider"></view>
        <view class="undo-btn" @tap="handleClick(menuItems[4])">
          <image
            class="icon"
            src="/static/homework/drawing_board/undo_icon.svg"
            mode="aspectFit"
          ></image>
        </view>
      </view>
      <view class="header-right">
        <text class="clear-btn" @tap="handleClick(menuItems[5])">清空</text>
        <view class="close-btn" @tap="closeDrawing">
          <image
            class="close-icon"
            src="/static/homework/drawing_board/close_icon.svg"
            mode="aspectFit"
          >
          </image>
        </view>
      </view>
    </view>

    <!-- 插槽区域 1 -->
    <view class="area-container" v-if="$slots.area1">
      <slot name="area1"></slot>
    </view>

    <!-- 画布区域 -->
    <view class="canvas-container">
      <canvas
        canvas-id="canvasId"
        id="canvasId"
        :disable-scroll="true"
        :style="{ width: '100%', height: '100%' }"
        @touchstart="touchstart"
        @touchmove="touchmove"
        @touchend="touchend"
      ></canvas>

      <!-- 橡皮擦指示器 -->
      <view
        v-if="eraserIndicator.visible && isEraser"
        class="eraser-indicator"
        :style="{
          left: eraserIndicator.x + 'px',
          top: eraserIndicator.y + 'px',
          width: eraserIndicator.size + 'px',
          height: eraserIndicator.size + 'px',
          transform: 'translate(-50%, -50%)',
        }"
      ></view>
    </view>

    <!-- 底部工具栏区域 -->
    <view class="toolbar-wrapper">
      <view
        class="toolbar"
        ref="toolbarRef"
        :style="{
          transform: `translate(${toolbarDrag.x}px, ${toolbarDrag.y}px)`,
        }"
        @touchstart="onToolbarTouchStart"
        @touchmove="onToolbarTouchMove"
        @touchend="onToolbarTouchEnd"
      >
        <view class="tool-group">
          <!-- 背景色选择工具 -->
          <view class="tool-item" :class="{ active: setup === 1 }" @tap="selectTool('bgcolor')">
            <image
              class="tool-icon"
              style="width: 43rpx; height: 43rpx"
              src="/static/homework/drawing_board/paint_icon.svg"
              mode="aspectFit"
            >
            </image>
            <image
              class="dropdown-arrow"
              src="/static/homework/drawing_board/arrow_down.svg"
              mode="aspectFit"
            >
            </image>
          </view>
          <!-- 分隔线 -->
          <view class="divider"></view>
          <!-- 文本工具（线宽） -->
          <view class="tool-item" :class="{ active: setup === 3 }" @tap="selectTool('text')">
            <image
              class="tool-icon"
              src="/static/homework/drawing_board/weight_icon.svg"
              mode="aspectFit"
            >
            </image>
            <image
              class="dropdown-arrow"
              src="/static/homework/drawing_board/arrow_down.svg"
              mode="aspectFit"
            >
            </image>
          </view>
          <!-- 分隔线 -->
          <view class="divider"></view>
          <!-- 颜色选择器（文字颜色） -->
          <view class="tool-item" :class="{ active: setup === 2 }" @tap="selectTool('color')">
            <view class="color-picker">
              <view class="color-circle" :style="{ backgroundColor: lineColor }"></view>
            </view>
            <image
              class="dropdown-arrow"
              src="/static/homework/drawing_board/arrow_down.svg"
              mode="aspectFit"
            >
            </image>
          </view>
          <!-- 分隔线 -->
          <view class="divider"></view>
          <!-- 橡皮擦工具 -->
          <view
            class="tool-item"
            :class="{ active: isEraser || setup === 4 }"
            @tap="selectTool('eraser')"
          >
            <image
              class="tool-icon"
              style="width: 43rpx; height: 43rpx"
              src="/static/homework/drawing_board/eraser_icon.svg"
              mode="aspectFit"
            >
            </image>
          </view>
        </view>

        <!-- 统一的设置面板 -->
        <view
          class="setting-panel"
          v-if="setup === 1 || setup === 2 || setup === 3 || setup === 4"
          :style="{
            bottom: '100%',
            left: '50%',
            transform: 'translateX(-50%)',
            'margin-bottom': '20rpx',
          }"
        >
          <!-- 线条粗细设置 -->
          <template v-if="setup === 3">
            <text class="setting-title">粗细</text>
            <text class="setting-value">{{ lineWidth }}px</text>
            <view class="slider-container">
              <slider
                class="custom-slider"
                min="1"
                max="20"
                step="1"
                block-size="18"
                activeColor="#E5E7EB"
                block-color="#fff"
                backgroundColor="#E5E7EB"
                :value="lineWidth"
                @changing="sliderChange"
                @change="sliderChange"
              >
              </slider>
            </view>
          </template>

          <!-- 背景颜色选择 -->
          <template v-if="setup === 1">
            <text class="setting-title">背景色</text>
            <view class="color-grid">
              <view
                v-for="(color, index) in props.colorList"
                :key="'bg-' + index"
                class="color-item"
                :class="{
                  active:
                    canvasBgColor === color || validateBackgroundColor(color) === canvasBgColor,
                }"
                :style="{ backgroundColor: color }"
                @tap="changeColor(color)"
              ></view>
            </view>
          </template>

          <!-- 文字颜色选择 -->
          <template v-if="setup === 2">
            <text class="setting-title">文字颜色</text>
            <view class="color-grid">
              <view
                v-for="(color, index) in props.colorList"
                :key="'text-' + index"
                class="color-item"
                :class="{ active: lineColor === color }"
                :style="{ backgroundColor: color }"
                @tap="changeColor(color)"
              >
              </view>
            </view>
          </template>

          <!-- 橡皮擦大小设置 -->
          <template v-if="setup === 4">
            <text class="setting-title">橡皮擦大小</text>
            <text class="setting-value">{{ eraserSize }}px</text>
            <view class="slider-container">
              <slider
                class="custom-slider"
                min="20"
                max="50"
                step="1"
                block-size="18"
                activeColor="#E5E7EB"
                block-color="#fff"
                backgroundColor="#E5E7EB"
                :value="eraserSize"
                @changing="eraserSizeChange"
                @change="eraserSizeChange"
              >
              </slider>
            </view>
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, getCurrentInstance, nextTick } from 'vue';
import type {
  Position,
  DrawingRecord,
  MenuItem,
  TouchEvent,
  SliderEvent,
  DrawingData,
  ExportResult,
  EraserIndicator,
} from './types';

/** 组件属性接口 */
export interface DrawingBoardProps {
  width?: number;
  height?: number;
  lineWidth?: number;
  lineColor?: string;
  bgColor?: string;
  colorList?: string[];
  data?: DrawingData;
  customWidth?: string;
  customHeight?: string;
  debug?: boolean; // 是否开启调试模式
}

// 为uni-app声明全局类型
declare const uni: any;

// 获取当前组件实例
const instance = getCurrentInstance();

// 定义Props
const props = withDefaults(defineProps<DrawingBoardProps>(), {
  width: 375,
  height: 400,
  lineWidth: 1, // 默认线宽改为1
  lineColor: '#000000',
  bgColor: '#ffffff', // 默认白色背景，不允许透明
  colorList: () => [
    '#000000',
    '#ffffff',
    '#E63E34',
    '#E76711',
    '#EFCC00',
    '#65BA4F',
    '#6CC2DC',
    '#649BFB',
  ],
  customWidth: '',
  customHeight: '',
  debug: false, // 默认不开启调试模式
});

// 定义Emits
const emit = defineEmits<{
  (e: 'save', value: ExportResult): void;
  (e: 'close'): void;
}>();

// 验证背景色是否有效
const validateBackgroundColor = (color: string): string => {
  // 检查是否为透明色或无效色
  const transparentColors = [
    'transparent',
    'rgba(0,0,0,0)',
    'rgba(255,255,255,0)',
    'rgba(0,0,0,0.0)',
    'rgba(255,255,255,0.0)',
  ];
  if (transparentColors.includes(color.toLowerCase())) {
    return props.bgColor || '#ffffff';
  }
  // 允许正常的颜色值通过
  return color;
};

// 画板状态
const ctx = ref<any>(null); // uniapp的canvas上下文类型较特殊
const canvasBgColor = ref(validateBackgroundColor(props.bgColor));
const lineColor = ref(props.lineColor);
const lineWidth = ref(props.lineWidth);
const eraserSize = ref(Math.max(20, props.lineWidth)); // 橡皮擦大小，最小值为20
const setup = ref(0); // 默认不激活任何工具
const isEraser = ref(false);
const showExportOptions = ref(false);
const realWidth = ref(props.width);
const realHeight = ref(props.height);

// 绘制状态
const start = reactive<Position>({ x: 0, y: 0 });
const end = reactive<Position>({ x: 0, y: 0 });
const temp = ref<DrawingRecord[]>([]);
const lineArr = ref<DrawingRecord[][]>([]);

// 橡皮擦指示器状态
const eraserIndicator = reactive<EraserIndicator>({
  x: 0,
  y: 0,
  size: 0,
  visible: false,
});

// 橡皮擦指示器更新节流
let lastIndicatorUpdate = 0;
const INDICATOR_UPDATE_INTERVAL = 32; // 约30fps，在性能和流畅度间平衡

// 工具栏拖动状态
const toolbarDrag = reactive({
  x: 0,
  y: 0,
  isDragging: false,
  startX: 0,
  startY: 0,
});

// 菜单项配置
const menuItems = reactive<MenuItem[]>([
  { label: '背景色', icon: 'icon-beijingse', action: 1, type: 1 },
  { label: '文字颜色', icon: 'icon-wenziyanse', action: 2, type: 1 },
  { label: '大小调整', icon: 'icon-wenzidaxiao2', action: 3, type: 1 },
  { label: '橡皮擦', icon: 'icon-qingkong_huaban1', action: 4, type: 1 },
  { label: '撤回操作', icon: 'icon-chehui', action: null, type: 2 },
  { label: '清空画板', icon: 'icon-qingkong_huaban1', action: null, type: 3 },
]);

// 调试日志函数
const log = (message: string, ...args: any[]) => {
  if (props.debug) {
    console.log(`[MyDrawingBoard] ${message}`, ...args);
  }
};

// 初始化画布
const init = () => {
  if (!ctx.value) return;
  log('初始化画布');

  // 使用当前的背景色，确保切换能正常工作
  const bgColor = canvasBgColor.value;
  log('应用背景色:', bgColor);

  ctx.value.fillStyle = bgColor;
  ctx.value.fillRect(0, 0, realWidth.value, realHeight.value);
  ctx.value.draw(true);
};

// 重新绘制所有线条
const restoreLine = () => {
  if (!ctx.value) return;
  if (lineArr.value.length === 0) return;

  log('重新绘制线条，线条数量:', lineArr.value.length);
  ctx.value.beginPath();
  lineArr.value.forEach(item => {
    if (item.length > 1) {
      const firstItem = item[0] as DrawingRecord;

      if (firstItem.isEraser) {
        ctx.value.strokeStyle = canvasBgColor.value;
      } else if (firstItem.color) {
        ctx.value.strokeStyle = firstItem.color;
      }

      ctx.value.lineWidth = firstItem.width;

      // 设置起始点
      const secondItem = item[1] as DrawingRecord;
      if (secondItem.x !== undefined && secondItem.y !== undefined) {
        ctx.value.moveTo(secondItem.x, secondItem.y);

        // 绘制路径中的所有点
        item.forEach((point, index) => {
          if (index === 0) return;

          if (point.x !== undefined && point.y !== undefined) {
            ctx.value.lineTo(point.x, point.y);
          }
        });

        ctx.value.stroke();
        ctx.value.draw(true);
      }
    }
  });
};

// 从导入数据恢复画板
const restoreFromData = (data: DrawingData) => {
  if (!data) return;
  log('正在从数据恢复画板:', data);

  // 确保ctx已初始化
  if (!ctx.value) {
    console.log('Canvas上下文尚未初始化');
    return;
  }

  // 优先使用data中的背景色，如果没有则使用props.bgColor
  if (data.bgColor) {
    const bgColor = validateBackgroundColor(data.bgColor);
    canvasBgColor.value = bgColor;
    log('从data恢复背景色:', bgColor);
  } else {
    const bgColor = validateBackgroundColor(props.bgColor || '#ffffff');
    canvasBgColor.value = bgColor;
    log('使用props背景色:', bgColor);
  }

  // 只恢复线条颜色，线条粗细始终使用组件默认值
  lineColor.value = data.lineColor;
  // lineWidth.value = data.lineWidth; // 不再从数据中恢复线条粗细
  log('线条粗细保持默认值:', lineWidth.value, '(数据中的值被忽略:', data.lineWidth, ')');
  lineArr.value = [...data.lines]; // 使用解构拷贝确保数据完整复制

  // 立即重绘
  init();
  setTimeout(() => {
    log('重新绘制线条，线条数量:', lineArr.value.length);
    restoreLine();
  }, 300); // 给一点延迟确保初始化完成
};

// 对外暴露的方法：设置画板内容
const setDrawingData = (data: DrawingData) => {
  if (!data) {
    console.error('设置画板内容失败：数据为空');
    return false;
  }

  try {
    log('通过setDrawingData方法设置画板数据', data);
    restoreFromData(data);
    return true;
  } catch (err) {
    console.error('设置画板内容失败:', err);
    return false;
  }
};

// 打开/关闭调试模式
const setDebugMode = (enabled: boolean) => {
  log(`${enabled ? '开启' : '关闭'}调试模式`);
  return enabled;
};

// 监听Props变化
// watch(
//   () => props.bgColor,
//   newVal => {
//     canvasBgColor.value = newVal;
//     if (ctx.value) {
//       init(); // 背景色变化时重新初始化画布
//       restoreLine(); // 重新绘制线条
//     }
//   },
//   { immediate: true }
// );

// watch(
//   () => props.lineColor,
//   newVal => {
//     lineColor.value = newVal;
//   },
//   { immediate: true }
// );

// watch(
//   () => props.lineWidth,
//   newVal => {
//     lineWidth.value = newVal;
//   },
//   { immediate: true }
// );

// 监听数据变化，用于导入数据
watch(
  () => props.data,
  newData => {
    if (newData) {
      log('检测到初始数据，开始恢复');
      restoreFromData(newData);
    }
  },
  { immediate: true }
);

watch(
  () => props.debug,
  newVal => {
    setDebugMode(newVal);
  },
  { immediate: true }
);

// 菜单点击处理
const handleClick = (item: MenuItem) => {
  if (item.action !== null) {
    setup.value = item.action;
    if (item.action !== 4) {
      isEraser.value = false;
    } else {
      isEraser.value = true;
    }
  }

  if (item.action === null) {
    if (item.type === 2) {
      // 撤回操作
      log('撤销上一步操作');
      lineArr.value.pop();
      init();
      restoreLine();
    }
    if (item.type === 3) {
      // 清空画板
      log('清空画板');
      lineArr.value = [];
      // 清空时重置所有颜色和线宽为默认值
      canvasBgColor.value = validateBackgroundColor(props.bgColor || '#ffffff');
      lineColor.value = props.lineColor;
      lineWidth.value = props.lineWidth;
      eraserSize.value = Math.max(20, props.lineWidth); // 重置橡皮擦大小，最小值为20
      // 重置工具状态
      setup.value = 0;
      isEraser.value = false;
      eraserIndicator.visible = false;
      log('重置为默认值:', {
        bgColor: canvasBgColor.value,
        lineColor: lineColor.value,
        lineWidth: lineWidth.value,
      });
      init();
    }
  }
};

// 工具选择
const selectTool = (tool: string) => {
  log('选择工具:', tool);
  if (tool === 'bgcolor') {
    if (setup.value === 1) {
      setup.value = -1;
    } else {
      setup.value = 1;
      isEraser.value = false;
      eraserIndicator.visible = false;
    }
  } else if (tool === 'pen') {
    if (setup.value === 0 && !isEraser.value) {
      setup.value = -1;
      isEraser.value = false;
      eraserIndicator.visible = false;
    } else {
      setup.value = 0;
      isEraser.value = false;
      eraserIndicator.visible = false;
    }
  } else if (tool === 'text') {
    if (setup.value === 3) {
      setup.value = -1;
    } else {
      setup.value = 3;
      isEraser.value = false;
      eraserIndicator.visible = false;
    }
  } else if (tool === 'color') {
    if (setup.value === 2) {
      setup.value = -1;
    } else {
      setup.value = 2;
      isEraser.value = false;
      eraserIndicator.visible = false;
    }
  } else if (tool === 'eraser') {
    if (isEraser.value) {
      isEraser.value = false;
      setup.value = -1;
      eraserIndicator.visible = false;
    } else {
      isEraser.value = true;
      setup.value = 4;
    }
  }
};

// 触摸处理
const touchstart = (e: any) => {
  if (!ctx.value) return;

  ctx.value.beginPath();
  start.x = e.touches[0].x;
  start.y = e.touches[0].y;

  if (isEraser.value) {
    temp.value.push({ isEraser: true, width: eraserSize.value });
    // 显示橡皮擦指示器，设置合适的大小
    eraserIndicator.x = e.touches[0].x;
    eraserIndicator.y = e.touches[0].y;
    // 指示器大小与橡皮擦实际大小一致，最小20px
    eraserIndicator.size = Math.max(20, eraserSize.value);
    eraserIndicator.visible = true;
  } else {
    temp.value.push({ color: lineColor.value, width: lineWidth.value });
  }

  temp.value.push({ x: e.touches[0].x, y: e.touches[0].y } as any);

  if (isEraser.value) {
    ctx.value.strokeStyle = canvasBgColor.value;
    ctx.value.lineWidth = eraserSize.value;
  } else {
    ctx.value.strokeStyle = lineColor.value;
    ctx.value.lineWidth = lineWidth.value;
  }
  ctx.value.lineCap = 'round';

  log('触摸开始:', { x: start.x, y: start.y });
};

const touchmove = (e: any) => {
  end.x = e.touches[0].x;
  end.y = e.touches[0].y;
  temp.value.push({ x: end.x, y: end.y } as any);

  // 节流更新橡皮擦指示器位置，减少性能消耗
  if (isEraser.value) {
    const now = Date.now();
    if (now - lastIndicatorUpdate >= INDICATOR_UPDATE_INTERVAL) {
      eraserIndicator.x = e.touches[0].x;
      eraserIndicator.y = e.touches[0].y;
      lastIndicatorUpdate = now;
    }
  }

  draw();

  if (props.debug) {
    log('触摸移动:', { x: end.x, y: end.y });
  }
};

const touchend = () => {
  log('触摸结束，线条点数:', temp.value.length);
  start.x = 0;
  start.y = 0;
  end.x = 0;
  end.y = 0;

  // 隐藏橡皮擦指示器
  if (isEraser.value) {
    eraserIndicator.visible = false;
  }

  // 保存当前绘制的线条
  lineArr.value.push([...temp.value]);
  temp.value = [];
};

// 绘制线条
const draw = () => {
  if (!ctx.value) return;

  ctx.value.moveTo(start.x, start.y);
  ctx.value.lineTo(end.x, end.y);
  ctx.value.stroke();
  ctx.value.draw(true);

  // 更新起点为当前终点，准备下一次绘制
  start.x = end.x;
  start.y = end.y;
};

// 滑块改变处理
const sliderChange = (e: SliderEvent) => {
  lineWidth.value = e.detail.value;
  // 如果当前是橡皮擦模式，更新指示器大小
  if (isEraser.value) {
    // 确保指示器大小合理，最小20px，最大60px
    eraserIndicator.size = Math.max(20, Math.min(60, lineWidth.value * 2));
  }
  log('线宽变更:', lineWidth.value);
};

// 橡皮擦大小改变处理
const eraserSizeChange = (e: SliderEvent) => {
  eraserSize.value = Math.max(20, e.detail.value); // 确保最小值为20
  // 如果当前是橡皮擦模式，更新指示器大小
  if (isEraser.value) {
    eraserIndicator.size = Math.max(20, eraserSize.value); // 指示器大小与橡皮擦实际大小一致，最小20px
  }
  log('橡皮擦大小变更:', eraserSize.value);
};

// 工具栏拖动处理
const onToolbarTouchStart = (e: any) => {
  toolbarDrag.isDragging = true;
  toolbarDrag.startX = e.touches[0].clientX - toolbarDrag.x;
  toolbarDrag.startY = e.touches[0].clientY - toolbarDrag.y;
  e.stopPropagation(); // 防止触发画布事件
};

const onToolbarTouchMove = (e: any) => {
  if (!toolbarDrag.isDragging) return;

  e.preventDefault();
  e.stopPropagation();

  toolbarDrag.x = e.touches[0].clientX - toolbarDrag.startX;
  toolbarDrag.y = e.touches[0].clientY - toolbarDrag.startY;
};

const onToolbarTouchEnd = (e: any) => {
  toolbarDrag.isDragging = false;
  e.stopPropagation();
};

// 颜色改变处理
const changeColor = (color: string) => {
  log('颜色变更:', setup.value, color);
  if (setup.value === 1) {
    // 背景色变更时，确保不是透明色
    const bgColor = validateBackgroundColor(color);
    log('背景色变更:', { original: color, validated: bgColor, current: canvasBgColor.value });
    canvasBgColor.value = bgColor;
    log('背景色已更新为:', canvasBgColor.value);
    init();
    restoreLine();
  }
  if (setup.value === 2) {
    lineColor.value = color;
  }
};

// 创建结构化数据
const createDrawingData = (): DrawingData => {
  const data = {
    bgColor: validateBackgroundColor(canvasBgColor.value),
    lineWidth: lineWidth.value,
    lineColor: lineColor.value,
    lines: lineArr.value,
    canvasWidth: realWidth.value,
    canvasHeight: realHeight.value,
    version: '1.0.0', // 版本号，用于后续兼容性判断
  };
  log('创建绘图数据:', data);
  return data;
};

// 保存并导出画板内容
const save = () => {
  log('开始保存画板');

  // 检查画板是否有内容
  if (lineArr.value.length === 0) {
    log('画板无内容，返回空结果');
    // 直接返回空结果
    emit('save', {
      imageUrl: '',
      data: null,
      tempFilePath: '',
    });
    return;
  }
  // 生成结构化数据
  const drawingData = createDrawingData();

  // 导出图片
  uni.canvasToTempFilePath(
    {
      canvasId: 'canvasId',
      fileType: 'png',
      success: async (res: { tempFilePath: string }) => {
        // 同时返回图片和结构化数据
        const result: ExportResult = {
          imageUrl: res.tempFilePath,
          data: drawingData,
        };

        emit('save', result);

        // uni.showToast({
        //   title: '保存成功',
        //   icon: 'success',
        // });

        log('画板保存成功');
      },
      fail(err: any) {
        console.error('画板保存失败:', err);
        uni.showModal({
          title: '温馨提示',
          content: JSON.stringify(err),
        });
      },
    },
    instance
  );
};

// 关闭画板
const closeDrawing = () => {
  log('关闭画板');
  emit('close');
};

const toolbarRef = ref();

// 组件挂载完成后初始化
onMounted(() => {
  log('组件已挂载，初始化画布');
  log('props.data:', props.data);

  // 动态获取容器宽高
  uni
    .createSelectorQuery()
    .in(instance)
    .select('.canvas-container')
    .boundingClientRect((rect: any) => {
      if (rect) {
        realWidth.value = rect.width;
        realHeight.value = rect.height;
      }
      ctx.value = uni.createCanvasContext('canvasId', instance);

      // 确保初始化时使用正确的背景色
      if (props.data) {
        log('使用props.data初始化:', props.data);
        restoreFromData(props.data);
      } else {
        log('没有props.data，使用默认初始化');
        // 如果没有初始数据，强制初始化背景色
        init();
      }
    })
    .exec();
});

// 对外暴露方法
defineExpose({
  save,
  init,
  setDrawingData,
  setDebugMode,
});
</script>

<style lang="scss">
.my-drawing-board {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  position: relative;
}

/* 顶部操作栏样式 */
.header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e7eb;
  background: #fff;
  // padding-top: var(--status-bar-height);
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
}

.header-left {
  gap: 24rpx;
}

.header-right {
  gap: 24rpx;
}

.save-btn,
.clear-btn {
  font-size: 32rpx;
}

.save-btn {
  color: #7d4dff;
}

.clear-btn {
  color: #4e5969;
}

.header-divider {
  width: 1rpx;
  height: 32rpx;
  background: #e5e7eb;
  margin: 0 8rpx;
}

.undo-btn {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: transparent;
  transition: background 0.2s;
}

.undo-btn:active {
  background: #f5f6fa;
}

.close-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16rpx;
  background: transparent;
  transition: background 0.2s;
  margin-left: 8rpx;
}

.close-btn:active {
  background: #f5f6fa;
}

.icon {
  width: 34rpx;
  height: 34rpx;
  filter: grayscale(1) brightness(0.6);
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 画布样式 */
.canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
}

/* 橡皮擦指示器样式 */
.eraser-indicator {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
  border-radius: 50%;
  border: 2rpx solid #999;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  /* 移除过度动画，避免性能消耗 */
  /* transition: all 0.1s ease; */
  /* 设置最小尺寸，确保指示器不会太小 */
  min-width: 40rpx;
  min-height: 40rpx;
  /* 简化阴影效果，减少性能消耗 */
  /* box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.4); */
  /* 使用更轻量的视觉效果 */
  opacity: 0.8;
}

/* 移除不再使用的内部元素样式，简化DOM结构 */

/* 底部工具栏样式 */
.toolbar-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  // margin-bottom: 20rpx;
  // height: 220rpx;
  position: absolute;
  bottom: 200rpx;
  left: 0;
  right: 0;
  z-index: 100;
}

.toolbar {
  position: relative;
  width: 600rpx;
  height: 104rpx;
  background-color: #ffffff;
  border-radius: 52rpx;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0rpx 20rpx;
  cursor: move;
  user-select: none;
}

.tool-group {
  display: flex;
  align-items: center;
  height: 80rpx;
}

.tool-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 80rpx;
  border-radius: 16rpx;
  padding: 0 10rpx;
}

.tool-item.active {
  background-color: #f5f6f8;
}

.tool-icon {
  width: 54rpx;
  height: 54rpx;
}

.color-picker {
  width: 46rpx;
  height: 46rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.color-circle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #e5e7eb;
  box-sizing: border-box;
}

.dropdown-arrow {
  width: 28rpx;
  height: 24rpx;
  margin-left: 20rpx;
}

.divider {
  width: 1rpx;
  height: 50rpx;
  background-color: #e5e7eb;
  margin: 0 10rpx;
}

/* 设置面板样式 */
.setting-panel {
  position: absolute;
  background-color: #ffffff;
  border: 1rpx solid #e5e7eb;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 20rpx;
  z-index: 100;
  width: 396rpx;
}

.setting-title {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 10rpx;
}

.setting-value {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 28rpx;
  color: #4e5969;
}

.slider-container {
  margin-top: 20rpx;
  padding: 0rpx 10rpx;
}

.color-grid {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  gap: 20rpx;
}

.color-item {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  border: 1rpx solid #e5e7eb;
  box-sizing: border-box;
  position: relative;
}

.color-item.active {
  position: relative;
}

.color-item.active::after {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border: 4rpx solid #b184ff;
  border-radius: 50%;
  pointer-events: none;
}

.custom-slider {
  margin: 0 !important;
  width: 100%;
}

.area-container {
  position: relative;
}
</style>
