# MyDrawingBoard 画板自动高度扩展功能

## 📋 功能概述

MyDrawingBoard 画板组件现已支持**智能自动高度扩展**功能，当用户在画板底部区域绘制内容时，画板会自动扩展高度以提供更多绘制空间，同时支持多次连续扩展和自动滚动定位。

## 🎯 核心特性

### 1. 智能底部检测

- **检测区域**：默认监听画板底部20%区域的绘制行为
- **实时检测**：在每次绘制结束、撤销操作、清空画板后自动触发检测
- **精确计算**：基于绘制点的Y坐标与底部阈值进行精确比较

### 2. 多次连续扩展

- **无限扩展**：支持根据内容需要进行多次连续扩展
- **智能计算**：根据绘制内容的最大Y坐标，自动计算需要的扩展次数
- **增量扩展**：每次扩展400rpx高度，可根据内容需要累积扩展

### 3. 自动滚动定位

- **扩展后滚动**：画板扩展完成后自动滚动到底部区域
- **平滑体验**：使用0.3秒过渡动画实现平滑滚动
- **智能计算**：根据画板实际高度和屏幕高度计算最佳滚动位置

### 4. 智能收缩机制

- **内容清空收缩**：当底部区域内容被清空或擦除时，画板自动收缩
- **多级收缩**：支持从多次扩展状态收缩到任意扩展级别
- **状态重置**：清空画板时完全重置到原始状态

## 🔧 技术实现

### 核心算法

#### 1. 底部内容检测算法

```typescript
const calculateNeededExpansions = (): number => {
  // 获取所有绘制点的最大Y坐标
  let maxY = 0;
  for (const line of lineArr.value) {
    for (const point of line) {
      if (point.y !== undefined) {
        maxY = Math.max(maxY, point.y);
      }
    }
  }

  // 计算原始画布高度（不包括已扩展部分）
  const originalCanvasHeight = realHeight.value - expandedTimes.value * EXPAND_HEIGHT;
  const bottomThreshold = originalCanvasHeight * (1 - BOTTOM_DETECTION_RATIO);

  // 计算需要的扩展次数
  let neededExpansions = 0;
  if (maxY > bottomThreshold) {
    const excessHeight = maxY - bottomThreshold;
    neededExpansions = Math.ceil(excessHeight / EXPAND_HEIGHT);
  }

  return neededExpansions;
};
```

#### 2. 高度动态调整算法

```typescript
const adjustBoardHeight = () => {
  debounceHeightCheck(() => {
    const neededExpansions = calculateNeededExpansions();

    if (neededExpansions > expandedTimes.value) {
      // 需要扩展
      expandBoard(neededExpansions);
    } else if (neededExpansions < expandedTimes.value) {
      // 需要收缩
      shrinkBoard(neededExpansions);
    }
  });
};
```

#### 3. 扩展高度计算

```typescript
const expandBoard = (targetExpansions: number) => {
  const baseValue = parseFloat(baseHeight.value.replace(/[^\d.]/g, ''));
  const unit = baseHeight.value.replace(/[\d.]/g, '');

  let newValue: number;
  if (unit.includes('vh')) {
    // vh单位转换为rpx计算
    const systemInfo = uni.getSystemInfoSync();
    const vhInRpx = (baseValue / 100) * systemInfo.windowHeight * (750 / systemInfo.windowWidth);
    newValue = vhInRpx + targetExpansions * EXPAND_HEIGHT;
    dynamicHeight.value = `${Math.round(newValue)}rpx`;
  } else {
    // 其他单位直接相加
    newValue = baseValue + targetExpansions * EXPAND_HEIGHT;
    dynamicHeight.value = `${newValue}${unit}`;
  }

  expandedTimes.value = targetExpansions;
};
```

### 关键技术点

#### 1. 防抖机制

- **延迟执行**：使用300ms防抖延迟，避免频繁的高度调整
- **性能优化**：减少不必要的DOM操作和画布重绘

#### 2. 状态管理

```typescript
// 动态高度管理状态
const baseHeight = ref(props.customHeight || '100vh'); // 基础高度
const dynamicHeight = ref(baseHeight.value); // 当前动态高度
const expandedTimes = ref(0); // 扩展次数
const scrollTop = ref(0); // 滚动位置
```

#### 3. 滚动容器集成

```vue
<scroll-view
  class="drawing-board-scroll"
  scroll-y="true"
  :scroll-top="scrollTop"
  :style="{ height: '100vh' }"
>
  <view class="my-drawing-board" :style="{
    height: dynamicHeight,
    transition: 'height 0.3s ease-in-out'
  }">
    <!-- 画板内容 -->
  </view>
</scroll-view>
```

## 🎮 用户交互流程

### 扩展流程

1. **用户绘制** → 在画板底部20%区域开始绘制
2. **内容检测** → 系统检测到绘制点超出底部阈值
3. **计算扩展** → 根据超出距离计算需要的扩展次数
4. **执行扩展** → 画板高度增加400rpx×扩展次数
5. **自动滚动** → 滚动到新的底部区域
6. **继续绘制** → 用户可以在新区域继续绘制

### 收缩流程

1. **内容变化** → 用户擦除/撤销底部区域内容
2. **重新检测** → 系统重新计算所需扩展次数
3. **执行收缩** → 画板高度减少到合适大小
4. **状态更新** → 更新画布尺寸和绘制内容

### 清空重置流程

1. **用户清空** → 点击清空按钮
2. **状态重置** → 重置所有扩展状态和工具状态
3. **高度恢复** → 画板高度恢复到原始状态
4. **滚动重置** → 滚动位置重置到顶部
5. **工具栏重置** → 工具栏位置重置到默认位置

## 📊 配置参数

### 核心常量

```typescript
const BOTTOM_DETECTION_RATIO = 0.2; // 底部检测区域比例（20%）
const EXPAND_HEIGHT = 400; // 单次扩展高度（400rpx）
const DEBOUNCE_DELAY = 300; // 防抖延迟（300ms）
```

### 样式配置

```scss
.my-drawing-board {
  transition: 'height 0.3s ease-in-out'; // 高度变化过渡动画
  min-height: 100vh; // 最小高度
}

.drawing-board-scroll {
  height: 100vh; // 滚动容器高度
  overflow-y: auto; // 垂直滚动
}
```

## 🐛 问题解决记录

### 问题1: 扩展一次后无法继续扩展

**原因**：使用扩展后的画布高度计算底部阈值，导致阈值错误
**解决**：改为使用原始画布高度计算底部阈值

```typescript
// 错误做法
const bottomThreshold = realHeight.value * (1 - BOTTOM_DETECTION_RATIO);

// 正确做法
const originalCanvasHeight = realHeight.value - expandedTimes.value * EXPAND_HEIGHT;
const bottomThreshold = originalCanvasHeight * (1 - BOTTOM_DETECTION_RATIO);
```

### 问题2: 清空画板后工具栏消失

**原因**：工具栏使用相对定位，画板高度变化影响工具栏位置
**解决**：工具栏改为固定定位，清空时重置拖动状态

```scss
.toolbar-wrapper {
  position: fixed; // 固定定位
  bottom: 200rpx; // 相对视窗定位
  z-index: 1000; // 高层级显示
}
```

### 问题3: 清空后无法再次扩展

**原因**：清空时状态重置不完整，滚动位置未重置
**解决**：完整重置所有相关状态

```typescript
// 清空时重置所有状态
expandedTimes.value = 0;
dynamicHeight.value = baseHeight.value;
scrollTop.value = 0;
toolbarDrag.x = 0;
toolbarDrag.y = 0;
```

## 🔍 调试信息

### 启用调试

组件提供详细的调试日志，可通过控制台查看：

```typescript
// 扩展检测日志
🔍 [检测] 扩展计算: {
  最大Y坐标: 850,
  当前实际画布高度: 1000,
  原始画布高度: 600,
  底部阈值: 480,
  当前扩展次数: 1,
  需要扩展次数: 2,
  超出高度: 370
}

// 扩展执行日志
🔧 [扩展] 开始扩展画板高度到 2 次
✅ [扩展] 扩展完成! { expandedTimes: 2, to: "1400rpx" }

// 滚动日志
📜 [滚动] 滚动参数: { boardHeight: 1400, targetScrollTop: 700 }
✅ [滚动] 滚动到底部完成
```

### 调试技巧

1. **开启调试模式**：设置 `debug: true` 查看详细日志
2. **观察扩展计算**：重点关注"扩展计算"日志中的各项数值
3. **检查状态重置**：清空后查看状态重置是否完整
4. **监控滚动行为**：确认扩展后是否正确滚动到底部

## 🚀 性能优化

### 1. 防抖优化

- 使用300ms防抖延迟，避免频繁检测
- 减少不必要的DOM查询和计算

### 2. 画布重绘优化

- 只在必要时重新初始化画布
- 保持现有绘制内容，避免重复绘制

### 3. 动画优化

- 使用CSS过渡动画，性能优于JavaScript动画
- 0.3秒过渡时间，在流畅性和响应性间取得平衡

### 4. 内存管理

- 及时清理定时器，避免内存泄漏
- 状态重置时完整清理所有引用

## 📱 兼容性说明

### 平台支持

- ✅ 微信小程序
- ✅ H5浏览器
- ✅ App端
- ✅ 支付宝小程序

### 依赖要求

- uni-app框架
- 支持CSS3过渡动画
- 支持scroll-view组件

### 注意事项

1. **画布API兼容性**：依赖uni-app的canvas API
2. **滚动行为**：不同平台的scroll-view行为可能略有差异
3. **性能考虑**：大量绘制内容时检测可能有轻微延迟

## 🎯 最佳实践

### 1. 使用建议

- 适用于需要大量绘制空间的场景
- 特别适合长文档标注、思维导图绘制等
- 移动端用户体验优化场景

### 2. 自定义配置

```vue
<MyDrawingBoard
  customHeight="800rpx"     <!-- 自定义初始高度 -->
  :debug="true"             <!-- 开启调试模式 -->
  @save="onSave"
/>
```

### 3. 开发调试

- 开发阶段建议开启调试模式
- 关注控制台日志，了解扩展行为
- 测试各种绘制场景，确保功能稳定

## 📝 更新日志

- **v1.4.0**: 添加自动高度扩展功能
  - ✅ 支持智能底部区域检测
  - ✅ 支持多次连续扩展
  - ✅ 支持自动滚动定位
  - ✅ 支持智能收缩机制
  - ✅ 完整的状态管理和重置
  - ✅ 详细的调试信息输出
  - ✅ 工具栏位置优化
  - ✅ 性能优化和防抖处理

---

> 📌 **提示**: 此功能为内置功能，无需额外配置即可使用。用户在画板底部绘制时，画板会自动智能扩展，提供流畅的绘制体验。
