# MyDrawingBoard 画板组件

MyDrawingBoard 是一个基于 uni-app 开发的多功能画板组件，支持自由绘制、橡皮擦、颜色选择、线宽调整等功能，适用于需要手绘功能的应用场景。

## 功能特点

- 支持自由绘制线条
- 支持选择线条颜色和背景颜色
- 支持调整线条粗细
- 支持橡皮擦功能（带可视化指示器）
- 支持撤销上一步操作
- 支持清空画板
- 支持保存画板内容为图片
- 支持导出/导入结构化数据
- 支持自定义组件宽高
- 支持调试模式（日志输出）
- 支持通过ref动态控制画板

## 使用方法

### 基础用法

```vue
<template>
  <view class="container">
    <my-drawing-board @save="onDrawingSaved" />
  </view>
</template>

<script>
import MyDrawingBoard from '@/components/MyDrawingBoard/index.vue';

export default {
  components: {
    MyDrawingBoard,
  },
  methods: {
    onDrawingSaved(result) {
      console.log('保存成功:', result);
      // result包含imageUrl和data两部分
      uni.saveImageToPhotosAlbum({
        filePath: result.imageUrl,
        success: function () {
          uni.showToast({ title: '已保存到相册' });
        },
      });
    },
  },
};
</script>
```

### 自定义尺寸

```vue
<template>
  <view class="container">
    <my-drawing-board customWidth="600rpx" customHeight="800rpx" @save="onDrawingSaved" />
  </view>
</template>
```

### 开启调试模式

```vue
<template>
  <view class="container">
    <my-drawing-board :debug="true" @save="onDrawingSaved" />
  </view>
</template>
```

## Ref操作详解

通过ref可以获取画板组件实例，实现对画板的精确控制，包括动态设置画板内容、主动保存、初始化等操作。

### 获取画板ref

```vue
<template>
  <view class="container">
    <my-drawing-board ref="drawingBoard" @save="onDrawingSaved" />
  </view>
</template>

<script>
import { ref, onMounted } from 'vue';
import MyDrawingBoard from '@/components/MyDrawingBoard/index.vue';

export default {
  setup() {
    // 在setup中使用ref
    const drawingBoard = ref(null);

    onMounted(() => {
      // 组件挂载后可以访问
      console.log('画板组件已挂载');
    });

    return {
      drawingBoard,
    };
  },
};
</script>
```

也可以在选项式API中使用：

```vue
<script>
export default {
  mounted() {
    // 在mounted钩子中可以访问
    console.log('画板组件已挂载');
    // this.$refs.drawingBoard
  },
};
</script>
```

### 通过ref动态设置画板内容

可以使用`setDrawingData`方法动态设置画板内容：

```typescript
// 在setup中
const setCanvasContent = () => {
  const drawingData = {
    bgColor: '#ffffff',
    lineWidth: 3,
    lineColor: '#000000',
    lines: [
      // 绘制一条简单的线
      [
        { color: '#000000', width: 3 }, // 线条属性
        { x: 100, y: 100 }, // 开始点
        { x: 200, y: 200 }  // 结束点
      ]
    ],
    canvasWidth: 375,
    canvasHeight: 600,
    version: '1.0.0'
  }

  // 设置画板内容
  drawingBoard.value?.setDrawingData(drawingData)
}

// 在选项式API中
methods: {
  setCanvasContent() {
    const drawingData = {
      // 同上...
    }
    this.$refs.drawingBoard.setDrawingData(drawingData)
  }
}
```

### 预定义图形示例

以下是一些常用预定义图形的结构化数据示例：

#### 绘制笑脸

```typescript
const smileFace = {
  bgColor: '#ffffff',
  lineWidth: 3,
  lineColor: '#000000',
  lines: [
    // 脸部轮廓
    [
      { color: '#000000', width: 3 },
      { x: 150, y: 150 },
      { x: 160, y: 130 },
      { x: 180, y: 120 },
      { x: 200, y: 120 },
      { x: 220, y: 130 },
      { x: 230, y: 150 },
      { x: 230, y: 170 },
      { x: 220, y: 190 },
      { x: 200, y: 200 },
      { x: 180, y: 200 },
      { x: 160, y: 190 },
      { x: 150, y: 170 },
      { x: 150, y: 150 },
    ],
    // 左眼
    [
      { color: '#000000', width: 3 },
      { x: 170, y: 160 },
      { x: 175, y: 160 },
      { x: 180, y: 160 },
    ],
    // 右眼
    [
      { color: '#000000', width: 3 },
      { x: 200, y: 160 },
      { x: 205, y: 160 },
      { x: 210, y: 160 },
    ],
    // 嘴巴(微笑)
    [
      { color: '#000000', width: 3 },
      { x: 170, y: 180 },
      { x: 180, y: 185 },
      { x: 190, y: 185 },
      { x: 200, y: 185 },
      { x: 210, y: 180 },
    ],
  ],
  canvasWidth: 375,
  canvasHeight: 300,
  version: '1.0.0',
};
```

#### 绘制简单房子

```typescript
const house = {
  bgColor: '#ffffff',
  lineWidth: 3,
  lineColor: '#000000',
  lines: [
    // 房子轮廓
    [
      { color: '#000000', width: 3 },
      { x: 100, y: 200 },
      { x: 100, y: 150 },
      { x: 150, y: 100 },
      { x: 200, y: 150 },
      { x: 200, y: 200 },
      { x: 100, y: 200 },
    ],
    // 门
    [
      { color: '#000000', width: 3 },
      { x: 140, y: 200 },
      { x: 140, y: 170 },
      { x: 160, y: 170 },
      { x: 160, y: 200 },
    ],
    // 窗户
    [
      { color: '#000000', width: 3 },
      { x: 120, y: 160 },
      { x: 120, y: 140 },
      { x: 140, y: 140 },
      { x: 140, y: 160 },
      { x: 120, y: 160 },
    ],
  ],
  canvasWidth: 375,
  canvasHeight: 300,
  version: '1.0.0',
};
```

### 其他ref操作

#### 主动保存画板

```typescript
// 保存画板内容
const saveDrawing = () => {
  drawingBoard.value?.save();
};
```

#### 初始化画板（清空并重置）

```typescript
// 初始化画板
const initDrawing = () => {
  drawingBoard.value?.init();
};
```

#### 动态切换调试模式

```typescript
// 开启调试模式
const enableDebug = () => {
  drawingBoard.value?.setDebugMode(true);
};

// 关闭调试模式
const disableDebug = () => {
  drawingBoard.value?.setDebugMode(false);
};
```

#### 在Vue 3 Composition API中完整示例

```vue
<template>
  <view class="container">
    <my-drawing-board ref="drawingBoard" @save="onDrawingSaved" />
    <view class="control-panel">
      <button @tap="saveDrawing">保存画板</button>
      <button @tap="initDrawing">重置画板</button>
      <button @tap="loadSmileFace">加载笑脸</button>
      <button @tap="loadHouse">加载房子</button>
      <button @tap="toggleDebug">{{ debugEnabled ? '关闭' : '开启' }}调试</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import MyDrawingBoard from '@/components/MyDrawingBoard/index.vue';

const drawingBoard = ref(null);
const debugEnabled = ref(false);

// 笑脸数据
const smileFace = {
  // ... 同上述笑脸数据
};

// 房子数据
const house = {
  // ... 同上述房子数据
};

// 保存画板
const saveDrawing = () => {
  drawingBoard.value?.save();
};

// 初始化画板
const initDrawing = () => {
  drawingBoard.value?.init();
};

// 加载笑脸
const loadSmileFace = () => {
  drawingBoard.value?.setDrawingData(smileFace);
};

// 加载房子
const loadHouse = () => {
  drawingBoard.value?.setDrawingData(house);
};

// 切换调试模式
const toggleDebug = () => {
  debugEnabled.value = !debugEnabled.value;
  drawingBoard.value?.setDebugMode(debugEnabled.value);
};

// 保存回调
const onDrawingSaved = result => {
  console.log('保存成功:', result);
  uni.showToast({
    title: '保存成功',
    icon: 'success',
  });
};

onMounted(() => {
  console.log('画板组件已挂载');
});
</script>
```

### 在TypeScript环境下使用ref

如果使用TypeScript，可以为ref添加类型：

```typescript
import { ref, onMounted } from 'vue';
import MyDrawingBoard from '@/components/MyDrawingBoard/index.vue';
import type { DrawingData } from '@/components/MyDrawingBoard/types';

// 指定ref类型
const drawingBoard = ref<InstanceType<typeof MyDrawingBoard> | null>(null);

// 强类型的数据对象
const drawingData: DrawingData = {
  bgColor: '#ffffff',
  lineWidth: 3,
  lineColor: '#000000',
  lines: [],
  canvasWidth: 375,
  canvasHeight: 600,
  version: '1.0.0',
};

// 设置画板内容
const setCanvasContent = () => {
  // 使用可选链操作符
  drawingBoard.value?.setDrawingData(drawingData);
};
```

## 属性说明

| 属性名       | 类型    | 默认值                                                                                   | 说明                               |
| ------------ | ------- | ---------------------------------------------------------------------------------------- | ---------------------------------- |
| customWidth  | String  | '750rpx'                                                                                 | 画板宽度，支持rpx、px等单位        |
| customHeight | String  | '100vh'                                                                                  | 画板高度，支持rpx、vh等单位        |
| width        | Number  | 375                                                                                      | 画布内部宽度 (不推荐直接使用)      |
| height       | Number  | 400                                                                                      | 画布内部高度 (不推荐直接使用)      |
| lineWidth    | Number  | 6                                                                                        | 默认线条粗细                       |
| lineColor    | String  | '#000000'                                                                                | 默认线条颜色                       |
| bgColor      | String  | '#ffffff'                                                                                | 默认背景颜色                       |
| colorList    | Array   | ['#000000', '#ffffff', '#E63E34', '#E76711', '#EFCC00', '#65BA4F', '#6CC2DC', '#649BFB'] | 颜色选择器可选颜色列表             |
| data         | Object  | null                                                                                     | 用于恢复画板内容的数据对象         |
| debug        | Boolean | false                                                                                    | 是否开启调试模式，开启后会输出日志 |

## 事件说明

| 事件名 | 说明               | 返回值                                         |
| ------ | ------------------ | ---------------------------------------------- |
| save   | 保存画板内容时触发 | { imageUrl: 画板图片路径, data: 画板数据对象 } |
| close  | 关闭画板内容时触发 | -                                              |

## 方法说明

通过ref可以调用组件的以下方法：

| 方法名         | 参数            | 返回值  | 说明                               |
| -------------- | --------------- | ------- | ---------------------------------- |
| save           | 无              | 无      | 保存当前画板，触发save事件         |
| init           | 无              | 无      | 初始化画板（清空并重置）           |
| setDrawingData | DrawingData对象 | Boolean | 动态设置画板内容，返回是否设置成功 |
| setDebugMode   | Boolean         | Boolean | 动态设置是否开启调试模式           |

## 数据结构

### DrawingData 结构

```ts
{
  bgColor: string;      // 背景颜色
  lineWidth: number;    // 线条宽度
  lineColor: string;    // 线条颜色
  lines: DrawingRecord[][]; // 线条数据
  canvasWidth: number;  // 画布宽度
  canvasHeight: number; // 画布高度
  version: string;      // 版本号
}
```

### DrawingRecord 结构

```ts
{
  color?: string;       // 线条颜色
  width: number;        // 线条宽度
  isEraser?: boolean;   // 是否为橡皮擦
  x?: number;           // X坐标
  y?: number;           // Y坐标
}
```

## 调试模式

开启调试模式后，组件会在控制台输出详细的操作日志，包括：

- 画板初始化过程
- 线条绘制信息
- 工具选择和切换
- 数据导入导出过程
- 触摸事件信息

调试模式可以通过以下两种方式开启：

1. 通过属性设置：`<my-drawing-board :debug="true" />`
2. 通过方法设置：`this.$refs.drawingBoard.setDebugMode(true)`

## 注意事项

1. 组件内部使用了uni-app的canvas API，确保在目标平台上canvas功能正常可用
2. 保存功能会生成临时文件，若需永久保存，请使用相关API保存到相册或其他位置
3. 恢复画板功能需要完整的数据结构，请确保保存的数据格式正确
4. 自定义宽高时，请确保父容器能够正确显示画板
5. 大量日志输出可能影响性能，生产环境建议关闭调试模式
6. 通过ref操作时，建议在组件挂载完成后进行，以确保组件已完全初始化

## 橡皮擦指示器功能

### 功能说明

橡皮擦指示器是一个可视化功能，当用户选择橡皮擦工具并开始绘制时，会在画布上显示一个圆形指示器，帮助用户：

- 清楚地看到橡皮擦的大小范围
- 精确控制橡皮擦的位置
- 提供更好的用户体验

### 指示器特性

- **实时跟随**：指示器会实时跟随用户的触摸位置移动
- **智能大小**：指示器大小基于线宽计算，最小20px，最大60px，确保清晰可见
- **视觉反馈**：使用红色边框、半透明背景和阴影效果，清晰可见
- **自动隐藏**：当用户停止绘制或切换工具时自动隐藏
- **无干扰**：指示器不会影响正常的绘制操作

### 使用方式

橡皮擦指示器功能是自动启用的，无需额外配置：

1. 点击工具栏中的橡皮擦图标
2. 在画布上开始绘制，指示器会自动显示
3. 移动手指时，指示器会跟随移动
4. 停止绘制时，指示器会自动隐藏

### 自定义样式

如需自定义指示器样式，可以修改组件内部的CSS样式：

```scss
.eraser-indicator {
  border: 4rpx solid #ff6b6b; // 边框颜色和粗细
  background-color: rgba(255, 107, 107, 0.2); // 背景颜色
  min-width: 60rpx; // 最小宽度
  min-height: 60rpx; // 最小高度
  box-shadow: 0 0 10rpx rgba(255, 107, 107, 0.4); // 阴影效果
}

.eraser-indicator-inner {
  background-color: rgba(255, 107, 107, 0.4); // 内圈颜色
  border: 3rpx solid rgba(255, 107, 107, 0.8); // 内圈边框
}
```

## 更新日志

- v1.0.0: 初始版本
- v1.1.0: 添加自定义宽高功能
- v1.1.1: 将事件名从success改为save
- v1.2.0: 添加调试模式和通过ref动态设置画板内容功能
- v1.3.0: 添加橡皮擦指示器功能，提升用户体验
