<template>
  <scroll-view
    class="drawing-board-scroll"
    scroll-y="true"
    :scroll-top="scrollTop"
    :style="{ height: '100vh' }"
  >
    <view
      class="my-drawing-board"
      :style="{
        width: props.customWidth || '750rpx',
        height: dynamicHeight,
        transition: 'height 0.3s ease-in-out',
      }"
    >
      <!-- 顶部工具栏 -->
      <view class="header">
        <view class="header-left">
          <text class="save-btn" @tap="save">保存</text>
          <view class="header-divider"></view>
          <view class="undo-btn" @tap="handleClick(menuItems[4])">
            <image
              class="icon"
              src="/static/homework/drawing_board/undo_icon.svg"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="header-right">
          <text class="clear-btn" @tap="handleClick(menuItems[5])">清空</text>
          <view class="close-btn" @tap="closeDrawing">
            <image
              class="close-icon"
              src="/static/homework/drawing_board/close_icon.svg"
              mode="aspectFit"
            >
            </image>
          </view>
        </view>
      </view>

      <!-- 插槽区域 1 -->
      <view class="area-container" v-if="$slots.area1">
        <slot name="area1"></slot>
      </view>

      <!-- 画布区域 -->
      <view class="canvas-container">
        <canvas
          canvas-id="canvasId"
          id="canvasId"
          :disable-scroll="true"
          :style="{ width: '100%', height: '100%' }"
          @touchstart="touchstart"
          @touchmove="touchmove"
          @touchend="touchend"
        ></canvas>

        <!-- 底部扩展区域标注框 -->
        <view class="expansion-indicator">
          <view class="expansion-zone-label">扩展触发区域</view>
          <view class="expansion-zone-info">底部50px区域</view>
          <view class="expansion-debug">当前高度: {{ dynamicHeight }}</view>
          <view class="expansion-debug">扩展次数: {{ expandedTimes }}</view>
        </view>

        <!-- 橡皮擦指示器 -->
        <view
          v-if="eraserIndicator.visible && isEraser"
          class="eraser-indicator"
          :style="{
            left: eraserIndicator.x + 'px',
            top: eraserIndicator.y + 'px',
            width: eraserIndicator.size + 'px',
            height: eraserIndicator.size + 'px',
            transform: 'translate(-50%, -50%)',
          }"
        ></view>
      </view>

      <!-- 底部工具栏区域 -->
      <view
        class="toolbar-wrapper"
        ref="toolbarRef"
        :style="{
          transform: `translate(${toolbarDrag.x}px, ${toolbarDrag.y}px)`,
        }"
        @touchstart="onToolbarTouchStart"
        @touchmove="onToolbarTouchMove"
        @touchend="onToolbarTouchEnd"
      >
        <view class="toolbar">
          <view class="tool-group">
            <!-- 背景色选择工具 -->
            <view class="tool-item" :class="{ active: setup === 1 }" @tap="selectTool('bgcolor')">
              <image
                class="tool-icon"
                style="width: 43rpx; height: 43rpx"
                src="/static/homework/drawing_board/paint_icon.svg"
                mode="aspectFit"
              >
              </image>
              <image
                class="dropdown-arrow"
                src="/static/homework/drawing_board/arrow_down.svg"
                mode="aspectFit"
              >
              </image>
            </view>
            <!-- 分隔线 -->
            <view class="divider"></view>
            <!-- 文本工具（线宽） -->
            <view class="tool-item" :class="{ active: setup === 3 }" @tap="selectTool('text')">
              <image
                class="tool-icon"
                src="/static/homework/drawing_board/weight_icon.svg"
                mode="aspectFit"
              >
              </image>
              <image
                class="dropdown-arrow"
                src="/static/homework/drawing_board/arrow_down.svg"
                mode="aspectFit"
              >
              </image>
            </view>
            <!-- 分隔线 -->
            <view class="divider"></view>
            <!-- 颜色选择器（文字颜色） -->
            <view class="tool-item" :class="{ active: setup === 2 }" @tap="selectTool('color')">
              <view class="color-picker">
                <view class="color-circle" :style="{ backgroundColor: lineColor }"></view>
              </view>
              <image
                class="dropdown-arrow"
                src="/static/homework/drawing_board/arrow_down.svg"
                mode="aspectFit"
              >
              </image>
            </view>
            <!-- 分隔线 -->
            <view class="divider"></view>
            <!-- 橡皮擦工具 -->
            <view
              class="tool-item"
              :class="{ active: isEraser || setup === 4 }"
              @tap="selectTool('eraser')"
            >
              <image
                class="tool-icon"
                style="width: 43rpx; height: 43rpx"
                src="/static/homework/drawing_board/eraser_icon.svg"
                mode="aspectFit"
              >
              </image>
            </view>
            <!-- 分隔线 -->
            <view class="divider"></view>
            <!-- 向上滚动按钮 -->
            <view class="tool-item scroll-button" @tap="scrollUp">
              <image
                class="tool-icon scroll-up-icon"
                style="width: 40rpx; height: 40rpx"
                src="/static/homework/drawing_board/arrow_down.svg"
                mode="aspectFit"
              >
              </image>
            </view>
            <!-- 向下滚动按钮 -->
            <view class="tool-item scroll-button" @tap="scrollDown">
              <image
                class="tool-icon scroll-down-icon"
                style="width: 40rpx; height: 40rpx"
                src="/static/homework/drawing_board/arrow_down.svg"
                mode="aspectFit"
              >
              </image>
            </view>
          </view>

          <!-- 统一的设置面板 -->
          <view
            class="setting-panel"
            v-if="setup === 1 || setup === 2 || setup === 3 || setup === 4"
            :style="{
              bottom: '100%',
              left: '50%',
              transform: 'translateX(-50%)',
              'margin-bottom': '20rpx',
            }"
          >
            <!-- 线条粗细设置 -->
            <template v-if="setup === 3">
              <text class="setting-title">粗细</text>
              <text class="setting-value">{{ lineWidth }}px</text>
              <view class="slider-container">
                <slider
                  class="custom-slider"
                  min="1"
                  max="20"
                  step="1"
                  block-size="18"
                  activeColor="#E5E7EB"
                  block-color="#fff"
                  backgroundColor="#E5E7EB"
                  :value="lineWidth"
                  @changing="sliderChange"
                  @change="sliderChange"
                >
                </slider>
              </view>
            </template>

            <!-- 背景颜色选择 -->
            <template v-if="setup === 1">
              <text class="setting-title">背景色</text>
              <view class="color-grid">
                <view
                  v-for="(color, index) in props.colorList"
                  :key="'bg-' + index"
                  class="color-item"
                  :class="{
                    active:
                      canvasBgColor === color || validateBackgroundColor(color) === canvasBgColor,
                  }"
                  :style="{ backgroundColor: color }"
                  @tap="changeColor(color)"
                ></view>
              </view>
            </template>

            <!-- 文字颜色选择 -->
            <template v-if="setup === 2">
              <text class="setting-title">文字颜色</text>
              <view class="color-grid">
                <view
                  v-for="(color, index) in props.colorList"
                  :key="'text-' + index"
                  class="color-item"
                  :class="{ active: lineColor === color }"
                  :style="{ backgroundColor: color }"
                  @tap="changeColor(color)"
                >
                </view>
              </view>
            </template>

            <!-- 橡皮擦大小设置 -->
            <template v-if="setup === 4">
              <text class="setting-title">橡皮擦大小</text>
              <text class="setting-value">{{ eraserSize }}px</text>
              <view class="slider-container">
                <slider
                  class="custom-slider"
                  min="20"
                  max="50"
                  step="1"
                  block-size="18"
                  activeColor="#E5E7EB"
                  block-color="#fff"
                  backgroundColor="#E5E7EB"
                  :value="eraserSize"
                  @changing="eraserSizeChange"
                  @change="eraserSizeChange"
                >
                </slider>
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, getCurrentInstance, nextTick } from 'vue';
import type {
  Position,
  DrawingRecord,
  MenuItem,
  TouchEvent,
  SliderEvent,
  DrawingData,
  ExportResult,
  EraserIndicator,
} from './types';

/** 组件属性接口 */
export interface DrawingBoardProps {
  width?: number;
  height?: number;
  lineWidth?: number;
  lineColor?: string;
  bgColor?: string;
  colorList?: string[];
  data?: DrawingData;
  customWidth?: string;
  customHeight?: string;
  debug?: boolean; // 是否开启调试模式
}

// 为uni-app声明全局类型
declare const uni: any;

// 获取当前组件实例
const instance = getCurrentInstance();

// 定义Props
const props = withDefaults(defineProps<DrawingBoardProps>(), {
  width: 375,
  height: 400,
  lineWidth: 1, // 默认线宽改为1
  lineColor: '#000000',
  bgColor: '#ffffff', // 默认白色背景，不允许透明
  colorList: () => [
    '#000000',
    '#ffffff',
    '#E63E34',
    '#E76711',
    '#EFCC00',
    '#65BA4F',
    '#6CC2DC',
    '#649BFB',
  ],
  customWidth: '',
  customHeight: '',
  debug: true, // 开启调试模式以查看扩展问题
});

// 定义Emits
const emit = defineEmits<{
  (e: 'save', value: ExportResult): void;
  (e: 'close'): void;
}>();

// 验证背景色是否有效
const validateBackgroundColor = (color: string): string => {
  // 检查是否为透明色或无效色
  const transparentColors = [
    'transparent',
    'rgba(0,0,0,0)',
    'rgba(255,255,255,0)',
    'rgba(0,0,0,0.0)',
    'rgba(255,255,255,0.0)',
  ];
  if (transparentColors.includes(color.toLowerCase())) {
    return props.bgColor || '#ffffff';
  }
  // 允许正常的颜色值通过
  return color;
};

// 画板状态
const ctx = ref<any>(null); // uniapp的canvas上下文类型较特殊
const canvasBgColor = ref(validateBackgroundColor(props.bgColor));
const lineColor = ref(props.lineColor);
const lineWidth = ref(props.lineWidth);
const eraserSize = ref(Math.max(20, props.lineWidth)); // 橡皮擦大小，最小值为20
const setup = ref(0); // 默认不激活任何工具
const isEraser = ref(false);
const showExportOptions = ref(false);
const realWidth = ref(props.width);
const realHeight = ref(props.height);

// 动态高度管理
const baseHeight = ref(props.customHeight || '100vh');
const dynamicHeight = ref(baseHeight.value);
const expandedTimes = ref(0); // 扩展次数
const scrollTop = ref(0); // 滚动位置
const BOTTOM_DETECTION_FIXED_HEIGHT = 50; // 距离底部50px时触发扩展（调试用）
const EXPAND_HEIGHT = 400; // 每次扩展400rpx

// 绘制状态
const start = reactive<Position>({ x: 0, y: 0 });
const end = reactive<Position>({ x: 0, y: 0 });
const temp = ref<DrawingRecord[]>([]);
const lineArr = ref<DrawingRecord[][]>([]);

// 橡皮擦指示器状态
const eraserIndicator = reactive<EraserIndicator>({
  x: 0,
  y: 0,
  size: 0,
  visible: false,
});

// 橡皮擦指示器更新节流
let lastIndicatorUpdate = 0;
const INDICATOR_UPDATE_INTERVAL = 32; // 约30fps，在性能和流畅度间平衡

// 工具栏拖动状态
const toolbarDrag = reactive({
  x: 0,
  y: 0,
  isDragging: false,
  startX: 0,
  startY: 0,
});

// 菜单项配置
const menuItems = reactive<MenuItem[]>([
  { label: '背景色', icon: 'icon-beijingse', action: 1, type: 1 },
  { label: '文字颜色', icon: 'icon-wenziyanse', action: 2, type: 1 },
  { label: '大小调整', icon: 'icon-wenzidaxiao2', action: 3, type: 1 },
  { label: '橡皮擦', icon: 'icon-qingkong_huaban1', action: 4, type: 1 },
  { label: '撤回操作', icon: 'icon-chehui', action: null, type: 2 },
  { label: '清空画板', icon: 'icon-qingkong_huaban1', action: null, type: 3 },
]);

// 调试日志函数
const log = (message: string, ...args: any[]) => {
  if (props.debug) {
    console.log(`[MyDrawingBoard] ${message}`, ...args);
  }
};

// 防抖函数
let heightCheckTimer: any = null;
let isAdjustingHeight = false; // 防止递归调用
const debounceHeightCheck = (callback: Function, delay: number = 300) => {
  if (heightCheckTimer) clearTimeout(heightCheckTimer);
  heightCheckTimer = setTimeout(callback, delay);
};

// 检测需要的扩展次数
const calculateNeededExpansions = (): number => {
  if (lineArr.value.length === 0) {
    console.log('❌ [检测] 没有绘制内容');
    return 0;
  }

  let maxY = 0;
  for (const line of lineArr.value) {
    for (const point of line) {
      if (point.y !== undefined) {
        maxY = Math.max(maxY, point.y);
      }
    }
  }

  // 计算原始画布高度（不包括任何扩展）
  // 需要将EXPAND_HEIGHT从rpx转换为像素
  const systemInfo = uni.getSystemInfoSync();
  const expandHeightPx = (EXPAND_HEIGHT / 750) * systemInfo.windowWidth;
  const originalCanvasHeight = realHeight.value - expandedTimes.value * expandHeightPx;

  // 使用固定的底部检测高度，而不是比例
  const bottomThreshold = originalCanvasHeight - BOTTOM_DETECTION_FIXED_HEIGHT;

  console.log('📐 [固定阈值计算]:', {
    EXPAND_HEIGHT_RPX: EXPAND_HEIGHT,
    expandHeightPx: expandHeightPx,
    screenWidth: systemInfo.windowWidth,
    realHeight: realHeight.value,
    expandedTimes: expandedTimes.value,
    totalExpandedHeightPx: expandedTimes.value * expandHeightPx,
    originalCanvasHeight: originalCanvasHeight,
    固定检测距离: BOTTOM_DETECTION_FIXED_HEIGHT,
    底部触发阈值: bottomThreshold,
    最大Y坐标: maxY,
    阈值计算公式: `${originalCanvasHeight} - ${BOTTOM_DETECTION_FIXED_HEIGHT} = ${bottomThreshold}`,
    触发条件: `${maxY} > ${bottomThreshold} = ${maxY > bottomThreshold}`,
  });

  console.log('🔍 [检测] 阈值计算:', {
    最大Y坐标: maxY,
    当前画布总高度: realHeight.value,
    原始画布高度: originalCanvasHeight,
    固定底部阈值: bottomThreshold,
    距离底部: originalCanvasHeight - maxY,
    当前扩展次数: expandedTimes.value,
    是否超出阈值: maxY > bottomThreshold,
  });

  // 检查是否需要扩展（基于原始画布高度的阈值）
  if (maxY > bottomThreshold) {
    // 只要超出原始画布的阈值，就扩展一次
    const nextExpansionTimes = expandedTimes.value + 1;

    console.log('🔍 [检测] 需要扩展一次:', {
      最大Y坐标: maxY,
      固定底部阈值: bottomThreshold,
      超出高度: maxY - bottomThreshold,
      距离底部: originalCanvasHeight - maxY,
      当前扩展次数: expandedTimes.value,
      下次扩展次数: nextExpansionTimes,
    });

    return nextExpansionTimes;
  } else {
    // 检查是否可以收缩
    if (expandedTimes.value > 0) {
      // 计算如果收缩一次后的阈值
      const shrinkTestHeight = originalCanvasHeight + (expandedTimes.value - 1) * expandHeightPx;
      const shrinkTestThreshold = shrinkTestHeight - BOTTOM_DETECTION_FIXED_HEIGHT;

      if (maxY <= shrinkTestThreshold) {
        const shrinkToTimes = expandedTimes.value - 1;
        console.log('🔍 [检测] 可以收缩一次:', {
          当前扩展次数: expandedTimes.value,
          收缩后扩展次数: shrinkToTimes,
          收缩后阈值: shrinkTestThreshold,
          最大Y坐标: maxY,
        });
        return shrinkToTimes;
      }
    }

    console.log('🔍 [检测] 无需调整');
    return expandedTimes.value;
  }
};

// 调整画板高度
const adjustBoardHeight = () => {
  if (isAdjustingHeight) {
    console.log('⏳ [调整] 正在调整中，跳过本次检测');
    return;
  }

  console.log('🚀 [调整] 开始检测高度调整需求...');
  debounceHeightCheck(() => {
    const targetExpansions = calculateNeededExpansions();

    console.log('⚖️ [调整] 当前状态:', {
      当前扩展次数: expandedTimes.value,
      目标扩展次数: targetExpansions,
      当前高度: dynamicHeight.value,
      基础高度: baseHeight.value,
      是否需要扩展: targetExpansions > expandedTimes.value,
      是否需要收缩: targetExpansions < expandedTimes.value,
    });

    if (targetExpansions > expandedTimes.value) {
      console.log('📈 [调整] 需要扩展画板一次');
      isAdjustingHeight = true;
      expandBoard(targetExpansions);
    } else if (targetExpansions < expandedTimes.value) {
      console.log('📉 [调整] 需要收缩画板一次');
      isAdjustingHeight = true;
      shrinkBoard(targetExpansions);
    } else {
      console.log('✅ [调整] 无需调整高度');
    }
  });
};

// 扩展画板到指定次数
const expandBoard = (targetExpansions: number) => {
  console.log('🔧 [扩展] 开始扩展画板高度到', targetExpansions, '次');

  // 解析基础高度值
  const baseValue = parseFloat(baseHeight.value.replace(/[^\d.]/g, ''));
  const unit = baseHeight.value.replace(/[\d.]/g, '');

  console.log('🔧 [扩展] 基础高度解析:', { baseValue, unit, original: baseHeight.value });

  // 计算新高度（基础高度 + 扩展次数 * 扩展高度）
  let newValue: number;
  if (unit.includes('vh')) {
    // 如果是vh单位，转换为rpx进行计算
    const systemInfo = uni.getSystemInfoSync();
    const vhInRpx = (baseValue / 100) * systemInfo.windowHeight * (750 / systemInfo.windowWidth);
    newValue = vhInRpx + targetExpansions * EXPAND_HEIGHT;
    dynamicHeight.value = `${Math.round(newValue)}rpx`;
    console.log('🔧 [扩展] vh转换计算:', {
      windowHeight: systemInfo.windowHeight,
      windowWidth: systemInfo.windowWidth,
      vhInRpx,
      totalExpansionHeight: targetExpansions * EXPAND_HEIGHT,
      newValue: Math.round(newValue),
    });
  } else {
    // 其他单位直接相加
    newValue = baseValue + targetExpansions * EXPAND_HEIGHT;
    dynamicHeight.value = `${newValue}${unit}`;
    console.log('🔧 [扩展] 直接计算:', {
      baseValue,
      targetExpansions,
      expandHeight: EXPAND_HEIGHT,
      totalExpansionHeight: targetExpansions * EXPAND_HEIGHT,
      newValue,
    });
  }

  expandedTimes.value = targetExpansions;
  console.log('✅ [扩展] 扩展完成!', {
    from: baseHeight.value,
    to: dynamicHeight.value,
    expandedTimes: expandedTimes.value,
    实际计算的新高度: newValue,
    单位: unit,
  });

  // 延迟更新画布尺寸
  nextTick(() => {
    setTimeout(() => {
      updateCanvasSize();
      // 扩展后自动滚动到底部
      scrollToBottom();

      // 扩展完成后，重置调整状态
      setTimeout(() => {
        isAdjustingHeight = false;
        console.log('✅ [扩展] 扩展完成，状态已重置');
      }, 200);
    }, 100);
  });
};

// 收缩画板到指定次数
const shrinkBoard = (targetExpansions: number) => {
  console.log('🔧 [收缩] 收缩画板高度到', targetExpansions, '次');

  if (targetExpansions === 0) {
    // 完全收缩回基础高度
    dynamicHeight.value = baseHeight.value;
  } else {
    // 收缩到指定扩展次数
    const baseValue = parseFloat(baseHeight.value.replace(/[^\d.]/g, ''));
    const unit = baseHeight.value.replace(/[\d.]/g, '');

    let newValue: number;
    if (unit.includes('vh')) {
      const systemInfo = uni.getSystemInfoSync();
      const vhInRpx = (baseValue / 100) * systemInfo.windowHeight * (750 / systemInfo.windowWidth);
      newValue = vhInRpx + targetExpansions * EXPAND_HEIGHT;
      dynamicHeight.value = `${Math.round(newValue)}rpx`;
    } else {
      newValue = baseValue + targetExpansions * EXPAND_HEIGHT;
      dynamicHeight.value = `${newValue}${unit}`;
    }
  }

  expandedTimes.value = targetExpansions;
  console.log('✅ [收缩] 收缩完成!', {
    to: dynamicHeight.value,
    expandedTimes: expandedTimes.value,
  });

  // 延迟更新画布尺寸
  nextTick(() => {
    setTimeout(() => {
      updateCanvasSize();

      // 收缩完成后，重置调整状态
      setTimeout(() => {
        isAdjustingHeight = false;
        console.log('🔄 [收缩] 收缩完成');
      }, 200);
    }, 100);
  });
};

// 更新画布尺寸
const updateCanvasSize = () => {
  if (!ctx.value) return;

  uni
    .createSelectorQuery()
    .in(instance)
    .select('.canvas-container')
    .boundingClientRect((rect: any) => {
      if (rect) {
        const oldHeight = realHeight.value;
        realWidth.value = rect.width;
        realHeight.value = rect.height;

        log('画布尺寸更新', { oldHeight, newHeight: realHeight.value });

        // 重新初始化画布并保持内容
        init();
        setTimeout(() => {
          restoreLine();
        }, 50);
      }
    })
    .exec();
};

// 滚动到底部
const scrollToBottom = () => {
  console.log('📜 [滚动] 开始滚动到底部');

  nextTick(() => {
    // 延迟执行，确保DOM更新完成
    setTimeout(() => {
      // 获取画板容器的高度
      uni
        .createSelectorQuery()
        .in(instance)
        .select('.my-drawing-board')
        .boundingClientRect((boardRect: any) => {
          if (!boardRect) {
            console.log('❌ [滚动] 未找到画板容器');
            return;
          }

          // 获取滚动容器的高度
          uni
            .createSelectorQuery()
            .in(instance)
            .select('.drawing-board-scroll')
            .boundingClientRect((scrollRect: any) => {
              if (!scrollRect) {
                console.log('❌ [滚动] 未找到滚动容器');
                return;
              }

              // 计算需要滚动的距离（滚动到底部，留一些余量）
              const targetScrollTop = Math.max(0, boardRect.height - scrollRect.height + 100);

              console.log('📜 [滚动] 滚动参数:', {
                scrollContainerHeight: scrollRect.height,
                boardHeight: boardRect.height,
                targetScrollTop,
              });

              // 使用 scroll-view 的滚动
              if (targetScrollTop > 0) {
                // 通过设置 scroll-top 属性来滚动
                scrollTop.value = targetScrollTop;
                console.log('✅ [滚动] 滚动到底部完成，scrollTop:', targetScrollTop);
              } else {
                console.log('✅ [滚动] 无需滚动，内容未超出容器');
              }
            })
            .exec();
        })
        .exec();
    }, 200);
  });
};

// 向上滚动
const scrollUp = () => {
  const scrollStep = 200; // 每次滚动200像素
  const newScrollTop = Math.max(0, scrollTop.value - scrollStep);
  scrollTop.value = newScrollTop;
  console.log('⬆️ [滚动] 向上滚动:', {
    from: scrollTop.value + scrollStep,
    to: newScrollTop,
  });
};

// 向下滚动
const scrollDown = () => {
  const scrollStep = 200; // 每次滚动200像素
  // 获取最大可滚动距离
  uni
    .createSelectorQuery()
    .in(instance)
    .select('.my-drawing-board')
    .boundingClientRect((boardRect: any) => {
      if (!boardRect) return;

      uni
        .createSelectorQuery()
        .in(instance)
        .select('.drawing-board-scroll')
        .boundingClientRect((scrollRect: any) => {
          if (!scrollRect) return;

          const maxScrollTop = Math.max(0, boardRect.height - scrollRect.height);
          const newScrollTop = Math.min(maxScrollTop, scrollTop.value + scrollStep);
          scrollTop.value = newScrollTop;

          console.log('⬇️ [滚动] 向下滚动:', {
            from: scrollTop.value - scrollStep,
            to: newScrollTop,
            maxScrollTop,
          });
        })
        .exec();
    })
    .exec();
};

// 初始化画布
const init = () => {
  if (!ctx.value) return;
  log('初始化画布');

  // 使用当前的背景色，确保切换能正常工作
  const bgColor = canvasBgColor.value;
  log('应用背景色:', bgColor);

  ctx.value.fillStyle = bgColor;
  ctx.value.fillRect(0, 0, realWidth.value, realHeight.value);
  ctx.value.draw(true);
};

// 重新绘制所有线条
const restoreLine = () => {
  if (!ctx.value) return;
  if (lineArr.value.length === 0) return;

  log('重新绘制线条，线条数量:', lineArr.value.length);
  ctx.value.beginPath();
  lineArr.value.forEach(item => {
    if (item.length > 1) {
      const firstItem = item[0] as DrawingRecord;

      if (firstItem.isEraser) {
        ctx.value.strokeStyle = canvasBgColor.value;
      } else if (firstItem.color) {
        ctx.value.strokeStyle = firstItem.color;
      }

      ctx.value.lineWidth = firstItem.width;

      // 设置起始点
      const secondItem = item[1] as DrawingRecord;
      if (secondItem.x !== undefined && secondItem.y !== undefined) {
        ctx.value.moveTo(secondItem.x, secondItem.y);

        // 绘制路径中的所有点
        item.forEach((point, index) => {
          if (index === 0) return;

          if (point.x !== undefined && point.y !== undefined) {
            ctx.value.lineTo(point.x, point.y);
          }
        });

        ctx.value.stroke();
        ctx.value.draw(true);
      }
    }
  });
};

// 从导入数据恢复画板
const restoreFromData = (data: DrawingData) => {
  if (!data) return;
  log('正在从数据恢复画板:', data);

  // 确保ctx已初始化
  if (!ctx.value) {
    console.log('Canvas上下文尚未初始化');
    return;
  }

  // 优先使用data中的背景色，如果没有则使用props.bgColor
  if (data.bgColor) {
    const bgColor = validateBackgroundColor(data.bgColor);
    canvasBgColor.value = bgColor;
    log('从data恢复背景色:', bgColor);
  } else {
    const bgColor = validateBackgroundColor(props.bgColor || '#ffffff');
    canvasBgColor.value = bgColor;
    log('使用props背景色:', bgColor);
  }

  // 只恢复线条颜色，线条粗细始终使用组件默认值
  lineColor.value = data.lineColor;
  // lineWidth.value = data.lineWidth; // 不再从数据中恢复线条粗细
  log('线条粗细保持默认值:', lineWidth.value, '(数据中的值被忽略:', data.lineWidth, ')');
  lineArr.value = [...data.lines]; // 使用解构拷贝确保数据完整复制

  // 立即重绘
  init();
  setTimeout(() => {
    log('重新绘制线条，线条数量:', lineArr.value.length);
    restoreLine();
    // 恢复数据后检测是否需要调整画板高度
    adjustBoardHeight();
  }, 300); // 给一点延迟确保初始化完成
};

// 对外暴露的方法：设置画板内容
const setDrawingData = (data: DrawingData) => {
  if (!data) {
    console.error('设置画板内容失败：数据为空');
    return false;
  }

  try {
    log('通过setDrawingData方法设置画板数据', data);
    restoreFromData(data);
    return true;
  } catch (err) {
    console.error('设置画板内容失败:', err);
    return false;
  }
};

// 打开/关闭调试模式
const setDebugMode = (enabled: boolean) => {
  log(`${enabled ? '开启' : '关闭'}调试模式`);
  return enabled;
};

// 监听Props变化
// watch(
//   () => props.bgColor,
//   newVal => {
//     canvasBgColor.value = newVal;
//     if (ctx.value) {
//       init(); // 背景色变化时重新初始化画布
//       restoreLine(); // 重新绘制线条
//     }
//   },
//   { immediate: true }
// );

// watch(
//   () => props.lineColor,
//   newVal => {
//     lineColor.value = newVal;
//   },
//   { immediate: true }
// );

// watch(
//   () => props.lineWidth,
//   newVal => {
//     lineWidth.value = newVal;
//   },
//   { immediate: true }
// );

// 监听数据变化，用于导入数据
watch(
  () => props.data,
  newData => {
    if (newData) {
      log('检测到初始数据，开始恢复');
      restoreFromData(newData);
    }
  },
  { immediate: true }
);

watch(
  () => props.debug,
  newVal => {
    setDebugMode(newVal);
  },
  { immediate: true }
);

// 菜单点击处理
const handleClick = (item: MenuItem) => {
  if (item.action !== null) {
    setup.value = item.action;
    if (item.action !== 4) {
      isEraser.value = false;
    } else {
      isEraser.value = true;
    }
  }

  if (item.action === null) {
    if (item.type === 2) {
      // 撤回操作
      log('撤销上一步操作');
      lineArr.value.pop();
      init();
      restoreLine();
      // 检测是否需要调整画板高度
      adjustBoardHeight();
    }
    if (item.type === 3) {
      // 清空画板
      console.log('🧹 [清空] 开始清空画板');
      lineArr.value = [];

      // 清空时重置所有颜色和线宽为默认值
      canvasBgColor.value = validateBackgroundColor(props.bgColor || '#ffffff');
      lineColor.value = props.lineColor;
      lineWidth.value = props.lineWidth;
      eraserSize.value = Math.max(20, props.lineWidth); // 重置橡皮擦大小，最小值为20

      // 重置工具状态
      setup.value = 0;
      isEraser.value = false;
      eraserIndicator.visible = false;

      // 重置高度扩展状态
      expandedTimes.value = 0;
      dynamicHeight.value = baseHeight.value;

      // 重置滚动位置
      scrollTop.value = 0;

      // 重置工具栏拖动状态
      toolbarDrag.x = 0;
      toolbarDrag.y = 0;
      toolbarDrag.isDragging = false;

      console.log('🧹 [清空] 重置完成:', {
        bgColor: canvasBgColor.value,
        lineColor: lineColor.value,
        lineWidth: lineWidth.value,
        expandedTimes: expandedTimes.value,
        dynamicHeight: dynamicHeight.value,
        scrollTop: scrollTop.value,
        toolbarResetToOriginal: { x: toolbarDrag.x, y: toolbarDrag.y },
      });

      init();

      // 延迟更新画布尺寸，确保高度重置生效
      nextTick(() => {
        setTimeout(() => {
          console.log('🧹 [清空] 开始更新画布尺寸');
          updateCanvasSize();
          console.log('🧹 [清空] 画布尺寸更新完成，准备进行高度检测测试');
        }, 100);
      });
    }
  }
};

// 工具选择
const selectTool = (tool: string) => {
  log('选择工具:', tool);
  if (tool === 'bgcolor') {
    if (setup.value === 1) {
      setup.value = -1;
    } else {
      setup.value = 1;
      isEraser.value = false;
      eraserIndicator.visible = false;
    }
  } else if (tool === 'pen') {
    if (setup.value === 0 && !isEraser.value) {
      setup.value = -1;
      isEraser.value = false;
      eraserIndicator.visible = false;
    } else {
      setup.value = 0;
      isEraser.value = false;
      eraserIndicator.visible = false;
    }
  } else if (tool === 'text') {
    if (setup.value === 3) {
      setup.value = -1;
    } else {
      setup.value = 3;
      isEraser.value = false;
      eraserIndicator.visible = false;
    }
  } else if (tool === 'color') {
    if (setup.value === 2) {
      setup.value = -1;
    } else {
      setup.value = 2;
      isEraser.value = false;
      eraserIndicator.visible = false;
    }
  } else if (tool === 'eraser') {
    if (isEraser.value) {
      isEraser.value = false;
      setup.value = -1;
      eraserIndicator.visible = false;
    } else {
      isEraser.value = true;
      setup.value = 4;
    }
  }
};

// 触摸处理
const touchstart = (e: any) => {
  if (!ctx.value) return;

  ctx.value.beginPath();
  start.x = e.touches[0].x;
  start.y = e.touches[0].y;

  if (isEraser.value) {
    temp.value.push({ isEraser: true, width: eraserSize.value });
    // 显示橡皮擦指示器，设置合适的大小
    eraserIndicator.x = e.touches[0].x;
    eraserIndicator.y = e.touches[0].y;
    // 指示器大小与橡皮擦实际大小一致，最小20px
    eraserIndicator.size = Math.max(20, eraserSize.value);
    eraserIndicator.visible = true;
  } else {
    temp.value.push({ color: lineColor.value, width: lineWidth.value });
  }

  temp.value.push({ x: e.touches[0].x, y: e.touches[0].y } as any);

  if (isEraser.value) {
    ctx.value.strokeStyle = canvasBgColor.value;
    ctx.value.lineWidth = eraserSize.value;
  } else {
    ctx.value.strokeStyle = lineColor.value;
    ctx.value.lineWidth = lineWidth.value;
  }
  ctx.value.lineCap = 'round';

  log('触摸开始:', { x: start.x, y: start.y });
};

const touchmove = (e: any) => {
  end.x = e.touches[0].x;
  end.y = e.touches[0].y;
  temp.value.push({ x: end.x, y: end.y } as any);

  // 节流更新橡皮擦指示器位置，减少性能消耗
  if (isEraser.value) {
    const now = Date.now();
    if (now - lastIndicatorUpdate >= INDICATOR_UPDATE_INTERVAL) {
      eraserIndicator.x = e.touches[0].x;
      eraserIndicator.y = e.touches[0].y;
      lastIndicatorUpdate = now;
    }
  }

  draw();

  if (props.debug) {
    log('触摸移动:', { x: end.x, y: end.y });
  }
};

const touchend = () => {
  log('触摸结束，线条点数:', temp.value.length);

  // 分析本次绘制的Y坐标范围
  let minY = Infinity;
  let maxY = 0;
  temp.value.forEach(point => {
    if (point.y !== undefined) {
      minY = Math.min(minY, point.y);
      maxY = Math.max(maxY, point.y);
    }
  });

  console.log('🎨 [本次绘制] Y坐标分析:', {
    本次最小Y: minY === Infinity ? '无' : minY,
    本次最大Y: maxY,
    绘制点数: temp.value.length,
    画布高度: realHeight.value,
    固定触发阈值: realHeight.value - BOTTOM_DETECTION_FIXED_HEIGHT,
    距离底部: realHeight.value - maxY,
    是否触发扩展: maxY > realHeight.value - BOTTOM_DETECTION_FIXED_HEIGHT,
  });

  start.x = 0;
  start.y = 0;
  end.x = 0;
  end.y = 0;

  // 隐藏橡皮擦指示器
  if (isEraser.value) {
    eraserIndicator.visible = false;
  }

  // 保存当前绘制的线条
  lineArr.value.push([...temp.value]);
  temp.value = [];

  console.log('🎨 [画板] 绘制完成，开始检测高度调整');
  console.log('📏 [画板] 当前画布高度:', realHeight.value);
  console.log('📝 [画板] 总线条数:', lineArr.value.length);

  // 检测是否需要调整画板高度
  adjustBoardHeight();
};

// 绘制线条
const draw = () => {
  if (!ctx.value) return;

  ctx.value.moveTo(start.x, start.y);
  ctx.value.lineTo(end.x, end.y);
  ctx.value.stroke();
  ctx.value.draw(true);

  // 更新起点为当前终点，准备下一次绘制
  start.x = end.x;
  start.y = end.y;
};

// 滑块改变处理
const sliderChange = (e: SliderEvent) => {
  lineWidth.value = e.detail.value;
  // 如果当前是橡皮擦模式，更新指示器大小
  if (isEraser.value) {
    // 确保指示器大小合理，最小20px，最大60px
    eraserIndicator.size = Math.max(20, Math.min(60, lineWidth.value * 2));
  }
  log('线宽变更:', lineWidth.value);
};

// 橡皮擦大小改变处理
const eraserSizeChange = (e: SliderEvent) => {
  eraserSize.value = Math.max(20, e.detail.value); // 确保最小值为20
  // 如果当前是橡皮擦模式，更新指示器大小
  if (isEraser.value) {
    eraserIndicator.size = Math.max(20, eraserSize.value); // 指示器大小与橡皮擦实际大小一致，最小20px
  }
  log('橡皮擦大小变更:', eraserSize.value);
};

// 工具栏拖动处理
const onToolbarTouchStart = (e: any) => {
  toolbarDrag.isDragging = true;
  toolbarDrag.startX = e.touches[0].clientX - toolbarDrag.x;
  toolbarDrag.startY = e.touches[0].clientY - toolbarDrag.y;
  e.stopPropagation(); // 防止触发画布事件
};

const onToolbarTouchMove = (e: any) => {
  if (!toolbarDrag.isDragging) return;

  e.preventDefault();
  e.stopPropagation();

  toolbarDrag.x = e.touches[0].clientX - toolbarDrag.startX;
  toolbarDrag.y = e.touches[0].clientY - toolbarDrag.startY;
};

const onToolbarTouchEnd = (e: any) => {
  toolbarDrag.isDragging = false;
  e.stopPropagation();
};

// 颜色改变处理
const changeColor = (color: string) => {
  log('颜色变更:', setup.value, color);
  if (setup.value === 1) {
    // 背景色变更时，确保不是透明色
    const bgColor = validateBackgroundColor(color);
    log('背景色变更:', { original: color, validated: bgColor, current: canvasBgColor.value });
    canvasBgColor.value = bgColor;
    log('背景色已更新为:', canvasBgColor.value);
    init();
    restoreLine();
  }
  if (setup.value === 2) {
    lineColor.value = color;
  }
};

// 创建结构化数据
const createDrawingData = (): DrawingData => {
  const data = {
    bgColor: validateBackgroundColor(canvasBgColor.value),
    lineWidth: lineWidth.value,
    lineColor: lineColor.value,
    lines: lineArr.value,
    canvasWidth: realWidth.value,
    canvasHeight: realHeight.value,
    version: '1.0.0', // 版本号，用于后续兼容性判断
  };
  log('创建绘图数据:', data);
  return data;
};

// 保存并导出画板内容
const save = () => {
  log('开始保存画板');

  // 检查画板是否有内容
  if (lineArr.value.length === 0) {
    log('画板无内容，返回空结果');
    // 直接返回空结果
    emit('save', {
      imageUrl: '',
      data: null,
      tempFilePath: '',
    });
    return;
  }
  // 生成结构化数据
  const drawingData = createDrawingData();

  // 导出图片
  uni.canvasToTempFilePath(
    {
      canvasId: 'canvasId',
      fileType: 'png',
      success: async (res: { tempFilePath: string }) => {
        // 同时返回图片和结构化数据
        const result: ExportResult = {
          imageUrl: res.tempFilePath,
          data: drawingData,
        };

        emit('save', result);

        // uni.showToast({
        //   title: '保存成功',
        //   icon: 'success',
        // });

        log('画板保存成功');
      },
      fail(err: any) {
        console.error('画板保存失败:', err);
        uni.showModal({
          title: '温馨提示',
          content: JSON.stringify(err),
        });
      },
    },
    instance
  );
};

// 关闭画板
const closeDrawing = () => {
  log('关闭画板');
  emit('close');
};

const toolbarRef = ref();

// 组件挂载完成后初始化
onMounted(() => {
  log('组件已挂载，初始化画布');
  log('props.data:', props.data);

  // 初始化高度状态
  // 临时改为固定高度进行测试
  baseHeight.value = props.customHeight || '1000rpx';
  dynamicHeight.value = baseHeight.value;

  console.log('[组件初始化] 高度状态初始化:', {
    baseHeight: baseHeight.value,
    dynamicHeight: dynamicHeight.value,
    expandedTimes: expandedTimes.value,
  });

  // 动态获取容器宽高
  uni
    .createSelectorQuery()
    .in(instance)
    .select('.canvas-container')
    .boundingClientRect((rect: any) => {
      if (rect) {
        realWidth.value = rect.width;
        realHeight.value = rect.height;

        console.log('[组件初始化] 画布容器尺寸:', {
          width: realWidth.value,
          height: realHeight.value,
        });
      }
      ctx.value = uni.createCanvasContext('canvasId', instance);

      // 确保初始化时使用正确的背景色
      if (props.data) {
        log('使用props.data初始化:', props.data);
        restoreFromData(props.data);
      } else {
        log('没有props.data，使用默认初始化');
        // 如果没有初始数据，强制初始化背景色
        init();
      }
    })
    .exec();
});

// 对外暴露方法
defineExpose({
  save,
  init,
  setDrawingData,
  setDebugMode,
});
</script>

<style lang="scss">
.drawing-board-scroll {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
}

.my-drawing-board {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  position: relative;
  min-height: 100vh;
}

/* 顶部操作栏样式 */
.header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e7eb;
  background: #fff;
  // padding-top: var(--status-bar-height);
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
}

.header-left {
  gap: 24rpx;
}

.header-right {
  gap: 24rpx;
}

.save-btn,
.clear-btn {
  font-size: 32rpx;
}

.save-btn {
  color: #7d4dff;
}

.clear-btn {
  color: #4e5969;
}

.header-divider {
  width: 1rpx;
  height: 32rpx;
  background: #e5e7eb;
  margin: 0 8rpx;
}

.undo-btn {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: transparent;
  transition: background 0.2s;
}

.undo-btn:active {
  background: #f5f6fa;
}

.close-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16rpx;
  background: transparent;
  transition: background 0.2s;
  margin-left: 8rpx;
}

.close-btn:active {
  background: #f5f6fa;
}

.icon {
  width: 34rpx;
  height: 34rpx;
  filter: grayscale(1) brightness(0.6);
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 画布样式 */
.canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
}

/* 橡皮擦指示器样式 */
.eraser-indicator {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
  border-radius: 50%;
  border: 2rpx solid #999;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  /* 移除过度动画，避免性能消耗 */
  /* transition: all 0.1s ease; */
  /* 设置最小尺寸，确保指示器不会太小 */
  min-width: 40rpx;
  min-height: 40rpx;
  /* 简化阴影效果，减少性能消耗 */
  /* box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.4); */
  /* 使用更轻量的视觉效果 */
  opacity: 0.8;
}

/* 移除不再使用的内部元素样式，简化DOM结构 */

/* 底部工具栏样式 */
.toolbar-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 200rpx;
  left: 0;
  right: 0;
  z-index: 1000;
  pointer-events: none;
  /* 让容器本身不阻止事件 */
}

.toolbar {
  position: relative;
  width: 750rpx;
  /* 增加宽度以容纳新按钮 */
  height: 104rpx;
  background-color: #ffffff;
  border-radius: 52rpx;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0rpx 20rpx;
  cursor: move;
  user-select: none;
  pointer-events: auto;
  /* 恢复工具栏本身的事件响应 */
}

.tool-group {
  display: flex;
  align-items: center;
  height: 80rpx;
}

.tool-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 80rpx;
  border-radius: 16rpx;
  padding: 0 10rpx;
}

.tool-item.active {
  background-color: #f5f6f8;
}

.tool-icon {
  width: 54rpx;
  height: 54rpx;
}

.color-picker {
  width: 46rpx;
  height: 46rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.color-circle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #e5e7eb;
  box-sizing: border-box;
}

.dropdown-arrow {
  width: 28rpx;
  height: 24rpx;
  margin-left: 20rpx;
}

.divider {
  width: 1rpx;
  height: 50rpx;
  background-color: #e5e7eb;
  margin: 0 10rpx;
}

/* 设置面板样式 */
.setting-panel {
  position: absolute;
  background-color: #ffffff;
  border: 1rpx solid #e5e7eb;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 20rpx;
  z-index: 100;
  width: 396rpx;
}

.setting-title {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 10rpx;
}

.setting-value {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 28rpx;
  color: #4e5969;
}

.slider-container {
  margin-top: 20rpx;
  padding: 0;
}

.color-grid {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  gap: 20rpx;
}

.color-item {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  border: 1rpx solid #e5e7eb;
  box-sizing: border-box;
  position: relative;
}

.color-item.active {
  position: relative;
}

.color-item.active::after {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border: 4rpx solid #b184ff;
  border-radius: 50%;
  pointer-events: none;
}

.custom-slider {
  margin: 0 !important;
  width: 100%;
}

/* 滚动按钮样式 */
.scroll-button {
  opacity: 0.7;
  transition:
    opacity 0.2s,
    background-color 0.2s;
}

.scroll-button:active {
  opacity: 1;
  background-color: #f0f0f0;
}

.scroll-button .tool-icon {
  filter: grayscale(0.3);
}

/* 向上滚动箭头旋转180度 */
.scroll-up-icon {
  transform: rotate(180deg);
}

/* .scroll-down-icon 保持原始方向，无需额外样式 */

/* 扩展区域指示器样式 */
.expansion-indicator {
  position: absolute;
  bottom: 0;
  /* 从画布底部开始 */
  left: 0;
  right: 0;
  height: 100px;
  /* 扩展触发区域的高度 */
  border: 2px dashed #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  pointer-events: none;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.expansion-zone-label {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.expansion-zone-info {
  font-size: 20rpx;
  color: #ff6b6b;
  opacity: 0.8;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.expansion-debug {
  font-size: 18rpx;
  color: #0066cc;
  margin-top: 4rpx;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

.area-container {
  position: relative;
}
</style>
