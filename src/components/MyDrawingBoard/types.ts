/**
 * 绘图板组件类型定义
 */

/** 位置坐标接口 */
export interface Position {
  x: number;
  y: number;
}

/** 绘图记录接口 */
export interface DrawingRecord {
  color?: string;
  width: number;
  isEraser?: boolean;
  x?: number;
  y?: number;
}

/** 菜单项接口 */
export interface MenuItem {
  label: string;
  icon: string;
  action: number | null;
  type: number;
}

/** 触摸事件接口 */
export interface TouchEvent {
  touches: Array<{
    x: number;
    y: number;
  }>;
}

/** 滑块事件接口 */
export interface SliderEvent {
  detail: {
    value: number;
  };
}

/** 橡皮擦指示器接口 */
export interface EraserIndicator {
  x: number;
  y: number;
  size: number;
  visible: boolean;
}

/** 绘图数据结构 */
export interface DrawingData {
  bgColor: string;
  lineWidth: number;
  lineColor: string;
  lines: DrawingRecord[][];
  canvasWidth: number;
  canvasHeight: number;
  version: string;
}

/** 导出结果接口 */
export interface ExportResult {
  imageUrl: string;
  data: DrawingData | null;
  tempFilePath?: string;
  imageFile?: any;
}
