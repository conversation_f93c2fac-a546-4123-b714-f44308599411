import { resolveUrl } from './url';
import EventSourceRaw from '@/common/network/event-source';
import dayjs from '../../utils/day';
import { RouteConfigManager } from '@/router/config';

// Add interface for EventSource constructor options
interface EventSourceOptions {
  url: string;
  method: string;
  header: Record<string, string>;
  data: any;
  onOpen?: (res: any) => void;
  onMessage?: (event: { event: string; data: string }) => void;
  onError?: (err: any) => void;
  onEnd?: () => void;
}

interface EventSourceInstance {
  close: () => void;
  onOpen?: (res: any) => void;
  onMessage?: (event: { event: string; data: string }) => void;
  onError?: (err: any) => void;
  onEnd?: () => void;
}

interface EventSourceConstructor {
  new (options: EventSourceOptions): EventSourceInstance;
}

const TypedEventSource = EventSourceRaw as unknown as EventSourceConstructor;

export const ChatRoleEnum = {
  system: 'System',
  human: 'Human',
  ai: 'AI',
  function: 'Function',
  tool: 'Tool',
} as const;

export type ChatRole = (typeof ChatRoleEnum)[keyof typeof ChatRoleEnum];

export const MessageRoleEnum = {
  system: 'system',
  user: 'user',
  assistant: 'assistant',
  function: 'function',
  tool: 'tool',
} as const;

export type MessageRole = (typeof MessageRoleEnum)[keyof typeof MessageRoleEnum];

export const SseEventEnum = {
  error: 'error',
  answer: 'answer',
  fastAnswer: 'fastAnswer',
  flowNodeStatus: 'flowNodeStatus',
  toolCall: 'toolCall',
  toolParams: 'toolParams',
  toolResponse: 'toolResponse',
  flowResponses: 'flowResponses',
  updateVariables: 'updateVariables',
} as const;

export type SseEvent = (typeof SseEventEnum)[keyof typeof SseEventEnum];

const chatMessageRoleMap = {
  [ChatRoleEnum.system]: MessageRoleEnum.system,
  [ChatRoleEnum.human]: MessageRoleEnum.user,
  [ChatRoleEnum.ai]: MessageRoleEnum.assistant,
  [ChatRoleEnum.function]: MessageRoleEnum.function,
  [ChatRoleEnum.tool]: MessageRoleEnum.tool,
};

const messageChatRoleMap = {
  [MessageRoleEnum.system]: ChatRoleEnum.system,
  [MessageRoleEnum.user]: ChatRoleEnum.human,
  [MessageRoleEnum.assistant]: ChatRoleEnum.ai,
  [MessageRoleEnum.function]: ChatRoleEnum.function,
  [MessageRoleEnum.tool]: ChatRoleEnum.tool,
};

interface ChatMessage {
  dataId?: string;
  obj: ChatRole;
  content?: string;
}

interface ApiMessage {
  dataId?: string;
  role: MessageRole;
  content: string;
}

export function adaptChat2Messages(messages: ChatMessage[], reserveId = true): ApiMessage[] {
  return messages.map(it => ({
    ...(reserveId && {
      dataId: it.dataId,
    }),
    content: it.content || '',
    role: chatMessageRoleMap[it.obj],
  }));
}

const requestAnimationFrame = (callback: () => void): number => {
  return setTimeout(callback, 1000 / 60) as unknown as number;
};

const replaceSensitiveLink = (text: string): string => {
  const urlRegex = /(?<=https?:\/\/)[^\s]+/g;
  return text.replace(urlRegex, 'xxx');
};

const getErrText = (err: any, def = ''): string => {
  const msg = typeof err === 'string' ? err : err?.message || def || '';
  if (msg) console.log('error =>', msg);
  return replaceSensitiveLink(msg);
};

interface MessageItem {
  event: SseEvent;
  text?: string;
  reasoningText?: string;
  [key: string]: any;
}

export interface AbortSignalType {
  aborted: boolean;
  reason: string;
  addEventListener: (listener: (reason?: string) => void) => void;
  removeEventListener: (listener: (reason?: string) => void) => void;
  abort: (reason?: string) => void;
}

interface FetchParams {
  url?: string;
  data?: any;
  chatType?: string | number;
  onMessage: (item: MessageItem) => void;
  abortSignal?: AbortSignalType;
}

interface FetchResult {
  responseText: string;
  responseData: any[];
  reasoningText?: string;
}

export const fetch = ({
  url = '/huayun-ai/client/chat/completions',
  data,
  onMessage,
  abortSignal,
}: FetchParams): Promise<FetchResult> => {
  return new Promise((resolve, reject) => {
    let responseText = '';
    let reasoningText = '';
    let responseQueue: MessageItem[] = [];
    let errMsg = '';
    let responseData: any[] = [];
    let finished = false;

    const timeoutId = setTimeout(() => {
      abortSignal?.abort('Time out');
    }, 120000);

    const finish = (): void => {
      if (errMsg) {
        return failedFinish();
      }
      resolve({
        responseText,
        reasoningText,
        responseData,
      });
    };

    const failedFinish = (err?: any): void => {
      finished = true;
      reject(new Error(getErrText(err, errMsg || '响应过程出现异常~')));
    };

    const isAnswerEvent = (event: SseEvent): boolean =>
      event === SseEventEnum.answer || event === SseEventEnum.fastAnswer;

    const callerOnMessage = onMessage;

    const animateResponseText = (): void => {
      if (abortSignal?.aborted) {
        responseQueue.forEach(item => {
          callerOnMessage(item);
          if (isAnswerEvent(item.event)) {
            responseText += item.text || '';
            reasoningText += item.reasoningText || '';
          }
        });
        return finish();
      }
      if (responseQueue.length > 0) {
        const fetchCount = Math.max(1, Math.round(responseQueue.length / 30));
        for (let i = 0; i < fetchCount && i < responseQueue.length; i++) {
          const item = responseQueue[i];
          callerOnMessage(item);
          if (isAnswerEvent(item.event)) {
            responseText += item.text || '';
            reasoningText += item.reasoningText || '';
          }
        }
        responseQueue = responseQueue.slice(fetchCount);
      }

      if (finished && responseQueue.length === 0) {
        return finish();
      }

      requestAnimationFrame(animateResponseText);
    };

    animateResponseText();

    try {
      const variables = data?.variables || {};
      variables.cTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

      const eventSource = new TypedEventSource({
        url: resolveUrl(url),
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          Authorization: uni.getStorageSync('token'),
        },
        data: {
          ...data,
          variables,
          detail: true,
          stream: true,
        },
        onOpen: res => {
          clearTimeout(timeoutId);
          if (res.statusCode !== 200) {
            failedFinish('Status code: ' + res.statusCode);
          }
          if ([401, 403, 406].includes(res.statusCode)) {
            uni.redirectTo({
              url: RouteConfigManager.getLoginPage(),
            });
          }
        },
        onMessage: (eventData: { event: string; data: string }) => {
          const { event, data: eventPayload } = eventData;

          if (eventPayload === '[DONE]') {
            return;
          }

          const parseJson = (() => {
            try {
              return JSON.parse(eventPayload);
            } catch (error) {
              console.warn(
                '[fetch.ts] Failed to parse JSON from event data:',
                eventPayload,
                'Error:',
                error
              );
              return {};
            }
          })();

          if (event === SseEventEnum.answer || event === SseEventEnum.fastAnswer) {
            const text = parseJson?.choices?.[0]?.delta?.content || '';
            const reasoningText = parseJson?.choices?.[0]?.delta?.reasoning_content || '';
            if (event === SseEventEnum.answer) {
              for (const char of text) {
                responseQueue.push({ event: event as SseEvent, text: char });
              }
              for (const char of reasoningText) {
                responseQueue.push({ event: event as SseEvent, reasoningText: char });
              }
            } else {
              responseQueue.push({ event: event as SseEvent, text });
            }
          } else if (
            event === SseEventEnum.toolCall ||
            event === SseEventEnum.toolParams ||
            event === SseEventEnum.toolResponse
          ) {
            responseQueue.push({ event: event as SseEvent, ...parseJson });
          } else if (event === SseEventEnum.flowNodeStatus) {
            callerOnMessage({ event: event as SseEvent, ...parseJson });
          } else if (event === SseEventEnum.flowResponses && Array.isArray(parseJson)) {
            responseData = parseJson;
          } else if (event === SseEventEnum.updateVariables) {
            callerOnMessage({ event: event as SseEvent, variables: parseJson });
          } else if (event === SseEventEnum.error) {
            console.error(
              '************ [fetch.ts] EventSource onMessage: Received error event from stream:',
              parseJson
            );
            errMsg = getErrText(parseJson, '流响应错误');
          } else {
          }
        },
        onError: (err: any) => {
          console.error('>>>>>> [fetch.ts] EventSource ON_ERROR_CALLBACK CALLED. Error:', err);
          clearTimeout(timeoutId);
          failedFinish(getErrText(err));
        },
        onEnd: () => {
          finished = true;
        },
      });

      abortSignal?.addEventListener(() => {
        eventSource.close();
      });
    } catch (err) {
      clearTimeout(timeoutId);
      if (abortSignal?.aborted) {
        finished = true;
        return finish();
      }
      console.error('[fetch.ts] Error during EventSource setup:', err);
      failedFinish(err);
    }
  });
};
