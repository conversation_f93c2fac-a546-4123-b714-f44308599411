<template>
  <view class="container">
    <view
      class="create-app"
      @tap="handleCreateApp"
      :style="{
        bottom: `calc(${tabContainerPaddingBottom} + ${props.platform === 'ios' ? '170rpx' : '140rpx'} + 10px)`,
      }"
    >
      <view class="create-app-text">
        <image
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/f10573eaeac5b525dd70c43373fdbfb9.svg"
        />
        创建应用
      </view>
    </view>

    <!-- 顶部导航栏 -->
    <view class="app-center-header">
      <!-- <u-navbar title="应用中心" :autoBack="false"></u-navbar> -->
      <view class="text-search">
        <view class="text-search-title">应用中心</view>
        <view class="text-search-input" @tap="handleSearch">
          <LkSvg width="24px" height="24px" src="/static/database/search.svg" />
        </view>
      </view>
      <!-- 锚点定位 tab -->
      <u-tabs
        :list="[
          {
            id: 'personage',
            name: '我的应用',
          },
          ...scenesList,
        ]"
        lineColor="transparent"
        keyName="name"
        v-model:current="currentTab"
        class="app-list-tabs"
        itemStyle="height: 80rpx;"
        :scrollable="true"
      >
        <template #content="{ item, keyName, index }">
          <view class="app-list-tabs-item" @tap="handleTabClick(item, index)">
            <view class="app-list-tabs-item-box">
              <view
                class="app-list-tabs-item-text"
                :class="currentTab === index ? 'active' : 'inactive'"
              >
                {{ item[keyName] }}
              </view>

              <image
                v-show="currentTab === index"
                class="app-list-tabs-item-icon"
                src="https://huayun-ai-obs-public.huayuntiantu.com/a4180d57745271fa2056e474797826f7.png"
              >
              </image>
            </view>
          </view>
        </template>
        <template #left>
          <view class="app-list-tabs_empty"></view>
        </template>
      </u-tabs>
    </view>

    <view class="app-list-container">
      <LkLoading v-if="loading" :loading="loading" class="loading-icon" />

      <!-- 应用列表 -->
      <scroll-view
        scroll-y
        class="app-list"
        :class="{ 'ios-scroll': systemInfo?.platform === 'ios' }"
        :style="{
          height: getAppListHeight(),
        }"
        :scroll-into-view="targetId"
        :show-scrollbar="false"
        :bounces="false"
        :scroll-with-animation="true"
        @scroll="handleScroll"
      >
        <uni-swipe-action
          style="position: relative"
          v-for="(item, outerIndex) in combinedList"
          :id="'tab-' + item?.sceneId"
        >
          <view class="app-list-container" :key="item?.sceneId">
            <view class="app-list-item-title">
              <view class="title-box">
                <view class="title-box-text">{{ item?.name }}</view>
                <image
                  v-if="checkString(item?.name)"
                  class="app-list-item-title-icon"
                  src="https://huayun-ai-obs-public.huayuntiantu.com/77c2698413255275ad29152c8d515e1f.png"
                />
                <image
                  v-else
                  class="app-list-item-title-icon-long"
                  src="https://huayun-ai-obs-public.huayuntiantu.com/33fc7ff8-3dc3-48fc-a832-dc844acc701b.png"
                />
              </view>
            </view>

            <uni-swipe-action-item
              v-for="(appItem, innerIndex) in item?.apps"
              class="app-list-item"
              :auto-close="true"
              :show="openedStates[appItem.id]"
              :disabled="appItem.source !== DataSource.Personal"
              @change="(e: any) => handleSwipeActionChange(e, appItem.id)"
            >
              <view class="app-list-item-content" @tap.stop="handleItemClick(appItem)">
                <view class="icon">
                  <image
                    :src="
                      appItem?.avatarUrl ||
                      'https://huayun-ai-obs-public.huayuntiantu.com/7dd8c2f8d3b0d94cc769ccee58f8b753.svg'
                    "
                    lazy-load
                  />
                </view>

                <view class="box">
                  <view class="box-title">{{ appItem?.name }}</view>
                  <view class="box-text">{{ appItem.intro }}</view>
                  <view class="box-icon">
                    <image :src="appSourceImgObj[appItem?.source]" />
                    <text>{{ appSourceTextObj[appItem?.source] }}</text>
                  </view>
                </view>
                <view
                  :class="[
                    'box-btn',
                    {
                      'index2-guide-target': outerIndex === 1 && innerIndex === 0,
                    },
                  ]"
                >
                  <view
                    :class="[
                      'btn',
                      {
                        'guide-target':
                          outerIndex === guideOuterIdx && innerIndex === guideInnerIdx,
                      },
                    ]"
                  >
                    <!-- 添加按钮 -->
                    <view
                      class="btn-add"
                      v-if="!appItem.isCommonApp"
                      @tap.stop="!processingIds[appItem.id] && handleSetCommonApp(appItem.id)"
                      :class="{
                        'btn-disabled': processingIds[appItem.id],
                      }"
                    >
                      <!-- 如果正在处理该ID，显示loading图标 -->
                      <u-loading-icon
                        v-if="processingIds[appItem.id]"
                        mode="circle"
                        size="18rpx"
                        color="#FFFFFF"
                      ></u-loading-icon>
                      <image
                        v-else
                        src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/767d109fe43f6ea906d95e3ba986ed0c.svg"
                      />
                    </view>

                    <!-- 完成按钮 -->
                    <view
                      class="btn-done"
                      v-else
                      @tap.stop="!processingIds[appItem.id] && handleRemoveCommonApp(appItem.id)"
                      :class="{ 'btn-disabled': processingIds[appItem.id] }"
                    >
                      <!-- 如果正在处理该ID，显示loading图标 -->
                      <u-loading-icon
                        v-if="processingIds[appItem.id]"
                        mode="circle"
                        size="18rpx"
                        color="#909399"
                      ></u-loading-icon>
                      <image
                        v-else
                        src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3b1b53849070aeba988002f23dc97b62.svg"
                      />
                    </view>
                  </view>
                </view>
              </view>

              <template v-slot:right>
                <view class="slide-btn-container">
                  <view v-if="!deleteStates[appItem.id]" class="slide-btn">
                    <view class="slide-btn-item" @touchstart.stop.prevent="handleEditApp(appItem)">
                      <image
                        src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/d5e3c541edb2e8c317d6d04b35097428.svg"
                      />
                    </view>
                    <view class="slide-btn-item" @touchstart.stop="handleDelete(appItem.id)">
                      <image
                        src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3d7681a3318bfc7f020f70f91973d526.svg"
                      />
                    </view>
                  </view>
                  <view class="slide-btn" v-else>
                    <view
                      class="slide-pop-btn"
                      @touchstart.stop="confirmDelete(appItem.id, appItem.tmbId)"
                    >
                      <image
                        src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3d7681a3318bfc7f020f70f91973d526.svg"
                      />
                      <text>确认删除</text>
                    </view>
                  </view>
                </view>
              </template>
            </uni-swipe-action-item>
          </view>
          <view
            style="height: 10rpx; background: #fff; visibility: hidden"
            v-if="outerIndex === combinedList.length - 1"
            class="app-list-item-placeholder"
          ></view>
        </uni-swipe-action>
      </scroll-view>
    </view>
  </view>
  <LkToast ref="uToastRef" />
  <LkStepGuide
    :steps="guideSteps"
    @complete="onGuideComplete"
    @next="onGuideNext"
    ref="stepGuide"
  />
</template>

<script setup lang="ts">
import {
  deleteClientApp,
  getMyAppList,
  getOtherAppList,
  getTenantSceneList,
  getTenantSceneListOfficialHome,
  rmCommonApp,
  setCommonApp,
} from '@/api/app-center';
import { SearchType } from '@/constants/search';
import { ref, onMounted, computed, watch, toRef, nextTick, onUnmounted } from 'vue';
import { useAppStore } from '@/store/useAppStore';
import type { AppListItemType } from '@/types/api/app-center';
import { onLoad, onResize, onShow } from '@dcloudio/uni-app';
import LkSvg from '@/components/svg/index.vue';
enum DataSource {
  /** 专属 */
  Tenant = 1,
  /** 官方 */
  Offical = 2,
  /** 个人 */
  Personal = 3,
}
const { safeAreaInsets } = uni.getSystemInfoSync();

const appSourceImgObj = {
  [DataSource.Tenant]:
    'https://huayun-ai-obs-public.huayuntiantu.com/0c161930a6b4315e16d593e7ec1cdbb3.svg',
  [DataSource.Offical]:
    'https://huayun-ai-obs-public.huayuntiantu.com/56bcad1ca41befc676c13c77066acd9d.svg',
  [DataSource.Personal]:
    'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3dd9642ef52df696fec7f3ccb3fc3cf1.svg',
};

const appSourceTextObj = {
  [DataSource.Tenant]: '专属',
  [DataSource.Offical]: '官方',
  [DataSource.Personal]: '个人',
};

// 元素高度
const customTabbarHeight = ref(0);
const appCenterHeaderHeight = ref(0);
const systemInfo = ref<UniNamespace.GetSystemInfoResult>();
const tabContainerPaddingBottom = ref('85px');
/** 顶部应用列表 */
const scenesList = ref<any[]>([]);
const otherApps = ref<any[]>([]);
// 当前选中的tab (从0开始，对应数组索引)
const currentTab = ref(0);

const props = defineProps({
  platform: {
    type: String,
    default: 'ios',
  },
});

// 监控currentTab变化，用于调试
watch(
  currentTab,
  (newVal, oldVal) => {
    console.log(`currentTab变化: ${oldVal} -> ${newVal}`);
  },
  { immediate: true }
);
const uToastRef = ref<any>(null);
// 跳转元素id
const targetId = ref('');
// 控制滑动 - 修改为对象形式
const openedStates = ref<Record<string, 'left' | 'right' | 'none'>>({});
// 控制二次确认删除 - 修改为对象形式
const deleteStates = ref<Record<string, boolean>>({});
/** 应用列表 loading */
const loading = ref(false);

// 监控loading状态变化
watch(
  loading,
  (newVal, oldVal) => {
    if (!uni.getStorageSync('Guide')?.AppCenter) {
      startGuide();
    }
  },
  { immediate: true }
);

const appStore = useAppStore();
const { loadMyApps } = appStore;

const combinedList = ref<{ name: any; sceneId: any; apps: AppListItemType[] }[]>([]);

// 添加新的状态变量记录正在处理的ID
const processingIds = ref<Record<string, boolean>>({});

// 添加标志位，防止重复初始化tab
const tabInitialized = ref(false);

// 移除tabs组件引用，不再需要
// const tabsRef = ref<any>(null);

// 计算引导目标索引
const guideOuterIdx = ref(-1);
const guideInnerIdx = ref(-1);
watch(
  combinedList,
  val => {
    let found = false;
    for (let i = 0; i < val.length; i++) {
      const apps = val[i].apps || [];
      if (apps.length > 0) {
        guideOuterIdx.value = i;
        guideInnerIdx.value = 0;
        found = true;
        break;
      }
    }
    if (!found) {
      guideOuterIdx.value = -1;
      guideInnerIdx.value = -1;
    }
  },
  { immediate: true }
);
const guideSteps = [
  {
    target: '.app-list-tabs_empty',
    content: '左右滑分类，应用一触即达。',
    position: 'bottom',
  },
  {
    target: '.create-app',
    content: '创建专属智能应用，懂你所需，高效助力。',
    position: 'top',
  },
  {
    target: '.index2-guide-target',
    content: '选择高频使用应用，添加至「我的常用」中，方便快速找到并使用。',
    position: 'bottom',
    lollipop: true,
    offsetX: 17,
    offsetY: -30,
    guideStepsStyle: {
      minWidth: '420rpx',
    },
  },
];

// 引用组件实例
const stepGuide = ref();

// 开始引导
const startGuide = () => {
  stepGuide.value.start();
};

const onGuideNext = () => {
  // scrollToAnchor('personage');
  // 滚动到我的应用的下一个tab
  const secondTabId = scenesList.value[0].id;
  console.log('滑动到第二个tab:', secondTabId, scenesList);
  scrollToAnchor(secondTabId);
};

// 处理引导完成事件
const onGuideComplete = () => {
  const data = uni.getStorageSync('Guide') || {};
  uni.setStorageSync('Guide', { ...data, AppCenter: true });
};

// 新增防抖函数
let debounceTimer: any = null;
function debounce(fn: Function, delay: number = 300) {
  return function (this: any, ...args: any[]) {
    if (debounceTimer) clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

// 动态获取延迟时间
function getDebounceDelay() {
  return systemInfo.value?.platform === 'ios' ? 200 : 50;
}

// 添加滚动监听相关变量
const sectionPositions = ref<
  Array<{ id: string; top: number; height: number; originalIndex?: number } | null>
>([]);

// 可视区域监听相关变量
const visibleSections = ref<Set<number>>(new Set());
const viewportHeight = ref(0);
const scrollContainerTop = ref(0);
const scrollTop = ref(0);
const isScrolling = ref(false);
let scrollTimer: any = null;
let visibilityCheckTimer: any = null;

// 处理滚动事件
function handleScroll(e: any) {
  // 记录当前滚动位置
  scrollTop.value = e.detail.scrollTop;

  // 标记正在滚动中
  isScrolling.value = true;

  // 清除之前的定时器
  if (scrollTimer) clearTimeout(scrollTimer);
  if (visibilityCheckTimer) clearTimeout(visibilityCheckTimer);

  // 立即检查可视区域
  checkSectionVisibility();

  // 设置新的定时器，滚动结束后更新选中的tab
  scrollTimer = setTimeout(() => {
    updateCurrentTabByScrollWithVisibility();
    isScrolling.value = false;
  }, 100);
}

// 检查section可视区域状态 - 兼容安卓和iOS
function checkSectionVisibility() {
  if (sectionPositions.value.length === 0) {
    return;
  }

  // 获取当前视口信息
  const query = uni.createSelectorQuery();

  // 获取滚动容器信息
  query
    .select('.app-list')
    .boundingClientRect((containerRect: any) => {
      if (!containerRect) {
        console.warn('无法获取滚动容器信息');
        return;
      }

      viewportHeight.value = containerRect.height;
      scrollContainerTop.value = containerRect.top;

      console.log('视口信息:', {
        height: viewportHeight.value,
        top: scrollContainerTop.value,
        scrollTop: scrollTop.value,
      });

      // 检查每个section的可视状态
      const newVisibleSections = new Set<number>();

      sectionPositions.value.forEach((section, index) => {
        if (!section) return;

        // 计算section相对于视口的位置
        const sectionTop = section.top - scrollTop.value;
        const sectionBottom = sectionTop + section.height;

        // 判断section是否在可视区域内
        // 考虑一定的容差，section部分可见即认为可见
        const isVisible = sectionBottom > 0 && sectionTop < viewportHeight.value;

        // 更精确的可见性判断：section至少有30%在可视区域内
        const visibleHeight =
          Math.min(sectionBottom, viewportHeight.value) - Math.max(sectionTop, 0);
        const visibilityRatio = visibleHeight / section.height;

        if (isVisible && visibilityRatio > 0.3) {
          newVisibleSections.add(index);
          console.log(
            `Section ${index} (${section.id}) 可见, 可见比例: ${(visibilityRatio * 100).toFixed(1)}%`
          );
        }
      });

      // 更新可见sections
      visibleSections.value = newVisibleSections;
      console.log('当前可见的sections:', Array.from(visibleSections.value));
    })
    .exec();
}

// 基于可视区域状态更新当前选中的tab
function updateCurrentTabByScrollWithVisibility() {
  try {
    console.log('updateCurrentTabByScrollWithVisibility 被调用');
    console.log('当前可见sections:', Array.from(visibleSections.value));

    // 如果是tab点击导致的滚动，跳过更新
    if (isTabClickScrolling.value) {
      console.log('tab点击滚动中，跳过更新');
      return;
    }

    // 如果没有可见的sections，使用原来的逻辑
    if (visibleSections.value.size === 0) {
      console.log('没有可见sections，使用原来的滚动逻辑');
      updateCurrentTabByScroll();
      return;
    }

    // 找到最合适的section作为当前选中项
    let targetSectionIndex = 0;

    if (visibleSections.value.size === 1) {
      // 只有一个可见section，直接选择它
      targetSectionIndex = Array.from(visibleSections.value)[0];
      console.log('只有一个可见section:', targetSectionIndex);
    } else {
      // 多个可见sections，选择最靠近视口中心的
      let minDistanceToCenter = Infinity;
      const viewportCenter = viewportHeight.value / 2;

      visibleSections.value.forEach(index => {
        const section = sectionPositions.value[index];
        if (!section) return;

        const sectionTop = section.top - scrollTop.value;
        const sectionCenter = sectionTop + section.height / 2;
        const distanceToCenter = Math.abs(sectionCenter - viewportCenter);

        if (distanceToCenter < minDistanceToCenter) {
          minDistanceToCenter = distanceToCenter;
          targetSectionIndex = index;
        }
      });

      console.log('选择最靠近中心的section:', targetSectionIndex);
    }

    // 确保索引在有效范围内
    const tabListLength = 1 + scenesList.value.length;
    if (targetSectionIndex >= tabListLength) {
      console.warn('计算出的索引超出范围:', targetSectionIndex, '最大索引:', tabListLength - 1);
      targetSectionIndex = tabListLength - 1;
    }

    // 更新当前选中的tab
    if (targetSectionIndex !== currentTab.value) {
      nextTick(() => {
        try {
          currentTab.value = targetSectionIndex;
          console.log('基于可视区域更新tab为:', currentTab.value);
        } catch (error) {
          console.warn('更新tab失败:', error);
        }
      });
    } else {
      console.log('tab索引相同，无需更新');
    }
  } catch (error) {
    console.warn('updateCurrentTabByScrollWithVisibility执行失败:', error);
  }
}

// 根据滚动位置更新当前选中的tab (保留原来的逻辑作为备用)
function updateCurrentTabByScroll() {
  try {
    console.log(
      'updateCurrentTabByScroll 被调用, isScrolling:',
      isScrolling.value,
      'scrollTop:',
      scrollTop.value
    );

    if (sectionPositions.value.length === 0) {
      console.log('sectionPositions为空，重新计算');
      calculateSectionPositions();
      return;
    }

    console.log('当前sectionPositions:', sectionPositions.value);

    // 找到当前可见的section - 修复算法
    let currentSectionIndex = 0;

    console.log('sectionPositions数组长度:', sectionPositions.value.length);
    console.log(
      'sectionPositions详细信息:',
      sectionPositions.value.map((item, index) => ({
        index,
        exists: !!item,
        id: item?.id,
        top: item?.top,
      }))
    );

    // 过滤掉null/undefined的元素，只处理有效的section
    const validSections = sectionPositions.value
      .map((section, index) => ({ section, originalIndex: index }))
      .filter(item => item.section !== null && item.section !== undefined);

    console.log('有效的sections:', validSections);

    // 从后往前遍历有效的sections
    for (let i = validSections.length - 1; i >= 0; i--) {
      const { section, originalIndex } = validSections[i];

      // 由于已经过滤了null，这里section一定不为null
      if (section) {
        console.log(
          `检查section ${originalIndex} (${section.id}): top=${section.top}, scrollTop=${scrollTop.value}`
        );

        // 使用较小的容差值，避免过早切换
        if (scrollTop.value >= section.top - 30) {
          currentSectionIndex = originalIndex;
          console.log(`选择section ${originalIndex} (${section.id})`);
          break;
        }
      }
    }

    console.log(
      '计算出的currentSectionIndex:',
      currentSectionIndex,
      '当前currentTab:',
      currentTab.value
    );

    // 只有在用户滚动时才更新tab（不是点击tab导致的滚动）
    if (!isScrolling.value) {
      console.log('不是滚动导致的，跳过更新');
      return;
    }

    // 如果是tab点击导致的滚动，也跳过更新
    if (isTabClickScrolling.value) {
      console.log('tab点击滚动中，跳过更新');
      return;
    }

    // 确保索引在有效范围内
    const tabListLength = 1 + scenesList.value.length; // "我的应用" + scenesList
    if (currentSectionIndex >= tabListLength) {
      console.warn('计算出的索引超出范围:', currentSectionIndex, '最大索引:', tabListLength - 1);
      currentSectionIndex = tabListLength - 1;
    }

    // 更新当前选中的tab，添加防护措施
    if (currentSectionIndex !== currentTab.value) {
      // 使用nextTick确保DOM更新
      nextTick(() => {
        try {
          currentTab.value = currentSectionIndex;
          console.log('自动更新tab为:', currentTab.value);
        } catch (error) {
          console.warn('更新tab失败:', error);
        }
      });
    } else {
      console.log('tab索引相同，无需更新');
    }
  } catch (error) {
    console.warn('updateCurrentTabByScroll执行失败:', error);
  }
}

// 计算各个section的位置信息
function calculateSectionPositions() {
  console.log('开始计算section位置, combinedList长度:', combinedList.value.length);

  if (combinedList.value.length === 0) {
    console.warn('combinedList为空，无法计算section位置');
    return;
  }

  const query = uni.createSelectorQuery();

  // 清空之前的位置信息，并初始化为正确长度的数组
  sectionPositions.value = new Array(combinedList.value.length).fill(null);

  let completedQueries = 0;
  const totalQueries = combinedList.value.length;

  // 查询所有section标题元素
  combinedList.value.forEach((item, index) => {
    const sectionId = `tab-${item.sceneId}`;
    console.log(`查询section: ${sectionId}, index: ${index}`);

    query.select(`#${sectionId}`).boundingClientRect((rect: any) => {
      completedQueries++;

      if (rect) {
        sectionPositions.value[index] = {
          id: item.sceneId,
          top: rect.top,
          height: rect.height,
          originalIndex: index,
        };
        console.log(`找到section ${sectionId}:`, rect, `原始索引: ${index}`);
      } else {
        console.warn(`未找到section元素: ${sectionId}`);
        // 即使没找到，也要在对应位置设置一个占位符
        sectionPositions.value[index] = null;
      }

      // 当所有查询完成时，进行验证
      if (completedQueries === totalQueries) {
        console.log('所有section查询完成');
        validateSectionPositions();
      }
    });
  });

  query.exec();
}

// 验证section位置的函数
function validateSectionPositions() {
  console.log('Section positions calculated:', sectionPositions.value);
  console.log('Section positions (保持原始顺序):', sectionPositions.value);

  // 验证索引对应关系
  sectionPositions.value.forEach((section, index) => {
    if (section) {
      console.log(`验证: index ${index} -> sceneId ${section.id}, top: ${section.top}`);

      // 验证与combinedList的对应关系
      if (combinedList.value[index]) {
        const expectedSceneId = combinedList.value[index].sceneId;
        if (section.id !== expectedSceneId) {
          console.error(
            `索引不匹配! index ${index}: section.id=${section.id}, expected=${expectedSceneId}`
          );
        } else {
          console.log(`✓ 索引匹配: index ${index} -> ${section.id}`);
        }
      }
    } else {
      console.warn(`index ${index} 的section为null`);
    }
  });

  // 验证tab列表与combinedList的对应关系
  const tabList = [{ id: 'personage', name: '我的应用' }, ...scenesList.value];
  console.log('Tab列表:', tabList);
  console.log(
    'CombinedList:',
    combinedList.value.map((item, index) => ({ index, id: item.sceneId, name: item.name }))
  );
}

// 初始化可视区域监听
function initializeVisibilityMonitoring() {
  console.log('初始化可视区域监听');

  // 获取系统信息，设置视口高度
  uni.getSystemInfo({
    success: res => {
      viewportHeight.value = res.windowHeight;
      console.log('设置视口高度:', viewportHeight.value);

      // 立即执行一次可视区域检查
      setTimeout(() => {
        checkSectionVisibility();
      }, 200);
    },
  });
}

// 定期检查可视区域状态（作为备用机制）
function startPeriodicVisibilityCheck() {
  if (visibilityCheckTimer) {
    clearInterval(visibilityCheckTimer);
  }

  // 每500ms检查一次可视区域状态
  visibilityCheckTimer = setInterval(() => {
    if (!isTabClickScrolling.value) {
      checkSectionVisibility();
    }
  }, 500);
}

// 停止定期检查
function stopPeriodicVisibilityCheck() {
  if (visibilityCheckTimer) {
    clearInterval(visibilityCheckTimer);
    visibilityCheckTimer = null;
  }
}

// 监听combinedList变化，重新计算section位置
watch(
  combinedList,
  () => {
    // 等待DOM更新后计算位置
    nextTick(() => {
      setTimeout(() => {
        calculateSectionPositions();
      }, 300);
    });
  },
  { deep: true }
);

// 添加防抖变量，避免重复点击
let tabClickTimer: any = null;

// 添加点击状态，用于视觉反馈
const isTabClicking = ref(false);

// 添加一个标志位，用于暂时禁用滚动跟随
const isTabClickScrolling = ref(false);

// 处理tab点击事件（包括重复点击）
function handleTabClick(item: any, index: number) {
  try {
    console.log('点击tab:', item, index, '当前currentTab:', currentTab.value);

    // 防止过快重复点击
    if (tabClickTimer) {
      clearTimeout(tabClickTimer);
    }

    // 添加视觉反馈
    isTabClicking.value = true;

    // 确保item存在且有id属性
    if (!item || !item.id) {
      console.warn('handleTabClick: item或item.id不存在', item);
      isTabClicking.value = false;
      return;
    }

    // 立即更新当前选中的tab，避免滚动跟随干扰
    currentTab.value = index;

    // 标记为手动点击tab导致的滚动，禁用滚动跟随
    isScrolling.value = false;
    isTabClickScrolling.value = true;

    // 每次点击都滚动到对应位置，即使是重复点击
    tabClickTimer = setTimeout(() => {
      console.log('滚动到:', item);
      scrollToAnchor(item.id);

      // 滚动完成后重置点击状态，并延迟一段时间再启用滚动跟随
      setTimeout(() => {
        isTabClicking.value = false;
        // 延迟启用滚动跟随，避免滚动动画过程中的干扰
        setTimeout(() => {
          isTabClickScrolling.value = false;
          console.log('重新启用滚动跟随');
        }, 1000); // 增加延迟时间，确保滚动动画完全结束
      }, 200);
    }, getDebounceDelay());
  } catch (error) {
    console.warn('handleTabClick执行失败:', error);
    isTabClicking.value = false;
    isTabClickScrolling.value = false;
  }
}

// handleChange函数已移除，现在使用handleTabClick处理所有tab点击事件

function fetchloadMyapps() {
  // 如果已有操作正在进行中，则不显示全局loading
  const hasProcessingItem = Object.values(processingIds.value).some(status => status);

  // 只有当没有单独操作正在处理时，才显示全局loading
  if (!hasProcessingItem) {
    loading.value = true;
  }
  // 计算请求时间
  const startTime = Date.now();
  return loadMyApps()
    .then(myApps => {
      const data = computed(() => {
        return [
          {
            name: '我的应用',
            sceneId: 'personage',
            apps: myApps.filter(({ source }) => source === DataSource.Personal),
          },
          ...scenesList.value.map((scene: any) => {
            const apps = myApps.filter((item: any) =>
              item.labelList?.some((it: any) => String(it.tenantSceneId) === String(scene.id))
            );
            if (scene.name === 'AI评价' && otherApps.value.length > 0) {
              apps.push(...otherApps.value);
            }
            return {
              name: scene.name,
              sceneId: scene.id,
              apps,
            };
          }),
        ];
      });
      combinedList.value = data.value;
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log('combinedList', combinedList.value);
      console.log('应用列表---请求时间', duration);
      return combinedList.value; // 返回结果，确保Promise链正确传递
    })
    .finally(() => {
      loading.value = false;

      // loading完成后，等待DOM更新，然后进行tab初始化
      nextTick(() => {
        setTimeout(() => {
          console.log('应用列表loading完成，开始初始化tab');

          // 重新计算section位置
          calculateSectionPositions();

          // 初始化可视区域监听
          initializeVisibilityMonitoring();

          // 初始化第二个tab
          if (scenesList.value.length > 0) {
            initializeSecondTab();
          }
        }, 300); // 给足够时间让DOM完全渲染
      });
    });
}

onShow(async () => {
  // 等待前两个请求完成后再调用fetchloadMyapps
  try {
    // await Promise.all([fetchGetTenantSceneList(), fetchGetOtherAppList()]);
    fetchGetTenantSceneList();
    fetchGetOtherAppList();

    // 前两个请求完成后再加载应用列表
    // fetchloadMyapps函数内部会在loading完成后自动进行tab初始化
    await fetchloadMyapps();
  } catch (error) {
    console.error('加载数据失败:', error);
    uToastRef.value?.show({
      message: '加载数据失败，请重试',
      type: 'error',
    });
  }
});

onResize(() => {
  console.log('onResize窗口尺寸变化：');
  getDeviceInfo(); // 重新获取设备信息和元素尺寸
});

onMounted(async () => {
  try {
    // 确保DOM已完全渲染
    await nextTick();
    console.log('safeAreaInsets', safeAreaInsets);
    getDeviceInfo();

    // 添加全局错误处理
    originalConsoleError = console.error;
    console.error = function (...args) {
      // 检查是否是u-tabs相关的错误
      const errorMessage = args.join(' ');
      if (errorMessage.includes("Cannot read properties of undefined (reading 'rect')")) {
        console.warn('捕获到u-tabs组件rect错误，已忽略:', ...args);
        return;
      }
      // 其他错误正常输出
      originalConsoleError.apply(console, args);
    };

    // 等待DOM完全渲染后进行基础初始化
    setTimeout(() => {
      // 只进行基础的设备信息获取，tab初始化在loading完成后进行
      console.log('onMounted: 基础初始化完成，等待应用列表loading完成后进行tab初始化');
    }, 500);

    uni.onWindowResize(res => {
      console.log('onWindowResize窗口尺寸变化：', res);
      getDeviceInfo(); // 重新获取设备信息和元素尺寸
      // 重新计算section位置
      setTimeout(() => {
        calculateSectionPositions();
      }, 100);
    });
  } catch (error) {
    console.error('onMounted执行失败:', error);
  }
});

// 存储原始的console.error函数
let originalConsoleError: any = null;

// 在组件销毁时移除监听
onUnmounted(() => {
  uni.offWindowResize(res => {
    console.log('onUnmounted窗口尺寸变化：', res);
  });

  // 恢复原始的console.error
  if (originalConsoleError) {
    console.error = originalConsoleError;
  }

  // 清理定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  if (tabClickTimer) {
    clearTimeout(tabClickTimer);
  }
  if (visibilityCheckTimer) {
    clearInterval(visibilityCheckTimer);
  }
});

// 获取元素信息
function getDeviceInfo() {
  const query = uni.createSelectorQuery();
  query
    .select('.app-center-header')
    .boundingClientRect((data: any) => {
      if (data) {
        appCenterHeaderHeight.value = data.height;
        console.log('appCenterHeaderHeight', appCenterHeaderHeight.value);
      } else {
        console.error('未能获取到.app-center-header元素');
        // 设置一个默认值
        appCenterHeaderHeight.value = 120;
      }
    })
    .exec();

  query
    .select('.custom-tabbar-wrap')
    .boundingClientRect((data: any) => {
      if (data) {
        customTabbarHeight.value = data.height;
        console.log('customTabbarHeight', customTabbarHeight.value);
      } else {
        console.error('未能获取到.custom-tabbar-wrap元素');
        // 设置一个默认值
        customTabbarHeight.value = 50;
      }
    })
    .exec();

  query
    .select('.tab-container')
    .fields(
      {
        computedStyle: ['paddingBottom'],
      },
      (data: any) => {
        const paddingBottom = data.paddingBottom;
        tabContainerPaddingBottom.value = paddingBottom;
      }
    )
    .exec();

  uni.getSystemInfo({
    success: res => {
      systemInfo.value = res;

      // iOS特殊优化
      if (res.platform === 'ios') {
        // 在iOS上，我们使用UniApp的方式添加特殊的CSS类
        // 添加一个延迟，确保DOM已经渲染
        setTimeout(() => {
          // 使用createSelectorQuery找到滚动元素
          const query = uni.createSelectorQuery();
          query
            .select('.app-list')
            .boundingClientRect(data => {
              if (data) {
                console.log('找到app-list元素，应用iOS优化');

                // 由于不能直接操作style，我们通过添加类来实现
                // 优化样式已在<style>部分添加
              }
            })
            .exec();
        }, 300);
      }
    },
  });
}

/** 获取应用列表 */
function fetchGetTenantSceneList() {
  return getTenantSceneList().then(res => {
    scenesList.value = res;
    console.log('场景列表获取完成:', res.length, '个场景');

    // 不在这里初始化tab，等待应用列表loading完成后统一初始化
    return res; // 返回结果，确保Promise链正确传递
  });
}

function fetchGetMyAppList() {
  console.log('wdnmd-fetchGetMyAppList');
  return getMyAppList({}).then(res => {
    console.log('wdnmd-fetchGetMyAppList', res);
    return res;
  });
}

function fetchGetTenantSceneListOfficialHome() {
  return getTenantSceneListOfficialHome({}).then(res => {
    console.log('wdnmd-fetchGetTenantSceneListOfficialHome', res);
    return res;
  });
}

function fetchGetOtherAppList() {
  return getOtherAppList().then(res => {
    console.log('获取其他应用', res);
    otherApps.value = res;
    return res; // 返回结果，确保Promise链正确传递
  });
}

// 点击项目内容区域
function handleItemClick(appItem: any) {
  uni.setStorageSync('selectApp', appItem);
  uni.navigateTo({
    url: '/pages/chat/index',
  });
  // 这里可以添加进入应用详情页或其他逻辑
  console.log('点击应用项', appItem);
  // 如果有滑出状态，关闭滑出
  if (openedStates.value[appItem.id] === 'right') {
    openedStates.value[appItem.id] = 'none';
    deleteStates.value[appItem.id] = false;
  }
}

// 点击删除按钮
function handleDelete(id: string) {
  deleteStates.value[id] = true;
}

// 确认删除
function confirmDelete(id: string, tmbId: string) {
  // 这里添加实际删除应用的逻辑
  console.log('确认删除应用id', id, tmbId);

  deleteClientApp({ id, tmbId })
    .then(res => {
      console.log('删除应用', res);
      // 显示删除成功提示
      uToastRef.value.show({
        message: '删除成功',
        type: 'success',
      });
    })
    .finally(() => {
      // 删除后关闭所有滑动
      Object.keys(openedStates.value).forEach(key => {
        openedStates.value[key] = 'none';
      });
      // 删除后关闭所有滑动
      Object.keys(deleteStates.value).forEach(key => {
        deleteStates.value[key] = false;
      });

      // 删除后关闭对应的滑动
      // openedStates.value[id] = 'none';
      // deleteStates.value[id] = false;

      // 重新加载应用列表
      fetchloadMyapps();
    });
}

// 滑动状态改变
function handleSwipeActionChange(status: 'left' | 'right' | 'none', id: string) {
  console.log('滑动状态改变', status, id);

  // 记录当前滑动状态
  openedStates.value[id] = status;

  // 如果关闭了滑动，重置删除确认状态
  if (status === 'none') {
    deleteStates.value[id] = false;
  }
}

function handleCreateApp() {
  uni.navigateTo({
    url: '/pages-subpackages/app-center-pkg/create-app/index',
  });
}

function handleSearch() {
  // 使用storage存储activeType，避免URL参数在安卓打包时的问题
  uni.setStorageSync('searchActiveType', SearchType.APP);
  uni.navigateTo({
    url: '/pages/search/index',
  });
}

async function scrollToAnchor(anchorId: string) {
  // 在iOS平台上增加延迟处理
  if (systemInfo.value?.platform === 'ios') {
    // 先把目标ID设为空，强制触发DOM更新
    targetId.value = '';

    // 使用setTimeout给iOS足够时间处理渲染
    setTimeout(() => {
      targetId.value = `tab-${anchorId}`;

      // 再添加一个短延迟以确保滚动后视图正确渲染
      setTimeout(() => {
        // 强制重新计算布局
        nextTick(() => {
          // 可以在这里添加额外的处理，比如通知列表刷新等
          console.log('iOS滚动处理完成');
        });
      }, 50);
    }, 100);
  } else {
    // 非iOS平台直接设置目标ID
    targetId.value = `tab-${anchorId}`;
  }
}

// 设为常用app
function handleSetCommonApp(id: string) {
  // 记录当前正在处理的ID
  processingIds.value[id] = true;

  setCommonApp({ id })
    .then(res => {
      uToastRef.value.show({
        message: '已添加至首页-我的常用',
        type: 'success',
      });
    })
    .finally(() => {
      // 先将当前ID的处理状态重置
      processingIds.value[id] = false;
      // 然后重新加载应用列表
      fetchloadMyapps();
    });
}

// 移除常用app
function handleRemoveCommonApp(id: string) {
  // 记录当前正在处理的ID
  processingIds.value[id] = true;

  rmCommonApp({ id })
    .then(res => {
      uToastRef.value.show({
        message: '已从首页-我的常用中移除',
        type: 'success',
      });
    })
    .finally(() => {
      // 先将当前ID的处理状态重置
      processingIds.value[id] = false;
      // 然后重新加载应用列表
      fetchloadMyapps();
    });
}

// 点击编辑应用
function handleEditApp(params: any) {
  console.log('点击编辑应用', params);
  // 先关闭滑动状态
  if (params.id) {
    openedStates.value[params.id] = 'none';
  }

  // 防止事件重复触发，使用setTimeout延迟导航
  uni.navigateTo({
    url: `/pages-subpackages/app-center-pkg/edit-app/index?id=${params.id}`,
  });
}

function getAppListHeight() {
  // 获取底部安全区域高度
  const safeAreaBottom = safeAreaInsets?.bottom || 0;

  // 计算实际可用高度
  if (systemInfo.value?.platform === 'ios') {
    return `calc(100vh - (${appCenterHeaderHeight.value}px + ${customTabbarHeight.value}px)`;
  } else {
    return `calc(100vh - (${appCenterHeaderHeight.value}px + ${customTabbarHeight.value}px))`;
  }
}

function checkString(str: string) {
  const isAllChinese = /^[\u4e00-\u9fa5]+$/.test(str);
  const hasAlpha = /[a-zA-Z]/.test(str);

  if (str.length < 4) {
    return true;
  } else if (hasAlpha && str.length <= 4) {
    return true;
  } else if (isAllChinese && str.length <= 4) {
    return false;
  } else {
    return false;
  }
}
console.log('checkString', checkString('评价'));

// 初始化第二个tab的函数
function initializeSecondTab() {
  try {
    // 如果已经初始化过，则不再重复执行
    if (tabInitialized.value || scenesList.value.length === 0) {
      return;
    }

    // 标记为已初始化
    tabInitialized.value = true;

    // 使用较长的延迟确保页面已经完全稳定
    setTimeout(() => {
      try {
        // 设置当前tab为第二个tab（索引为1）
        currentTab.value = 1;
        console.log('初始化设置currentTab为:', currentTab.value);

        // 标记为初始化滚动，暂时禁用滚动跟随
        isScrolling.value = false;
        isTabClickScrolling.value = true;

        // 根据平台确定不同的延迟
        const delay = systemInfo.value?.platform === 'ios' ? 300 : 150;

        // 延迟后再滚动到对应的区域，确保tab切换动画完成
        setTimeout(() => {
          if (scenesList.value.length > 0) {
            const secondTabId = scenesList.value[0].id;
            console.log('初始化第二个tab:', secondTabId);
            scrollToAnchor(secondTabId);

            // 滚动完成后，延迟启用滚动跟随
            setTimeout(() => {
              isTabClickScrolling.value = false;
              console.log('初始化滚动完成，启用滚动跟随');

              // 通知用户界面更新完成
              console.log('Tab切换完成，内容区域已更新');
            }, 800); // 给足够时间让滚动动画完成
          }
        }, delay);
      } catch (error) {
        console.warn('initializeSecondTab内部执行失败:', error);
        isTabClickScrolling.value = false;
      }
    }, 500);
  } catch (error) {
    console.warn('initializeSecondTab执行失败:', error);
    isTabClickScrolling.value = false;
  }
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

/* iOS滚动优化样式 */
.ios-scroll {
  -webkit-overflow-scrolling: touch;
  /* 硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
}

.container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: url('https://huayun-ai-obs-public.huayuntiantu.com/83fb33156350a87984c65ad3d7493509.png')
    no-repeat center center;
  background-size: 100% 100%;
  overflow: hidden; /* 确保容器不可滚动 */
  padding-top: 14px;
}

.create-app {
  position: fixed;
  bottom: 70 * 2rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  display: flex;
  width: 135 * 2rpx;
  height: 40 * 2rpx;
  padding: 8 * 2rpx 16 * 2rpx;
  justify-content: center;
  align-items: center;
  gap: 4 * 2rpx;
  flex-shrink: 0;
  border-radius: 100 * 2rpx;
  background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
  box-shadow: 0px 4 * 2rpx 4 * 2rpx 0px rgba(154, 142, 255, 0.23);
  color: #fff;
  image {
    width: 20 * 2rpx;
    height: 20 * 2rpx;
  }
  .create-app-text {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14 * 2rpx;
    image {
      width: 20 * 2rpx;
      height: 20 * 2rpx;
    }
  }
}

.app-center-header {
  flex-shrink: 0; /* 防止头部被压缩 */
  z-index: 10; /* 确保头部在滚动内容之上 */
}

.text-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16 * 2rpx;
  /* padding-top: 30 * 2rpx; */
  padding-top: 0;
  padding-bottom: 0;
  padding-top: var(--status-bar-height);
  .text-search-title {
    font-size: 20 * 2rpx;
    font-weight: 700;
  }
}

/* tab 高度设置 */
.app-list-tabs {
  position: relative;
  &_empty {
    position: absolute;
    top: -2rpx;
    left: 8rpx;
    right: 8rpx;
    bottom: -2rpx;
    border-radius: 8px;
  }

  .app-list-tabs-item {
    display: flex;
    align-items: center;
    height: 48 * 2rpx;
  }

  // 选中状态
  .active {
    font-size: 36rpx;
    color: #1d2129;
    font-weight: 600;
  }

  // 未选中状态
  .inactive {
    font-size: 32rpx;
    color: #4e5969;
  }

  .app-list-tabs-item-box {
    position: relative;
    overflow: visible;
    height: 24 * 2rpx;
    text-align: center;
    line-height: 24 * 2rpx;
  }

  .app-list-tabs-item-text {
    display: inline-block;
    position: relative;
    white-space: nowrap;
    overflow: auto;
    height: 100%;
    z-index: 2;
  }

  .app-list-tabs-item-icon {
    position: absolute;
    top: -16%;
    right: -20%;
    width: 23 * 2rpx;
    height: 20 * 2rpx;
    z-index: 1;
  }
}
.app-list-container {
  position: relative;
  margin: 0 16 * 2rpx;
  // border-radius: 14 * 2rpx;
  overflow: hidden;
}

.app-list {
  flex: 1; /* 占据剩余所有空间 */
  overflow-y: scroll; /* 只允许垂直滚动 */
  -webkit-overflow-scrolling: touch; /* 在iOS上提供更好的滚动体验 */
  /* padding: 16 * 2rpx; */
  /* padding-bottom: calc(48 * 2rpx + 50px); */
  position: relative;
  /* padding-bottom: 28 * 2rpx; */
  box-sizing: border-box;

  .app-list-container {
    /* width: 343 * 2rpx; */
    width: 100%;
    background-color: #fff;
    border-radius: 14 * 2rpx;
    margin: 0 auto;
    margin-bottom: 14 * 2rpx;
  }

  .app-list-item-title {
    position: relative;
    color: #000;
    font-size: 18 * 2rpx;
    font-weight: 500;
    height: 51 * 2rpx;
    border-bottom: 1px solid #f2f3f5;
    padding-top: 12 * 2rpx;
    padding-left: 16 * 2rpx;

    .title-box {
      position: relative;
      display: inline-block;
      .title-box-text {
        position: relative;
        z-index: 2;
      }
      .app-list-item-title-icon {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 53 * 2rpx;
        width: 100%;
        height: 9 * 2rpx;
        z-index: 1;
      }
      .app-list-item-title-icon-long {
        position: absolute;
        left: 0;
        bottom: -4 * 2rpx;
        width: 63 * 2rpx;
        width: 100%;
        height: 16 * 2rpx;
        z-index: 1;
      }
    }
  }

  .app-list-item {
    /* width: 343 * 2rpx; */
    width: 100%;
    height: 88 * 2rpx;
    margin: 0 auto;

    .app-list-item-content {
      display: flex;
      width: 100%;
      height: 88 * 2rpx;
      /* background-color: skyblue; */
      align-items: center;
      padding: {
        left: 16 * 2rpx;
        right: 14 * 2rpx;
        top: 12 * 2rpx;
        bottom: 12 * 2rpx;
      }
      .icon {
        width: 58 * 2rpx;
        /* width: 18%; */
        height: 58 * 2rpx;
        background-color: #f1f2f4;
        border-radius: 50 * 2rpx;
        overflow: hidden;
        /* flex: 0.18; */
        image,
        .image {
          width: 100%;
          height: 100%;
        }
      }
      .box-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 24rpx;
        min-height: 88rpx;
        min-width: 88rpx;
        width: 88rpx;
        height: 88rpx;
      }

      .box {
        /* flex: 0.65; */
        width: 65%;
        flex: 1;
        margin: 0 10 * 2rpx;
        margin-right: 5rpx;
        .box-title {
          font-size: 16 * 2rpx;
          font-weight: 500;
          color: #000;
          line-height: 24 * 2rpx;
        }
        .box-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #606266;
          font-size: 12 * 2rpx;
          font-weight: 400;
          line-height: 20 * 2rpx;
        }
        .box-icon {
          display: flex;
          align-items: center;
          font-size: 12 * 2rpx;
          color: #909399;
          font-weight: 400;
          line-height: 16 * 2rpx;
          image {
            width: 14 * 2rpx;
            height: 14 * 2rpx;
            margin-right: 4 * 2rpx;
          }
        }
      }

      .btn {
        /* flex: 0.09; */
        /* width: 9%; */
        border-radius: 100 * 2rpx;
        .btn-add {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28 * 2rpx;
          height: 28 * 2rpx;
          background: linear-gradient(180deg, #ab57ff 0%, #7d4dff 100%);
          border-radius: 100 * 2rpx;
          image {
            width: 18 * 2rpx;
            height: 18 * 2rpx;
          }

          &.btn-disabled {
            opacity: 0.7;
            pointer-events: none;
          }
        }
        .btn-done {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28 * 2rpx;
          height: 28 * 2rpx;
          background-color: #f2f3f5;
          border-radius: 100 * 2rpx;
          image {
            width: 18 * 2rpx;
            height: 18 * 2rpx;
          }

          &.btn-disabled {
            opacity: 0.7;
            pointer-events: none;
          }
        }
      }
    }
  }

  .slide-btn-container {
    width: 51 * 2 * 2rpx;

    .slide-btn {
      display: flex;
      height: 100%;

      .slide-pop-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        height: 100%;
        background-color: #db443c;
        text {
          color: #fff;
          font-size: 14 * 2rpx;
        }
        image {
          width: 20 * 2rpx;
          height: 20 * 2rpx;
        }
      }

      .slide-btn-item {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        height: 100%;

        image {
          width: 20 * 2rpx;
          height: 20 * 2rpx;
        }
        &:nth-child(1) {
          background-color: #7d4dff;
        }
        &:nth-child(2) {
          background-color: #db443c;
        }
      }
    }
  }
}

.loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10001;
}

::v-deep .u-tabs__wrapper__nav__item {
  padding: 0 16 * 2rpx;
}
</style>
