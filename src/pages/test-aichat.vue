<template>
  <view class="test-page">
    <view class="header">
      <text class="title">AIChat组件测试页面</text>
      <button @click="showModal = !showModal" class="toggle-btn">
        {{ showModal ? '关闭' : '打开' }}聊天
      </button>
    </view>

    <!-- 弹窗形式的AIChat -->
    <view v-if="showModal" class="modal-overlay" @click="showModal = false">
      <view class="modal-container" @click.stop>
        <view class="modal-header">
          <text class="modal-title">智能答疑</text>
          <view class="close-btn" @click="showModal = false">✕</view>
        </view>
        <view class="modal-body">
          <AIChat />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import AIChat from '@/components/AIChat/index.vue';

const showModal = ref(false);
</script>

<style scoped>
.test-page {
  height: 100vh;
  background: #f5f5f5;
}

.header {
  padding: 20px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.toggle-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-container {
  width: 80%;
  height: 80%;
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 16px;
  font-weight: bold;
}

.close-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  background: #f0f0f0;
}

.modal-body {
  flex: 1;
  overflow: hidden;
}
</style>
