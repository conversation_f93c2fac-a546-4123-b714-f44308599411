<script setup lang="ts">
// 使用Pinia store
import { useUserStore } from '@/store/userStore';
import { ref, onMounted, nextTick } from 'vue';
import LkSvg from '@/components/svg/index.vue';
import DatabaseItem from './components/DatabaseItem.vue';
import AddSelect from '@/pages/data-space/components/AddSelect.vue';
import RecordTransfer from '@/pages/data-space/components/RecordTransfer.vue';
import OptionShare from '@/pages/data-space/components/OptionShare.vue';
import OptionDel from '@/pages/data-space/components/OptionDel.vue';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';
import LkToast from '@/components/LkToast/index.vue';
import {
  mixBatchDelete,
  myMixBatchDelete,
  deleteSpaceFile,
  batchUpdateParentFileAndFolder,
} from '@/api/database';
import { useTabStore } from '@/store/tabStore';

import { SearchType } from '@/constants/search';
import { isAuditSwitch } from '@/api/database';
const mainStore = useUserStore();

const curTab = ref('1');
const addSelectRef = ref<any>(null);
const recordTransferRef = ref<any>(null);
const userStore = useUserStore();
const tabStore = useTabStore();

const isEditing = ref(false);
const batchSelectedIdArr = ref<any[]>([]);
const batchSelectedArr = ref<any[]>([]);

const canBatchShare = ref(false);
const canBatchMove = ref(false);
const canBatchDelete = ref(false);
const optionShare = ref<InstanceType<typeof OptionShare>>();
const optionDel = ref<InstanceType<typeof OptionDel>>();
const lkDatabasePopupRef = ref<InstanceType<typeof LkDatabasePopup>>();
const uToastRef = ref();
const platform = ref('ios');

const calculateBatchPermissions = (selectedItems: any[]) => {
  if (selectedItems.length === 0) {
    canBatchShare.value = false;
    canBatchMove.value = false;
    canBatchDelete.value = false;
    return;
  }
  const currentUserInfo = userStore.getUserInfo;
  const isAdmin =
    Number(currentUserInfo?.roleType) === 1 || Number(currentUserInfo?.roleType) === 3;
  const isAdminMode = curTab.value === '2';

  const checkItemPermissions = (item: any) => {
    const isUploader = item.uploader === currentUserInfo?.username;
    const privileges = Number(item.privileges?.[0] || 4);
    const isManagePrivilege = privileges >= 3 || isAdmin;
    const isFile = item.fileType === 1;

    const hasSharePermission =
      isAdminMode || isManagePrivilege || privileges >= 2 || (privileges === 1 && isUploader);
    const canShare = isFile && hasSharePermission;

    const canMove = isAdminMode || isManagePrivilege || privileges >= 2;

    const canDelete =
      isAdminMode || isManagePrivilege || (isUploader && (privileges === 1 || privileges === 2));

    return { canShare, canMove, canDelete };
  };

  canBatchShare.value = selectedItems.every(item => checkItemPermissions(item).canShare);
  canBatchMove.value = selectedItems.every(item => checkItemPermissions(item).canMove);
  canBatchDelete.value = selectedItems.every(item => checkItemPermissions(item).canDelete);
};

const clickEditItem = () => {
  isEditing.value = !isEditing.value;
  tabStore.hideTabBarAction();
};

const updateSelectedItems = (items: any[]) => {
  batchSelectedIdArr.value = items.map((item: any) => item.id);
  batchSelectedArr.value = items;
  console.log('更新选中的ID数组:', batchSelectedIdArr.value);
  calculateBatchPermissions(items);
};

const clickCheckAll = () => {
  console.log('全选');
  databaseItemRef.value?.toggleCheckAll();
};

const clickBatchShare = () => {
  console.log('批量分享');
  if (batchSelectedArr.value.length === 0) {
    uni.showToast({ title: '请选择文件', icon: 'none' });
    return;
  }
  optionShare.value?.onOpen({ fileObj: batchSelectedArr.value });
};

const clickBatchMove = () => {
  console.log('批量移动');
  if (batchSelectedIdArr.value.length === 0) {
    uni.showToast({ title: '请选择文件', icon: 'none' });
    return;
  }
  lkDatabasePopupRef.value?.openPopup();
};

const clickBatchDel = () => {
  if (batchSelectedIdArr.value.length === 0) {
    uni.showToast({ title: '请选择文件', icon: 'none' });
    return;
  }
  optionDel.value?.onOpen({ fileObj: batchSelectedArr.value });
};

const clickCancelEdit = () => {
  isEditing.value = false;
  tabStore.showTabBarAction();
};

const shareCb = () => {};

const delCb = async (items: any) => {
  const itemsToDelete = Array.isArray(items) ? items : [items];
  console.log('确认批量删除:', itemsToDelete);

  try {
    const filesToDelete = itemsToDelete.filter(item => item.fileType === 1 || item.fileType === 2);
    const spacesToDelete = itemsToDelete.filter(item => item.fileType === 3);

    if (filesToDelete.length > 0) {
      const api = curTab.value === '1' ? mixBatchDelete : myMixBatchDelete;
      await api({
        list: filesToDelete.map(item => ({ id: item.id, fileType: item.fileType })),
      });
    }

    if (spacesToDelete.length > 0) {
      for (const space of spacesToDelete) {
        await deleteSpaceFile({ id: space.id });
        uToastRef.value.show({
          message: '空间删除成功',
          type: 'success',
        });
      }
    }

    uToastRef.value.show({
      message: '文件删除成功',
      type: 'success',
    });
    isEditing.value = false;
    batchSelectedArr.value = [];
    batchSelectedIdArr.value = [];
    refreshPageList();
  } catch (error) {
    console.error('删除失败:', error);
    uni.showToast({ title: '删除失败', icon: 'none' });
  }
};

const confirmMove = async (payload: { currentParentObj: any; fileObj: any }) => {
  console.log('确认移动到:', payload.currentParentObj);
  console.log('被移动的文件:', batchSelectedArr.value);
  const { currentParentObj } = payload;

  try {
    const fileIds = batchSelectedArr.value.filter(item => item.fileType === 1).map(item => item.id);

    const folderItems = batchSelectedArr.value.filter(item => item.fileType === 2);

    const isSchoolSpace = curTab.value === '1';
    const folderIds = !isSchoolSpace ? folderItems.map(item => item.id) : [];
    const spaceIds = isSchoolSpace ? folderItems.map(item => item.id) : [];

    await batchUpdateParentFileAndFolder({
      parentId: currentParentObj.id || '0',
      ids: fileIds,
      folderIds,
      spaceIds,
    });

    uToastRef.value.show({
      message: '文件移动位置已完成',
      type: 'success',
    });
    lkDatabasePopupRef.value?.closePopup();
    isEditing.value = false;
    batchSelectedArr.value = [];
    batchSelectedIdArr.value = [];
    refreshPageList();
  } catch (error) {
    console.error('移动失败:', error);
    uni.showToast({ title: '移动失败', icon: 'none' });
  }
};

// 切换tab
const handleClickTab = (type: string) => {
  console.log(type);
  curTab.value = type;
};
// 点击+号
const handleAddSpace = () => {
  addSelectRef.value.onOpen({
    title: curTab.value === '1' ? '学校数据空间' : '我的数据空间',
    type: curTab.value === '1' ? 'subAdd' : 'add',
  });
};

const onWatchTransferRecord = () => {
  recordTransferRef.value.onOpen();
};

const isModal = ref(false);
const clickQuestion = () => {
  console.log('clickQuestion');
  isModal.value = true;
};

const clickSearch = () => {
  console.log('跳转搜索页面');
  // 使用storage存储activeType，避免URL参数在安卓打包时的问题
  uni.setStorageSync('searchActiveType', SearchType.FILE);
  uni.navigateTo({
    url: '/pages/search/index',
  });
};
const guideSteps = [
  {
    target: '.wrapTab',
    content: '工作成果一键存储至数据空间，支持智能调取与迭代完善。',
    position: 'bottom',
    btnText: '下一步',
  },
];

// 引用组件实例
const stepGuide = ref();
const hasDataSpaceList = ref(true);

// 开始引导
const onStartGuide = (list: any) => {
  if (list.length <= 0) {
    hasDataSpaceList.value = false;
    guideSteps[0].btnText = '知道了';
  }
  nextTick(() => {
    if (!uni.getStorageSync('Guide')?.DataSpace) {
      stepGuide.value.start();
    }
  });
};

// 处理引导完成事件
const onGuideComplete = () => {
  if (!hasDataSpaceList.value) {
    const data = uni.getStorageSync('Guide') || {};
    uni.setStorageSync('Guide', { ...data, DataSpace: true });
  } else {
    // 获取当前列表数据
    const list = databaseItemRef.value?.lkPageListRef?.list || [];
    // 找到第一个满足条件的项
    const target = list.find((item: any) => item.fileType === 3 || item.fileType === 2);
    if (target) {
      uni.navigateTo({
        url: `/pages-subpackages/data-space-pkg/sub-space/index?id=${target.id}&navTitle=${target.spaceName || target.folderName}&bizType=${curTab.value}&guide=true&privileges=${target?.privileges?.length ? target?.privileges?.[0] : 4}`,
      });
    }
  }
};

const databaseItemRef = ref();
const isQuestion = ref(false);
const refreshPageList = () => {
  console.log('调用分页的refresh刷新');
  databaseItemRef.value?.lkPageListRef?.refresh();
};

const getIsAuditSwitch = async () => {
  const res = await isAuditSwitch({});
  if (res.status === '1') {
    isQuestion.value = true;
  } else {
    isQuestion.value = false;
  }
};

onMounted(() => {
  getIsAuditSwitch();
  platform.value = uni.getSystemInfoSync().platform;
});
</script>
<template>
  <view class="container">
    <view class="header">
      <view class="title">数据空间</view>
      <view class="tool">
        <LkSvg
          width="24px"
          height="24px"
          src="/static/database/question.svg"
          @tap="clickQuestion"
          v-if="curTab === '1' && isQuestion"
        />
        <template v-if="curTab === '2'">
          <LkSvg
            v-if="!isEditing"
            width="24px"
            height="24px"
            src="/static/recycleBin/editItem.svg"
            @click="clickEditItem"
            class="more-checked"
          />
          <text v-else class="nav-action-text" @click="clickCheckAll">全选</text>
        </template>
        <LkSvg
          width="24px"
          height="24px"
          src="/static/database/transfer.svg"
          @tap="onWatchTransferRecord"
        />
        <LkSvg width="24px" height="24px" src="/static/database/search.svg" @tap="clickSearch" />
      </view>
    </view>
    <view class="wrapTab">
      <view class="wrapTxt" :class="{ active: curTab === '1' }" @click="handleClickTab('1')">
        <view class="txt">学校数据空间</view>
      </view>
      <view class="wrapTxt" :class="{ active: curTab === '2' }" @click="handleClickTab('2')">
        <view class="txt">我的数据空间</view>
      </view>
    </view>
    <view class="content" :style="{ paddingBottom: platform === 'ios' ? '170rpx' : '140rpx' }">
      <!-- 根级数据空间组件 -->
      <DatabaseItem
        ref="databaseItemRef"
        :bizType="curTab"
        @goToMySpace="curTab = '2'"
        @guide="onStartGuide"
        :isEditing="isEditing"
        @updateSelectedItems="updateSelectedItems"
      />
    </view>
    <!-- +号 -->
    <view class="addSpace" @tap="handleAddSpace" v-if="curTab === '2' && !isEditing">
      <LkSvg width="24px" height="24px" src="/static/database/addSpace.svg" />
    </view>
    <view v-if="isEditing" class="wrapEdit">
      <view class="editItem" @click="clickBatchShare" :class="{ disabled: !canBatchShare }">
        <LkSvg width="24px" height="24px" src="/static/database/tool_share.svg" />
        <view class="txt">分享</view>
      </view>
      <view class="editItem" @click="clickBatchMove" :class="{ disabled: !canBatchMove }">
        <LkSvg width="24px" height="24px" src="/static/database/tool_move.svg" />
        <view class="txt">移动</view>
      </view>
      <view class="editItem" @click="clickBatchDel" :class="{ disabled: !canBatchDelete }">
        <LkSvg width="24px" height="24px" src="/static/database/tool_delete.svg" />
        <view class="txt">删除</view>
      </view>
      <view class="editItem" @click="clickCancelEdit">
        <LkSvg width="24px" height="24px" src="/static/recycleBin/checkAll_close.svg" />
        <view class="txt close">取消</view>
      </view>
    </view>
  </view>
  <AddSelect ref="addSelectRef" :bizType="curTab" parentId="0" @refreshPageList="refreshPageList" />
  <up-modal
    :show="isModal"
    :showConfirmButton="false"
    :closeOnClickOverlay="true"
    @close="isModal = false"
  >
    <view class="title">提示</view>
    <view class="content"
      >学校数据空间上传文件开启了审核，审核通过后您上传的文件将在学校数据空间可见，暂支持在pc端查看上传文件的审核状态。</view
    >
    <view class="btn" @click="isModal = false">好的</view>
  </up-modal>
  <RecordTransfer ref="recordTransferRef" />
  <LkStepGuide :steps="guideSteps" @complete="onGuideComplete" ref="stepGuide" />
  <OptionShare ref="optionShare" @cb="shareCb" />
  <OptionDel ref="optionDel" @confirm="delCb" />
  <LkDatabasePopup
    ref="lkDatabasePopupRef"
    :layoutType="5"
    :bizType="curTab"
    :fileObj="batchSelectedArr"
    @confirm-move="confirmMove"
  ></LkDatabasePopup>
  <LkToast ref="uToastRef" />
</template>
<style lang="scss" scoped>
@import 'uview-plus/theme.scss';
.nav-action-text {
  font-size: 32rpx;
  color: #7d4dff;
  font-weight: 600;
  margin-left: 14px;
}
::v-deep .u-modal__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    color: #1d2129;
    font-size: 36rpx;
    font-weight: 600;
  }
  .content {
    color: #4e5969;
    font-size: 32rpx;
    margin-top: 8px;
  }
  .btn {
    font-size: 32rpx;
    font-weight: 600;
    color: #fff;
    margin-top: 24px;
    background-color: #7d4dff;
    border-radius: 8px;
    line-height: 40px;
    width: 100%;
    text-align: center;
  }
}
.container {
  background-image: url('https://huayun-ai-obs-public.huayuntiantu.com/8ea290b68afdbb3817d17878d41f60c5.png');
  background-size: 100% 50%;
  background-repeat: no-repeat;
  min-height: 100%;
  height: 100%;
  padding-top: 14px;
  display: flex;
  flex-direction: column;

  .header {
    padding: 0 16px;
    padding-top: var(--status-bar-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      color: #0f0f0f;
      font-family: 'Alibaba PuHuiTi 2.0';
      font-size: 40rpx;
      font-weight: 700;
    }
    .tool {
      display: flex;
      margin-bottom: 6rpx;
      .lk-svg {
        &:not(:first-child) {
          margin-left: 14px;
        }
      }
    }
  }
  .wrapTab {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-radius: 14px;
    padding-top: 5px;
    .wrapTxt {
      padding-top: 4rpx;
      padding-right: 30rpx;
      &.active {
        font-weight: 600; //小米平板600看不出区别,要700,但是ui说和应用中心保持一致,bug: 5399
        background: url('/static/database/txtBg.svg') no-repeat right top / 46rpx 40rpx;
      }
      .txt {
        color: #1d2129;
        font-size: 36rpx;
      }
      &:not(:first-child) {
        margin-left: 20px;
      }
    }
  }
  .content {
    flex: 1;
    background-color: #fff;
    border-radius: 16px 16px 0px 0px;
    background: #fff;
    box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.04);
    height: 100%;
    overflow-y: auto;
  }
  .addSpace {
    width: 48px;
    height: 48px;
    border-radius: 24px;
    background: linear-gradient(180deg, #4da3ff 0%, #7d4dff 100%);
    box-shadow:
      0px 3px 14px 2px rgba(0, 0, 0, 0.05),
      0px 8px 10px 1px rgba(0, 0, 0, 0.06),
      0px 5px 5px -3px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 146px;
    right: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.wrapEdit {
  width: calc(100vw - 20px);
  position: fixed;
  bottom: 14px;
  left: 50%;
  z-index: 2;
  transform: translateX(-50%);
  background-color: #fff;
  padding: 8.5px 57.83px;
  border-top: 1px solid #f4f4f4;
  border-radius: 14px;
  box-shadow:
    0px -1px 15.9px 0px rgba(0, 0, 0, 0.14),
    0px 1px 10px 0px rgba(0, 0, 0, 0.05),
    0px 2px 6.9px -1px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-between;
  .editItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    &.disabled {
      opacity: 0.5;
      pointer-events: none;
    }
    .txt {
      font-size: 26rpx;
      color: #303133;
      margin-top: 4px;
      &.close {
        color: #d54941;
      }
    }
  }
}
</style>
