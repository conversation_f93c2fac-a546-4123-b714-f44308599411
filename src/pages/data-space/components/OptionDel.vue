<script lang="ts" setup>
import { defineOptions, ref, type PropType } from 'vue';

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const emit = defineEmits(['confirm', 'cancel', 'close']);
const curValue = ref<any>(null);

const onOpen = (value: any) => {
  console.log(value);
  curValue.value = value.fileObj;
  show.value = true;
};

const onClose = () => {
  show.value = false;
  emit('cancel');
  emit('close');
};

const onConfirm = () => {
  emit('confirm', curValue.value);
  onClose();
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'Delete' });
</script>

<template>
  <up-popup :show="show" :round="10" mode="center" @close="onClose" v-bind="$attrs">
    <view class="delete">
      <view class="delete__body flex-center">
        <LkText bold size="xlarge">删除</LkText>
        <LkText type="secondary" size="large" style="text-align: center"
          >删除文件全部内容将进入回收站，30 天后自动彻底删除</LkText
        >
      </view>
      <view class="delete__footer">
        <LkButton type="plain" block @click="onClose" size="large">取消</LkButton>
        <LkButton type="danger" block @click="onConfirm" size="large">确定删除</LkButton>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.delete {
  background: #fff;
  border-radius: 28rpx;
  padding: 0 24px;
  padding-top: 32px;
  padding-bottom: 24px;
  &__body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 70vw;
    .lk-text {
      &:not(:first-child) {
        margin-top: 8px;
        font-size: 16px;
        color: #4e5969;
      }
    }
  }
  &__footer {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .lk-button {
      &:not(:first-child) {
        margin-left: 15px;
      }
    }
  }
}
</style>
