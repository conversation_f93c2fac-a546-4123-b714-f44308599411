<script lang="ts" setup>
import { defineOptions, ref, type PropType } from 'vue';

import LkSvg from '@/components/svg/index.vue';

interface ToolItem {
  name: string;
  icon: string;
  type: string;
}

const modelValue = ref<any>({
  fileObj: {},
});

const toolList = ref<ToolItem[]>([
  {
    name: '微信好友',
    icon: 'wechat',
    type: 'weixin',
  },
  // 暂时不支持QQ分享,开放平台需要有新主体来注册
  // {
  //   name: 'QQ好友',
  //   icon: 'qq',
  //   type: 'qq',
  // },
]);

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const emit = defineEmits(['close']);

const onOpen = (value: any) => {
  console.log('onOpen', value);
  show.value = true;
  modelValue.value.fileObj = value.fileObj;
};

const onClose = () => {
  show.value = false;
  modelValue.value.fileObj = {
    file: {},
  };
  emit('close');
};

const handleShare = (item: any) => {
  let shareTargetFile = modelValue.value.fileObj;

  // 检查是否为批量分享（数组）
  if (Array.isArray(shareTargetFile)) {
    // 如果批量分享中只有一个文件，则按单文件处理
    if (shareTargetFile.length === 1) {
      shareTargetFile = shareTargetFile[0];
    }
    // 如果批量分享中有多个文件，则执行文本汇总分享
    else {
      console.log('批量分享多个文件', shareTargetFile);
      const shareContent = shareTargetFile
        .map((file: any) => `${file.fileName}: ${file.file?.fileUrl || file.fileUrl}`)
        .join('\n');
      uni.share({
        provider: item.type,
        scene: 'WXSceneSession',
        type: 1, // 纯文本分享
        summary: shareContent,
        href: item.type === 'qq' ? ' ' : undefined, // QQ必填
        title: item.type === 'qq' ? '批量分享文件' : undefined, // QQ必填
        success: () => {
          console.log('批量分享成功');
          onClose();
        },
        fail: err => {
          console.error('批量分享失败', err);
          uni.showToast({ title: '分享失败', icon: 'none' });
        },
      });
      return; // 结束执行
    }
  }

  // --- 从这里开始，无论是单文件操作还是单文件的批量操作，都按单文件逻辑处理 ---
  console.log('按单文件逻辑分享', item, shareTargetFile);
  const fileExtension =
    shareTargetFile?.file?.fileUrl?.split('?')[0]?.split('.').pop()?.toLowerCase() ?? '';

  console.log(fileExtension);
  // 支持officeapps预览的格式
  if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension)) {
    const previewUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(shareTargetFile?.file?.fileUrl)}`;
    console.log('previewUrl1111', previewUrl);
    uni.share({
      provider: item.type,
      scene: 'WXSceneSession',
      type: 1, // 纯文字分享(分享链接地址)
      summary: previewUrl,
      // qq且type不为2(2:纯图片)时必须提供href和title才可以分享
      href: item.type === 'qq' ? previewUrl : undefined,
      title: item.type === 'qq' ? shareTargetFile?.file?.fileName : undefined,
      success: function (res) {
        console.log('success:' + JSON.stringify(res));
        onClose();
      },
      fail: function (err) {
        console.log('fail:' + JSON.stringify(err));
        uni.showToast({
          title: err?.errMsg,
          icon: 'none',
        });
      },
    });
    // 常见图片格式
  } else if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(fileExtension)) {
    // 图片有url参数会分享失败,所以先下载图片到本地临时路径,再进行分享
    uni.showLoading({
      title: '准备分享中...',
    });

    uni.downloadFile({
      url: shareTargetFile?.file?.fileUrl,
      success: res => {
        if (res.statusCode === 200) {
          uni.hideLoading();
          // 使用本地临时路径进行分享
          uni.share({
            provider: item.type,
            scene: 'WXSceneSession',
            type: 2, // 纯图片分享
            imageUrl: res.tempFilePath,
            success: function (res) {
              console.log('success:' + JSON.stringify(res));
              onClose();
            },
            fail: function (err) {
              console.log('fail:' + JSON.stringify(err));
              if (err?.errMsg.indexOf('低版本手Q不支持该项功能') != -1) {
                uni.showToast({
                  title: '低版本手Q不支持该项功能!',
                  icon: 'none',
                });
              } else {
                uni.showToast({
                  title: err?.errMsg,
                  icon: 'none',
                });
              }
            },
          });
        } else {
          uni.hideLoading();
          uni.showToast({
            title: '图片下载失败',
            icon: 'none',
          });
        }
      },
      fail: err => {
        uni.hideLoading();
        console.log('download fail:' + JSON.stringify(err));
        uni.showToast({
          title: '图片下载失败',
          icon: 'none',
        });
      },
    });
    // 常见音频格式
  } else if (
    ['mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac', 'ape', 'wma', 'mka'].includes(fileExtension)
  ) {
    if (item.type === 'qq') {
      // 音频分享
      uni.share({
        provider: item.type,
        scene: 'WXSceneSession',
        type: 3,
        mediaUrl: shareTargetFile?.file?.fileUrl,
        href: item.type === 'qq' ? shareTargetFile?.file?.fileUrl : undefined,
        title: item.type === 'qq' ? shareTargetFile?.file?.fileName : undefined,
        success: function (res) {
          console.log('success:' + JSON.stringify(res));
          onClose();
        },
        fail: function (err) {
          console.log('fail:' + JSON.stringify(err));
          uni.showToast({
            title: err?.errMsg,
            icon: 'none',
          });
        },
      });
    } else {
      // 微信音频分享不知道为什么不行,改用链接分享的方式
      uni.share({
        provider: item.type,
        scene: 'WXSceneSession',
        type: 1, // 纯文字分享
        summary: shareTargetFile?.file?.fileUrl,
        // qq且type不为2(2:纯图片)时必须提供href和title才可以分享
        href: item.type === 'qq' ? shareTargetFile?.file?.fileUrl : undefined,
        title: item.type === 'qq' ? shareTargetFile?.file?.fileName : undefined,
        success: function (res) {
          console.log('success:' + JSON.stringify(res));
          onClose();
        },
        fail: function (err) {
          console.log('fail:' + JSON.stringify(err));
          uni.showToast({
            title: err?.errMsg,
            icon: 'none',
          });
        },
      });
    }
    // 常见视频格式
  } else if (['mp4', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'webm'].includes(fileExtension)) {
    // qq不支持视频分享,所以分享链接
    if (item.type === 'qq') {
      uni.share({
        provider: item.type,
        scene: 'WXSceneSession',
        type: 1, // 纯文字分享
        summary: shareTargetFile?.file?.fileUrl,
        // qq且type不为2(2:纯图片)时必须提供href和title才可以分享
        href: item.type === 'qq' ? shareTargetFile?.file?.fileUrl : undefined,
        title: item.type === 'qq' ? shareTargetFile?.file?.fileName : undefined,
        success: function (res) {
          console.log('success:' + JSON.stringify(res));
          onClose();
        },
        fail: function (err) {
          uni.showToast({
            title: err?.errMsg,
            icon: 'none',
          });
        },
      });
    } else {
      uni.share({
        provider: item.type,
        scene: 'WXSceneSession',
        type: 4, // 视频分享
        mediaUrl: shareTargetFile?.file?.fileUrl,
        title: shareTargetFile?.file?.fileName,
        summary: '分享视频',
        success: function (res) {
          console.log('success:' + JSON.stringify(res));
          onClose();
        },
        fail: function (err) {
          console.log('fail:' + JSON.stringify(err));
          uni.showToast({
            title: err?.errMsg,
            icon: 'none',
          });
        },
      });
    }
  } else if (['svg'].includes(fileExtension)) {
    // 不支持的分享格式
    uni.showToast({
      title: '不支持的分享格式',
      icon: 'none',
    });
  } else {
    console.log('modelValue.value', modelValue.value);
    uni.share({
      provider: item.type,
      scene: 'WXSceneSession',
      type: 1, // 纯文字分享
      summary: shareTargetFile?.file?.fileUrl,
      // qq且type不为2(2:纯图片)时必须提供href和title才可以分享
      href: item.type === 'qq' ? shareTargetFile?.file?.fileUrl : undefined,
      title: item.type === 'qq' ? shareTargetFile?.file?.fileName : undefined,
      success: function (res) {
        console.log('success:' + JSON.stringify(res));
        onClose();
      },
      fail: function (err) {
        uni.showToast({
          title: err?.errMsg,
          icon: 'none',
        });
      },
    });
  }
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'Share' });
</script>

<template>
  <up-popup closeable :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="share safe-area-inset-bottom">
      <view class="share__header flex-center">
        <LkText bold>分享</LkText>
      </view>
      <view class="share__list">
        <template v-for="item in toolList" :key="item.type">
          <view class="share__list-item flex-center" @click="handleShare(item)">
            <LkSvg :src="`/static/database/${item.icon}.svg`" width="40px" height="40px" />
            <LkText size="small">{{ item.name }}</LkText>
          </view>
        </template>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
::v-deep .u-popup__content__close {
  .u-icon__icon {
    color: #1a1e24 !important;
  }
}
.share {
  background: #fff;
  border-radius: 28rpx 28rpx 0 0;
  padding: 20rpx;
  padding-top: 30rpx;
  &__header {
  }
  &__list {
    margin-top: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    &-item {
      flex-direction: column;
      width: 150rpx;
      padding: 20rpx;
      border-radius: 28rpx;
      background: white;
      .lk-svg {
        margin-bottom: 5px;
      }
    }
  }
}
</style>
