<script lang="ts" setup>
import { defineOptions, ref, type PropType, defineEmits } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import LkSvg from '@/components/svg/index.vue';
import OptionDel from '@/pages/data-space/components/OptionDel.vue';
import OptionShare from '@/pages/data-space/components/OptionShare.vue';
import OptionRename from '@/pages/data-space/components/OptionRename.vue';
import { useChatStore } from '@/store/chatStore';
import { useUserStore } from '@/store/userStore';
import {
  getAppointedAppList,
  mixBatchDelete,
  myMixBatchDelete,
  deleteSpaceFile,
  updateParentFile,
  updateParentFolder,
  updateParentSpace,
} from '@/api/database';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';
import { uploadFileWithResume } from '@/utils/uploadWithResume';
import { getBaseUrl } from '@/common/ai/url';
import LkToast from '@/components/LkToast/index.vue';

const uToastRef = ref();
const props = defineProps<{
  bizType: string;
}>();

interface ToolItem {
  name: string;
  icon: string;
  type: string;
}

interface ModelValue {
  title?: string;
  user?: string;
  fileObj?: any;
  bizType?: string;
}

const emit = defineEmits(['refreshPageList']);

const chatStore = useChatStore();
const userStore = useUserStore();
const modelValue = ref<ModelValue>({});
const optionDelRef = ref<any>(null);
const optionShareRef = ref<any>(null);
const optionRenameRef = ref<any>(null);
const lkDatabasePopupRef = ref<any>(null);
const xeUploadRef = ref<any>(null);
const uploadOptions = ref({});
const isContentVisible = ref(true);

// 可见工具列表
const visibleToolList = ref<ToolItem[]>([]);
// 可见链接列表
const visibleLinkList = ref<ToolItem[]>([]);

const toolList = ref<ToolItem[]>([
  {
    name: '分享',
    icon: 'tool_share',
    type: 'share-square',
  },
  {
    name: '移动',
    icon: 'tool_move',
    type: 'cut',
  },
  {
    name: '更新',
    icon: 'tool_update',
    type: 'updateFile',
  },
  {
    name: '删除',
    icon: 'tool_delete',
    type: 'trash',
  },
]);

const linkList = ref<ToolItem[]>([
  {
    name: '重命名',
    icon: '/static/database/tool_rename.svg',
    type: 'rename',
  },
  {
    name: '思维导图',
    icon: '/static/database/tool_mindmap.svg',
    type: 'mindMap',
  },
  {
    name: '文档解读',
    icon: '/static/database/tool_document.svg',
    type: 'documentAnalysis',
  },
]);

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const urlOptions = ref<any>();

onLoad((options: any) => {
  urlOptions.value = { ...options };
});

const onOpen = (value: ModelValue) => {
  isContentVisible.value = true;
  show.value = true;
  modelValue.value = value;
  console.log(value);
  console.log(modelValue.value?.fileObj?.fileType);

  // 获取用户信息
  const userInfo = userStore.getUserInfo;
  // 判断是否为上传人
  const isUploader = modelValue.value?.fileObj?.uploader === userInfo?.username;
  // 判断是否为管理员
  const isAdmin = Number(userInfo?.roleType) === 1 || Number(userInfo?.roleType) === 3;
  // 文件权限级别
  const privileges = Number(modelValue.value?.fileObj?.privileges?.[0] || 4);
  console.log(modelValue.value?.fileObj);
  console.log(privileges);

  // 设置可见工具列表
  const isManagePrivilege = privileges >= 3 || isAdmin; // 管理权限或管理员角色
  // 是否为文件(只有文件才能更新)
  const isFile = modelValue.value?.fileObj?.fileType === 1;
  // 是否为文件夹
  const isFolder = modelValue.value?.fileObj?.fileType === 2;
  // 是否为管理员模式（bizType === '2'时为管理员模式）
  const isAdminMode = props.bizType === '2';

  // 过滤链接列表 - 思维导图和文档解读只对文件显示
  const filteredLinkList = linkList.value.filter(item => {
    // 如果是思维导图或文档解读，只有文件类型(fileType === 1)才显示
    if (item.type === 'mindMap' || item.type === 'documentAnalysis') {
      return isFile;
    }
    // 其他类型的链接项直接显示
    return true;
  });

  // 当bizType === '2'时，拥有管理员权限，显示所有可用选项
  if (isAdminMode) {
    // 管理员模式：显示所有选项，但更新只对文件可见
    visibleToolList.value = toolList.value.filter(item => item.type !== 'updateFile' || isFile);
    visibleLinkList.value = [...filteredLinkList];
  } else if (isManagePrivilege && isUploader) {
    // 可管理+上传人：所有可见，但更新只对文件可见
    visibleToolList.value = toolList.value.filter(item => item.type !== 'updateFile' || isFile);
    visibleLinkList.value = [...filteredLinkList];
  } else if (isManagePrivilege && !isUploader) {
    // 可管理+非上传人：仅更新不可见
    visibleToolList.value = toolList.value.filter(item => item.type !== 'updateFile');
    visibleLinkList.value = [...filteredLinkList];
  } else if (privileges === 1 && !isUploader) {
    // 可查看权限：思维导图和文档解读(仅对文件)
    visibleToolList.value = [];
    visibleLinkList.value = filteredLinkList.filter(
      item => item.type === 'mindMap' || item.type === 'documentAnalysis'
    );
  } else if (privileges === 1 && isUploader) {
    // 可查看+上传人权限：思维导图、文档解读(仅对文件)、分享、更新(仅文件)、删除
    visibleToolList.value = toolList.value.filter(
      item =>
        item.type === 'share-square' ||
        (item.type === 'updateFile' && isFile) ||
        item.type === 'trash'
    );
    visibleLinkList.value = filteredLinkList.filter(
      item => item.type === 'mindMap' || item.type === 'documentAnalysis'
    );
  } else if (privileges === 2 && !isUploader) {
    // 可编辑权限：分享、移动、重命名、思维导图(仅对文件)、文档解读(仅对文件)
    visibleToolList.value = toolList.value.filter(
      item => item.type === 'share-square' || item.type === 'cut'
    );
    visibleLinkList.value = filteredLinkList;
    console.log(visibleToolList.value);
  } else if (privileges === 2 && isUploader) {
    // 可编辑+上传人权限：分享、移动、更新(仅文件)、重命名、思维导图(仅对文件)、文档解读(仅对文件)
    visibleToolList.value = toolList.value.filter(
      item =>
        item.type === 'share-square' ||
        item.type === 'cut' ||
        (item.type === 'updateFile' && isFile)
    );
    visibleLinkList.value = [...filteredLinkList];
    console.log(visibleToolList.value);
  } else {
    // 默认情况下只显示基本功能
    visibleToolList.value = [];
    visibleLinkList.value = filteredLinkList.filter(
      item => item.type === 'mindMap' || item.type === 'documentAnalysis'
    );
  }
};

const onClose = () => {
  show.value = false;
  modelValue.value = {};
};

const onClickItem = (item: ToolItem) => {
  if (item.type === 'share-square') {
    isContentVisible.value = false;
    optionShareRef.value.onOpen({
      fileObj: modelValue.value.fileObj,
    });
  } else if (item.type === 'cut') {
    console.log('cut');
    lkDatabasePopupRef.value?.openPopup();
  } else if (item.type === 'updateFile') {
    xeUploadRef.value.upload('file', {});
  } else if (item.type === 'trash') {
    isContentVisible.value = false;
    optionDelRef.value.onOpen({
      fileObj: modelValue.value.fileObj,
    });
  }
};

const handleLinkItemClick = (item: ToolItem) => {
  console.log(item);
  console.log(modelValue.value);
  if (item.type === 'rename') {
    isContentVisible.value = false;
    optionRenameRef.value.onOpen({
      fileObj: modelValue.value.fileObj,
      bizType: props.bizType,
    });
  } else if (item.type === 'mindMap') {
    getAppointedAppList({}).then(res => {
      const appId = res.find((item: any) => item.appointedType === 1).id;
      console.log('modelValue.value.fileObj', modelValue.value.fileObj);
      const fileObjStr = JSON.stringify(modelValue.value.fileObj.file);

      // 使用URL查询字符串传递参数
      const params = {
        fileObj: modelValue.value.fileObj.file,
        chatType: 1,
        appId: res.find((item: any) => item.appointedType === 1).id,
      };
      chatStore.setToolGoChatParams(params);
      uni.navigateTo({
        url: `/pages/chat/index?chatType=1&appId=${appId}&fileObj=${encodeURIComponent(fileObjStr)}`,
      });
    });
  } else if (item.type === 'documentAnalysis') {
    getAppointedAppList({}).then(res => {
      console.log('resresresresres', res);
      const appId = res.find((item: any) => item.appointedType === 2).id;
      const fileObjStr = JSON.stringify(modelValue.value.fileObj.file);

      // 使用URL查询字符串传递参数
      const params = {
        fileObj: modelValue.value.fileObj.file,
        chatType: 2,
        appId: res.find((item: any) => item.appointedType === 2).id,
      };
      chatStore.setToolGoChatParams(params);
      uni.navigateTo({
        url: `/pages/chat/index?chatType=2&appId=${appId}&fileObj=${encodeURIComponent(fileObjStr)}`,
      });
    });
  }
};

const handleFileIcon = (item: any): string => {
  if (item?.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item?.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item?.fileType === 1) {
    // 截取文件扩展名
    const fileType = item?.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

const delCb = async (item: any) => {
  console.log('确认删除文件:', item);
  // 删除"学校/我的"的文件/文件夹
  if (item.fileType === 1 || item.fileType === 2) {
    const res = await (modelValue.value.bizType === '1' ? mixBatchDelete : myMixBatchDelete)({
      list: [{ id: item.id, fileType: item.fileType }],
    });
    uToastRef.value.show({
      message: '文件删除成功',
      type: 'success',
    });
    console.log(res);
    // 删除空间
  } else if (item.fileType === 3) {
    const res = await deleteSpaceFile({ id: item.id });
    uToastRef.value.show({
      message: '空间删除成功',
      type: 'success',
    });
    console.log(res);
  } else {
    console.log('未知文件类型');
  }

  onClose();
  emit('refreshPageList');
};

const confirmMove = async (item: any) => {
  console.log('confirmMove', item);
  console.log(modelValue.value);
  try {
    // 被移动的是文件
    if (modelValue.value?.fileObj?.fileType === 1) {
      console.log('移动文件');
      await updateParentFile({
        id: modelValue.value?.fileObj?.id,
        parentId: '', // 也可不传,但是看pc传了个空值就也跟着传吧
        // 被移动的文件处于我的数据空间中,需要传入folderId,学校数据空间中,需要传入spaceId
        [props.bizType === '1' ? 'spaceId' : 'folderId']:
          modelValue.value?.fileObj?.fileType === 1 ? item.currentParentObj?.id || '0' : undefined,
      });
      // 被移动的是文件夹
    } else if (modelValue.value?.fileObj?.fileType === 2) {
      console.log('移动文件夹');
      if (props.bizType === '1') {
        await updateParentSpace({
          id: modelValue.value?.fileObj?.id,
          parentId: item.currentParentObj?.id,
        });
      } else {
        await updateParentFolder({
          id: modelValue.value?.fileObj?.id,
          parentId: item.currentParentObj?.id || '0',
        });
      }
    }
    // 文件移动位置已完成
    uToastRef.value.show({
      message: '文件移动位置已完成',
      type: 'success',
    });
    onClose();
    emit('refreshPageList');
  } catch (error) {
    console.error('移动失败', error);
    uni.showToast({ title: '移动失败', icon: 'none' });
  }
};

function handleUploadCallback(e: any) {
  if (e.type === 'choose') {
    console.log('选择的文件:', e.data);
    // 兼容数组和单文件
    if (Array.isArray(e.data)) {
      e.data.forEach((file: any) => uploadToServer(file, 'file'));
    } else {
      uploadToServer(e.data, 'file');
    }
  } else if (e.type === 'warning') {
    console.error('文件上传警告:', e.data);
    uni.showToast({ title: e.data.message, icon: 'none', duration: e.data.duration || 2000 });
  }
}

function uploadToServer(file: any, type: string) {
  const uniqueFileId =
    file && file.uid ? file.uid : (file && file.name ? file.name : 'file') + '_' + Date.now();
  file.uniqueId = uniqueFileId;
  const bizType = props.bizType;
  const parentId = modelValue.value?.fileObj?.parentId || '0';
  const onProgress = (progress: number, message?: string) => {
    if (message) {
      uToastRef.value.show({
        message: message,
        type: 'info',
      });
    }
    let records = uni.getStorageSync('uploadRecords') || [];
    const idx = records.findIndex((r: any) => r.uniqueId === uniqueFileId);
    if (idx !== -1) {
      console.log(`上传进度更新: ${uniqueFileId} - ${progress}%`);
      // 确保有最小进度1%，避免显示为0%
      progress = Math.max(progress, 1);
      records[idx].progress = progress;
      records[idx].status = progress === 100 ? 'success' : 'uploading';
      uni.setStorageSync('uploadRecords', records);
      uni.$emit('uploadRecordsUpdated');
    }
  };

  // 记录初始化
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!records.find((r: any) => r.uniqueId === uniqueFileId)) {
    records.push({
      uniqueId: uniqueFileId,
      fileName: file.name,
      fileSize: file.size,
      status: 'uploading',
      progress: 0,
      uploadTime: Date.now(),
      bizType,
      parentId,
      file: file,
      fileKey: '',
    });
    uni.setStorageSync('uploadRecords', records);
    uni.$emit('uploadRecordsUpdated');
  }

  // 分片上传
  uploadFileWithResume(file, bizType, parentId, onProgress)
    .then(() => {
      // 分片上传成功后，执行原直传成功回调中的核心逻辑
      // 1. 更新进度为99
      let records = uni.getStorageSync('uploadRecords') || [];
      if (!Array.isArray(records)) records = [];
      const idx = records.findIndex((r: any) => r.uniqueId === uniqueFileId);
      if (idx !== -1 && records[idx].progress < 99) {
        updateRecordProgress(uniqueFileId, 99);
      }

      // 2. 使用新的云端登记函数，替代原来的uploadFile
      const recordItem = records[idx];
      if (recordItem && recordItem.fileKey) {
        cloudRegister(recordItem.fileKey, bizType, parentId, uniqueFileId, file);
      } else {
        console.error('缺少fileKey，无法进行云端登记');
        uni.showToast({ title: '上传信息不完整，请重试', icon: 'none' });
        updateRecordStatus(uniqueFileId, 'failed');
      }
    })
    .catch(err => {
      let records = uni.getStorageSync('uploadRecords') || [];
      const idx = records.findIndex((r: any) => r.uniqueId === uniqueFileId);
      if (idx !== -1) {
        records[idx].status = 'failed';
        uni.setStorageSync('uploadRecords', records);
        uni.$emit('uploadRecordsUpdated');
      }
      console.error('分片上传失败', err);
    });
}

function cloudRegister(
  fileKey: string,
  bizType: string,
  parentId: string,
  uniqueFileId: string,
  file: any
) {
  const baseUrl = getBaseUrl();
  console.log('尝试云端登记:', fileKey, bizType, parentId);

  // 初始设置为99%，表示正在云端登记
  updateRecordProgress(uniqueFileId, 99);

  uni.uploadFile({
    url: `${baseUrl}/huayun-ai/cloud/file/updateUpload`, // 确保使用完整URL
    method: 'POST',
    header: {
      Authorization: uni.getStorageSync('token'),
      // 'Content-Type': 'application/x-www-form-urlencoded',
    },
    filePath: file.tempFilePath || file.path,
    name: 'file',
    formData: {
      fileKey: fileKey,
      fileId: modelValue.value.fileObj?.id,
    },
    success: (res: any) => {
      console.log('云端登记响应:', res);
      res = JSON.parse(res.data);
      console.log(res);
      if (res.code === 200) {
        // 明确设置为100%
        updateRecordStatus(uniqueFileId, 'success', 100);
        emit('refreshPageList');
        uToastRef.value.show({
          message: '文件更新成功',
          type: 'success',
        });
        onClose();
      } else {
        console.error('云端登记接口失败:', res);
        uni.showToast({
          title: (res.data && res.data.message) || '云端登记失败',
          icon: 'none',
        });
        updateRecordStatus(uniqueFileId, 'failed');
      }
    },
    fail: err => {
      console.error('云端登记请求失败:', err);
      uni.showToast({ title: '云端登记请求失败', icon: 'none' });
      updateRecordStatus(uniqueFileId, 'failed');
    },
  });
}

// --- 辅助函数 ---
function updateRecordStatus(uniqueId: string, status: string, progress?: number) {
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!Array.isArray(records)) records = [];
  const idx = records.findIndex((r: any) => r.uniqueId === uniqueId);
  if (idx !== -1) {
    records[idx].status = status;
    if (typeof progress !== 'undefined') {
      records[idx].progress = progress;
    }
    uni.setStorageSync('uploadRecords', records);
    uni.$emit('uploadRecordsUpdated');
  }
}

function updateRecordProgress(uniqueId: string, progress: number) {
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!Array.isArray(records)) records = [];
  const idx = records.findIndex((r: any) => r.uniqueId === uniqueId);
  if (idx !== -1) {
    if (records[idx].status === 'uploading') {
      const safeProgress = progress === 100 ? 100 : Math.min(progress, 99);
      if (safeProgress > records[idx].progress) {
        records[idx].progress = safeProgress;
        uni.setStorageSync('uploadRecords', records);
        uni.$emit('uploadRecordsUpdated');
      }
    }
  }
}

defineExpose({ onOpen, onClose });
defineOptions({ name: 'Tools' });
</script>

<template>
  <up-popup closeable :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="space-popup" v-show="isContentVisible">
      <view class="space-popup__header">
        <view class="space-popup__header-sign">
          <LkSvg
            class="fileIcon"
            width="90rpx"
            height="90rpx"
            :src="handleFileIcon(modelValue.fileObj)"
            :errorSrc="`/static/fileTypeIcon/unknown.svg`"
          />
        </view>
        <view class="space-popup__header-body">
          <LkText type="primary" bold class="up-line-1">{{ modelValue?.title || '--' }}</LkText>
          <LkText type="tertiary" size="small">{{ modelValue?.user || '--' }}</LkText>
        </view>
        <view class="space-popup__header-tools"></view>
      </view>
      <up-line margin="0 0 20rpx 0" />
      <view class="space-popup__body">
        <view class="space-popup__body-tools">
          <template v-for="item in visibleToolList" :key="item.type">
            <view
              class="space-popup__body-tools-item"
              @tap="onClickItem(item)"
              :class="{
                disabled: modelValue?.fileObj?.fileType !== 1 && item.type === 'share-square',
              }"
            >
              <LkSvg :src="`/static/database/${item.icon}.svg`" width="24px" height="24px" />
              <LkText size="small">{{ item.name }}</LkText>
            </view>
          </template>
        </view>
        <view
          class="space-popup__body-cell"
          :style="{ backgroundColor: visibleLinkList.length > 0 ? 'white' : 'transparent' }"
        >
          <up-cell-group :border="false">
            <template v-for="(item, index) in visibleLinkList" :key="item.type">
              <up-cell
                :title="item.name"
                :border="index !== visibleLinkList.length - 1"
                @click="handleLinkItemClick(item)"
              >
                <template #icon>
                  <view class="custom-icon">
                    <LkSvg :src="item.icon" width="22px" height="22px" />
                  </view>
                </template>
              </up-cell>
            </template>
          </up-cell-group>
        </view>
      </view>
    </view>
    <OptionRename
      ref="optionRenameRef"
      @refreshPageList="
        emit('refreshPageList');
        onClose();
      "
      @close="isContentVisible = true"
    />
    <OptionDel ref="optionDelRef" @confirm="delCb" @close="isContentVisible = true" />
    <OptionShare ref="optionShareRef" @close="isContentVisible = true" />
    <LkDatabasePopup
      ref="lkDatabasePopupRef"
      :bizType="props.bizType"
      :layoutType="5"
      :fileObj="modelValue.fileObj"
      @confirmMove="confirmMove"
    />
    <xe-upload
      ref="xeUploadRef"
      :options="uploadOptions"
      @callback="handleUploadCallback"
    ></xe-upload>
  </up-popup>
  <LkToast ref="uToastRef" />
</template>

<style lang="scss" scoped>
.space-popup {
  background: #f2f3f5;
  border-radius: 28rpx 28rpx 0 0;
  &__header {
    display: flex;
    align-items: flex-start;
    padding: 30rpx;
    &-sign {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      margin-left: 10px;
      .lk-text {
        &:not(:first-child) {
          margin-top: 7px;
        }
      }
    }
    &-tools {
    }
  }
  &__body {
    padding: 0 16px 16px 16px;
    &-tools {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        padding: 20rpx;
        border-radius: 28rpx;
        background: white;
        &.disabled {
          opacity: 0.5;
          pointer-events: none;
        }
        &:not(:first-child) {
          margin-left: 10px;
        }
        .lk-text {
          &:not(:first-child) {
            margin-top: 5px;
          }
        }
      }
    }
    &-cell {
      padding: 10rpx 20rpx;
      margin-top: 20rpx;
      border-radius: 28rpx;
    }
  }
}
.custom-icon {
  margin-right: 5px;
  display: flex;
  align-items: center;
}
</style>
