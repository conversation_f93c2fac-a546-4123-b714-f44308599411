<script lang="ts" setup>
import { defineOptions, ref, defineEmits } from 'vue';
import { updateFolder, renameFolder, renameFile } from '@/api/database';
import LkSvg from '@/components/svg/index.vue';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';
import LkToast from '@/components/LkToast/index.vue';
const emit = defineEmits(['update:modelValue', 'refreshPageList', 'close']);

const lkDatabasePopupRef = ref<InstanceType<typeof LkDatabasePopup>>();
const fileName = ref('');
const uToastRef = ref();
const show = defineModel('show', {
  type: Boolean,
  default: false,
});

// 当前编辑的文件对象
const currentFile = ref<any>(null);
const curBizType = ref<string>('');

const onOpen = (value: any) => {
  console.log('onOpen', value);
  fileName.value = value?.fileObj?.fileName || '';
  currentFile.value = value?.fileObj;
  curBizType.value = value?.bizType;
  show.value = true;
};

const onClose = () => {
  show.value = false;
  fileName.value = '';
  currentFile.value = null;
  emit('close');
};

const placeholderStyle = 'color: #86909C; font-size: 14px;';

const handleRename = () => {
  console.log(currentFile.value);
  console.log(curBizType.value);
  if (!fileName.value || !currentFile.value) {
    uni.showToast({
      title: '文件名不能为空',
      icon: 'none',
    });
    return;
  }
  let api;
  let params = {};
  if (currentFile.value.fileType === 1) {
    api = renameFile;
    params = {
      id: currentFile.value.id,
      fileName: fileName.value,
    };
  } else if (currentFile.value.fileType === 2) {
    if (curBizType.value === '1') {
      api = updateFolder;
      params = {
        id: currentFile.value.id,
        spaceName: fileName.value,
      };
    } else {
      api = renameFolder;
      params = {
        id: currentFile.value.id,
        folderName: fileName.value,
      };
    }
  } else {
    uni.showToast({
      title: '文件类型不支持重命名',
      icon: 'none',
    });
    return;
  }
  api(params).then(res => {
    console.log(res);
    uToastRef.value.show({
      message: '文件重命名成功',
      type: 'success',
    });
    // 立即关闭会导致提示被关闭
    setTimeout(() => {
      onClose();
      emit('refreshPageList');
    }, 500);
  });
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'OptionRename' });
</script>

<template>
  <up-popup :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="addFolder">
      <view class="title">重命名</view>
      <view class="wrapClose">
        <LkSvg src="/static/database/close.svg" width="24px" height="24px" @tap="onClose" />
      </view>
      <view class="wrapContent">
        <view class="title">
          <view class="txt">文件名称</view>
          <LkSvg class="required" src="/static/database/required.svg" width="8px" height="8px" />
        </view>
        <view class="wrapInput">
          <up-input
            v-model="fileName"
            :show-action="false"
            placeholder="请输入"
            border="none"
            :placeholderStyle="placeholderStyle"
            clearable
          ></up-input>
        </view>
        <view class="wrapBtn">
          <LkButton :disabled="!fileName" type="primary" shape="round" @tap="handleRename"
            >确定</LkButton
          >
        </view>
      </view>
    </view>
  </up-popup>
  <LkDatabasePopup ref="lkDatabasePopupRef" bizType="2" :layoutType="2" />
  <LkToast ref="uToastRef" />
</template>

<style lang="scss" scoped>
.addFolder {
  background: #fff;
  border-radius: 14px 14px 0 0;
  min-height: 30vh;
  padding: 18px;
  padding-top: 16px;
  padding-bottom: 14px;
  position: relative;
  > .title {
    font-size: 18px;
    font-weight: 600;
    color: #1d2129;
    text-align: center;
  }
  .wrapClose {
    position: absolute;
    right: 10px;
    top: 10px;
  }
  .wrapContent {
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #1d2129;
      margin-top: 14px;
      .txt {
      }
      .required {
        margin-left: 6px;
      }
    }
  }
  .wrapInput {
    display: flex;
    height: 48px;
    align-items: center;
    border-radius: 8px;
    background: #f2f3f5;
    margin-top: 10px;
    padding: 0 16px;
    .u-input {
      font-size: 14px;
    }
  }
  .wrapBtn {
    margin-top: 32px;
    .lk-button {
      width: 100%;
    }
  }
}
</style>
