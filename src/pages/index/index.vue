<template>
  <view
    :class="`tab-container ${platform === 'ios' ? 'platform-ios' : 'platform-android'}`"
    :style="
      tabStore.activeTabIndex === 0 && {
        backgroundColor: '#F7F7FD',
        backgroundImage:
          'url(https://huayun-ai-obs-public.huayuntiantu.com/af8e099f-e740-43ef-aa04-52c9dc02eda5.png)',
        backgroundSize: '100% 40%',
        backgroundRepeat: 'no-repeat',
      }
    "
  >
    <!-- Tab内容区域 - 所有页面都预先渲染，通过样式控制显示/隐藏 -->
    <view class="tab-content">
      <!-- 首页 -->
      <view v-if="tabStore.activeTabIndex === 0" class="tab-page active">
        <home-view :platform="platform" />
      </view>

      <!-- 应用中心 -->
      <view v-else-if="tabStore.activeTabIndex === 1" class="tab-page active">
        <app-center-view :platform="platform" />
      </view>

      <!-- 数据空间 -->
      <view v-else-if="tabStore.activeTabIndex === 2" class="tab-page active">
        <data-space-view :platform="platform" />
      </view>

      <!-- 我的 -->
      <view v-else-if="tabStore.activeTabIndex === 3" class="tab-page active">
        <my-view :platform="platform" />
      </view>
    </view>

    <!-- 自定义TabBar -->
    <view class="custom-tabbar-wrap safe-area-inset-bottom" v-if="tabStore.showTabBar">
      <u-row justify="space-around" customStyle="height: 100rpx;margin-top: 10rpx;">
        <u-col span="3" v-for="(item, index) in tabStore.tabItems" :key="index">
          <view
            class="tabbar-item"
            :class="{ active: tabStore.activeTabIndex === index }"
            @click="handleTabClick(index)"
          >
            <image
              class="tabbar-icon"
              :src="tabStore.activeTabIndex === index ? item.selectedIconPath : item.iconPath"
            />
            <text
              class="tabbar-text"
              :class="{ visible: tabStore.activeTabIndex === index }"
              v-if="tabStore.activeTabIndex === index"
              >{{ item.text }}</text
            >
          </view>
        </u-col>
      </u-row>
    </view>

    <!-- 权限弹窗 -->
    <LKpermissionModal
      v-model:show="permissionModalShow"
      :title="permissionModalTitle"
      :content="permissionModalContent"
      cancel-text="取消"
      confirm-text="前往设置"
      @cancel="handlePermissionCancel"
      @confirm="handlePermissionConfirm"
    />
  </view>
</template>

<script>
import { onLoad, onReady, onShow } from '@dcloudio/uni-app';
import { defineComponent, onMounted, ref, watch } from 'vue';
import { useTabStore } from '@/store/tabStore';
import { checkPermission } from '@/utils/permission';
import LKpermissionModal from '@/components/LKpermissionModal/index.vue';
import { useAppCenterStore } from '@/store/useAppCenterStore';

// 使用动态导入实现懒加载，减少首次渲染负担
import HomeView from '@/pages/home/<USER>';
import AppCenterView from '@/pages/app-center/index.vue';
import DataSpaceView from '@/pages/data-space/index.vue';
import MyView from '@/pages/my/index.vue';

export default defineComponent({
  components: { HomeView, AppCenterView, DataSpaceView, MyView, LKpermissionModal },
  setup() {
    const tabStore = useTabStore();
    const appCenterStore = useAppCenterStore();
    const isTabsReady = ref(false);
    const keepAliveStatus = ref({
      0: false, // 首页
      1: false, // 应用中心
      2: false, // 数据空间
      3: false, // 我的
    });
    const platform = ref('');
    const shouldRetryImageSelection = ref(false);

    // 权限弹窗相关状态
    const permissionModalShow = ref(false);
    const permissionModalTitle = ref('');
    const permissionModalContent = ref('');
    const permissionOpenSettings = ref(null);

    const handleTabClick = index => {
      if (tabStore.activeTabIndex === index) return;
      console.log('切换到Tab:', index);

      // 标记当前Tab为已加载
      keepAliveStatus.value[index] = true;

      // 切换Tab
      tabStore.switchTab(index);
    };

    onLoad(options => {
      console.log('页面加载参数:', options);
      if (options?.tabIndex) {
        const index = parseInt(options.tabIndex);
        if (!isNaN(index) && index >= 0 && index < tabStore.tabItems.length) {
          // 标记首个加载的Tab为已加载
          keepAliveStatus.value[index] = true;
          tabStore.switchTab(index);
        }
      } else {
        // 默认标记首页为已加载
        keepAliveStatus.value[0] = true;
      }
    });

    onShow(async () => {
      // 检查是否需要重试图片选择
      if (shouldRetryImageSelection.value) {
        shouldRetryImageSelection.value = false;

        try {
          // 重新检查权限
          const permissionResult = await checkPermission('camera');

          if (permissionResult.granted) {
            // 权限已授予，自动重试图片选择
            chooseImageFunc();
          } else {
            // 权限仍未授予，提示用户
            uni.showToast({
              title: '权限未开启，无法选择图片',
              icon: 'none',
            });
          }
        } catch (error) {
          console.error('重试权限检查失败:', error);
          // 如果权限检查失败，使用原有的方式重试
          chooseImageFunc();
        }
      }
    });

    onMounted(() => {
      tabStore.initializeTabBar();
      isTabsReady.value = true;
      platform.value = uni.getSystemInfoSync().platform;
    });

    onReady(() => {
      console.log('页面已就绪');
    });

    // 监听Tab变化
    watch(
      () => tabStore.activeTabIndex,
      (newIndex, oldIndex) => {
        console.log('Tab索引变化:', oldIndex, '->', newIndex);
      }
    );

    // 选择图片
    const chooseImage = async () => {
      try {
        // #ifdef APP-PLUS
        // 特殊处理 iOS 相机权限
        if (plus.os.name === 'iOS') {
          console.log('iOS平台处理相机权限');
          try {
            // 导入AVCaptureDevice类
            const AVCaptureDevice = plus.ios.import('AVCaptureDevice');
            const AVMediaTypeVideo = 'vide'; // iOS中AVMediaTypeVideo的实际值

            // 检查当前权限状态
            const authStatus = AVCaptureDevice.authorizationStatusForMediaType(AVMediaTypeVideo);
            console.log('iOS相机权限状态:', authStatus);

            if (authStatus === 3) {
              // 已授权 (3)，直接选择图片
              console.log('相机权限已授权');
              plus.ios.deleteObject(AVCaptureDevice);
              chooseImageFunc();
              return;
            } else if (authStatus === 0) {
              // 未决定 (0)，请求权限
              console.log('相机权限未决定，请求权限');

              // 使用Promise包装原生回调
              const granted = await new Promise(resolve => {
                // 尝试多种可能的方法名
                try {
                  if (typeof AVCaptureDevice.requestAccessForMediaType === 'function') {
                    console.log('使用requestAccessForMediaType请求权限');
                    AVCaptureDevice.requestAccessForMediaType(AVMediaTypeVideo, granted => {
                      console.log('权限请求结果:', granted);
                      resolve(granted);
                    });
                  } else if (typeof AVCaptureDevice.requestAccess === 'function') {
                    console.log('使用requestAccess方法');
                    AVCaptureDevice.requestAccess(AVMediaTypeVideo, granted => {
                      console.log('权限请求结果:', granted);
                      resolve(granted);
                    });
                  } else {
                    console.log('没有找到请求权限的方法，尝试使用uni.authorize');
                    plus.ios.deleteObject(AVCaptureDevice);
                    uni.authorize({
                      scope: 'scope.camera',
                      success: () => resolve(true),
                      fail: () => resolve(false),
                    });
                  }
                } catch (e) {
                  console.error('请求权限错误:', e);
                  // 使用uni.chooseImage作为最后的尝试
                  uni.chooseImage({
                    count: 1,
                    sourceType: ['camera'],
                    success: () => resolve(true),
                    fail: () => resolve(false),
                  });
                  resolve(false);
                }
              });

              plus.ios.deleteObject(AVCaptureDevice);

              if (granted) {
                // 用户授予了权限
                chooseImageFunc();
                return;
              }
            }

            // 如果权限被拒绝(2)或受限(1)，或请求被拒绝，显示弹窗引导用户到设置中开启
            plus.ios.deleteObject(AVCaptureDevice);
            showCameraPermissionDialog();
          } catch (e) {
            console.error('iOS相机权限处理错误:', e);
            // 出错时回退到通用方式
            handlePermissionWithCommonWay();
          }
          return;
        } else {
          // Android平台
          try {
            // 直接请求Android相机权限
            const status = await new Promise(resolve => {
              plus.android.requestPermissions(
                ['android.permission.CAMERA'],
                result => {
                  console.log('Android相机权限结果:', JSON.stringify(result));
                  if (result.granted && result.granted.length > 0) {
                    resolve(true);
                  } else {
                    resolve(false);
                  }
                },
                () => resolve(false)
              );
            });

            if (status) {
              chooseImageFunc();
              return;
            } else {
              showCameraPermissionDialog();
              return;
            }
          } catch (e) {
            console.error('Android相机权限检查错误:', e);
            // 如果原生API失败，回退到通用方式
            handlePermissionWithCommonWay();
          }
        }
        // #endif

        // 非iOS平台或降级处理
        handlePermissionWithCommonWay();
      } catch (error) {
        console.error('权限处理失败:', error);
        // 出现异常时直接尝试选择图片
        chooseImageFunc();
      }
    };

    // 使用通用方式处理权限
    const handlePermissionWithCommonWay = async () => {
      try {
        // 使用项目的权限工具类进行权限检查
        const permissionResult = await checkPermission('camera');

        if (permissionResult.granted) {
          // 权限已授予，直接选择图片
          chooseImageFunc();
        } else {
          // 权限未授予，显示权限说明弹窗
          showCameraPermissionDialog(permissionResult);
        }
      } catch (error) {
        console.error('通用权限检查失败:', error);
        chooseImageFunc();
      }
    };

    // 显示相机权限对话框
    const showCameraPermissionDialog = permissionResult => {
      permissionModalTitle.value = permissionResult?.details?.deniedTitle || '相机权限未开启';
      permissionModalContent.value =
        permissionResult?.details?.deniedMessage ||
        '检测到手机设置中未对APP开启相机权限，请先在手机设置开启。';
      permissionOpenSettings.value = permissionResult?.openSettings || null;
      permissionModalShow.value = true;
    };

    // 权限弹窗事件处理
    function handlePermissionCancel() {
      permissionModalShow.value = false;
    }

    function handlePermissionConfirm() {
      permissionModalShow.value = false;
      // 设置重试标记，当用户从设置页面返回时会自动重试
      shouldRetryImageSelection.value = true;

      // 调起设置页面
      try {
        if (permissionOpenSettings.value && typeof permissionOpenSettings.value === 'function') {
          permissionOpenSettings.value();
        } else {
          // #ifdef MP
          uni.openSetting();
          // #endif

          // #ifdef APP-PLUS
          // 针对iOS和Android使用不同的设置URL
          if (plus.os.name === 'iOS') {
            try {
              // 对于iOS 10+，使用新的API
              const UIApplication = plus.ios.import('UIApplication');
              const application = UIApplication.sharedApplication();
              const NSURL = plus.ios.import('NSURL');
              const settingURL = NSURL.URLWithString('app-settings:');

              // 检查iOS版本
              const iosVersion = plus.os.version || '9.0';
              if (parseInt(iosVersion) >= 10) {
                // iOS 10+ 使用新方法
                console.log('使用iOS 10+方法打开设置');
                const options = plus.ios.newObject('NSDictionary');
                application.openURL_options_completionHandler(settingURL, options, null);
                plus.ios.deleteObject(options);
              } else {
                // iOS 9及以下使用旧方法
                application.openURL(settingURL);
              }

              plus.ios.deleteObject(settingURL);
              plus.ios.deleteObject(NSURL);
              plus.ios.deleteObject(application);
            } catch (iosError) {
              console.error('iOS原生方法打开设置失败:', iosError);
              // 备选方案
              plus.runtime.openURL('app-settings:');
            }
          } else {
            // Android
            plus.runtime.openURL(`package:${plus.runtime.appid}`);
          }
          // #endif

          // #ifdef H5
          uni.showToast({
            title: '请在浏览器设置中开启相机权限',
            icon: 'none',
          });
          // #endif
        }
      } catch (settingErr) {
        console.error('打开设置页面失败:', settingErr);
        uni.showToast({
          title: '无法打开设置页面，请手动前往设置开启权限',
          icon: 'none',
        });
      }
    }

    // 选择图片的具体实现函数
    const chooseImageFunc = () => {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          const tempFilePaths = res.tempFilePaths;
          // 处理选择的图片
          console.log('选择的图片:', tempFilePaths);
          // 这里可以根据需要进一步处理图片
        },
        fail: err => {
          console.log('chooseImage fail', err);
          if (err.errMsg && err.errMsg.includes('fail No Permission')) {
            uni.showToast({
              title: '请给予摄像头权限',
              icon: 'none',
            });

            // 设置重试标记，当用户从设置页面返回时会自动重试
            shouldRetryImageSelection.value = true;

            // 如果权限被拒绝，提示用户前往设置页面开启权限
            setTimeout(() => {
              uni.showModal({
                title: '提示',
                content: '您拒绝了相机权限，部分功能可能无法使用。是否前往设置开启权限？',
                confirmText: '去设置',
                cancelText: '取消',
                success: modalRes => {
                  if (modalRes.confirm) {
                    // #ifdef MP
                    uni.openSetting();
                    // #endif

                    // #ifdef APP-PLUS
                    if (plus.os.name === 'iOS') {
                      // iOS打开设置的特殊URL
                      plus.runtime.openURL('app-settings:');
                    } else {
                      // Android打开应用设置
                      plus.runtime.openURL('app-settings://');
                    }
                    // #endif

                    // #ifdef H5
                    uni.showToast({
                      title: '请在浏览器设置中开启相机权限',
                      icon: 'none',
                    });
                    // #endif
                  }
                },
              });
            }, 500);
          }
        },
      });
    };

    return {
      platform,
      tabStore,
      handleTabClick,
      isTabsReady,
      keepAliveStatus,
      chooseImage,
      chooseImageFunc,
      handlePermissionWithCommonWay,
      showCameraPermissionDialog,
      shouldRetryImageSelection,
      permissionModalShow,
      permissionModalTitle,
      permissionModalContent,
      permissionOpenSettings,
      handlePermissionCancel,
      handlePermissionConfirm,
    };
  },
});
</script>

<style lang="scss">
@import '@/styles/tabbar-fix.scss';

.platform-ios {
  // height: calc(100vh - 170rpx);
  height: 100vh;
  //padding-bottom: 170rpx;
  .custom-tabbar-wrap {
    height: 160rpx;
  }
}

.platform-android {
  height: 100vh;
  // padding-bottom: 140rpx;
  // height: calc(100vh - 140rpx);
  .custom-tabbar-wrap {
    height: 130rpx;
  }
}

/* 自定义TabBar样式 */
.custom-tabbar-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-left: 20rpx;
  padding-right: 20rpx;
  z-index: 1;
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    /* 减小背景高度 */
    background-color: #fff;
    border-radius: 40rpx 40rpx 0px 0px; /* 减小圆角 */
    box-shadow: 0px -2px 9px 0px rgba(0, 0, 0, 0.07);
    z-index: -1;
  }

  .tabbar-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6rpx; /* 减小间隔 */
    border-radius: 66rpx; /* 减小圆角 */
    min-width: 50rpx; /* 减小最小宽度 */
    max-width: 170rpx; /* 减小最小宽度 */
    min-height: 80rpx;
    transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
    position: relative;
    overflow: hidden;

    &.active {
      background: #f4f1fd;
      /* 默认活跃状态内边距适用于Android和H5 */
      padding: 12rpx 24rpx;
      /* iOS特定的活跃状态内边距调整 */
      @supports (-webkit-touch-callout: none) {
        padding: 8rpx 16rpx; /* iOS特定的较小内边距 */
      }

      .tabbar-icon {
        transform: scale(1.05); /* 减小变换效果 */
      }
    }
  }

  .tabbar-icon {
    /* 默认图标大小适用于Android和H5 */
    width: 40rpx;
    height: 40rpx;
    transition: transform 0.3s ease;
  }

  .tabbar-text {
    /* 默认字体大小适用于Android和H5 */
    font-size: 30rpx;
    color: #613eea;
    font-weight: 500;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(-10rpx);

    &.visible {
      opacity: 1;
      transform: translateX(0);
    }
  }
}
</style>
