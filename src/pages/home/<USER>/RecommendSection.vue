<template>
  <view class="home_recommend">
    <!-- 标题 -->
    <view class="home_title" style="padding-left: 32rpx; padding-right: 32rpx">
      <view style="position: relative; z-index: 1">精选推荐</view>
      <LkServiceImage
        name="textLine"
        style="position: absolute; bottom: -10rpx; height: 32rpx; width: 150rpx; z-index: 0"
      />
    </view>

    <!-- 推荐列表 -->
    <template v-if="recommendListFilter.length">
      <view
        class="home_recommend_body"
        :style="{
          transition: 'max-height 0.3s ease-in-out, opacity 0.3s ease',
          maxHeight: recommendMaxHeight,
          overflow: 'hidden',
        }"
      >
        <up-grid :border="false" :col="gapLen" gap="20rpx">
          <up-grid-item v-for="(item, index) in recommendListFilter" :key="item.id">
            <up-transition
              :show="recommendListFilter.length > 0"
              mode="fade-zoom"
              :class="`home_recommend_body-item gap-${gapLen} bg-gradient-${index % 6}`"
              :style="{
                ...(item.imageBackgroundColor && {
                  background: `linear-gradient(${item.imageBackgroundColor})`,
                }),
              }"
              @tap="handleRecommendTap(item)"
            >
              <LkText type="primary" bold size="large" class="up-line-1">
                {{ item.appName || '' }}
              </LkText>
              <LkText
                type="tertiary"
                :class="`${gapLen > 2 ? 'up-line-1' : 'up-line-2'}`"
                style="margin-top: 10rpx; font-size: 24rpx"
              >
                {{ item.appIntro || '' }}
              </LkText>
              <image :src="item.imageUrl" class="home_recommend_body-item-img" />
            </up-transition>
          </up-grid-item>
        </up-grid>
      </view>

      <!-- 展开/收起按钮 -->
      <up-transition
        :show="recommendListFilter.length > 0"
        mode="fade-zoom"
        @click="handleToggleRecommend"
      >
        <view
          class="home_recommend-more"
          v-if="recommendListFilter.length"
          :style="{ bottom: isRecommendCollapse ? '10rpx' : '20rpx' }"
        >
          <view class="home_recommend-more-body" v-if="showRecommendMore">
            <image
              src="https://huayun-ai-obs-public.huayuntiantu.com/7d8eacd8-2fe3-4684-99d5-79c4b5c9e005.png"
              class="home_recommend-more-body-bg"
            />
            <view class="home_recommend-more-body-icon">
              <LkSvg
                width="44rpx"
                height="44rpx"
                src="/static/common/up.svg"
                customClass="home_recommend-more-body-icon-wrap"
                :customStyle="{
                  transform: `translateX(-40%) rotate(${isRecommendCollapse ? -180 : 0}deg)`,
                  transition: 'transform 0.3s ease',
                }"
              />
            </view>
          </view>
        </view>
      </up-transition>
    </template>

    <!-- 空状态 -->
    <view class="home_empty" v-else>
      <LkServiceImage name="empty" style="width: 200rpx; height: 200rpx" />
      <LkText type="tertiary" size="small">暂无数据</LkText>
    </view>

    <!-- 引导区域 -->
    <view class="home_recommend-guide" :style="`bottom: ${gapLen === 2 ? '20' : '20'}rpx`" />
  </view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRecommendApps } from '../composables/useRecommendApps';
import LkServiceImage from '@/components/LkServiceImage/index.vue';
import LkText from '@/components/LkText/index.vue';
import LkSvg from '@/components/svg/index.vue';

// 使用推荐应用组合逻辑
const {
  // 状态
  isRecommendCollapse,
  loading,
  error,

  // 计算属性
  recommendListFilter,
  showRecommendMore,
  recommendMaxHeight,
  gapLen,

  // 方法
  fetchRecommendList,
  handleRecommendTap,
  handleToggleRecommend,
  refreshRecommendList,
} = useRecommendApps();

// 对外暴露的方法和状态
defineExpose({
  refresh: refreshRecommendList,
  fetchData: fetchRecommendList,
  loading,
});

// 组件挂载时获取数据
onMounted(() => {
  fetchRecommendList();
});
</script>

<style lang="scss" scoped>
.home_recommend {
  position: relative;
  z-index: 1;
  padding-bottom: 20rpx;
  margin-top: 5px;
  min-height: 400rpx;
  &-guide {
    position: absolute;
    background-color: transparent;
    top: -10rpx;
    left: 8rpx;
    bottom: 30rpx;
    right: 8rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    z-index: -10;
  }
  &_body {
    padding-left: 32rpx;
    padding-right: 32rpx;
    margin-top: 20rpx;
    margin-bottom: 15rpx;
    ::v-deep .u-grid-item--hover-class {
      opacity: 1;
    }
    &-item {
      border-radius: 54rpx 54rpx 40rpx 40rpx;
      padding: 30rpx;
      display: flex;
      flex-direction: column;
      min-height: 240rpx;
      width: 100%;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      z-index: 1;
      $color-list: linear-gradient(155deg, #ebf0ff 11.45%, #dfe5fd 100%)
        linear-gradient(180deg, #faf2e8 0%, #fbece7 100%)
        linear-gradient(155deg, #fdffeb 11.45%, #fdf3df 100%)
        linear-gradient(155deg, #ebfdff 11.45%, #dffdf9 100%)
        linear-gradient(155deg, #ebffed 11.45%, #dffde4 100%)
        linear-gradient(180deg, #e9f7ff 0%, #ebf3ff 100%);
      @each $gradient in $color-list {
        $index: index($color-list, $gradient);
        &.bg-gradient-#{$index - 1} {
          background: $gradient;
        }
      }
      &-img {
        width: 90rpx;
        height: 90rpx;
        position: absolute;
        right: 0rpx;
        bottom: 0;
      }
    }
    .gap-1 {
      align-items: flex-start;
    }
    .gap-2 {
      align-items: flex-start;
    }
    .gap-3 {
      align-items: center;
    }
  }
  &-more {
    height: 300rpx;
    width: 100%;
    box-sizing: border-box;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 30%,
      rgba(255, 255, 255, 0.7) 70%,
      white 100%
    );
    border-radius: 0 0 20rpx 20rpx;
    position: absolute;
    z-index: -1;
    bottom: 20rpx;
    left: 0;
    transition: opacity 0.5s ease-in-out;
    opacity: 1;
    &-body {
      position: absolute;
      left: 50%;
      bottom: -48rpx;
      transform: translateX(-50%);
      cursor: pointer;
      transition: transform 0.3s ease;

      &:active {
        transform: translateX(-50%) scale(0.95);
      }

      &-bg {
        width: 150rpx;
        height: 50rpx;
      }
      &-icon {
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-8%);

        &-wrap {
          border-radius: 50%;
          background-color: #f2edff;
          padding: 5rpx;
        }
      }
    }
  }
}

.home_title {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  font-size: 36rpx;
  font-weight: bold;
}

.home_empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 50rpx;
}
</style>
