import { ref, computed } from 'vue';
import { getRecommendList } from '@/api/userCommon';
import type { RecommendModuleItem } from '@/types/api/app';

/**
 * 推荐应用业务逻辑组合函数
 * 负责管理推荐列表的数据获取、状态管理和交互逻辑
 */
export const useRecommendApps = () => {
  // 响应式状态
  const recommendList = ref<RecommendModuleItem[]>([]);
  const isRecommendCollapse = ref(false);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性 - 过滤后的推荐列表
  const recommendListFilter = computed(() => {
    return recommendList.value.sort((a, b) => a.sort - b.sort);
  });

  // 计算属性 - 网格列数（与原始版本逻辑一致）
  const gapLen = computed(() => {
    const length = recommendListFilter.value.length;
    return length >= 2 ? 2 : 1;
  });

  // 计算属性 - 是否显示更多按钮
  const showRecommendMore = computed(() => {
    if (gapLen.value === 2 && recommendListFilter.value.length > 4) {
      return true;
    }
    return false;
  });

  // 计算属性 - 推荐区域最大高度（与原始版本逻辑一致）
  const recommendMaxHeight = computed(() => {
    const itemsPerRow = gapLen.value; // 从gapLen获取每行显示的卡片数
    const itemHeight = 245; // 每个卡片的基本高度(rpx)
    const baseMargin = 20; // 间距
    const totalItems = recommendListFilter.value.length;

    if (totalItems === 0) return '0px';

    // 如果是展开状态，显示所有卡片
    if (isRecommendCollapse.value) {
      const rows = Math.ceil(totalItems / itemsPerRow);
      return `${rows * itemHeight + (rows - 1) * baseMargin}rpx`;
    } else {
      // 收起状态：默认显示最多2行
      const maxRows = itemsPerRow > 1 ? 2 : 1;
      return `${maxRows * itemHeight + (maxRows - 1) * baseMargin}rpx`;
    }
  });

  /**
   * 获取推荐列表数据
   */
  const fetchRecommendList = async () => {
    if (loading.value) return;

    loading.value = true;
    error.value = null;

    try {
      const result = await getRecommendList();
      if (result && Array.isArray(result)) {
        recommendList.value = result;
      } else {
        recommendList.value = [];
      }
    } catch (err) {
      console.error('获取推荐列表失败:', err);
      error.value = '获取推荐数据失败，请稍后重试';
      recommendList.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 处理推荐应用点击事件（与原始版本逻辑一致）
   */
  const handleRecommendTap = (item: RecommendModuleItem) => {
    if (!item?.tenantApp) {
      console.warn('推荐应用数据不完整:', item);
      return;
    }

    try {
      // 存储应用信息到本地（与原始版本格式一致）
      uni.setStorageSync('selectApp', item.tenantApp);

      // 导航到聊天页面
      uni.navigateTo({
        url: '/pages/chat/index',
        fail: err => {
          console.error('导航失败:', err);
          uni.showToast({
            title: '打开应用失败',
            icon: 'none',
          });
        },
      });
    } catch (err) {
      console.error('处理推荐应用点击失败:', err);
      uni.showToast({
        title: '打开应用失败',
        icon: 'none',
      });
    }
  };

  /**
   * 切换推荐列表展开/收起状态
   */
  const handleToggleRecommend = () => {
    isRecommendCollapse.value = !isRecommendCollapse.value;
  };

  /**
   * 重新加载推荐数据
   */
  const refreshRecommendList = () => {
    fetchRecommendList();
  };

  return {
    // 状态
    recommendList,
    isRecommendCollapse,
    loading,
    error,

    // 计算属性
    recommendListFilter,
    showRecommendMore,
    recommendMaxHeight,
    gapLen,

    // 方法
    fetchRecommendList,
    handleRecommendTap,
    handleToggleRecommend,
    refreshRecommendList,
  };
};
