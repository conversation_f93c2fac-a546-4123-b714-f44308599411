<template>
  <view class="home_common-use">
    <!-- 标题和工具栏 -->
    <view class="home_common-use_header">
      <view class="home_title">
        <view style="position: relative; z-index: 1">我的常用</view>
        <LkServiceImage
          name="textLine"
          style="position: absolute; bottom: -10rpx; height: 32rpx; width: 150rpx; z-index: 0"
        />
      </view>
      <view class="home_tools" v-if="commonUseListFilter.length">
        <LkSvg
          width="44rpx"
          height="44rpx"
          color="#000"
          src="/static/common/classify_add.svg"
          @tap="onAddApp"
        />
        <LkSvg
          width="44rpx"
          height="44rpx"
          color="#000"
          src="/static/common/edit.svg"
          @tap="handleOpenEditModal"
          style="margin-left: 15rpx"
        />
      </view>
    </view>

    <!-- 常用应用列表 -->
    <template v-if="!guideMode && commonUseListFilter.length">
      <view class="home_common-use_body">
        <view
          v-for="(item, index) in commonUseListFilter"
          :key="item.id"
          class="home_common-use_body-main"
          :class="{
            'item-hidden': !showCardAnimation(index),
            'item-visible': showCardAnimation(index),
            'item-initial': isInitialRender,
          }"
          :style="{
            pointerEvents: !showCardAnimation(index) ? 'none' : 'auto',
            /* 渐进式动画延迟，多端兼容 */
            animationDelay: isInitialRender ? `${index * 100}ms` : '0ms',
            WebkitAnimationDelay: isInitialRender ? `${index * 100}ms` : '0ms',
          }"
          @tap="handleClick(item)"
        >
          <view class="home_common-use_body-main-item">
            <view class="home_common-use_body-main-item-img">
              <up-image
                :src="item?.tenantApp?.avatarUrl"
                shape="circle"
                width="100rpx"
                height="100rpx"
              />
            </view>
            <view class="home_common-use_body-main-item-info">
              <LkText type="primary" class="up-line-1" bold style="font-size: 30rpx">
                {{ item.tenantApp.name || '--' }}
              </LkText>
              <LkText type="tertiary" class="up-line-1" style="margin-top: 10rpx; font-size: 24rpx">
                {{ item.tenantApp.intro || '--' }}
              </LkText>
            </view>
            <view class="home_common-use_body-main-item-bt">
              <view class="home_common-use_body-main-item-bt-icon">
                <LkSvg width="44rpx" height="44rpx" src="/static/common/message.svg" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 展开/收起控制 -->
      <view
        class="home_common-use_collapse"
        @tap="handleCollapseCommonUse"
        :style="{
          paddingTop: !isCommonUseCollapse ? '20rpx' : '0rpx',
        }"
      >
        <view class="home_common-use_collapse-content" v-if="commonUseList.length > 4">
          <LkText type="tertiary" size="small">
            {{ !isCommonUseCollapse ? '展开全部' : '收起全部' }}
          </LkText>
          <LkSvg
            width="28rpx"
            height="28rpx"
            color="#000"
            src="/static/chat/collapse.svg"
            :class="{ 'icon-rotated': isCommonUseCollapse }"
          />
        </view>
      </view>
    </template>

    <!-- 空状态 -->
    <view class="home_common-use_empty" v-else @tap="onAddApp">
      <view class="icon">
        <up-icon name="plus" color="#7D4DFF" size="28rpx" />
      </view>
      <span>添加高频使用应用</span>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useCommonApps } from '../composables/useCommonApps';
import LkServiceImage from '@/components/LkServiceImage/index.vue';
import LkText from '@/components/LkText/index.vue';
import LkSvg from '@/components/svg/index.vue';
import type { CommonAppType } from '@/types/api/app';

interface Emits {
  (e: 'refreshRecentData'): void;
}

const emit = defineEmits<Emits>();

// 新增：引导模式标识，用于在引导时强制显示空态。
const { guideMode = false } = defineProps<{ guideMode?: boolean }>();

// 使用常用应用组合逻辑
const {
  // 状态
  commonUseList,
  isCommonUseCollapse,
  loading,
  error,
  showEditModal,
  isInitialRender,

  // 计算属性
  commonUseListFilter,
  commonUseSectionMaxHeight,
  showCardAnimation,

  // 方法
  fetchCommonAppList,
  handleClick,
  addCommonApp,
  removeCommonApp,
  sortCommonApps,
  handleCollapseCommonUse,
  handleOpenEditModal,
  handleEditConfirm,
  handleEditCancel,
  onAddApp,
  refreshCommonAppList,
  triggerInitialAnimation,
} = useCommonApps();

// 处理编辑确认
const handleEditConfirmLocal = () => {
  // 刷新当前数据
  fetchCommonAppList();
  // 刷新最近使用数据
  emit('refreshRecentData');
};

// 对外暴露的方法
defineExpose({
  refresh: refreshCommonAppList,
  fetchData: fetchCommonAppList,
  addApp: addCommonApp,
  removeApp: removeCommonApp,
  // 暴露弹窗相关状态和方法供父组件使用
  showEditModal,
  commonUseList,
  handleEditConfirm: handleEditConfirmLocal,
  handleEditCancel,
});

// 组件挂载时获取数据
onMounted(() => {
  fetchCommonAppList();
});
</script>

<style lang="scss" scoped>
.home_common-use {
  margin-top: 20rpx;
  &_header {
    padding-left: 32rpx;
    padding-right: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &_empty {
    background-color: white;
    margin: 20rpx;
    border-radius: 28rpx;
    min-height: 200rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20rpx;
    font-size: 30rpx;
  }
  &_body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20rpx 32rpx 0 32rpx;
    &-main {
      background-color: #fff;
      border-radius: 32rpx;
      padding: 32rpx 24rpx;
      /* 多端兼容的性能优化 */
      will-change: transform, opacity;
      /* 启用硬件加速，提升动画性能 */
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      /* 避免动画卡顿 */
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;

      // 多端适配的现代化动画控制
      &.item-hidden {
        /* 收起动画：向上收缩并缩小，更自然的消失效果 */
        transform: translateY(-25rpx) scale(0.9);
        opacity: 0;
        height: 0rpx;
        padding: 0rpx 24rpx;
        margin-bottom: -10rpx; /* 负边距让收起更紧凑 */
        overflow: hidden;
        /* 使用统一时序和流畅缓动函数 */
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        /* 多端兼容的硬件加速 */
        -webkit-transform: translateY(-25rpx) scale(0.9);
        -webkit-transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
      }
      &.item-visible {
        transform: translateY(0rpx) scale(1);
        opacity: 1;
        height: auto;
        padding: 32rpx 24rpx;
        margin-bottom: 20rpx;
        /* 展开动画使用温和的弹性缓动，与收起动画协调 */
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.1);
        /* 多端兼容 */
        -webkit-transform: translateY(0rpx) scale(1);
        -webkit-transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.1);
      }

      /* 动画完成后移除性能优化属性，节省内存 */
      &.animation-completed {
        will-change: auto;
      }

      &-item {
        display: flex;
        align-items: center;
        gap: 20rpx;
        &-img {
          width: 100rpx;
          height: 100rpx;
        }
        &-info {
          flex: 1;
        }
        &-bt {
          width: 100rpx;
          height: 100rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        &-bt-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 15rpx;
          background: #f3ecff;
          border-radius: 999999rpx;
        }
      }
    }
  }
  &_collapse {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 10rpx;
    padding-bottom: 20rpx;
    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10rpx;
    }
  }
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f2edff;
    width: 60rpx;
    height: 60rpx;
  }
}

.home_title {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  font-size: 36rpx;
  font-weight: bold;
}

.home_tools {
  display: flex;
  align-items: center;
  height: 54rpx;
}

/* 初始渐进动画效果 - 多端兼容 */
.item-initial {
  animation: slideInUp 0.4s cubic-bezier(0.34, 1.26, 0.64, 1) forwards;
  -webkit-animation: slideInUp 0.4s cubic-bezier(0.34, 1.26, 0.64, 1) forwards;
}

/* 定义渐进动画关键帧 - 兼容webkit内核 */
@keyframes slideInUp {
  0% {
    transform: translateY(40rpx) scale(0.9);
    opacity: 0;
  }
  100% {
    transform: translateY(0rpx) scale(1);
    opacity: 1;
  }
}

@-webkit-keyframes slideInUp {
  0% {
    -webkit-transform: translateY(40rpx) scale(0.9);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0rpx) scale(1);
    opacity: 1;
  }
}

.icon-rotated {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
  -webkit-transform: rotate(180deg);
  -webkit-transition: transform 0.3s ease-in-out;
}
</style>
