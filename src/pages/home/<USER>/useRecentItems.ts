import { ref, computed, watch, nextTick } from 'vue';
import { SearchType, SearchTypeLabel } from '@/constants/search';
import { recentlyFileList, recentlyAppCenterList, recentlyChatList } from '@/api/userCommon';
import type { PagingData } from '@/types/api/common';

interface PageResponse<T = any> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
  realTotal: number;
}

/**
 * 最近使用项目业务逻辑组合函数
 * 负责管理最近使用的文件、应用、聊天的数据获取和状态管理
 */
export const useRecentItems = () => {
  // 响应式状态
  const currentType = ref<SearchType>(SearchType.FILE);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const wholePaginationRef = ref<any>(null);
  const fileItemListRef = ref<any>(null);
  const appItemListRef = ref<any>(null);
  const chatItemListRef = ref<any>(null);

  // Tab选项配置
  const tabOptions = ref([
    { value: SearchType.FILE, label: SearchTypeLabel[SearchType.FILE] },
    { value: SearchType.APP, label: SearchTypeLabel[SearchType.APP] },
    { value: SearchType.CHAT, label: SearchTypeLabel[SearchType.CHAT] },
  ]);

  // 加载状态跟踪
  const loadedTypes = ref<Record<string, boolean>>({
    [SearchType.FILE]: false,
    [SearchType.APP]: false,
    [SearchType.CHAT]: false,
  });

  /**
   * 处理分页数据的通用函数
   */
  const handelPagination = <T>(result: any, params: any): PageResponse<T> => {
    // 检查返回的数据结构
    if (
      result &&
      typeof result === 'object' &&
      'records' in result &&
      Array.isArray(result.records)
    ) {
      return result as PageResponse<T>;
    }

    // 如果是数组格式，转换为分页格式
    if (Array.isArray(result)) {
      return {
        records: result,
        total: result.length,
        size: params?.size || 10,
        current: params?.current || 1,
        pages: Math.ceil(result.length / (params?.size || 10)),
        realTotal: result.length,
      };
    }

    // 默认空数据结构
    return {
      records: [],
      total: 0,
      size: params?.size || 10,
      current: params?.current || 1,
      pages: 1,
      realTotal: 0,
    };
  };

  /**
   * 获取当前类型对应的数据获取方法
   */
  const currentFetchMethod = computed(() => {
    if (currentType.value === SearchType.FILE) {
      return async (params: any): Promise<PageResponse<any>> => {
        try {
          console.log('调用文件接口', params);
          const result = await recentlyFileList();
          console.log('文件接口返回:', result);

          const res = handelPagination<any>(result, params);
          return {
            records: res.records || [],
            total: res.total || 0,
            size: res.size || params?.size || 10,
            current: res.current || params?.current || 1,
            pages: res.pages || 1,
            realTotal: res.total || 0,
          } as PageResponse<any>;
        } catch (error) {
          console.error('文件接口调用失败:', error);
          return {
            records: [],
            total: 0,
            size: params?.size || 10,
            current: params?.current || 1,
            pages: 1,
            realTotal: 0,
          };
        }
      };
    }

    if (currentType.value === SearchType.APP) {
      return async (params: any): Promise<PageResponse<any>> => {
        try {
          console.log('调用应用接口', params);
          const result = await recentlyAppCenterList({ ...params });
          console.log('应用接口返回:', result);

          const res = handelPagination<any>(result, params);
          return {
            records: res.records || [],
            total: res.total || 0,
            size: res.size || params?.size || 10,
            current: res.current || params?.current || 1,
            pages: res.pages || 1,
            realTotal: res.total || 0,
          } as PageResponse<any>;
        } catch (error) {
          console.error('应用接口调用失败:', error);
          return {
            records: [],
            total: 0,
            size: params?.size || 10,
            current: params?.current || 1,
            pages: 1,
            realTotal: 0,
          };
        }
      };
    }

    if (currentType.value === SearchType.CHAT) {
      return async (params: any): Promise<PageResponse<any>> => {
        try {
          console.log('调用聊天接口', params);
          const result = await recentlyChatList({ ...params });
          console.log('聊天接口返回:', result);

          const res = handelPagination<any>(result, params);
          return {
            records: res.records || [],
            total: res.total || 0,
            size: res.size || params?.size || 10,
            current: res.current || params?.current || 1,
            pages: res.pages || 1,
            realTotal: res.total || 0,
          } as PageResponse<any>;
        } catch (error) {
          console.error('聊天接口调用失败:', error);
          return {
            records: [],
            total: 0,
            size: params?.size || 10,
            current: params?.current || 1,
            pages: 1,
            realTotal: 0,
          };
        }
      };
    }

    // 默认返回空数据方法
    return async (params: any): Promise<PageResponse<any>> => {
      return {
        records: [],
        total: 0,
        size: params?.size || 10,
        current: params?.current || 1,
        pages: 1,
        realTotal: 0,
      };
    };
  });

  /**
   * 获取当前类型对应的数据处理方法
   */
  const currentProcessData = computed(() => {
    if (currentType.value === SearchType.FILE) {
      return fileItemListRef.value?.processData || ((data: any[]) => data);
    }
    if (currentType.value === SearchType.APP) {
      return appItemListRef.value?.processData || ((data: any[]) => data);
    }
    if (currentType.value === SearchType.CHAT) {
      return chatItemListRef.value?.processData || ((data: any[]) => data);
    }
    return (data: any[]) => data;
  });

  /**
   * 防抖函数
   */
  const debounce = (fn: Function, delay: number) => {
    let timer: number | null = null;
    return (...args: any[]) => {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        fn(...args);
        timer = null;
      }, delay);
    };
  };

  /**
   * 创建防抖版本的刷新函数
   */
  const debouncedRefresh = debounce(() => {
    wholePaginationRef.value?.refresh();
  }, 300);

  /**
   * 处理Tab切换
   */
  const handleTabChange = (value: string | number) => {
    currentType.value = value as SearchType;
    // 注意：这里不需要调用refresh，因为currentType的监听器已经会触发刷新
  };

  /**
   * 处理操作完成回调（如删除、添加等操作后的刷新）
   */
  const handleOk = (type?: string) => {
    if (type !== 'add' && type !== 'remove') {
      nextTick(() => {
        debouncedRefresh();
      });
    }
  };

  /**
   * 刷新当前数据
   */
  const refreshCurrentData = () => {
    nextTick(() => {
      debouncedRefresh();
    });
  };

  // 监听类型变化，自动刷新数据
  watch(currentType, () => {
    nextTick(() => {
      wholePaginationRef.value?.refresh();
    });
  });

  return {
    // 状态
    currentType,
    loading,
    error,
    wholePaginationRef,
    fileItemListRef,
    appItemListRef,
    chatItemListRef,
    loadedTypes,

    // 配置
    tabOptions,

    // 计算属性
    currentFetchMethod,
    currentProcessData,

    // 方法
    handleTabChange,
    handleOk,
    refreshCurrentData,
    debouncedRefresh,
  };
};
