import { ref, computed, nextTick } from 'vue';
import { commonAppList, setCommonApp, rmCommonApp, sortCommonApp } from '@/api/userCommon';
import type { CommonAppType } from '@/types/api/app';
import { useTabStore } from '@/store/tabStore';

/**
 * 常用应用业务逻辑组合函数
 * 负责管理常用应用的数据获取、状态管理和操作逻辑
 * 支持垂直列表布局，实现从上往下的展开收起动画
 */
export const useCommonApps = () => {
  // 响应式状态
  const commonUseList = ref<CommonAppType[]>([]);
  const isCommonUseCollapse = ref(false);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const showEditModal = ref(false);
  const isInitialRender = ref(true); // 控制初始渲染动画状态
  const tabStore = useTabStore();

  // 计算属性 - 过滤后的常用应用列表
  const commonUseListFilter = computed(() => {
    return commonUseList.value;
  });

  // 计算属性 - 统一动画控制逻辑（从上往下展开）
  const showCardAnimation = computed(() => {
    return (index: number) => {
      // 初始渲染时所有卡片都隐藏，等待渐进式动画
      if (isInitialRender.value) {
        return false;
      }
      // 展开收起逻辑：收起状态时只显示前4个卡片，展开状态显示所有卡片
      if (!isCommonUseCollapse.value) {
        // 收起状态：只显示前4个
        return index < 4;
      } else {
        // 展开状态：显示所有卡片
        return true;
      }
    };
  });

  // 计算属性 - 常用应用区域最大高度（适配垂直列表布局）
  const commonUseSectionMaxHeight = computed(() => {
    const itemsPerRow = 1; // 垂直布局，每行显示1个卡片
    const itemHeight = 184; // 每个卡片的实际高度(rpx)：padding(64) + 内容(100) + gap(20)
    const totalItems = commonUseListFilter.value.length;

    if (totalItems === 0) return '0px';

    // 收起状态：最多显示4个卡片
    // 展开状态：显示所有卡片
    const visibleItems = isCommonUseCollapse.value ? totalItems : Math.min(totalItems, 4);
    const rows = Math.ceil(visibleItems / itemsPerRow);

    return `${rows * itemHeight}rpx`;
  });

  /**
   * 触发初始渲染的渐进式动画 - 多端优化版本
   */
  const triggerInitialAnimation = async () => {
    if (commonUseListFilter.value.length === 0) return;

    await nextTick(); // 确保DOM渲染完成

    // 计算动画总时长，考虑多端性能
    const itemCount = commonUseListFilter.value.length;
    const staggerDelay = 100; // 每个卡片间隔时间
    const animationDuration = 400; // 单个卡片动画时长
    const totalAnimationTime = (itemCount - 1) * staggerDelay + animationDuration;

    // 设置初始渲染状态为false，开始动画
    setTimeout(() => {
      isInitialRender.value = false;
    }, 50);

    // 动画完成后的清理工作，优化性能
    setTimeout(() => {
      // 可以在这里添加动画完成后的优化逻辑
      console.log('常用应用初始动画完成');
    }, totalAnimationTime + 100);
  };

  /**
   * 获取常用应用列表
   */
  const fetchCommonAppList = async () => {
    if (loading.value) return;

    loading.value = true;
    error.value = null;

    try {
      const result = await commonAppList();
      if (result && Array.isArray(result)) {
        commonUseList.value = result;
        // 数据加载完成后触发初始动画
        if (isInitialRender.value) {
          triggerInitialAnimation();
        }
      } else {
        commonUseList.value = [];
      }
    } catch (err) {
      console.error('获取常用应用列表失败:', err);
      error.value = '获取常用应用失败，请稍后重试';
      commonUseList.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 处理常用应用点击事件
   */
  const handleClick = (item: CommonAppType) => {
    if (!item?.tenantApp?.id) {
      console.warn('常用应用数据不完整:', item);
      return;
    }

    try {
      // 存储应用信息到本地
      uni.setStorageSync('selectApp', {
        id: item.tenantApp.id,
        name: item.tenantApp.name,
        avatarUrl: item.tenantApp.avatarUrl,
        intro: item.tenantApp.intro,
      });

      // 导航到聊天页面
      uni.navigateTo({
        url: '/pages/chat/index',
        fail: err => {
          console.error('导航失败:', err);
          uni.showToast({
            title: '打开应用失败',
            icon: 'none',
          });
        },
      });
    } catch (err) {
      console.error('处理常用应用点击失败:', err);
      uni.showToast({
        title: '打开应用失败',
        icon: 'none',
      });
    }
  };

  /**
   * 添加应用到常用
   */
  const addCommonApp = async (appId: string) => {
    try {
      await setCommonApp({ id: appId });
      // 重新获取列表
      await fetchCommonAppList();
      return { success: true, message: '已添加到我的应用' };
    } catch (err) {
      console.error('添加常用应用失败:', err);
      return { success: false, message: '添加失败，请重试' };
    }
  };

  /**
   * 从常用中移除应用
   */
  const removeCommonApp = async (appId: string) => {
    try {
      await rmCommonApp({ id: appId });
      // 重新获取列表
      await fetchCommonAppList();
      return { success: true, message: '已移除' };
    } catch (err) {
      console.error('移除常用应用失败:', err);
      return { success: false, message: '移除失败，请重试' };
    }
  };

  /**
   * 排序常用应用
   */
  const sortCommonApps = async (sortData: { id: string; sort: number }[]) => {
    try {
      await sortCommonApp({ param: sortData });
      // 重新获取列表
      await fetchCommonAppList();
      return { success: true, message: '排序成功' };
    } catch (err) {
      console.error('排序常用应用失败:', err);
      return { success: false, message: '排序失败，请重试' };
    }
  };

  /**
   * 切换常用应用展开/收起状态 - 简化版本确保流畅度
   */
  const handleCollapseCommonUse = () => {
    // 直接切换状态，让CSS动画处理过渡效果
    isCommonUseCollapse.value = !isCommonUseCollapse.value;
  };

  /**
   * 打开编辑弹窗
   */
  const handleOpenEditModal = () => {
    showEditModal.value = true;
  };

  /**
   * 处理编辑确认
   */
  const handleEditConfirm = async (sortedList: CommonAppType[]) => {
    try {
      // 构建排序数据
      const sortData = sortedList.map((item, index) => ({
        id: item.id,
        sort: index + 1,
      }));

      const result = await sortCommonApps(sortData);
      showEditModal.value = false;

      return result;
    } catch (err) {
      console.error('编辑确认失败:', err);
      return { success: false, message: '保存失败，请重试' };
    }
  };

  /**
   * 处理编辑取消
   */
  const handleEditCancel = () => {
    showEditModal.value = false;
  };

  /**
   * 跳转到应用中心添加应用
   */
  const onAddApp = () => {
    tabStore.switchTab(1);
  };

  /**
   * 刷新常用应用列表
   */
  const refreshCommonAppList = () => {
    fetchCommonAppList();
  };

  return {
    // 状态
    commonUseList,
    isCommonUseCollapse,
    loading,
    error,
    showEditModal,
    isInitialRender,

    // 计算属性
    commonUseListFilter,
    commonUseSectionMaxHeight,
    showCardAnimation,

    // 方法
    fetchCommonAppList,
    handleClick,
    addCommonApp,
    removeCommonApp,
    sortCommonApps,
    handleCollapseCommonUse,
    handleOpenEditModal,
    handleEditConfirm,
    handleEditCancel,
    onAddApp,
    refreshCommonAppList,
    triggerInitialAnimation,
  };
};
