<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import {
  FileFilterTypes,
  FileFilterTime,
  FileFilterNameOrContentEnum,
  FileFilterNameOrContent,
} from '@/constants/search';
import { getFileGlobalListPage } from '@/api/cloud';
import { recentlyFileList, rmRecentlyFile } from '@/api/userCommon';
import { SearchTypeParams, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import type { CloudFileContentSearch } from '@/types/api/cloud';
import type { FileModuleItem } from '@/types/api/app';
import { FileFilterTypesEnum, FileFilterTimeEnum } from '@/constants/search';
import { isImageByExt } from '@/utils/common';
import FileViewer from '@/components/LkFileViewer/index.vue';

// 处理后的文件数据类型
interface ProcessedFileData extends FileModuleItem {
  size: string;
  type: string;
  date: string;
  creator: string;
  fileSuffix: string;
}

const props = defineProps<{
  keyword?: string;
  list?: ProcessedFileData[];
  uToastRef?: any;
  loading?: boolean;
}>();

const fileType = ref<FileFilterTypesEnum>(FileFilterTypesEnum.ALL);
const fileTime = ref<FileFilterTimeEnum>(FileFilterTimeEnum.ALL);
const fileNameOrContent = ref<FileFilterNameOrContentEnum>(FileFilterNameOrContentEnum.NAME);
const actionOptions = reactive([
  {
    icon: 'trash',
    iconSize: '40rpx',
    style: {
      backgroundColor: '#D54941',
    },
  },
]);
const fileProps = ref<{
  fileUrl: string;
  fileKey: string;
  fileType?: number;
}>({
  fileUrl: '',
  fileKey: '',
  fileType: undefined,
});

const emit = defineEmits(['ok']);

const onPreviewFile = (item: any) => {
  console.log('item', item.fileUrl);
  fileProps.value = {
    fileUrl: item?.fileUrl || '',
    fileKey: item?.fileKey || '',
    fileType: 1,
  };
};

// 获取文件列表的处理方法
const fetchFileList = async (params: any) => {
  try {
    return await recentlyFileList();
  } catch (error) {
    console.error('获取文件列表失败:', error);
    return { records: [], total: 0, pages: 0, current: 1, size: params.size, realTotal: 0 };
  }
};

// 处理文件数据 - 添加缓存机制提高效率
const processedCache = new Map<string, ProcessedFileData>();

const processFileData = (data: FileModuleItem[]) => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    // 使用ID作为缓存键，避免重复处理相同数据
    const cacheKey = item.id;
    if (processedCache.has(cacheKey)) {
      return processedCache.get(cacheKey) as ProcessedFileData;
    }

    // 计算文件大小
    const fileSizeText = item.fileSize ? formatFileSize(item.fileSize) : '未知大小';

    // 确定文件类型
    const fileExtension = getFileExtension(item.fileName);
    // if (item.bizType === BizTypeEnum.TenantLibrary) {
    //   if ((item.locationPath?.length || 0) < 2) {
    //     spaceName = [...(item.locationPath || [])].map(path => ' ' + path.name).join(' > ');
    //   } else {
    //     const firstPath = item.locationPath?.[0];
    //     const lastPath = item.locationPath?.[item.locationPath.length - 1];
    //     spaceName = firstPath?.name + ' >...> ' + lastPath?.name;
    //   }
    // } else {
    //   if (item.locationPath?.length === 0) {
    //     spaceName = '我的数据空间';
    //   } else if ((item.locationPath?.length || 0) <= 1) {
    //     spaceName =
    //       '我的数据空间 >' +
    //       [...(item.locationPath || [])].map(path => ' ' + path.name).join(' > ');
    //   } else {
    //     const lastPath = item.locationPath?.[item.locationPath.length - 1];
    //     spaceName = '我的数据空间' + ' >...> ' + lastPath?.name;
    //   }
    // }

    const fileSuffix = item.fileName.split('.').pop()?.toLowerCase() || '';

    const processed = {
      ...item,
      size: fileSizeText,
      type: fileExtension,
      date: item.updateTime?.slice(0, 10) || '',
      spaceName: item.spaceName || '未知空间',
      creator: item.username || '未知创建者',
      fileSuffix,
    };

    // 保存至缓存
    processedCache.set(cacheKey, processed);

    return processed;
  });
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
};

// 获取文件扩展名
const getFileExtension = (fileName: string) => {
  if (!fileName) return 'file';

  try {
    const parts = fileName.split('.');
    if (parts.length > 1) {
      const ext = parts[parts.length - 1].toLowerCase();
      return ['doc', 'docx', 'pdf', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext) ? ext : 'file';
    }
  } catch (error) {
    console.error('获取文件扩展名失败:', error);
  }
  return 'file';
};

const handleRemoveFile = (item: FileModuleItem) => {
  rmRecentlyFile({ id: item.id })
    .then(() => {
      props.uToastRef.show({
        type: 'info',
        message: '文件从记录中移除成功',
      });
      emit('ok');
    })
    .catch(() => {
      props.uToastRef.show({
        type: 'error',
        message: '删除失败',
      });
    });
};

const handleToolsClick = (item: FileModuleItem) => {
  handleRemoveFile(item);
};

// 添加getImageSrc辅助函数
const getImageSrc = (item: FileModuleItem) => {
  try {
    // 安全检查
    if (!item) return `/static/fileTypeIcon/file.svg`;

    const suffix = item?.fileType?.split('.')[1];
    // 判断是否为图片
    const isImage = isImageByExt(suffix);

    // 如果是图片且有预览URL
    if (isImage && item.fileUrl) {
      return item.fileUrl;
    }

    // 返回文件类型图标
    return `/static/fileTypeIcon/${suffix || 'file'}.svg`;
  } catch (error) {
    console.error('获取图片源失败:', error);
    return `/static/fileTypeIcon/file.svg`;
  }
};

const fileIcon = (item: any): string => {
  const fileType = item?.fileName?.split('.')?.pop()?.toLowerCase();
  if (fileType) {
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

function formatToMinutes(timeStr: string) {
  const date = new Date(timeStr);

  // 提取各时间组件并补零
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

defineExpose({
  fetchMethod: fetchFileList,
  processData: processFileData,
});
</script>

<template>
  <view class="file-list">
    <view class="list-container">
      <!-- 骨架屏 -->
      <template v-if="loading">
        <view v-for="index in 4" :key="`skeleton-${index}`" class="skeleton-item">
          <view class="skeleton-main-wrapper">
            <view class="skeleton-main">
              <view class="skeleton-icon"></view>
              <view class="skeleton-info">
                <view class="skeleton-name"></view>
                <view class="skeleton-meta"></view>
              </view>
            </view>
          </view>
          <view class="skeleton-footer">
            <view class="skeleton-space"></view>
            <view class="skeleton-creator"></view>
          </view>
        </view>
      </template>

      <!-- 真实内容 -->
      <up-swipe-action v-else>
        <up-swipe-action-item
          :options="actionOptions"
          v-for="item in list"
          :key="item.id"
          class="list-item"
          :auto-close="true"
          @tap="handleToolsClick(item)"
        >
          <view style="padding: 24rpx" @touchmove.stop @tap="onPreviewFile(item)">
            <view class="item-main-wrapper">
              <view class="item-main">
                <LkSvg
                  class="fileIcon"
                  width="84rpx"
                  height="84rpx"
                  :src="fileIcon(item)"
                  :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                />
                <view class="item-info">
                  <view class="item-name">
                    {{ item.fileName }}
                  </view>
                  <view class="item-meta">
                    <template v-if="item.fileSize && item.updateTime">
                      <text>{{ formatFileSize(item.fileSize) }}</text>
                      <text class="separator">|</text>
                      <text>{{ formatToMinutes(item.updateTime) }}</text>
                    </template>
                    <template v-else>--</template>
                  </view>
                </view>
              </view>
            </view>
            <view class="item-footer">
              <view class="spaceName text-ellipsis">
                <image src="/static/search/space.svg" />
                <text>{{ item.spaceName }}</text>
              </view>
              <text class="creator">{{ item.creator }}</text>
            </view>
          </view>
        </up-swipe-action-item>
      </up-swipe-action>
    </view>
    <!-- 文件预览 -->
    <FileViewer
      :file="fileProps"
      :shouldCallDetail="false"
      @resetFile="fileProps = { fileUrl: '', fileKey: '', fileType: undefined }"
    />
  </view>
</template>

<style lang="scss">
.home_empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 40rpx;
}

.file-list {
  height: 100%;
  // 骨架屏样式
  .skeleton-item {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
    border: 1rpx solid #f4f4f4;
    padding: 24rpx;

    .skeleton-main-wrapper {
      border-bottom: 1px solid #f4f4f4;
      padding-bottom: 24rpx;

      .skeleton-main {
        display: flex;
        gap: 10rpx;

        .skeleton-icon {
          width: 84rpx;
          height: 84rpx;
          border-radius: 4rpx;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          flex-shrink: 0;
          margin-right: 12rpx;
        }

        .skeleton-info {
          flex: 1;
          overflow: hidden;

          .skeleton-name {
            height: 32rpx;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 4rpx;
            margin-bottom: 8rpx;
            width: 75%;
          }

          .skeleton-meta {
            height: 24rpx;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 4rpx;
            width: 60%;
          }
        }
      }
    }

    .skeleton-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24rpx;

      .skeleton-space {
        height: 24rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
        width: 45%;
      }

      .skeleton-creator {
        height: 24rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
        width: 25%;
      }
    }
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  // 两行展示
  .two-line-ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 高亮样式 */
  .highlight {
    color: #1a5eff;
  }

  .list-container {
    flex: 1;
    overflow: hidden;
  }

  .list {
    &-item {
      background: #fff;
      border-radius: 20rpx;
      margin-bottom: 24rpx;
      box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
      border: 1px solid #f1f1f1;
      .item-main-wrapper {
        border-bottom: 1px solid #f3f3f3;
        padding-bottom: 24rpx;

        .content {
          overflow: hidden;
          color: #4e5969;
          text-overflow: ellipsis;

          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 171.429% */
          letter-spacing: 0.07px;
          em {
            color: #1a5eff;
          }
        }
      }
      .item-main {
        display: flex;
        gap: 20rpx;

        .file-icon {
          width: 84rpx;
          height: 84rpx;
          flex-shrink: 0;
          margin-right: 12rpx;
        }

        .item-info {
          flex: 1;
          overflow: hidden;
        }

        .item-name {
          font-size: 32rpx;
          color: #1d2129;
          margin-bottom: 8rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 100%;
          :deep(div) {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            width: 100%;
          }

          .item-type {
            color: #195eff;
          }
        }

        .item-meta {
          font-size: 24rpx;
          color: #86909c;

          .separator {
            margin: 0 16rpx;
          }
        }
      }

      .item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24rpx;
        font-size: 24rpx;
        color: #86909c;

        .spaceName {
          display: flex;
          align-items: center;
          gap: 8rpx;

          image {
            width: 36rpx;
            height: 36rpx;
          }
        }
      }
    }
  }

  // 内容显示样式
  .content-wrapper {
    margin-top: 16rpx;

    .content {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 28rpx;
      line-height: 1.5;
      color: #4e5969;

      :deep(span) {
        color: #1a5eff;
      }
    }
  }
}
</style>
<style lang="scss">
.highlight {
  color: #1a5eff;
}
</style>
