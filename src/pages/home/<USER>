<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { recentlyChatList, rmRecentlyChat } from '@/api/userCommon';
import type { ChatHistoryItemType } from '@/types/api/chat';
import type { PagingData } from '@/types/api/common';

// 处理后的文件数据类型
interface ProcessedChatData extends ChatHistoryItemType {
  titleHtml: string;
  date: string;
}

const props = defineProps<{
  keyword?: string;
  list?: ProcessedChatData[];
  uToastRef?: any;
  loading?: boolean;
}>();

const actionOptions = reactive([
  {
    icon: 'trash',
    iconSize: '40rpx',
    style: {
      backgroundColor: '#D54941',
    },
  },
]);

const emit = defineEmits(['ok']);

// 使用 LkPageList 替代原有的加载逻辑
const fetchChatHistory = async (params: any) => {
  try {
    // 调用真实接口
    return await recentlyChatList({
      ...params,
      keyword: props.keyword,
    });
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    return { records: [], total: 0, pages: 0, current: 1, size: params.size, realTotal: 0 };
  }
};

// 缓存处理过的聊天数据
const processedChatCache = new Map<string, ProcessedChatData>();

// 处理数据和关键字高亮
const processChatData = (data: ChatHistoryItemType[]) => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    // 使用聊天ID作为缓存键
    const cacheKey = item.chatId;

    // 检查缓存中是否已有处理过的结果
    // 如果关键词未变化且有缓存，直接返回缓存结果
    if (processedChatCache.has(cacheKey)) {
      return processedChatCache.get(cacheKey) as ProcessedChatData;
    }

    // 添加高亮处理
    let title = item.title || '无标题对话';

    if (props.keyword && title) {
      // 替换所有匹配的关键字为带有高亮样式的文本
      title = highlightKeyword(title, props.keyword);
    }

    const date = item.updateTime ? new Date(item.updateTime).toLocaleString() : '未知时间';

    const processed = {
      ...item,
      titleHtml: title,
      date: date,
    };

    // 保存至缓存
    processedChatCache.set(cacheKey, processed);

    return processed;
  });
};

// 关键字高亮处理函数
const highlightKeyword = (text: string, keyword: string): string => {
  if (!keyword || !keyword.trim() || !text) return text;
  try {
    // 使用正则表达式进行全局匹配，忽略大小写
    const reg = new RegExp(keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'gi');
    return text.replace(reg, match => `<span class="highlight">${match}</span>`);
  } catch (error) {
    console.error('高亮处理失败:', error);
    return text;
  }
};

// 对话点击处理
const handleChatClick = (item: ProcessedChatData) => {
  if (!item.chatId || !item.tenantAppId) return;
  uni.navigateTo({
    url: `/pages/chat/index?chatId=${encodeURIComponent(item.chatId)}&appId=${encodeURIComponent(item.tenantAppId)}`,
  });
};

const handleRemoveChat = (item: ProcessedChatData) => {
  rmRecentlyChat({ id: item.id!, chatId: item.chatId })
    .then(() => {
      props.uToastRef.show({
        type: 'info',
        message: '对话从记录中移除成功',
      });
      emit('ok');
    })
    .catch(() => {
      props.uToastRef.show({
        type: 'error',
        message: '删除失败',
      });
    });
};

const handleToolsClick = (item: ProcessedChatData) => {
  handleRemoveChat(item);
};

function formatToMinutes(timeStr?: string) {
  if (!timeStr) return '';
  const date = new Date(timeStr);

  // 提取各时间组件并补零
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

defineExpose({
  fetchMethod: fetchChatHistory,
  processData: processChatData,
});
</script>

<template>
  <view class="chat-list">
    <!-- 骨架屏 -->
    <template v-if="loading">
      <view v-for="index in 4" :key="`skeleton-${index}`" class="skeleton-item">
        <view class="skeleton-content">
          <view class="skeleton-title"></view>
          <view class="skeleton-date"></view>
        </view>
      </view>
    </template>

    <!-- 真实内容 -->
    <up-swipe-action v-else>
      <up-swipe-action-item
        :options="actionOptions"
        v-for="item in list"
        :key="item.chatId"
        class="list-item"
        @click="handleToolsClick(item)"
      >
        <view style="padding: 24rpx" @click="handleChatClick(item)" @touchmove.stop>
          <view class="item-title">
            <rich-text class="title-text" :nodes="item.title"></rich-text>
          </view>
          <view class="item-date">{{ formatToMinutes(item.updateTime) }}</view>
        </view>
      </up-swipe-action-item>
    </up-swipe-action>
  </view>
</template>

<style lang="scss">
.chat-list {
  height: 100%;

  // 骨架屏样式
  .skeleton-item {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
    border: 1rpx solid #f4f4f4;
    padding: 24rpx;

    .skeleton-content {
      .skeleton-title {
        height: 32rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
        margin-bottom: 22rpx;
        width: 80%;
      }

      .skeleton-date {
        height: 24rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
        width: 40%;
      }
    }
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  .list {
    &-item {
      background: #fff;
      border-radius: 20rpx;
      margin-bottom: 24rpx;
      box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
      border: 1px solid #f1f1f1;

      .item-title {
        margin-bottom: 22rpx;
        :deep(div) {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 100%;
        }

        .title-text {
          font-size: 32rpx;
          color: #1d2129;
          line-height: 48rpx;
        }
      }

      .item-date {
        font-size: 24rpx;
        color: #86909c;
      }
    }
  }
}

/* 高亮样式 */
.highlight {
  color: #1a5eff;
}
</style>
