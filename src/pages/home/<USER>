<script setup lang="ts">
import { ref, reactive } from 'vue';
import { recentlyAppCenterList } from '@/api/userCommon';
import type { AppListItemType } from '@/types/api/app';
import { AppTypeEnum } from '@/constants/api/app';
import { DataSourceMap, DataSource } from '@/constants/api';
import { setCommonApp, rmCommonApp, rmRecentlyAppCenter } from '@/api/userCommon';

// 处理后的文件数据类型
interface ProcessedAppData extends AppListItemType {
  icon: string;
  description: string;
}

const props = defineProps<{
  keyword?: string;
  list?: ProcessedAppData[];
  uToastRef: any;
  loading?: boolean;
}>();

const emit = defineEmits(['ok']);

const actionOptions = reactive([
  {
    icon: 'trash',
    iconSize: '40rpx',
    style: {
      backgroundColor: '#D54941',
    },
  },
]);

// 使用真实API获取应用列表
const fetchAppList = async (params: any) => {
  try {
    // 调用真实接口
    return await recentlyAppCenterList({
      ...params,
      keyword: props.keyword,
    });
  } catch (error) {
    console.error('获取应用列表失败:', error);
    return { records: [], total: 0, pages: 0, current: 1, size: params.size, realTotal: 0 };
  }
};

// 缓存处理过的应用数据以提高性能
const processedAppCache = new Map<string, ProcessedAppData>();

// 处理数据
const processAppData = (data: AppListItemType[]) => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    // 使用应用ID作为缓存键
    const cacheKey = item.id || item.finalAppId;

    // 如果缓存中有，直接返回缓存的处理结果
    if (processedAppCache.has(cacheKey)) {
      const cached = processedAppCache.get(cacheKey) as ProcessedAppData;

      // 更新isCommonApp状态，因为这个状态可能随时会变
      if (cached.isCommonApp !== item.isCommonApp) {
        cached.isCommonApp = item.isCommonApp;
      }

      return cached;
    }

    // 处理新条目
    const processed = {
      ...item,
      icon: item.avatarUrl || '/static/app/test-icon.png',
      description: item.intro || '暂无描述',
      isOfficial: item.type === AppTypeEnum.simple,
    };

    // 保存到缓存
    processedAppCache.set(cacheKey, processed);

    return processed;
  });
};

// 监听关键词变化的处理已由 watchEffect 在 LkPageList 中处理

const handleAddApp = (app: AppListItemType) => {
  // 预先更新UI状态，提高响应速度
  app.isCommonApp = 1;

  setCommonApp({ id: app.id })
    .then(() => {
      props.uToastRef.show({
        type: 'success',
        message: '已添加到我的应用',
        icon: 'checkmark-circle-fill',
      });
      emit('ok', 'add');
    })
    .catch(error => {
      // 操作失败，恢复状态
      app.isCommonApp = 0;
      props.uToastRef.show({
        type: 'error',
        message: '添加失败，请重试',
        icon: 'close-circle-fill',
      });
    });
};

const handleRemoveApp = (app: AppListItemType) => {
  // 预先更新UI状态
  app.isCommonApp = 0;

  rmCommonApp({ id: app.id })
    .then(() => {
      props.uToastRef.show({
        type: 'success',
        message: '已移除',
        icon: 'checkmark-circle-fill',
      });
      emit('ok', 'remove');
    })
    .catch(error => {
      // 操作失败，恢复状态
      app.isCommonApp = 1;
      props.uToastRef.show({
        type: 'error',
        message: '移除失败，请重试',
        icon: 'close-circle-fill',
      });
    });
};

const handleRemoveRecentlyApp = (app: AppListItemType) => {
  rmRecentlyAppCenter({ id: app.id })
    .then(() => {
      props.uToastRef.show({
        type: 'info',
        message: '应用从记录中移除成功',
      });
      emit('ok');
    })
    .catch(() => {
      props.uToastRef.show({
        type: 'error',
        message: '删除失败',
      });
    });
};

const handleToolsClick = (app: AppListItemType) => {
  handleRemoveRecentlyApp(app);
};

const handleAppTap = (item: ProcessedAppData) => {
  uni.setStorageSync('selectApp', item);
  uni.navigateTo({
    url: '/pages/chat/index',
  });
};

defineExpose({
  fetchMethod: fetchAppList,
  processData: processAppData,
});
</script>

<template>
  <view class="app-list">
    <!-- 骨架屏 -->
    <template v-if="loading">
      <view v-for="index in 4" :key="`skeleton-${index}`" class="skeleton-item">
        <view class="skeleton-main">
          <view class="skeleton-icon"></view>
          <view class="skeleton-info">
            <view class="skeleton-name"></view>
            <view class="skeleton-desc"></view>
            <view class="skeleton-tag"></view>
          </view>
          <view class="skeleton-btn"></view>
        </view>
      </view>
    </template>

    <!-- 真实内容 -->
    <up-swipe-action v-else>
      <up-swipe-action-item
        :options="actionOptions"
        v-for="item in list"
        :key="item.finalAppId"
        class="list-item"
        @tap="handleToolsClick(item)"
      >
        <view style="padding: 24rpx" @touchmove.stop @tap.stop="handleAppTap(item)">
          <view class="item-main">
            <image :src="item.avatarUrl" class="app-icon" />
            <view class="item-info">
              <view class="item-name">{{ item.name }}</view>
              <view class="item-desc">{{ item.description }}</view>
              <view class="item-tag">
                <image
                  v-if="item.source && DataSourceMap[item.source]"
                  :src="DataSourceMap[item.source]?.imgSrc || '/static/search/app.svg'"
                />
                <text>{{
                  item.source && DataSourceMap[item.source]
                    ? DataSourceMap[item.source].label
                    : '未知来源'
                }}</text>
              </view>
            </view>
            <view class="add-btn" v-if="!item.isCommonApp" @tap.stop="handleAddApp(item)">
              <image src="/static/search/plus.svg" />
            </view>
            <view class="remove-btn" v-else @tap.stop="handleRemoveApp(item)">
              <image src="/static/search/check.svg" />
            </view>
          </view>
        </view>
      </up-swipe-action-item>
    </up-swipe-action>
  </view>
</template>

<style lang="scss">
.app-list {
  height: 100%;

  // 骨架屏样式
  .skeleton-item {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
    border: 1rpx solid #f4f4f4;
    padding: 24rpx;

    .skeleton-main {
      display: flex;
      align-items: center;

      .skeleton-icon {
        width: 120rpx;
        height: 120rpx;
        border-radius: 20rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        flex-shrink: 0;
      }

      .skeleton-info {
        flex: 1;
        padding-left: 20rpx;
        padding-right: 20rpx;

        .skeleton-name {
          height: 32rpx;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4rpx;
          margin-bottom: 12rpx;
          width: 70%;
        }

        .skeleton-desc {
          height: 24rpx;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4rpx;
          margin-bottom: 12rpx;
          width: 90%;
        }

        .skeleton-tag {
          height: 24rpx;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4rpx;
          width: 50%;
        }
      }

      .skeleton-btn {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        flex-shrink: 0;
      }
    }
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  .list-item {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
    border: 1px solid #f1f1f1;

    .item-main {
      display: flex;
      align-items: center;

      .app-icon {
        width: 120rpx;
        height: 120rpx;
        border-radius: 99999px;
        flex-shrink: 0;
      }

      .item-info {
        flex: 1;
        overflow: hidden;
        padding-left: 24rpx;
        padding-right: 24rpx;
      }

      .item-name {
        font-size: 32rpx;
        color: #1d2129;
        margin-bottom: 8rpx;
        font-weight: 500;
      }

      .item-desc {
        font-size: 24rpx;
        color: #86909c;
        margin-bottom: 8rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .item-tag {
        display: flex;
        align-items: center;
        gap: 8rpx;

        image {
          width: 28rpx;
          height: 28rpx;
        }

        text {
          font-size: 24rpx;
          color: #86909c;
        }
      }

      .add-btn {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        background: linear-gradient(180deg, #ab57ff 0%, #7d4dff 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        image {
          width: 36rpx;
          height: 36rpx;
        }
      }
      .remove-btn {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        background: #f2f3f5;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        image {
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
  }
}
</style>
