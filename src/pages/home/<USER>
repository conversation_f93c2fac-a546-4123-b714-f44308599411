<template>
  <up-popup
    v-model:show="showModal"
    mode="bottom"
    :round="20"
    :safeAreaInsetBottom="true"
    @close="handleCancel"
  >
    <view class="modal-container">
      <!-- 顶部标题栏 -->
      <view class="modal-header">
        <view class="modal-title">编辑我的常用</view>
        <LkSvg
          width="44rpx"
          height="44rpx"
          color="#999"
          src="/static/common/close.svg"
          @tap="handleCancel"
        />
      </view>

      <!-- 拖拽内容区域 -->
      <view class="modal-content">
        <!-- 操作提示 -->
        <view class="drag-tip">
          <LkSvg width="32rpx" height="32rpx" src="/static/common/tips.svg" />
          <LkText type="primary" size="small" style="margin-left: 15rpx">长按拖动排序哦~</LkText>
        </view>

        <view class="drag-container">
          <LDrag
            v-if="showModal"
            ref="dragRef"
            longpress
            :list="editList"
            :column="1"
            gridHeight="166rpx"
            scrollHeight="40vh"
            :disabled="false"
            @change="handleDragChange"
          >
            <template #grid="{ content: item, active, index, oldindex, oindex }">
              <view :key="item.id" class="drag-grid-item">
                <view class="drag-item-content">
                  <view class="drag-item-left">
                    <up-image
                      :src="item?.tenantApp?.avatarUrl"
                      shape="circle"
                      width="100rpx"
                      height="100rpx"
                    />
                  </view>
                  <view class="drag-item-info">
                    <LkText type="primary" class="up-line-1" bold style="font-size: 30rpx">
                      {{ item.tenantApp.name || '--' }}
                    </LkText>
                    <LkText
                      type="tertiary"
                      class="up-line-1"
                      style="margin-top: 10rpx; font-size: 24rpx"
                    >
                      {{ item.tenantApp.intro || '--' }}
                    </LkText>
                  </view>
                  <!-- 删除按钮 -->
                  <view class="drag-item-delete" @tap="handleRemoveItem(oindex, item.id)">
                    <up-icon name="minus-circle-fill" color="#F12409" size="48rpx" />
                  </view>
                </view>
              </view>
            </template>
          </LDrag>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="modal-footer safe-area-inset-bottom">
        <LkButton
          type="secondary"
          shape="round"
          size="large"
          style="flex: 1; margin-right: 20rpx"
          @tap="handleCancel"
          >取消</LkButton
        >
        <LkButton
          type="primary"
          shape="round"
          size="large"
          style="flex: 1"
          :loading="isConfirming"
          :disabled="isConfirming"
          @tap="handleConfirm"
          >完成</LkButton
        >
      </view>
    </view>
  </up-popup>

  <!-- 删除全部应用确认弹窗 -->
  <LkConfirmModal
    v-model:show="showDeleteConfirm"
    type="delete"
    title="确认移除"
    content="是否移除全部我的常用应用"
    @confirm="handleConfirmDelete"
    @cancel="handleCancelDelete"
  />
  <LkToast ref="uToastRef"></LkToast>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import LDrag from '@/uni_modules/lime-drag/components/l-drag/l-drag.vue';
import type { CommonAppType } from '@/types/api/app';
import { rmCommonApp, sortCommonApp } from '@/api/userCommon';

interface Props {
  show: boolean;
  list: CommonAppType[];
}

interface Emits {
  (e: 'update:show', value: boolean): void;
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  list: () => [],
});

const emit = defineEmits<Emits>();

const showModal = computed({
  get: () => props.show,
  set: value => emit('update:show', value),
});

const uToastRef = ref<any>(null);
const editList = ref<CommonAppType[]>([]);
const dragRef = ref<any>(null);
const editParams = ref<{ id: string; sort: number }[]>([]);

// 删除确认弹窗相关状态
const showDeleteConfirm = ref(false);
const pendingDeleteItem = ref<{ oindex: number; id: string } | null>(null);

// 确认按钮loading状态
const isConfirming = ref(false);

// 监听传入的列表数据变化
watch(
  () => props.list,
  newList => {
    if (newList && newList.length > 0) {
      editList.value = JSON.parse(JSON.stringify(newList));
    }
  },
  { immediate: true, deep: true }
);

// 监听弹窗显示状态
watch(showModal, newVal => {
  if (newVal && props.list.length > 0) {
    // 弹窗打开时重新初始化数据
    editList.value = JSON.parse(JSON.stringify(props.list));
    editParams.value = [];
  }
});

// 处理拖拽变化
const handleDragChange = (list: any[]) => {
  const newList = JSON.parse(JSON.stringify(list));
  const param = newList.map((item: any, index: any) => ({
    id: item.content.id,
    sort: index,
  }));
  editParams.value = param;
};

// 处理取消
const handleCancel = () => {
  showModal.value = false;
  emit('cancel');
};

// 处理确认
const handleConfirm = async () => {
  if (isConfirming.value) return; // 防止重复点击

  isConfirming.value = true;
  try {
    // 获取当前实际的列表数据（从LDrag组件获取）
    const currentList = dragRef.value?.cloneList?.map((item: any) => item.content) || [];

    // 处理被删除的项
    const removedItems = props.list.filter(
      item => !currentList.some((cur: CommonAppType) => cur.id === item.id)
    );

    // 删除被移除的项
    for (const item of removedItems) {
      await rmCommonApp({ id: item?.tenantApp?.id });
    }

    // 如果有排序变化，更新排序
    if (editParams.value.length > 0) {
      await sortCommonApp({ param: editParams.value });
    }

    showModal.value = false;
    emit('confirm');

    uToastRef.value.show({
      type: 'success',
      message: '我的常用编辑完成',
    });
  } catch (error) {
    console.error('编辑失败:', error);
    uToastRef.value.show({
      type: 'error',
      message: '编辑失败',
    });
  } finally {
    isConfirming.value = false;
  }
};

// 处理删除项
const handleRemoveItem = (oindex: number, id: string) => {
  if (dragRef.value && typeof oindex === 'number' && oindex >= 0) {
    // 如果是最后一个元素
    if (dragRef.value.cloneList?.length === 1) {
      // 显示删除全部应用确认弹窗
      pendingDeleteItem.value = { oindex, id };
      showDeleteConfirm.value = true;
    } else {
      console.log('不是最后一个元素，直接删除');
      dragRef.value.remove(oindex);
    }
  }
};

// 处理确认删除
const handleConfirmDelete = () => {
  if (pendingDeleteItem.value && dragRef.value) {
    dragRef.value.remove(pendingDeleteItem.value.oindex);
    pendingDeleteItem.value = null;
  }
  showDeleteConfirm.value = false;
  handleConfirm();
};

// 处理取消删除
const handleCancelDelete = () => {
  pendingDeleteItem.value = null;
  showDeleteConfirm.value = false;
};

// 处理点击应用项
const handleItemClick = (item: CommonAppType) => {
  uni.setStorageSync('selectApp', item.tenantApp);
  uni.navigateTo({
    url: '/pages/chat/index',
  });
  // 关闭弹窗
  showModal.value = false;
};
</script>

<style lang="scss" scoped>
.modal-container {
  background: #f7f7fd;
  display: flex;
  flex-direction: column;
  border-radius: 24rpx 24rpx 0 0;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-content {
  flex: 1;
  overflow: hidden;
  padding: 20rpx;
}

.drag-tip {
  display: flex;
  align-items: center;
  padding: 15rpx 30rpx;
  margin-bottom: 20rpx;
  border-radius: 28rpx;
  background: #fff;
}

.drag-container {
  ::v-deep .l-drag {
    overflow: visible;
    &__view {
      padding: 10rpx 0rpx;
    }
  }
}

.drag-grid-item {
  width: 100%;
  padding: 0 10rpx;
  box-sizing: border-box;
}

.drag-item-content {
  width: 100%;
  background-color: white;
  border-radius: 32rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}

.drag-item-left {
  width: 100rpx;
  height: 100rpx;
}

.drag-item-info {
  flex: 1;
  padding: 0 20rpx;
}

.drag-item-delete {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  z-index: 10;
}

.modal-footer {
  display: flex;
  padding: 30rpx 40rpx;
  background: white;
  border-top: 1px solid #f0f0f0;
  gap: 20rpx;
}
</style>
