<template>
  <view :style="{ paddingBottom: props.platform === 'ios' ? '170rpx' : '140rpx' }">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-content">
        <LkTenantSwitch @switch="handleTenantSwitch" />
        <view class="text-search-input" @tap="handleSearch">
          <LkSvg width="24px" height="24px" src="/static/database/search.svg" />
        </view>
      </view>
    </view>
    <!-- 整页分页组件 -->
    <LkWholePagination
      ref="wholePaginationRef"
      :fetch-method="currentFetchMethod"
      :processData="currentProcessData"
      :page-size="10"
      :enablePullUp="false"
      defaultSkeletonLoading
      class="home_whole-pagination"
    >
      <template #default="{ list = [], skeletonLoading = false }">
        <!-- 主容器 -->
        <view class="container">
          <!-- 精选推荐区域 -->
          <RecommendSection ref="recommendSectionRef" />

          <!-- 我的常用区域 -->
          <CommonAppsSection
            ref="commonAppsSectionRef"
            :guideMode="isGuideMode"
            @refreshRecentData="handleRefreshRecentData"
          />

          <!-- 最近使用区域 -->
          <RecentSection
            ref="recentSectionRef"
            :uToastRef="uToastRef"
            :list="list"
            :skeletonLoading="skeletonLoading"
            :currentType="currentType"
            @commonAppUpdate="handleCommonAppUpdate"
            @tabChange="originalHandleTabChange"
          />
        </view>
      </template>

      <!-- 空状态 -->
      <template #empty>
        <view class="home_empty" style="padding-bottom: 80rpx; background: #fff">
          <LkServiceImage name="empty" style="width: 200rpx; height: 200rpx" />
          <LkText type="tertiary" size="small">暂无使用数据</LkText>
        </view>
      </template>
    </LkWholePagination>

    <!-- 全局组件 -->
    <LkToast ref="uToastRef" />
    <LkStepGuide :steps="guideSteps" @complete="onGuideComplete" ref="stepGuide" />

    <!-- 编辑弹窗 -->
    <CommonUseEditModal
      v-model:show="showEditModal"
      :list="commonAppsSectionRef?.commonUseList || []"
      @confirm="handleEditConfirm"
      @cancel="handleEditCancel"
    />
  </view>
</template>

<script setup lang="ts">
import { onShow, onLoad } from '@dcloudio/uni-app';
import { onMounted, ref, nextTick, computed, watch } from 'vue';
import { useSystemStore } from '@/store/systemStore';
import { useUserStore } from '@/store/userStore';
import { useTabStore } from '@/store/tabStore';
import { SearchType } from '@/constants/search';
import { useRecentItems } from './composables/useRecentItems';
import { useCommonApps } from './composables/useCommonApps';
import type { CommonAppType } from '@/types/api/app';
import useNetworkRefresh from '@/hooks/useNetworkRefresh';

// 导入组件
import LkTenantSwitch from '@/components/LkTenantSwitch/index.vue';
import LkSvg from '@/components/svg/index.vue';
import LkToast from '@/components/LkToast/index.vue';
import LkStepGuide from '@/components/LkStepGuide/index.vue';
import LkServiceImage from '@/components/LkServiceImage/index.vue';
import LkText from '@/components/LkText/index.vue';
import LkWholePagination from './LkWholePagination.vue';
import CommonUseEditModal from './CommonUseEditModal.vue';

// 导入拆分的区域组件
import RecommendSection from './components/RecommendSection.vue';
import CommonAppsSection from './components/CommonAppsSection.vue';
import RecentSection from './components/RecentSection.vue';

// Store实例
const systemStore = useSystemStore();
const userStore = useUserStore();
const tabStore = useTabStore();

// 组件引用
const uToastRef = ref();
const stepGuide = ref();
const recommendSectionRef = ref();
const commonAppsSectionRef = ref();
const recentSectionRef = ref();

const props = defineProps({
  platform: {
    type: String,
    default: 'ios',
  },
});

// 使用最近使用项目组合逻辑
const {
  currentType,
  wholePaginationRef,
  currentFetchMethod,
  currentProcessData,
  handleTabChange: originalHandleTabChange,
  debouncedRefresh,
} = useRecentItems();

// 使用常用应用组合逻辑（仅为了在弹窗中显示数据，不涉及状态管理）
const { commonUseList } = useCommonApps();

// 弹窗显示状态的计算属性
const showEditModal = computed({
  get: () => commonAppsSectionRef.value?.showEditModal || false,
  set: (value: boolean) => {
    if (commonAppsSectionRef.value?.showEditModal !== undefined) {
      commonAppsSectionRef.value.showEditModal = value;
    }
  },
});

// 弹窗取消事件处理
const handleEditCancel = () => {
  commonAppsSectionRef.value?.handleEditCancel?.();
};

// 定义Step接口类型
interface Step {
  target: string;
  title: string;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'topRight';
  validate?: () => boolean;
  guideStepsStyle?: string | Record<string, any>;
  offsetX?: number;
  offsetY?: number;
  lollipop?: boolean;
  btnText?: string;
}

// 引导步骤配置
const guideSteps = ref<Step[]>([
  {
    target: '.home_recommend-guide',
    title: '精选推荐',
    content: '官方精选推荐应用，一键畅用。',
    position: 'bottom',
  },
  {
    target: '.home_common-use_empty',
    title: '我的常用',
    content: '高频使用的应用，可添加至「我的常用」中，一键直达。',
    position: 'top',
  },
]);

// 引导模式：用于在引导阶段强制显示空态占位
const isGuideMode = ref(false);

/**
 * 处理编辑确认事件
 */
const handleEditConfirm = () => {
  // 使用子组件的编辑确认方法
  commonAppsSectionRef.value?.handleEditConfirm?.();
};

/**
 * 处理租户切换事件
 */
const handleTenantSwitch = () => {
  console.log('租户切换');
  // 刷新所有数据
  refreshAllSections();
};

/**
 * 处理搜索点击事件
 */
const handleSearch = () => {
  try {
    uni.setStorageSync('searchActiveType', SearchType.APP);

    uni.navigateTo({
      url: '/pages/search/index',
      fail: err => {
        console.error('导航到搜索页面失败:', err);
        uToastRef.value?.show({
          type: 'error',
          message: '跳转失败',
        });
      },
    });
  } catch (err) {
    console.error('跳转到搜索页面失败:', err);
    uToastRef.value?.show({
      type: 'error',
      message: '跳转失败',
    });
  }
};

/**
 * 处理最近使用数据刷新请求
 */
const handleRefreshRecentData = () => {
  nextTick(() => {
    wholePaginationRef.value?.refresh();
  });
};

/**
 * 处理常用应用更新通知
 */
const handleCommonAppUpdate = () => {
  commonAppsSectionRef.value?.refresh?.();
};

/**
 * 刷新所有区域数据
 */
const refreshAllSections = () => {
  nextTick(() => {
    recommendSectionRef.value?.refresh?.();
    commonAppsSectionRef.value?.refresh?.();
    wholePaginationRef.value?.refresh();
  });
};

/**
 * 处理引导完成事件
 */
const onGuideComplete = () => {
  console.log('用户完成了所有引导步骤');
  // 关闭引导模式，恢复正常渲染
  isGuideMode.value = false;
  const data = uni.getStorageSync('Guide') || {};
  uni.setStorageSync('Guide', { ...data, Home: true });
};

/**
 * 初始化引导
 */
const initGuide = () => {
  setTimeout(() => {
    nextTick(() => {
      if (!uni.getStorageSync('Guide')?.Home) {
        // 启用引导模式，确保第二步元素始终存在
        isGuideMode.value = true;
        stepGuide?.value?.start?.();
      }
    });
  }, 100);
};

// 生命周期钩子
onLoad(() => {
  console.log('首页开始加载');
});

// 监听推荐区域loading状态，当加载完成后执行引导
watch(
  () => recommendSectionRef.value?.loading,
  (newLoading, oldLoading) => {
    // 当loading从true变为false时，说明数据加载完成
    if (oldLoading === true && newLoading === false) {
      console.log('推荐数据加载完成，开始引导');
      initGuide();
    }
  }
);

onShow(() => {
  console.log('首页显示');
  // 刷新数据
  refreshAllSections();
});

onMounted(() => {
  console.log('首页组件已挂载');
});

// 监听网络状态变化，网络恢复时自动刷新首页数据
useNetworkRefresh({
  onReconnect: refreshAllSections,
  onDisconnect: () => {
    uni.showToast({
      title: '网络已断开',
      icon: 'error',
    });
  },
  throttleDuration: 3000,
});
</script>

<style lang="scss" scoped>
// @import './styles/index.scss';

// 主容器样式在 styles/index.scss 中定义

// 顶部导航栏
.navbar {
  --status-bar-padding-top: calc(var(--status-bar-height) + 14px);
  padding: var(--status-bar-padding-top) 32rpx 0 32rpx;
  &-content {
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.home_empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 50rpx;
}

.home_whole-pagination {
  height: calc(100% - 60rpx - 14px - var(--status-bar-height));
}
</style>
