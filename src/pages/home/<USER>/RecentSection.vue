<template>
  <view class="home_recent">
    <!-- 标题 -->
    <view class="home_title">
      <view style="position: relative; z-index: 1">最近使用</view>
      <LkServiceImage
        name="textLine"
        style="position: absolute; bottom: -10rpx; height: 32rpx; width: 150rpx; z-index: 0"
      />
    </view>

    <!-- Tab切换 -->
    <view class="home_recent-tab">
      <LkTabGroup
        :modelValue="props.currentType"
        :tabs="tabOptions"
        @update:modelValue="handleTabChange"
      />
    </view>

    <!-- 内容区域 -->
    <!-- 文件列表 -->
    <template v-if="props.currentType === SearchType.FILE">
      <FileItemList
        ref="fileItemListRef"
        :list="Array.isArray(props.list) ? props.list : (props.list as any)?.records || []"
        :uToastRef="props.uToastRef"
        :loading="props.skeletonLoading"
        style="margin-top: 20rpx"
        @ok="handleOk"
      />
    </template>

    <!-- 应用列表 -->
    <template v-else-if="props.currentType === SearchType.APP">
      <AppItemList
        ref="appItemListRef"
        :list="Array.isArray(props.list) ? props.list : (props.list as any)?.records || []"
        :uToastRef="props.uToastRef"
        :loading="props.skeletonLoading"
        style="margin-top: 20rpx"
        @ok="handleOk"
      />
    </template>

    <!-- 聊天列表 -->
    <template v-else-if="props.currentType === SearchType.CHAT">
      <ChatItemList
        ref="chatItemListRef"
        :list="Array.isArray(props.list) ? props.list : (props.list as any)?.records || []"
        :uToastRef="props.uToastRef"
        :loading="props.skeletonLoading"
        style="margin-top: 20rpx"
        @ok="handleOk"
      />
    </template>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { SearchType } from '@/constants/search';
import LkServiceImage from '@/components/LkServiceImage/index.vue';
import LkText from '@/components/LkText/index.vue';
import LkTabGroup from '@/components/LkTabGroup/index.vue';
import FileItemList from '../FileItemList.vue';
import ChatItemList from '../ChatItemList.vue';
import AppItemList from '../AppItemList.vue';

interface Props {
  uToastRef?: any;
  list?: any[];
  skeletonLoading?: boolean;
  currentType?: SearchType;
}

interface Emits {
  (e: 'commonAppUpdate'): void;
  (e: 'tabChange', value: string | number): void;
}

const props = withDefaults(defineProps<Props>(), {
  list: () => [],
  skeletonLoading: false,
  currentType: SearchType.FILE,
});

const emit = defineEmits<Emits>();

// 组件引用
const fileItemListRef = ref();
const appItemListRef = ref();
const chatItemListRef = ref();

// Tab选项配置
const tabOptions = ref([
  { value: SearchType.FILE, label: '文件' },
  { value: SearchType.APP, label: '应用' },
  { value: SearchType.CHAT, label: '对话' },
]);

// 处理Tab切换事件
const handleTabChange = (value: string | number) => {
  emit('tabChange', value);
};

// 处理OK事件
const handleOk = (type?: string) => {
  // 如果是添加或移除操作，通知父组件更新常用应用
  if (type === 'add' || type === 'remove') {
    emit('commonAppUpdate');
  }
};

// 对外暴露的方法
defineExpose({
  refresh: () => {
    // 通过emit通知父组件刷新数据
    console.log('RecentSection刷新请求');
  },
  switchTab: (tabType: SearchType) => {
    emit('tabChange', tabType);
  },
});
</script>

<style lang="scss" scoped>
.home_recent {
  background-color: white;
  padding: 20rpx 32rpx;
  &-tab {
    padding-top: 30rpx;
  }
  ::v-deep .u-swipe-action-item__right {
    border-radius: 0 20rpx 20rpx 0;
    overflow: hidden;
  }
}

.home_title {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  font-size: 36rpx;
  font-weight: bold;
}

.home_empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 40rpx;
}
</style>
