<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  FileFilterTypes,
  FileFilterTime,
  FileFilterNameOrContentEnum,
  FileFilterNameOrContent,
} from '@/constants/search';
import { getFileGlobalListPage } from '@/api/cloud';
import { SearchTypeParams, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import type { CloudFileContentSearch } from '@/types/api/cloud';
import { FileFilterTypesEnum, FileFilterTimeEnum } from '@/constants/search';
import { isImageByExt } from '@/utils/common';
import { fileIcon } from '@/utils/file';
import FileViewer from '@/components/LkFileViewer/index.vue';

const props = defineProps<{
  keyword: string;
}>();

const fileType = ref<FileFilterTypesEnum>(FileFilterTypesEnum.ALL);
const fileTime = ref<FileFilterTimeEnum>(FileFilterTimeEnum.ALL);
const fileNameOrContent = ref<FileFilterNameOrContentEnum>(FileFilterNameOrContentEnum.NAME);

// 获取文件列表的处理方法
const fetchFileList = async (params: any) => {
  if (!props.keyword) {
    return {
      records: [],
      total: 0,
      size: 10,
      current: 1,
      pages: 0,
    };
  }

  return await getFileGlobalListPage({
    ...params,
  });
};

// 处理文件数据
const processFileData = (data: CloudFileContentSearch[]) => {
  return data.map(item => {
    // 计算文件大小
    const fileSizeText = item.fileSize ? formatFileSize(item.fileSize) : '未知大小';

    // 确定文件类型
    const fileExtension = getFileExtension(item.fileName);
    let spaceName = '';
    if (item.bizType === BizTypeEnum.TenantLibrary) {
      if ((item.locationPath?.length || 0) < 2) {
        spaceName = [...(item.locationPath || [])].map(path => ' ' + path.name).join(' > ');
      } else {
        const firstPath = item.locationPath?.[0];
        const lastPath = item.locationPath?.[item.locationPath.length - 1];
        spaceName = firstPath?.name + ' >...> ' + lastPath?.name;
      }
    } else {
      if (item.locationPath?.length === 0) {
        spaceName = '我的数据空间';
      } else if ((item.locationPath?.length || 0) <= 1) {
        spaceName =
          '我的数据空间 >' +
          [...(item.locationPath || [])].map(path => ' ' + path.name).join(' > ');
      } else {
        const lastPath = item.locationPath?.[item.locationPath.length - 1];
        spaceName = '我的数据空间' + ' >...> ' + lastPath?.name;
      }
    }

    // 文件名高亮处理
    let fileName = item.fileName;
    if (props.keyword && fileNameOrContent.value === FileFilterNameOrContentEnum.NAME) {
      fileName = highlightKeyword(fileName, props.keyword);
    }

    // 文件内容高亮处理 - 直接使用API返回的高亮内容
    const fileContentArray = item.highlight?.fileContent || [];
    const fileContent = fileContentArray.map(content => {
      // 将<em>标签替换为自定义高亮样式
      return content
        .replace(/<em>/g, '<span style="color: #1A5EFF;">')
        .replace(/<\/em>/g, '</span>');
    });

    return {
      ...item,
      size: fileSizeText,
      type: fileExtension,
      date: item.updateTime.slice(0, 10),
      spaceName: spaceName,
      creator: (item as any).uploader || '未知创建者',
      fileNameHtml: fileName,
      fileContentHtml: fileContent.join('<br>'),
    };
  });
};

// 关键字高亮处理函数
const highlightKeyword = (text: string, keyword: string): string => {
  if (!keyword.trim()) return text;
  // 使用正则表达式进行全局匹配，忽略大小写
  const reg = new RegExp(keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'gi');
  return text.replace(reg, match => `<span class="highlight">${match}</span>`);
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
};

// 获取文件扩展名
const getFileExtension = (fileName: string) => {
  const parts = fileName.split('.');
  if (parts.length > 1) {
    const ext = parts[parts.length - 1].toLowerCase();
    return ['doc', 'docx', 'pdf', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext) ? ext : 'file';
  }
  return 'file';
};
const fileProps = ref<{
  fileUrl: string;
  fileKey: string;
  fileType?: number;
}>({
  fileUrl: '',
  fileKey: '',
  fileType: undefined,
});
const handleItemClick = (item: any) => {
  console.log('item', item);
  fileProps.value = {
    fileUrl: item.file?.fileUrl,
    fileKey: item.file?.fileKey,
    fileType: 1,
  };
};
</script>

<template>
  <view class="file-list">
    <!-- 筛选器 -->
    <FileViewer
      :file="fileProps"
      @resetFile="fileProps = { fileUrl: '', fileKey: '', fileType: undefined }"
    />

    <view class="filter" v-if="keyword">
      <view class="filter-item">
        <LkSelectList
          v-model="fileNameOrContent"
          :list="FileFilterNameOrContent"
          placeholder="文件"
          title="筛选"
        />
      </view>
      <view class="filter-item">
        <LkSelectList
          title="筛选类型"
          v-model="fileType"
          :list="FileFilterTypes"
          placeholder="全部类型"
        />
      </view>

      <view class="filter-item">
        <LkSelectList
          title="筛选时间"
          v-model="fileTime"
          :list="FileFilterTime"
          placeholder="全部时间"
        />
      </view>
    </view>

    <view class="list-container">
      <!-- 列表 -->
      <LkPageList
        :real-total-way="true"
        :fetch-method="fetchFileList"
        :process-data="processFileData"
        :extra-params="{
          searchKey: keyword,
          searchType: fileNameOrContent,
          searchFileType: fileType,
          updateTimeType: fileTime,
        }"
        :page-size="10"
      >
        <template #default="{ list }">
          <view v-if="list.length">
            <view
              v-for="item in list"
              :key="item.id"
              class="list-item"
              @click="handleItemClick(item)"
            >
              <view class="item-main-wrapper">
                <view class="item-main">
                  <!-- <image
                    :src="
                      !isImageByExt(item.fileSuffix)
                        ? `/static/fileTypeIcon/${item.fileSuffix}.svg`
                        : item.file?.fileUrl
                    "
                  /> -->
                  <LkSvg
                    class="file-icon"
                    width="84rpx"
                    height="84rpx"
                    :src="fileIcon(item)"
                    :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                  />
                  <view class="item-info">
                    <view class="item-name">
                      <rich-text :nodes="item.fileNameHtml"></rich-text>
                    </view>
                    <view class="item-meta">
                      <text>{{ item.size }}</text>
                      <text class="separator">|</text>
                      <text>{{ item.date }}</text>
                    </view>
                  </view>
                </view>
                <!-- 文件内容部分 -->
                <view class="content-wrapper" v-if="item?.highlight?.fileContent?.length">
                  <view class="content">
                    <rich-text :nodes="item.fileContentHtml"></rich-text>
                  </view>
                </view>
              </view>
              <view class="item-footer">
                <view class="spaceName text-ellipsis">
                  <image src="/static/search/space.svg" />
                  <text>{{ item.spaceName }}</text>
                </view>
                <text class="creator">{{ item.uploader }}</text>
              </view>
            </view>
          </view>
          <template v-else>
            <u-empty text="暂无相关文件" mode="file" />
          </template>
        </template>
      </LkPageList>
    </view>
  </view>
</template>

<style lang="scss">
.file-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  // 两行展示
  .two-line-ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 高亮样式 */
  .highlight {
    color: #1a5eff;
  }

  .filter {
    display: flex;
    gap: 24rpx;
    margin-bottom: 24rpx;
    box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
    border: 4rpx solid #f1f1f1;
    border-radius: 20rpx;
    padding: 0 32rpx;
    justify-content: space-between;

    .filter-item {
      flex: 1;
      width: 33%;
      overflow: hidden;
    }
  }

  .list-container {
    flex: 1;
    overflow: hidden;
  }

  .list {
    &-item {
      background: #fff;
      border-radius: 20rpx;
      padding: 24rpx;
      margin-bottom: 24rpx;
      box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
      border: 4rpx solid #f1f1f1;
      box-sizing: border-box;
      .item-main-wrapper {
        border-bottom: 1px solid #f4f4f4;
        padding-bottom: 24rpx;

        .content {
          overflow: hidden;
          color: #4e5969;
          text-overflow: ellipsis;

          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 171.429% */
          letter-spacing: 0.07px;
          em {
            color: #1a5eff;
          }
        }
      }
      .item-main {
        display: flex;
        gap: 10rpx;

        .file-icon {
          width: 84rpx;
          height: 84rpx;
          flex-shrink: 0;
          margin-right: 12rpx;
        }

        .item-info {
          flex: 1;
          overflow: hidden;
        }

        .item-name {
          font-size: 32rpx;
          color: #1d2129;
          margin-bottom: 8rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 100%;
          :deep(div) {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            width: 100%;
          }

          .item-type {
            color: #195eff;
          }
        }

        .item-meta {
          font-size: 24rpx;
          color: #86909c;

          .separator {
            margin: 0 16rpx;
          }
        }
      }

      .item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24rpx;
        font-size: 24rpx;
        color: #86909c;

        .spaceName {
          display: flex;
          align-items: center;
          gap: 8rpx;

          image {
            width: 36rpx;
            height: 36rpx;
          }
        }
      }
    }
  }

  // 内容显示样式
  .content-wrapper {
    margin-top: 16rpx;

    .content {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 28rpx;
      line-height: 1.5;
      color: #4e5969;

      :deep(span) {
        color: #1a5eff;
      }
    }
  }
}
</style>
<style lang="scss">
.highlight {
  color: #1a5eff;
}
</style>
