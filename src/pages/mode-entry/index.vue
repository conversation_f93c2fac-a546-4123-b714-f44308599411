<template>
  <view class="mode-entry-container">
    <!-- 背景图片 -->
    <view class="background">
      <LkServiceImage name="loginBg" class="bg-image" mode="aspectFill"></LkServiceImage>
    </view>
    <!-- 顶部标志 -->
    <view class="logo-section">
      <LkServiceImage name="loginLogo" class="logo" mode="aspectFit"></LkServiceImage>
    </view>

    <!-- 主内容区域 -->
    <view class="main-content">
      <view class="header-wrap">
        <LkServiceImage name="loginIpImage" class="logo" mode="aspectFit" />
        <view class="slogan">智驱世界</view>
        <view class="slogan">共创美好未来</view>
      </view>
      <!-- 身份提示文本 -->
      <view class="identity-prompt">
        <text>你的身份是：</text>
      </view>

      <!-- 选择卡片区域 -->
      <view class="selection-cards">
        <!-- 教师卡片 -->
        <view class="role-card" @click="handleTeacherMode">
          <view class="avatar-container">
            <image class="avatar-image" src="/static/common/teacher.jpg" mode="widthFix"></image>
          </view>
          <view class="card-content"> 我是老师 </view>
        </view>

        <!-- 学生卡片 -->
        <view class="role-card" style="margin-top: 25rpx" @click="handleStudentMode">
          <view class="avatar-container">
            <image class="avatar-image" src="/static/common/student.jpg" mode="widthFix"></image>
          </view>
          <view class="card-content">
            <text class="role-text">我是学生</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useUserStore } from '@/store/userStore';
import { SimpleRouter, type UserMode } from '@/router';
import LkServiceImage from '@/components/LkServiceImage/index.vue';
import { onLoad } from '@dcloudio/uni-app';

// 用户状态管理
const userStore = useUserStore();

/**
 * 处理模式选择的通用逻辑
 */
const handleModeSelection = async (mode: UserMode) => {
  console.log(`选择${mode === 'teacher' ? '教师' : '学生'}模式`);

  try {
    // 使用简化的路由管理器处理模式切换
    const result = await SimpleRouter.switchMode(mode);

    if (result.success) {
      console.log(`模式切换成功: ${result.message}`);
    } else {
      console.error('模式切换失败:', result.message);
      uni.showToast({
        title: result.message || '模式切换失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('模式选择处理失败:', error);
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'error',
    });
  }
};

// 处理教师模式点击
const handleTeacherMode = () => {
  handleModeSelection('teacher');
};

// 处理学生模式点击
const handleStudentMode = () => {
  handleModeSelection('student');
};

// 页面加载时的初始化
onMounted(() => {
  console.log('模式入口页面已加载');
  console.log('当前登录状态:', userStore.isLoggedIn);
});

onLoad(options => {
  // 检测是否需要自动跳转
  autoNavigateByMode(options);
});

const closeSplashscreen = () => {
  // #ifdef APP-PLUS
  console.log('-------------- 关闭启动页 ------------->');
  plus.navigator.closeSplashscreen();
  // #endif
};

/**
 * 根据当前模式自动跳转到对应页面
 */
const autoNavigateByMode = async (options?: any) => {
  try {
    // 如果是登录跳转过来的，不执行自动跳转
    if (options?.type === 'login') {
      console.log('登录跳转，停留在模式选择页面');
      closeSplashscreen();
      return;
    }

    // 获取当前存储的用户模式
    const currentMode = uni.getStorageSync('userMode') as UserMode;

    if (!currentMode) {
      console.log('未检测到用户模式，停留在模式选择页面');
      closeSplashscreen();
      return;
    }
    // 检查登录状态
    const token = uni.getStorageSync('token');
    const isLoggedIn = !!token;

    let targetUrl: string;
    if (isLoggedIn) {
      // 已登录，跳转到对应的首页
      targetUrl = SimpleRouter.getHomePage(currentMode);
      console.log(`用户已登录，跳转到首页: ${targetUrl}`);
    } else {
      // 未登录，跳转到登录页
      targetUrl = SimpleRouter.getLoginPage(currentMode);
      console.log(`用户未登录，跳转到登录页: ${targetUrl}`);
    }

    // 使用 reLaunch 确保清除页面栈
    await uni.reLaunch({ url: targetUrl });
    closeSplashscreen();
  } catch (error) {
    console.error('自动跳转失败:', error);
    // 跳转失败时保持在当前页面，让用户手动选择
    closeSplashscreen();
  }
};
</script>

<style scoped lang="scss">
.mode-entry-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  .bg-image {
    width: 100%;
    height: 100%;
  }
}

.logo-section {
  --status-bar-padding-top: calc(var(--status-bar-height) + 20px);
  position: absolute;
  top: 0;
  left: 15px;
  padding-top: var(--status-bar-padding-top);

  .logo {
    width: 158rpx;
    height: 64rpx;
  }
}

.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 60rpx;
  position: relative;
}

.identity-prompt {
  margin-bottom: 30rpx;
  align-self: flex-start;

  text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
  }
}

.selection-cards {
  width: 100%;
  display: flex;
  flex-direction: column;

  .role-card {
    width: 100%;
    height: 204rpx;
    background-color: #fff;
    border-radius: 28rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;

    .avatar-container {
      height: 100%;
      position: relative;
      width: 300rpx;
      .avatar-image {
        width: 80rpx;
        height: 10rpx;
        position: absolute;
        bottom: 0;
        left: 50%;
      }
    }

    .card-content {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30rpx;
      font-weight: bold;
      margin-left: 50rpx;
    }
  }
}

.header-wrap {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-bottom: 60rpx;

  .logo {
    position: absolute;
    left: -60rpx;
    top: -60rpx;
    width: 125rpx;
    height: 125rpx;
  }

  .slogan {
    font-size: 50rpx;
    color: #280868;
    text-align: center;
    font-weight: bold;
    letter-spacing: 1.6rpx;
  }
}
</style>
