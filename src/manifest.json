{
    "name" : "华云天图",
    "appid" : "__UNI__3402EE1",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "screenOrientation" : [ "portrait-primary" ],
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : false,
            "autoclose" : false,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "OAuth" : {},
            "Record" : {},
            "Share" : {},
            "Camera" : {},
            "UIWebview" : {},
            "Webview-x5" : {}
        },
        /* webview配置 - 针对原生webview优化 */
        "webView" : {
            "x5" : {
                "timeOut" : 3000,
                "showTipsWithoutWifi" : true,
                "allowDownloadWithoutWiFi" : false
            }
        },
        /* 内核配置 - 指定默认webview内核 */
        "kernel" : {
            "ios" : "WKWebview",
            "android" : "WKWebview"
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_MEDIA_IMAGES\"/>",
                    "<uses-permission android:name=\"android.permission.READ_MEDIA_VIDEO\"/>"
                ],
                "minSdkVersion" : "21",
                "targetSdkVersion" : 30,
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [
                            "applinks:static-mp-ec38e661-0866-4dfa-ba87-f35096fd5070.next.bspapp.com"
                        ]
                    }
                },
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "需要访问您的相册，以便保存图片或选择图片上传。",
                    "NSPhotoLibraryAddUsageDescription" : "需要保存思维导图图片到相册",
                    "NSCameraUsageDescription" : "需要使用相机拍摄照片或扫描二维码，以便完成相关功能。",
                    "NSMicrophoneUsageDescription" : "需要使用麦克风录制音频，以便实现语音输入功能",
                    "NSSpeechRecognitionUsageDescription" : "需要使用麦克风录制音频，以便实现语音输入功能",
                    "NSAppleMusicUsageDescription" : "测试获取媒体库",
                    "NSLocalNetworkUsageDescription" : "测试获取本地网络",
                    "NSLocationWhenInUseUsageDescription" : "需要获取您的位置信息，以便提供基于位置的服务和精准信息推送",
                    "NSContactsUsageDescription" : "测试获取通讯录"
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "oauth" : {
                    "weixin" : {
                        "appid" : "wxf755e7341f2ce6b2",
                        "appsecret" : "ce71201c030e7a9c3a112dff77c30891",
                        "UniversalLinks" : "https://gpt-pre.hwzxs.com/"
                    },
                    "apple" : {}
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wxf755e7341f2ce6b2",
                        "UniversalLinks" : "https://gpt-pre.hwzxs.com/"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "iosStyle" : "common"
            }
        },
        "compatible" : {
            "ignoreVersion" : true
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "_spaceID" : "mp-ec38e661-0866-4dfa-ba87-f35096fd5070",
    "h5" : {
        "devServer" : {
            "https" : false
        }
    }
}
