/** 错题状态：0未过关；1已过关 */
export enum WrongAnswerType {
  /** 未过关 */
  FAILED = 0,
  /** 已过关 */
  PASSED = 1,
}

/** 题目入口状态 */
export enum QuestionEntryState {
  /** 错题本 */
  WRONG = 'wrong',
  /** 推荐练习 */
  RECOMMEND = 'recommend',
  /** 手动批改 */
  MANUAL = 'manual',
}

/**
 * 错题本相关事件名称常量
 */
export const ErrorBookEventNames = {
  /** 错题本详情页 - 打开反馈弹窗 */
  WRONG_OPEN_FEEDBACK_POPUP: 'wrong-open-feedback-popup',
  /** 错题本详情页 - 打开笔记弹窗 */
  WRONG_OPEN_NOTE_POPUP: 'wrong-open-note-popup',
  /** 错题本详情页 - 打开上传文件弹窗 */
  WRONG_OPEN_UPLOAD_POPUP: 'wrong-open-upload-popup',
  /** 错题本详情页 - 打开绘图弹窗 */
  WRONG_OPEN_DRAWING_BOARD_POPUP: 'wrong-open-drawing-board-popup',

  /** 推荐练习页 - 打开反馈弹窗 */
  RECOMMEND_OPEN_FEEDBACK_POPUP: 'recommend-open-feedback-popup',
  /** 推荐练习页 - 打开笔记弹窗 */
  RECOMMEND_OPEN_NOTE_POPUP: 'recommend-open-note-popup',
  /** 推荐练习页 - 打开绘图弹窗 */
  RECOMMEND_OPEN_DRAWING_BOARD_POPUP: 'recommend-open-drawing-board-popup',

  /** 手动批改页 - 打开反馈弹窗 */
  MANUAL_OPEN_FEEDBACK_POPUP: 'manual-open-feedback-popup',
  /** 手动批改页 - 打开笔记弹窗 */
  MANUAL_OPEN_NOTE_POPUP: 'manual-open-note-popup',
  /** 手动批改页 - 打开绘图弹窗 */
  MANUAL_OPEN_DRAWING_BOARD_POPUP: 'manual-open-drawing-board-popup',
};
