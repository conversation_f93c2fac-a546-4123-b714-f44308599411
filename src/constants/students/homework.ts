/**
 * 作业状态枚举
 */
export enum HomeworkStatus {
  /** 待提交：接收到教师端已发布的作业，在截止时间内未提交作业 */
  TO_BE_SUBMITTED = 0,

  /** 未提交：在作业截止时间内未提交，或订正作业截止时间未提交 */
  NOT_SUBMITTED = 1,

  /** 待批改：已完成作业并提交，待教师端或AI批改 */
  TO_BE_CORRECTED = 2,

  /** 批改中：提交后教师正在批改或AI正在批改 */
  CORRECTING = 3,

  /** 待确认：教师端或AI已完成批改，等待学生确认 */
  TO_BE_CONFIRMED = 4,

  /** 已完成：教师端或AI已完成批改 */
  COMPLETED = 5,

  /** 待订正：提交的作业存在错误，教师端发起了订正作业，待提交订正作业 */
  TO_BE_REVISED = 6,

  /** 批改失败：自主作业进入AI批改环节，AI批改返回失败 */
  CORRECTION_FAILED = 7,
}

/**
 * 前端作业类型枚举
 */
export enum HomeworkType {
  /** 课内作业（对应后端的智慧作业和自定义作业） */
  CLASS = '课内作业',

  /** 分层作业 */
  LAYERED = '分层作业',

  /** 写作任务 */
  WRITING = '写作任务',

  /** 自主作业（学生自主练习，不提交至教师端） */
  SELF = '自主作业',
}

/**
 * 后端返回的作业类型枚举
 */
export enum BackendHomeworkType {
  /** 智慧作业 */
  SMART = 'smart',

  /** 自定义作业 */
  CUSTOM = 'custom',

  /** 分层作业 */
  LAYERED = 'layered',

  /** 写作任务 */
  WRITING = 'writing',

  /** 自主作业 */
  SELF = 'self',
}

/**
 * 后端作业类型到前端作业类型的映射关系
 */
export const BACKEND_TO_FRONTEND_TYPE_MAP: Record<string, HomeworkType> = {
  [BackendHomeworkType.SMART]: HomeworkType.CLASS,
  [BackendHomeworkType.CUSTOM]: HomeworkType.CLASS,
  [BackendHomeworkType.LAYERED]: HomeworkType.LAYERED,
  [BackendHomeworkType.WRITING]: HomeworkType.WRITING,
  [BackendHomeworkType.SELF]: HomeworkType.SELF,
};
/**
 * 作业状态显示名称
 */
export const STATUS_LABEL = {
  [HomeworkStatus.TO_BE_SUBMITTED]: '待提交',
  [HomeworkStatus.TO_BE_CORRECTED]: '待批改',
  [HomeworkStatus.CORRECTING]: '批改中',
  [HomeworkStatus.COMPLETED]: '已完成',
  [HomeworkStatus.TO_BE_REVISED]: '待订正',
  [HomeworkStatus.TO_BE_CONFIRMED]: '待确认',
  [HomeworkStatus.NOT_SUBMITTED]: '未提交',
  [HomeworkStatus.CORRECTION_FAILED]: '批改失败',
};

/**
 * 前端作业类型名称
 */
export const FRONTEND_TYPE_LABEL = {
  [HomeworkType.CLASS]: '课内作业',
  [HomeworkType.LAYERED]: '分层作业',
  [HomeworkType.WRITING]: '写作任务',
  [HomeworkType.SELF]: '自主作业',
};

/**
 * 状态列表
 */
export const STATUS_LIST: { label: string; value: string | number }[] = [
  {
    label: '待提交',
    value: HomeworkStatus.TO_BE_SUBMITTED,
  },
  {
    label: '待批改',
    value: HomeworkStatus.TO_BE_CORRECTED,
  },
  {
    label: '批改中',
    value: HomeworkStatus.CORRECTING,
  },
  {
    label: '已完成',
    value: HomeworkStatus.COMPLETED,
  },
  {
    label: '待订正',
    value: HomeworkStatus.TO_BE_REVISED,
  },
  // {
  //   label: '待确认',
  //   value: HomeworkStatus.TO_BE_CONFIRMED,
  // },
  {
    label: '未提交',
    value: HomeworkStatus.NOT_SUBMITTED,
  },
  {
    label: '批改失败',
    value: HomeworkStatus.CORRECTION_FAILED,
  },
];
/**
 * 前端作业类型列表
 */
export const FRONTEND_TYPE_LIST: { label: string; value: string }[] = [
  {
    label: '课内作业',
    value: HomeworkType.CLASS,
  },
  {
    label: '分层作业',
    value: HomeworkType.LAYERED,
  },
  {
    label: '写作任务',
    value: HomeworkType.WRITING,
  },
  {
    label: '自主作业',
    value: HomeworkType.SELF,
  },
];
/**
 * 需要显示预计批改时长的状态
 */
export const NEED_ESTIMATE_TIME_STATUS = [
  HomeworkStatus.TO_BE_CORRECTED,
  HomeworkStatus.CORRECTING,
];

/**
 * 需要显示预计批改时长的作业类型
 */
export const NEED_ESTIMATE_TIME_TYPE = [HomeworkType.SELF];

/**
 * 作业相关事件名称常量
 */
export const HomeworkEventNames = {
  /** 打开反馈弹窗 */
  OPEN_FEEDBACK_POPUP: 'open-feedback-popup',
  /** 打开笔记弹窗 */
  OPEN_NOTE_POPUP: 'open-note-popup',
  /** 更新笔记 */
  UPDATE_NOTE: 'update-note',
  /** 切换到辅助资料 */
  SWITCH_TO_RESOURCES: 'switch-to-resources',
  /** 切换回答题区域 */
  SWITCH_TO_ANSWER: 'switch-to-answer',
  /** 打开上传文件弹窗 */
  OPEN_UPLOAD_POPUP: 'open-upload-popup',
  /** 上传文件成功 */
  UPLOAD_SUCCESS: 'upload-success',
  /** 打开绘图弹窗 */
  OPEN_DRAWING_BOARD_POPUP: 'open-drawing-board-popup',
};

/**
 * 作业做题方式枚举
 */
export enum HomeworkAnswerType {
  /** 在线做题 */
  ONLINE = 'online',
  /** 纸质做题 */
  PAPER = 'paper',
  /** 写作做题 */
  WRITING = 'writing',
}

/**
 * 题目类型枚举
 * @warn 跟后端保持统一
 */
export enum QuestionType {
  /** 单选题 */
  SINGLE = 'single_choice',
  /** 多选题 */
  MULTIPLE = 'multi_choice',
  /** 简答题 */
  SHORT_ANSWER = 'short_answer',
  /** 判断题 */
  TRUE_FALSE = 'true_false',
  /** 填空题 */
  FILL_BLANK = 'fill_blank',
  /** 连线题 */
  MATCHING = 'matching',
  /** 排序题 */
  SORTING = 'sorting',
  /** 图形填空 */
  GRAPHIC_FILL = 'graphic_fill',
  /** 选词填空 */
  WORD_TRANSLATION = 'wordTranslation',
  /** 完形填空 */
  CLOZE = 'cloze_test',
  /** 阅读理解 */
  READING = 'reading_comprehension',
  /** 材料分析 */
  MATERIAL_ANALYSIS = 'materialAnalysis',
  /** 绘图题 */
  DRAWING = 'drawing',
  /** 计算题 */
  CALCULATION = 'calculation',
  /** 万能题型 */
  ANY_TYPE = 'any_type',
  /** 写作题 */
  WRITING = 'zuowen',
  /** 判断说理题 */
  TRUE_FALSE_REASONING = 'reasoning',
}

// 题型标签映射
export const QUESTION_TYPE_LABELS: Record<string, string> = {
  [QuestionType.SINGLE]: '单选题',
  [QuestionType.MULTIPLE]: '多选题',
  [QuestionType.SHORT_ANSWER]: '简答题',
  [QuestionType.TRUE_FALSE]: '判断题',
  [QuestionType.FILL_BLANK]: '填空题',
  [QuestionType.MATCHING]: '连线题',
  [QuestionType.SORTING]: '排序题',
  [QuestionType.GRAPHIC_FILL]: '图形填空',
  [QuestionType.WORD_TRANSLATION]: '选词填空',
  [QuestionType.CLOZE]: '完形填空',
  [QuestionType.READING]: '阅读理解',
  [QuestionType.MATERIAL_ANALYSIS]: '材料分析',
  [QuestionType.DRAWING]: '绘图题',
  [QuestionType.CALCULATION]: '计算题',
  [QuestionType.WRITING]: '写作题',
};

// 答题状态
export enum AnswerStatus {
  /** 未答题 */
  UNANSWERED = 'unanswered',
  /** 已答题 */
  ANSWERED = 'answered',
  /** 正确 */
  CORRECT = 'correct',
  /** 错误 */
  INCORRECT = 'incorrect',
}

/**
 * 文件类型枚举
 */
export enum FileType {
  /** 图片文件 */
  IMAGE = 'image',
  /** 文档文件 */
  DOCUMENT = 'document',
  /** PDF文件 */
  PDF = 'pdf',
  /** 音频文件 */
  AUDIO = 'audio',
  /** 其他文件 */
  OTHER = 'other',
}

/**
 * 文件类型匹配规则
 */
export const FILE_TYPE_PATTERNS = {
  [FileType.IMAGE]: ['image', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
  [FileType.DOCUMENT]: ['doc', 'docx', 'word'],
  [FileType.PDF]: ['pdf'],
  [FileType.AUDIO]: ['audio', 'mp3', 'wav', 'aac', 'ogg'],
};

/**
 * 作业任务类型枚举
 */
export enum HomeworkTaskType {
  /** 在线作业 */
  ONLINE = 1,
  /** 纸质作业 */
  PAPER = 2,
  /** 写作任务 */
  WRITING = 3,
  /** 自主作业 */
  SELF = 4,
}

/**
 * 年级类型：1-小学低年级；2-小学高年级；3-初中；4-高中
 */
export enum GradeType {
  /** 小学低年级 */
  PRIMARY_LOW = 1,
  /** 小学高年级 */
  PRIMARY_HIGH = 2,
  /** 初中 */
  MIDDLE = 3,
  /** 高中 */
  SENIOR_HIGH = 4,
}

/**
 * 学科分科类型：0未选择；1文科；2理科
 */
export enum SubjectType {
  /** 未选择 */
  UNSELECTED = 0,
  /** 文科 */
  ARTS = 1,
  /** 理科 */
  SCIENCE = 2,
}

/** 答题形式：1选择；2填空；3解答 */
export enum AnswerType {
  /** 选择 */
  CHOICE = 1,
  /** 填空 */
  FILL_BLANK = 2,
  /** 解答 */
  ANSWER = 3,
}
