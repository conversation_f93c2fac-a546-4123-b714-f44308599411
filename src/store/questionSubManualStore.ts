import { defineStore } from 'pinia';
import type {
  FrontQuestion,
  FrontUserAnswer,
  FrontUserAnswersMap,
} from '@/types/students/homework';
import {
  AnswerType,
  HomeworkAnswerType,
  HomeworkStatus,
  HomeworkTaskType,
  QuestionType,
} from '@/constants/students/homework';
import { hasPartsProperty } from '@/pages-subpackages/students/homework/utils';
import { isQuestionAnswered } from '@/pages-subpackages/students/homework/utils';
import { convertUserAnswersToRequestList } from '@/pages-subpackages/students/homework/utils/convert';
import { resetHomeworkAnswerRecord, submitHomework } from '@/api/students/homework';
import dayjs from 'dayjs';

// 手动批改状态枚举
export enum ManualCorrectionStatus {
  /** 未批改 */
  UNCORRECTED = 'uncorrected',
  /** 做对了 */
  CORRECT = 'correct',
  /** 做错了 */
  INCORRECT = 'incorrect',
}

interface BatchQuestion {
  /** 题目ID */
  questionId: string;
  /** 子题ID列表 */
  subQuestionIds?: string[];
}
// 订正类型
export interface BatchInfo {
  /** 订正ID */
  id: string;
  /** 订正名称 */
  name: string;
  /** 用户答案 */
  userAnswers: FrontUserAnswersMap;
  /** 订正的题目列表 */
  questions?: BatchQuestion[];
  /** 当前订正文件列表（纸质作业存放图片等文件）写作任务和在线作业使用题目的 files */
  files?: any[];
}
/**
 * 题目笔记
 */
interface QuestionNote {
  note: string;
  noteId?: number | string;
}

interface HomeworkState {
  /** 作业ID */
  homeworkId: string;
  /** 作业做题方式 */
  answerType: HomeworkTaskType;
  /** 作业题目 */
  questions: FrontQuestion[];
  /** 所有订正信息 */
  batches: BatchInfo[];
  /** 当前选中的订正索引 */
  currentBatchIndex: number;
  /** 作业题目笔记 */
  questionNotes: Record<string, QuestionNote>;
  /** 当前题目索引 */
  currentQuestionIndex: number;
  /** 作业状态 */
  status: HomeworkStatus;
  /** 累计做题时间（秒） */
  elapsedTime: number;
  /** 是否显示答案、解析和判断状态 */
  showAnswer: boolean;
  /**当前作业后端数据 */
  backendData: any;
  /** 待批改可编辑状态 */
  toBeCorrectedEditable: boolean;
  /** 是否自动保存答题内容 */
  autoSaveAnswer: boolean;
  /** 作业是否达到截止时间 */
  isReachedDeadlineTime: boolean;
  /** 是否完成手势引导 */
  isFinishGestureGuide: boolean;
  /** 手动批改状态记录 - 记录每道题的批改状态 */
  manualCorrectionStatus: Record<string, ManualCorrectionStatus>;
}

interface InitHomeworkParams {
  homeworkId: string;
  answerType: HomeworkTaskType;
  questions: FrontQuestion[];
  batches?: BatchInfo[];
  status?: HomeworkStatus;
  elapsedTime?: number;
}

export const useQuestionSubManualStore = defineStore('question-sub-manual', {
  state: (): HomeworkState => ({
    homeworkId: '',
    answerType: HomeworkTaskType.ONLINE,
    questions: [] as FrontQuestion[],
    batches: [],
    currentBatchIndex: 0,
    questionNotes: {},
    currentQuestionIndex: 0,
    status: HomeworkStatus.TO_BE_SUBMITTED,
    elapsedTime: 0,
    showAnswer: false,
    backendData: {} as any,
    toBeCorrectedEditable: false,
    autoSaveAnswer: true,
    isReachedDeadlineTime: false,
    isFinishGestureGuide: false,
    manualCorrectionStatus: {},
  }),
  getters: {
    // 当前订正
    currentBatch(): BatchInfo | undefined {
      return this.batches[this.currentBatchIndex];
    },
    // 当前订正的用户答案
    userAnswers(): Record<string, FrontUserAnswer> {
      return this.currentBatch?.userAnswers || {};
    },
    // 最后一个订正用户回答数据
    lastBatchUserAnswers(): Record<string, FrontUserAnswer> {
      return this.batches[this.batches.length - 1]?.userAnswers || {};
    },
    // 当前订正是否只读
    readonly(): boolean {
      // 如果作业达到截止时间，
      // if (this.isReachedDeadlineTime) return true;

      // 如果显示答案，则始终为只读状态
      if (this.showAnswer || this.status == HomeworkStatus.COMPLETED) return true;

      // 最后一个订正才可以根据状态编辑
      if (this.currentBatchIndex == this.batches.length - 1) {
        // 如果是待批改 且 待批改可编辑状态为true，则可编辑
        if (this.status == HomeworkStatus.TO_BE_CORRECTED && this.toBeCorrectedEditable) {
          return false;
        }
        if (
          [
            HomeworkStatus.TO_BE_SUBMITTED,
            HomeworkStatus.NOT_SUBMITTED,
            HomeworkStatus.TO_BE_REVISED,
          ].includes(this.status)
        ) {
          return false;
        }
      }

      // 其他情况默认为只读状态
      return true;
    },
    /**是否为最后一个订正 */
    isLastBatch(): boolean {
      return this.currentBatchIndex === this.batches.length - 1;
    },
    /**已回答的题目数量 */
    answeredCount(): number {
      // 将 userAnswers 转换为数组格式，用于子题判断
      const allAnswers = Object.values(this.userAnswers);

      return this.questions.filter((q: any) =>
        isQuestionAnswered(q, this.userAnswers[q.id], allAnswers)
      ).length;
    },
    /**是否需要显示计时器 */
    shouldShowTimer(): boolean {
      // 只有在线作业且完成手势引导后才显示计时器
      const isShow =
        this.answerType === HomeworkTaskType.ONLINE &&
        this.isFinishGestureGuide &&
        (this.status === HomeworkStatus.TO_BE_SUBMITTED ||
          this.status === HomeworkStatus.TO_BE_REVISED ||
          this.status === HomeworkStatus.TO_BE_CORRECTED);
      // (this.status === HomeworkStatus.TO_BE_CORRECTED && this.toBeCorrectedEditable)
      return isShow;
    },
    /**是否可以编辑，与readonly正好相反 */
    canEdit(): boolean {
      // 直接返回readonly的取反，避免逻辑重复
      return !this.readonly;
    },

    // 是否显示解析
    showAnalysis(): boolean {
      // 如果 showAnswer 为 true，始终显示解析
      if (this.showAnswer) return true;
      return false;
      // // 暂时设置显示解析
      // return true;
      // // 根据作业状态判断是否显示解析
      // return [HomeworkStatus.COMPLETED, HomeworkStatus.TO_BE_REVISED].includes(
      //   this.status as HomeworkStatus
      // );
    },

    // 是否显示笔记
    showNote(): boolean {
      if (this.showAnswer) return true;
      return false;
    },

    // 递归查找题目
    getQuestion: state => (questionId: string) => {
      function findQuestion(questions: any[]): any | undefined {
        for (const q of questions) {
          if (q.id === questionId) return q;
          if (q.subQuestions && Array.isArray(q.subQuestions)) {
            const found = findQuestion(q.subQuestions);
            if (found) return found;
          }
        }
        return undefined;
      }
      return findQuestion(state.questions);
    },

    // 递归查找答案，兼容大题和 answer 对象下的子题
    getUserAnswer: state => (questionId: string) => {
      // const answer = state.batches[state.currentBatchIndex].userAnswers[questionId];
      // console.log('getUserAnswer', answer);
      // return answer;

      function findAnswer(questions: any[], userAnswers: any): any | undefined {
        for (const q of questions) {
          // 先查大题
          if (userAnswers[q.id]?.questionId === questionId) return userAnswers[q.id];
          // 再查子题（answer对象下的key）
          if (userAnswers[q.id]?.answer && typeof userAnswers[q.id].answer === 'object') {
            const subAnswer = userAnswers[q.id].answer[questionId];
            if (subAnswer) {
              // 返回一个模拟的 userAnswer 结构
              return { questionId, answer: subAnswer };
            }
          }
          // 递归查子题
          if (q.subQuestions && Array.isArray(q.subQuestions)) {
            const found = findAnswer(q.subQuestions, userAnswers[q.id]?.answer || {});
            if (found) return found;
          }
        }
        return undefined;
      }

      const batch = state.batches[state.currentBatchIndex];
      if (!batch) return undefined;
      return findAnswer(state.questions, batch.userAnswers || {});
    },

    // 获取当前订正可见的题目列表
    currentBatchQuestions(): FrontQuestion[] {
      let questions = this.questions;

      // 首次作业或没有指定questions字段(或为空数组)时展示全部题目
      if (!this.currentBatch?.questions || this.currentBatch.questions.length === 0) {
        // 深拷贝，避免污染原始数据
        questions = JSON.parse(JSON.stringify(questions));
      } else {
        // 订正只展示指定题目
        questions = JSON.parse(
          JSON.stringify(
            questions.filter((question: any) =>
              this.currentBatch!.questions!.some(
                (item: any) => String(item.questionId) === String(question.id)
              )
            )
          )
        );
      }

      // 递归替换 submitQuestion
      const replaceSubmitQuestion = (qList: any[]) => {
        for (const q of qList) {
          const userAnswer = this.userAnswers[q.id];
          if (userAnswer?.backendData?.question?.submitQuestion) {
            q.backendData.question.submitQuestion = userAnswer.backendData.question.submitQuestion;
          }
          if (q.subQuestions && Array.isArray(q.subQuestions)) {
            replaceSubmitQuestion(q.subQuestions);
          }
        }
      };
      replaceSubmitQuestion(questions);
      return questions;
    },

    // 获取订正可见的题目列表里当前正在做的题目数据
    currentUnderTakingQuestion(): FrontQuestion | null {
      const questions = this.currentBatchQuestions;
      if (this.currentQuestionIndex >= 0 && this.currentQuestionIndex < questions.length) {
        return questions[this.currentQuestionIndex] || null;
      }
      return null;
    },

    // 将用户答案转换为后端提交格式
    questionsRequestList(): any[] {
      return convertUserAnswersToRequestList(this.questions, this.lastBatchUserAnswers);
    },

    // 获取题目中所有的解答题
    objectiveQuestions(): any[] {
      return this.questions.filter(
        (item: any) => item.backendData?.question?.answerType === AnswerType.ANSWER
      );
    },

    /**是否禁用提交按钮 */
    isSubmitDisabled(): boolean {
      // 纸质作业
      if (this.answerType === HomeworkTaskType.PAPER) {
        // 判断当前用户回答的附件数量是否为 0
        const files = this.batches[this.batches.length - 1]?.files || [];
        return files.length == 0;
      }
      // 在线作业
      if (this.answerType === HomeworkTaskType.ONLINE) {
        if (this.isLastBatch) {
          return this.answeredCount == 0;
        } else {
          return true;
        }
      }
      // 写作任务
      if (this.answerType === HomeworkTaskType.WRITING) {
        const lastBatchUserAnswers = this.lastBatchUserAnswers;
        const values: any = Object.values(lastBatchUserAnswers);
        if (values.length > 0) {
          const files = values[0]?.answer?.files || [];
          return files.length == 0;
        }
        return false;
      }

      return false;
    },

    // 手动批改相关 getters
    /** 获取当前题目的批改状态 */
    getCurrentQuestionCorrectionStatus(): ManualCorrectionStatus {
      const currentQuestion = this.currentUnderTakingQuestion;
      if (!currentQuestion) return ManualCorrectionStatus.UNCORRECTED;
      return this.manualCorrectionStatus[currentQuestion.id] || ManualCorrectionStatus.UNCORRECTED;
    },

    /** 获取已批改的题目数量 */
    getCorrectedCount(): number {
      return Object.values(this.manualCorrectionStatus).filter(
        status => status !== ManualCorrectionStatus.UNCORRECTED
      ).length;
    },

    /** 获取做对的题目数量 */
    getCorrectCount(): number {
      return Object.values(this.manualCorrectionStatus).filter(
        status => status === ManualCorrectionStatus.CORRECT
      ).length;
    },

    /** 获取做错的题目数量 */
    getIncorrectCount(): number {
      return Object.values(this.manualCorrectionStatus).filter(
        status => status === ManualCorrectionStatus.INCORRECT
      ).length;
    },
  },
  actions: {
    // 初始化当前作业
    initHomework({ homeworkId, answerType, questions, batches = [] }: InitHomeworkParams) {
      this.homeworkId = homeworkId;
      this.answerType = answerType;
      this.questions = questions;

      // 获取后端数据中的作业状态
      this.status = this.backendData.status;
      // 判断是否达到截止时间
      this.isReachedDeadlineTime = dayjs(this.backendData.deadlineTime).isBefore(dayjs());

      // 根据后端数据初始化做题时间
      if (this.backendData.submitList && this.backendData.submitList.length > 0) {
        this.elapsedTime =
          this.backendData.submitList[this.backendData.submitList.length - 1].timeSecond;
      } else {
        this.elapsedTime = 0;
      }

      // 是否开启批改后，答案对学生可见：0-关闭；1-开启
      this.showAnswer =
        this.backendData.status === HomeworkStatus.COMPLETED &&
        this.backendData.enableAnswerVisible === 1;
      // this.showAnswer = true;

      // 如果没有提供订正，创建一个默认订正
      if (!batches.length) {
        this.batches = [
          {
            id: 'default',
            name: '首次',
            questions: [],
            userAnswers: {},
            // readonly: false,
          },
        ];
      } else {
        this.batches = batches;
      }

      this.currentBatchIndex = this.batches.length - 1; // 默认选中最后一个订正
      this.questionNotes = {}; // 清空笔记
      this.currentQuestionIndex = 0;
    },
    setHomeworkId(id: string) {
      this.homeworkId = id;
    },
    setQuestions(questions: FrontQuestion[]) {
      this.questions = questions;
    },
    setStatus(status: HomeworkStatus) {
      this.status = status;
    },
    /**更新累计做题时间 */
    updateElapsedTime(seconds: number) {
      this.elapsedTime = seconds;
    },
    /**添加做题时间 */
    addElapsedTime(seconds: number) {
      this.elapsedTime += seconds;
    },
    /**添加新订正 */
    addBatch(batchInfo: Omit<BatchInfo, 'userAnswers'>) {
      const newBatch: BatchInfo = {
        ...batchInfo,
        userAnswers: {},
      };
      this.batches.push(newBatch);
      this.currentBatchIndex = this.batches.length - 1;
    },
    /**切换订正 */
    setBatchIndex(index: number) {
      if (index >= 0 && index < this.batches.length) {
        this.currentBatchIndex = index;

        // 获取当前订正的题目
        const batch = this.batches[index];

        // 如果是有特定题目的订正，跳转到第一个题目
        if (batch.questions && batch.questions.length > 0) {
          // 查找第一个题目在原始题目列表中的索引
          const firstQuestionId = batch.questions[0].questionId;
          const questionIndex = this.questions.findIndex(
            (q: any) => String(q.id) === String(firstQuestionId)
          );

          if (questionIndex !== -1) {
            this.currentQuestionIndex = questionIndex;
          }
        } else {
          // 如果没有指定题目，默认从第一题开始
          this.currentQuestionIndex = 0;
        }
      }
    },
    /**递归更新答案，兼容大题和子题 id */
    setUserAnswer(questionId: string, answer: any) {
      function updateAnswer(questions: any[], userAnswers: any): boolean {
        for (const q of questions) {
          if (q.id === questionId) {
            // 命中大题，直接写
            userAnswers[q.id] = { questionId, answer };
            return true;
          }
          if (q.subQuestions && Array.isArray(q.subQuestions)) {
            // 查找子题
            const sub = q.subQuestions.find((sq: any) => sq.id === questionId);
            if (sub) {
              // 命中子题，写到父题的 answer 下
              if (!userAnswers[q.id]) userAnswers[q.id] = { questionId: q.id, answer: {} };
              userAnswers[q.id].answer[questionId] = answer;
              return true;
            }
            // 递归查找更深层
            if (!userAnswers[q.id]) userAnswers[q.id] = { questionId: q.id, answer: {} };
            const updated = updateAnswer(q.subQuestions, userAnswers[q.id].answer);
            if (updated) return true;
          }
        }
        return false;
      }

      const batch = this.currentBatch;
      if (batch) {
        updateAnswer(this.questions, batch.userAnswers);
      }
    },
    /**清空当前订正的用户答案 */
    async clearUserAnswers() {
      await resetHomeworkAnswerRecord({ id: this.currentBatch!.id });
      if (this.currentBatch) {
        this.currentBatch.userAnswers = {};
        this.currentBatch.files = [];
      }
    },
    setCurrentQuestionIndex(index: number) {
      this.currentQuestionIndex = index;
    },
    /**获取题目笔记 */
    getQuestionNote(questionId: string): QuestionNote | null {
      return this.questionNotes[questionId] || null;
    },
    /**设置题目笔记 */
    setQuestionNote(questionId: string, note: QuestionNote) {
      this.questionNotes[questionId] = note;
    },
    /**清空题目笔记 */
    clearQuestionNotes() {
      this.questionNotes = {};
    },
    /**设置是否显示答案和解析 */
    setShowAnswer(show: boolean) {
      this.showAnswer = show;
    },
    setBackendData(data: any) {
      this.backendData = data;

      // 根据后端数据重新初始化 toBeCorrectedEditable 状态
      // 当状态为 TO_BE_CORRECTED 时，默认不可编辑
      if (data.status === HomeworkStatus.TO_BE_CORRECTED) {
        this.toBeCorrectedEditable = false;
      }
    },
    setToBeCorrectedEditable(editable: boolean) {
      this.toBeCorrectedEditable = editable;
    },
    setAutoSaveAnswer(autoSave: boolean) {
      this.autoSaveAnswer = autoSave;
    },
    /**
     * 设置当前订正的附件内容
     * @param files 附件内容，数组
     */
    setCurrentBatchFiles(files: any[]) {
      if (this.currentBatch) {
        this.currentBatch.files = files;
      }
    },
    /**
     * 保存作业答题内容
     * @param options 保存选项
     * @param options.status 状态：0-答题更新，1-提交作业
     * @returns Promise<{success: boolean, data?: any, error?: string}> 保存结果
     */
    async saveHomework(options: {
      status: 0 | 1;
    }): Promise<{ success: boolean; data?: any; error?: string }> {
      try {
        // 构建提交数据
        const submitData: any = {
          id: '',
          homeworkStudentId: this.backendData.id,
          timeSecond: this.elapsedTime,
          status: options.status, // 状态：0-待提交，1-已提交
          // reviseCount: 0, // 订正次数
          // isRevise: 0, // 是否订正
        };

        // // 如果存在submitList记录，则使用submitList记录的reviseCount和isRevise
        // if (this.backendData.submitList.length > 0) {
        //   const lastSubmit = this.backendData.submitList[this.backendData.submitList.length - 1];
        //   submitData.reviseCount = lastSubmit.reviseCount;
        //   submitData.isRevise = lastSubmit.isRevise;
        // }

        // 在线作业
        if (this.answerType === HomeworkTaskType.ONLINE) {
          submitData.questionsRequestList = convertUserAnswersToRequestList(
            this.questions,
            this.lastBatchUserAnswers
          );
        }
        // 纸质作业
        if (this.answerType === HomeworkTaskType.PAPER) {
          // 获取最后一个订正的附件
          submitData.paperFiles = this.batches[this.batches.length - 1]?.files?.map(
            (item: any, index: number) => ({
              id: item.id,
              fileKey: item.fileKey,
              sort: index + 1, // 对附件进行排序
            })
          );
          submitData.questionsRequestList = [];
        }
        // 写作作业
        if (this.answerType === HomeworkTaskType.WRITING) {
          const questionsRequestList = this.questionsRequestList;
          questionsRequestList.forEach((item: any) => {
            item.files = item.files.map((file: any, index: number) => ({
              ...file,
              sort: index + 1, // 对附件进行排序
            }));
          });
          submitData.questionsRequestList = questionsRequestList;
        }

        // 如果存在submitList记录，则使用submitList记录的id
        if (this.backendData.submitList && this.backendData.submitList.length > 0) {
          submitData.id = this.backendData.submitList[this.backendData.submitList.length - 1].id;
        }

        console.log('saveHomework', submitData);

        const res = await submitHomework(submitData);
        console.log('保存成功', res);

        return {
          success: true,
          data: res,
        };
      } catch (error) {
        console.error('保存作业失败:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : '保存失败',
        };
      }
    },
    /**根据后端问题 id 查询前端问题 id */
    findFrontQuestionIdByBackendId(backendQuestionId: string): string {
      const question = this.questions.find(
        (q: any) => q.backendData?.question?.id == backendQuestionId
      );
      return question?.id || '';
    },
    /**设置作业是否达到截止时间 */
    setIsReachedDeadlineTime(isReached: boolean) {
      this.isReachedDeadlineTime = isReached;
    },
    /**设置是否完成手势引导 */
    setIsFinishGestureGuide(isFinish: boolean) {
      this.isFinishGestureGuide = isFinish;
    },
    /**清空所有数据 */
    clearAll() {
      // 退出答题界面时调用
      this.homeworkId = '';
      this.answerType = HomeworkTaskType.ONLINE;
      this.questions = [];
      this.batches = [];
      this.currentBatchIndex = 0;
      this.questionNotes = {}; // 清空笔记
      this.currentQuestionIndex = 0;
      this.elapsedTime = 0;
      this.status = HomeworkStatus.TO_BE_SUBMITTED;
      this.showAnswer = false;
      this.backendData = {};
      this.autoSaveAnswer = true;
      this.toBeCorrectedEditable = false;
      this.showAnswer = false;
      this.manualCorrectionStatus = {};
    },

    // 手动批改相关 actions
    /** 设置题目批改状态 */
    setQuestionCorrectionStatus(questionId: string, status: ManualCorrectionStatus) {
      this.manualCorrectionStatus[questionId] = status;

      // 获取题目信息
      const question = this.getQuestion(questionId);

      if (question?.subQuestions && question.subQuestions.length > 0) {
        // 复合题型：处理所有简答题子题
        const shortAnswerSubQuestions = question.subQuestions.filter(
          (subQ: any) => subQ.type === QuestionType.SHORT_ANSWER
        );

        for (const subQuestion of shortAnswerSubQuestions) {
          // 为每个简答题子题设置状态
          this.setSubQuestionCorrectionStatus(questionId, subQuestion.id, status);
        }
      } else {
        // 单题：直接设置
        const userAnswer = this.getUserAnswer(questionId);
        let answerContent = userAnswer?.answer || { files: [] };

        // 确保答案是对象格式
        if (typeof answerContent !== 'object' || answerContent === null) {
          answerContent = { files: [] };
        }

        // 设置 correct 字段
        answerContent.correct = status === ManualCorrectionStatus.CORRECT;

        // 更新用户答案
        this.setUserAnswer(questionId, answerContent);
      }
    },

    /** 设置复合题型中子题的批改状态 */
    setSubQuestionCorrectionStatus(
      parentQuestionId: string,
      subQuestionId: string,
      status: ManualCorrectionStatus
    ) {
      // 获取父题目的用户答案
      const parentUserAnswer = this.getUserAnswer(parentQuestionId);

      // 确保父题目有答案结构
      if (!parentUserAnswer) {
        // 如果父题目没有答案，创建一个基本结构
        this.setUserAnswer(parentQuestionId, {});
      }

      // 获取或创建子题答案
      const currentParentAnswer = this.getUserAnswer(parentQuestionId);
      let parentAnswerContent = currentParentAnswer?.answer || {};

      // 确保父答案是对象格式
      if (typeof parentAnswerContent !== 'object' || parentAnswerContent === null) {
        parentAnswerContent = {};
      }

      // 获取或创建子题答案
      let subQuestionAnswer = parentAnswerContent[subQuestionId] || { files: [] };

      // 确保子题答案是对象格式
      if (typeof subQuestionAnswer !== 'object' || subQuestionAnswer === null) {
        subQuestionAnswer = { files: [] };
      }

      // 设置子题的 correct 字段
      subQuestionAnswer.correct = status === ManualCorrectionStatus.CORRECT;

      // 更新子题答案到父题目的答案结构中
      parentAnswerContent[subQuestionId] = subQuestionAnswer;

      // 更新父题目的答案
      this.setUserAnswer(parentQuestionId, parentAnswerContent);
    },

    /** 重置题目批改状态 */
    resetQuestionCorrectionStatus(questionId: string) {
      delete this.manualCorrectionStatus[questionId];

      // 获取题目信息
      const question = this.getQuestion(questionId);

      if (question?.subQuestions && question.subQuestions.length > 0) {
        // 复合题型：重置所有简答题子题的状态
        const shortAnswerSubQuestions = question.subQuestions.filter(
          (subQ: any) => subQ.type === QuestionType.SHORT_ANSWER
        );

        for (const subQuestion of shortAnswerSubQuestions) {
          // 重置每个简答题子题的状态
          this.resetSubQuestionCorrectionStatus(questionId, subQuestion.id);
        }
      } else {
        // 单题：直接重置
        const userAnswer = this.getUserAnswer(questionId);
        if (userAnswer?.answer && typeof userAnswer.answer === 'object') {
          const answerContent = { ...userAnswer.answer };
          delete answerContent.correct;
          this.setUserAnswer(questionId, answerContent);
        }
      }
    },

    /** 重置复合题型中子题的批改状态 */
    resetSubQuestionCorrectionStatus(parentQuestionId: string, subQuestionId: string) {
      // 获取父题目的用户答案
      const parentUserAnswer = this.getUserAnswer(parentQuestionId);

      if (parentUserAnswer?.answer && typeof parentUserAnswer.answer === 'object') {
        const parentAnswerContent = { ...parentUserAnswer.answer };

        // 如果子题答案存在，清除其 correct 字段
        if (
          parentAnswerContent[subQuestionId] &&
          typeof parentAnswerContent[subQuestionId] === 'object'
        ) {
          const subQuestionAnswer = { ...parentAnswerContent[subQuestionId] };
          delete subQuestionAnswer.correct;
          parentAnswerContent[subQuestionId] = subQuestionAnswer;

          // 更新父题目的答案
          this.setUserAnswer(parentQuestionId, parentAnswerContent);
        }
      }
    },

    /** 设置当前题目批改状态 */
    setCurrentQuestionCorrectionStatus(status: ManualCorrectionStatus) {
      const currentQuestion = this.currentUnderTakingQuestion;
      if (currentQuestion) {
        this.setQuestionCorrectionStatus(currentQuestion.id, status);
      }
    },

    /** 重置当前题目批改状态 */
    resetCurrentQuestionCorrectionStatus() {
      const currentQuestion = this.currentUnderTakingQuestion;
      if (currentQuestion) {
        this.resetQuestionCorrectionStatus(currentQuestion.id);
      }
    },
  },
});
