import type { TabEnum } from '@/constants';
import { defineStore } from 'pinia';
import { nextTick } from 'vue';
import { RouteConfigManager, type UserMode, type TabBarItem } from '@/router';

// 定义Tabbar项的类型
export interface TabItem {
  pagePath: string;
  text: string;
  iconPath: string;
  selectedIconPath: string;
}

export const useTabStore = defineStore('tab', {
  state: () => ({
    activeTabIndex: 0,
    lastTabIndex: 0,
    showTabBar: true,
    isTabSwitching: false, // 控制是否在切换过程中
    tabItems: [] as TabItem[], // 动态配置，初始为空
    currentUserMode: null as UserMode | null,
  }),

  getters: {
    getCurrentTab: state => state.tabItems[state.activeTabIndex],
  },

  actions: {
    // 初始化TabBar配置
    initializeTabBar(userMode?: UserMode) {
      console.log(`[TabStore] 初始化TabBar配置: ${userMode}`);

      if (!userMode) {
        userMode = uni.getStorageSync('userMode');
      }

      if (!userMode) {
        return uni.reLaunch({
          url: '/pages/mode-entry/index',
        });
      }

      this.currentUserMode = userMode;
      const tabBarConfig = RouteConfigManager.getTabBarConfig(userMode);

      this.tabItems = tabBarConfig.map(item => ({
        pagePath: item.pagePath,
        text: item.text,
        iconPath: item.iconPath,
        selectedIconPath: item.selectedIconPath,
      }));

      // 重置激活索引
      this.activeTabIndex = 0;
      this.lastTabIndex = 0;

      console.log(`[TabStore] TabBar配置已更新，共${this.tabItems.length}个标签`);
    },

    // 切换到指定tab - 瞬间切换无延迟
    switchTab(index: TabEnum) {
      if (index === this.activeTabIndex || this.isTabSwitching) return;

      console.log(`[TabStore] 切换Tab: ${this.activeTabIndex} -> ${index}`);
      // 防止连续快速切换的保护
      this.isTabSwitching = true;
      this.lastTabIndex = this.activeTabIndex;
      this.activeTabIndex = index;

      // 使用Vue的nextTick实现极短延时后重置切换状态
      // 不会影响切换效果，仅用于防止高频点击
      nextTick(() => {
        this.isTabSwitching = false;
      });
    },

    // 根据路径查找tab索引
    findTabIndexByPath(path: string) {
      console.log(`[TabStore] 根据路径查找Tab: ${path}`);
      return this.tabItems.findIndex(item => {
        const itemPath = item.pagePath.startsWith('/') ? item.pagePath : `/${item.pagePath}`;
        const isMatch = path === itemPath || path.startsWith(itemPath);
        if (isMatch) {
          console.log(`[TabStore] 找到匹配的Tab索引: ${this.tabItems.indexOf(item)}`);
        }
        return isMatch;
      });
    },

    // 根据路径自动切换Tab
    switchTabByPath(path: string) {
      const tabIndex = this.findTabIndexByPath(path);
      if (tabIndex !== -1) {
        this.switchTab(tabIndex);
        return true;
      }
      return false;
    },

    // 显示TabBar
    showTabBarAction() {
      console.log('[TabStore] 显示TabBar');
      this.showTabBar = true;
    },

    // 隐藏TabBar
    hideTabBarAction() {
      console.log('[TabStore] 隐藏TabBar');
      this.showTabBar = false;
    },
    // 跳转首页tab
    goHome() {
      this.switchTab(0);
      uni.navigateTo({
        url: '/pages/index/index',
      });
    },
    // 跳转应用中心tab
    goAppCenter() {
      this.switchTab(1);
      uni.navigateTo({
        url: '/pages/index/index',
      });
    },
    // 跳转空间tab
    goSpace() {
      this.switchTab(2);
      uni.navigateTo({
        url: '/pages/index/index',
      });
    },
    // 跳转个人tab
    goMy() {
      this.switchTab(3);
      uni.navigateTo({
        url: '/pages/index/index',
      });
    },
  },
});
