import { defineStore } from 'pinia';
import { ref } from 'vue';
import {
  deleteClientApp,
  getOtherAppList,
  getTenantSceneList,
  rmCommonApp,
  setCommonApp,
} from '@/api/app-center';
import type { AppListItemType } from '@/types/api/app-center';
import { useAppStore } from './useAppStore';

export enum DataSource {
  /** 专属 */
  Tenant = 1,
  /** 官方 */
  Offical = 2,
  /** 个人 */
  Personal = 3,
}

export const useAppCenterStore = defineStore('appCenter', () => {
  // 状态变量
  const scenesList = ref<any[]>([]);
  const otherApps = ref<any[]>([]);
  const combinedList = ref<{ name: any; sceneId: any; apps: AppListItemType[] }[]>([]);
  const currentTab = ref(0);
  const loading = ref(false);
  const openedStates = ref<Record<string, 'left' | 'right' | 'none'>>({});
  const deleteStates = ref<Record<string, boolean>>({});
  const processingIds = ref<Record<string, boolean>>({});

  // 获取应用store实例
  const appStore = useAppStore();

  // 获取租户场景列表
  const fetchGetTenantSceneList = async () => {
    try {
      const res = await getTenantSceneList();
      scenesList.value = res;
      return res;
    } catch (error) {
      console.error('获取租户场景列表失败:', error);
      throw error;
    }
  };

  // 获取其他应用列表
  const fetchGetOtherAppList = async () => {
    try {
      const res = await getOtherAppList();
      console.log('获取其他应用', res);
      otherApps.value = res;
      return res;
    } catch (error) {
      console.error('获取其他应用列表失败:', error);
      throw error;
    }
  };

  // 加载我的应用并组合数据
  const fetchLoadMyApps = async () => {
    // 如果已有操作正在进行中，则不显示全局loading
    const hasProcessingItem = Object.values(processingIds.value).some(status => status);

    // 只有当没有单独操作正在处理时，才显示全局loading
    if (!hasProcessingItem) {
      loading.value = true;
    }

    const startTime = Date.now();

    try {
      // 强制重新加载应用数据
      const myApps = await appStore.loadMyApps(true);

      // 重新组合数据
      const newCombinedList = [
        {
          name: '我的应用',
          sceneId: 'personage',
          apps: myApps.filter(({ source }) => source === DataSource.Personal),
        },
        ...scenesList.value.map((scene: any) => {
          const apps = myApps.filter((item: any) =>
            item.labelList?.some((it: any) => String(it.tenantSceneId) === String(scene.id))
          );
          if (scene.name === 'AI评价' && otherApps.value.length > 0) {
            apps.push(...otherApps.value);
          }
          return {
            name: scene.name,
            sceneId: scene.id,
            apps,
          };
        }),
      ];

      // 更新组合列表
      combinedList.value = newCombinedList;
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log('combinedList', combinedList.value);
      console.log('应用列表---请求时间', duration);

      return combinedList.value;
    } catch (error) {
      console.error('加载应用列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 设为常用应用
  const handleSetCommonApp = async (id: string) => {
    processingIds.value[id] = true;

    try {
      await setCommonApp({ id });
      // 重新加载应用列表
      await fetchLoadMyApps();
      return { success: true, message: '已添加至首页-我的常用' };
    } catch (error) {
      console.error('设置常用应用失败:', error);
      throw error;
    } finally {
      processingIds.value[id] = false;
    }
  };

  // 移除常用应用
  const handleRemoveCommonApp = async (id: string) => {
    processingIds.value[id] = true;

    try {
      await rmCommonApp({ id });
      // 重新加载应用列表
      await fetchLoadMyApps();
      return { success: true, message: '已从首页-我的常用中移除' };
    } catch (error) {
      console.error('移除常用应用失败:', error);
      throw error;
    } finally {
      processingIds.value[id] = false;
    }
  };

  // 删除应用
  const handleDeleteApp = async (id: string, tmbId: string) => {
    try {
      await deleteClientApp({ id, tmbId });

      // 删除后关闭所有滑动状态
      Object.keys(openedStates.value).forEach(key => {
        openedStates.value[key] = 'none';
      });
      Object.keys(deleteStates.value).forEach(key => {
        deleteStates.value[key] = false;
      });

      // 重新加载应用列表
      await fetchLoadMyApps();

      return { success: true, message: '删除成功' };
    } catch (error) {
      console.error('删除应用失败:', error);
      throw error;
    }
  };

  // 初始化应用中心数据
  const initializeAppCenter = async () => {
    try {
      // 并行获取场景列表和其他应用列表
      await Promise.all([fetchGetTenantSceneList(), fetchGetOtherAppList()]);

      // 然后加载应用列表
      await fetchLoadMyApps();

      return { success: true };
    } catch (error) {
      console.error('初始化应用中心数据失败:', error);
      throw error;
    }
  };

  // 更新滑动状态
  const updateSwipeState = (id: string, status: 'left' | 'right' | 'none') => {
    openedStates.value[id] = status;

    // 如果关闭了滑动，重置删除确认状态
    if (status === 'none') {
      deleteStates.value[id] = false;
    }
  };

  // 更新删除状态
  const updateDeleteState = (id: string, state: boolean) => {
    deleteStates.value[id] = state;
  };

  // 更新当前tab
  const updateCurrentTab = (tabIndex: number) => {
    currentTab.value = tabIndex;
  };

  return {
    // 状态
    scenesList,
    otherApps,
    combinedList,
    currentTab,
    loading,
    openedStates,
    deleteStates,
    processingIds,

    // 方法
    fetchGetTenantSceneList,
    fetchGetOtherAppList,
    fetchLoadMyApps,
    handleSetCommonApp,
    handleRemoveCommonApp,
    handleDeleteApp,
    initializeAppCenter,
    updateSwipeState,
    updateDeleteState,
    updateCurrentTab,
  };
});
