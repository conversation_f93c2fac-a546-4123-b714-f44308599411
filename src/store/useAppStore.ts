import { getMyAppList } from '@/api/app-center';
import { getStuAppList } from '@/api/students/center';
import type { AppListItemType, AppListParams } from '@/types/api/app-center';
import { defineStore } from 'pinia';

interface AppStoreState {
  myApps: AppListItemType[];
  viewApp?: AppListItemType;
  stuApps: AppListItemType[];
}

// 这里可以根据实际业务需求定义你的状态类型
export const useAppStore = defineStore('appStore', {
  state: (): AppStoreState => ({
    myApps: [] as AppListItemType[],
    stuApps: [] as AppListItemType[],
  }),
  getters: {},
  actions: {
    async loadMyApps(init = true, data?: AppListParams) {
      if (this.myApps.length > 0 && !init) return this.myApps;
      const res = await getMyAppList(data ?? {});
      this.myApps = res;
      if (this.viewApp) {
        this.viewApp = res.find(it => it.id == this.viewApp?.id);
      }
      return res;
    },
    async loadStuApps(data?: AppListParams) {
      const res = await getStuAppList(data ?? {});
      this.stuApps = res;
      console.log('this.stuApps', this.stuApps);
      return res;
    },
  },
});
