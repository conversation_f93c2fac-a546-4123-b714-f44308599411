<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, onUnmounted, watch, nextTick } from 'vue';
import dayjs from '@/utils/day';
import { useUserStore } from '@/store/userStore';
import ai from '@/common/ai';
import { defaultAppTemplates, simpleTemplate } from '@/fastgpt/web/core/app/templates';
import { createApp } from '@/api/app-center';
import { AppTypeEnum, ModeTypeEnum } from '@/constants/api/app';
import { onHide, onBackPress } from '@dcloudio/uni-app';
import { useAppCenterStore } from '@/store/useAppCenterStore';
// 应用描述字符
const appDescribe = ref('');
const uToastRef = ref(null);
const aiProgressText = ref('AI正在生成应用名称、介绍');
const createAppLoading = ref(false);
let signal = ai.AbortSignal();
// 记录页面原始位置
const scrollTop = ref(0);
// 控制背景是否可滚动
const disableScroll = ref(false);
// 记录触摸起始位置，用于阻止橡皮筋效果
const touchStartY = ref(0);

const appCenterStore = useAppCenterStore();

// 监听返回按钮事件
onBackPress(event => {
  // 如果弹窗正在显示，优先处理取消创建逻辑
  if (createAppLoading.value) {
    cancelCreateApp();
    // 返回 true 代表事件已经被处理，阻止默认返回行为
    return true;
  }
  // 返回 false 表示继续执行默认返回行为
  return false;
});

onUnmounted(() => {
  signal.abort();
  // 确保页面恢复滚动状态
  disableScroll.value = false;
});

// 阻止页面滚动的函数
function moveHandle(e?: TouchEvent) {
  if (e && (disableScroll.value || createAppLoading.value)) {
    e.preventDefault();
    e.stopPropagation();
    return false;
  }
}

// 监听弹窗状态变化，控制页面滚动
watch(createAppLoading, newVal => {
  disableScroll.value = newVal;

  if (newVal) {
    // 记录当前滚动位置
    scrollTop.value =
      window?.pageYOffset || document?.documentElement?.scrollTop || document?.body?.scrollTop || 0;

    // 弹窗打开时，禁用页面滚动
    // 使用uni-app提供的API禁用页面滚动，适用于所有平台
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });

    // 针对H5平台
    if (typeof document !== 'undefined' && document.body) {
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.top = `-${scrollTop.value}px`;
      document.body.style.left = '0';
    }

    // 针对App平台（iOS和Android）
    const systemInfo = uni.getSystemInfoSync();
    if (systemInfo.platform === 'android' || systemInfo.platform === 'ios') {
      // 安卓和iOS的特殊处理
      uni.hideKeyboard(); // 隐藏键盘，防止键盘事件干扰

      // 使用plus API（如果在App环境中）
      // @ts-ignore
      if (typeof window !== 'undefined' && window.plus) {
        // @ts-ignore
        plus.webview.currentWebview().setStyle({
          bounce: 'none',
          scrollIndicator: 'none',
        });

        // 针对Android特殊处理
        if (systemInfo.platform === 'android') {
          // @ts-ignore
          plus.android.importClass('android.view.WindowManager');
          // @ts-ignore
          const main = plus.android.runtimeMainActivity();
          // @ts-ignore
          const windowManager = main.getWindowManager();
          // @ts-ignore
          windowManager.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        }
      }

      // 添加全局事件捕获
      nextTick(() => {
        document.addEventListener('touchmove', moveHandle, { passive: false });
      });
    }
  } else {
    // 弹窗关闭时，恢复页面滚动
    // 针对H5平台
    if (typeof document !== 'undefined' && document.body) {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
      document.body.style.left = '';
      // 恢复滚动位置
      window.scrollTo(0, scrollTop.value);
    }

    // 针对App平台（iOS和Android）
    // @ts-ignore
    if (typeof window !== 'undefined' && window.plus) {
      // @ts-ignore
      plus.webview.currentWebview().setStyle({
        bounce: 'vertical',
        scrollIndicator: 'none',
      });

      // 恢复Android设置
      const systemInfo = uni.getSystemInfoSync();
      if (systemInfo.platform === 'android') {
        try {
          // @ts-ignore
          plus.android.importClass('android.view.WindowManager');
          // @ts-ignore
          const main = plus.android.runtimeMainActivity();
          // @ts-ignore
          const windowManager = main.getWindowManager();
          // @ts-ignore
          windowManager.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        } catch (e) {
          console.error('恢复Android设置失败', e);
        }
      }
    }

    // 移除全局事件捕获
    document.removeEventListener('touchmove', moveHandle);
  }
});

// 注册全局触摸事件
onMounted(() => {
  // 确保在页面加载后添加事件监听
  document.addEventListener('touchmove', moveHandle, { passive: false });
});

onBeforeUnmount(() => {
  // 确保在页面卸载前移除事件监听
  document.removeEventListener('touchmove', moveHandle);
});

function showToast(params: any) {
  if (uToastRef.value) {
    (uToastRef.value as any).show({
      ...params,
      complete() {
        if (params.url) {
          uni.navigateTo({
            url: params.url,
          });
        }
      },
    });
  }
}

async function hangleAICreatedApplition() {
  createAppLoading.value = true;
  try {
    const result = await ai.fetch({
      url: '/huayun-ai/client/chat/once',
      data: {
        messages: [
          {
            role: 'user',
            content: JSON.stringify({
              creation_requirement: appDescribe.value,
            }),
          },
        ],
        stream: true,
        detail: true,
        type: 8,
        variables: {
          cTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        },
      },
      onMessage: message => {
        console.log('onMessage', message);
        if (message.event === 'flowNodeStatus') {
          aiProgressText.value = message.name || '';
        }
      },
      abortSignal: signal,
    });
    return result;
  } catch (error) {
    console.error('chatOnce请求初始化错误:', error);
  } finally {
    createAppLoading.value = false;
    aiProgressText.value = 'AI正在生成应用名称、介绍';
  }
}

async function handleCreateApp() {
  if (appDescribe.value.trim().length === 4) {
    showToast({
      type: 'info',
      message: '创建应用描述不得少于4个字',
    });
    return;
  }

  // 重新初始化signal对象
  signal = ai.AbortSignal();
  moveHandle();
  try {
    const result = await hangleAICreatedApplition();
    // 如果result为undefined，则不进行创建
    if (result === undefined) return;
    const aiParmas = result ? JSON?.parse(result?.responseText) : {};
    console.log('aiParmas', aiParmas);

    // 创建应用参数
    const createAppParams = {
      avatarUrl: aiParmas?.avatar || '',
      name: aiParmas?.name || '',
      intro: aiParmas?.intro || '',
      type: AppTypeEnum.simple,
      mode: ModeTypeEnum.simple,
      isAICreated: 1,
      edges: simpleTemplate.edges || [],
      modules: simpleTemplate.modules || [],
      chatConfig: {
        fileSelectConfig: {
          maxFiles: 20,
          canSelectFile: true,
          canSelectImg: true,
          canAutoParse: false,
          canParseORC: false,
        },
      },
    };
    createApp(createAppParams).then((res: any) => {
      console.log('创建成功APP', res);
      // 创建成功跳转页面,传递数据过去
      uni.navigateTo({
        url: `/pages-subpackages/app-center-pkg/create-app-success/index?id=${res.id}`,
        success: () => {
          console.log('createAppSuccess跳转成功', res);
        },
        complete: () => {
          appCenterStore.fetchLoadMyApps();
        },
      });
    });
  } catch (error) {
    console.error('chatOnce请求初始化错误:', error);
  } finally {
  }
}

function cancelCreateApp() {
  createAppLoading.value = false;
  signal.abort();
  console.log('取消创建应用');
}

// TODO: 测试代码跳转成功页面
function handleGotoSuccess() {
  uni.navigateTo({
    url: '/pages-subpackages/app-center-pkg/create-app-success/index',
  });
}
const exampleText =
  '你是语文作文批改助手，专注于语文作文批改，能从语法、结构、文采等多方面给出详细评语和改进建议';
// 复制文本到剪切板
function copyTextToClipboard(text: string) {
  uni.setClipboardData({
    data: text,
    showToast: false,
    success: () => {
      showToast({
        type: 'success',
        message: '内容复制成功',
      });
    },
    fail: () => {
      showToast({
        type: 'info',
        message: '复制内容失败',
      });
    },
  });
}
</script>

<template>
  <LkToast ref="uToastRef"></LkToast>

  <up-modal
    class="create-app-modal"
    :show="createAppLoading"
    :showConfirmButton="false"
    :maskClickable="false"
    :zoom="false"
    :closeOnClickOverlay="false"
    @clickMask="() => {}"
  >
    <view ref="animationContainer" class="modal-content" @touchmove.prevent.stop="moveHandle">
      <!-- <image
        class="lottie-image"
        src="https://huayun-ai-obs-public.huayuntiantu.com/a05492879292970f7374926e54d72eb0.gif"
        mode="scaleToFill"
      /> -->
      <c-lottie
        class="lottie-image"
        ref="cLottieRef"
        src="https://huayun-ai-obs-public.huayuntiantu.com/cb807f326d83d36d7f894d67017ec6d9.json"
        :loop="true"
        renderer="svg"
        width="264rpx"
        height="230rpx"
      ></c-lottie>
      <view class="lottie-text">{{ aiProgressText }}...</view>
      <LkButton
        class="lottie-button"
        type="primary"
        size="large"
        shape="round"
        @click="cancelCreateApp"
        >取消创建</LkButton
      >
    </view>
  </up-modal>

  <view
    class="container"
    :class="{ 'no-scroll': disableScroll }"
    @touchmove.prevent.stop="moveHandle"
  >
    <u-navbar :autoBack="true" bgColor="#f6f6fc" border placeholder height="88rpx">
      <template #center>
        <text class="navbar-title">AI创建应用</text>
      </template>
    </u-navbar>
    <view class="tips">
      <view class="description">
        💡
        <text class="description-text"> 示例： </text>
        <text :selectable="true" :user-select="true" @click="copyTextToClipboard(exampleText)">
          {{ exampleText }}
        </text>
      </view>
    </view>

    <view class="app-describe">
      <view class="app-describe-title">应用描述 <text class="required-text">*</text></view>
      <view class="app-describe-content">
        <up-textarea
          class="app-describe-textarea"
          v-model="appDescribe"
          placeholder="请输入内容"
          count
          maxlength="500"
          autoHeight
        ></up-textarea>
      </view>
    </view>

    <LkButton
      @click="handleCreateApp"
      class="create-app-button"
      type="primary"
      size="large"
      shape="round"
      :disabled="appDescribe.trim().length < 5"
    >
      确认创建
    </LkButton>
  </view>
</template>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 100vh;
  background-color: #f6f6fc;
  padding: 0 16 * 2rpx;
  overflow: hidden;

  &.no-scroll {
    overflow: hidden;
    touch-action: none;
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
}

.navbar-title {
  background-image: -webkit-linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
  background-image: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  font-size: 17 * 2rpx;
  font-weight: 600;
}

.tips {
  /* width: 343px; */
  height: 89 * 2rpx;
  border-radius: 14 * 2rpx;
  background-color: #fff;
  margin-top: 15 * 2rpx;
  padding: 12 * 2rpx;
  color: #1d2129;
  font-size: 14 * 2rpx;
  line-height: 22 * 2rpx;
  .description {
    user-select: text;
  }

  .description-text {
    background-image: -webkit-linear-gradient(114deg, #3472ff -18.56%, #9452ff 108.66%);
    background-image: linear-gradient(114deg, #3472ff -18.56%, #9452ff 108.66%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
  }

  ::v-deep .u-textarea__field {
    height: 0 !important;
  }
}

.app-describe {
  .app-describe-title {
    display: flex;
    align-items: center;
    color: #1d2129;
    font-size: 16 * 2rpx;
    font-weight: 500;
    line-height: 24 * 2rpx;
    margin-top: 16 * 2rpx;
    margin-bottom: 10 * 2rpx;
    .required-text {
      color: #f53f3f;
      margin-left: 4 * 2rpx;
    }
  }
  .app-describe-content {
    height: 330px;
    .app-describe-textarea {
      height: 100%;
      border-radius: 14 * 2rpx;
      border: none;
      color: #1d2129;
      padding: 10 * 2rpx 16 * 2rpx;
      font-size: 16 * 2rpx;
    }
    ::v-deep .u-textarea__field {
      height: 100% !important;
      font-size: 16 * 2rpx !important;
    }
  }
}
.create-app-button {
  width: 100%;
  margin-top: auto;
  margin-bottom: 30 * 2rpx;
}

.create-app-modal {
  position: relative;
  z-index: 9999;

  ::v-deep .u-popup__content {
    border-radius: 12px !important;
  }

  ::v-deep .u-modal__content {
    padding: 0 !important;
  }

  ::v-deep .u-modal__mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    touch-action: none; /* 禁止触摸行为 */
    -webkit-overflow-scrolling: touch; /* 提高iOS滚动体验 */
  }

  /* 防止事件穿透 */
  ::v-deep .u-modal {
    touch-action: none;
  }

  .modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* justify-content: center; */
    width: 100%;
    height: 339 * 2rpx;
    padding: 24 * 2rpx !important;
    touch-action: none; /* 阻止触摸行为 */
    border-radius: 12px;
  }
  .lottie-image {
    width: 132 * 2rpx;
    height: 115 * 2rpx;
    margin-top: 45 * 2rpx;
  }
  .lottie-text {
    font-size: 14 * 2rpx;
    line-height: 22 * 2rpx;
    color: #4e5969;
  }
  .lottie-button {
    width: 100%;
    background-color: #f3ecff;
    color: #7d4dff;
    font-size: 16 * 2rpx;
    margin-top: auto;
  }
}
::v-deep .uni-textarea-textarea,
::v-deep .uni-input-input {
  color: #1d2129;
}

/* 全局样式，禁止滚动时使用 */
:global(.overflow-hidden) {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}
</style>
