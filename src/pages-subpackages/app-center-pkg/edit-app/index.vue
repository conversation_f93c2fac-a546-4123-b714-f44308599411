<script setup lang="ts">
import { ref, watch, onUnmounted, onMounted, computed } from 'vue';
import { onLoad, onShow, onUnload, onHide } from '@dcloudio/uni-app';
import ai from '@/common/ai';
import dayjs from '@/utils/day';
import { getClientAppDetail, updateApp } from '@/api/app-center';
import { getBaseUrl } from '@/common/ai/url';
import { useAppCenterStore } from '@/store/useAppCenterStore';
import LKpermissionModal from '@/components/LKpermissionModal/index.vue';
import { checkPermission } from '@/utils/permission';
import { useTabStore } from '@/store/tabStore';
const tabStore = useTabStore();
// AI动画src
const aiAnimationSrc = ref(
  'https://huayun-ai-obs-public.huayuntiantu.com/eabaa1b140d10f8f54b6b96aba28d8dd.gif'
);
const defaultAvatarSrc =
  'https://huayun-ai-obs-public.huayuntiantu.com/7dd8c2f8d3b0d94cc769ccee58f8b753.svg';
// 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/7a76281c921b3e0cbbcb7749e300d749.svg';
const isLoadingAiAvatar = ref(false);
const utoastRef = ref();

const form = ref({
  id: '',
  name: '',
  intro: '',
  avatarUrl: defaultAvatarSrc,
});
const signal = ai.AbortSignal();
const appCenterStore = useAppCenterStore();
// 权限弹窗相关状态
const permissionModalShow = ref(false);
const permissionModalTitle = ref('');
const permissionModalContent = ref('');
const permissionOpenSettings = ref(null);

onLoad(options => {
  const id = options?.id;
  if (id) {
    getClientAppDetail(id).then(res => {
      console.log('onLoad--edit-app', res);
      form.value = res;
    });
  }
});

onUnmounted(() => {
  signal.abort();
});

const isSaveBtnDisabled = computed(() => {
  return !!(!form.value.name.trim() || form.value.name.trim().length > 20);
});

const formName = computed(() => {
  return form.value.name.trim();
});

watch(formName, (newVal: string) => {
  if (newVal.length > 20) {
    utoastRef.value?.show({
      message: '应用名称不能超过20个字符',
      type: 'info',
    });
  }
});

async function generateAvatar() {
  if (!form.value.name.trim()) {
    utoastRef.value?.show({
      message: '应用名称不能为空',
      type: 'info',
    });
    return;
  }

  isLoadingAiAvatar.value = true;
  try {
    const reuslt = await ai.fetch({
      url: '/huayun-ai/client/chat/once',
      data: {
        messages: [
          {
            role: 'user',
            content: JSON.stringify({
              name: form.value.name,
              intro: form.value.intro,
            }),
          },
        ],
        type: 9, // 头像生成
        variables: {
          cTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        },
        stream: true,
      },
      onMessage: message => {
        console.log(message);
        if (message.event === 'fastAnswer') {
          let data: any = {};
          try {
            data = message.text ? JSON.parse(message.text) : {};
          } catch (err) {
            console.warn('JSON parse error:', err);
            data = {};
          }
          form.value.avatarUrl = data?.avatar ?? defaultAvatarSrc;
          console.log('头像生成', { data });
        }
      },
      abortSignal: signal,
    });
    return reuslt;
  } catch (error) {
    console.log(error);
  } finally {
    isLoadingAiAvatar.value = false;
  }
}

function fetchUpdateApp() {
  return updateApp({
    id: form.value.id!,
    name: form.value.name,
    intro: form.value.intro,
    avatarUrl: form.value.avatarUrl,
  });
}

// 保存应用
function handleSaveApp() {
  fetchUpdateApp()
    .then(res => {
      if (res) {
        utoastRef.value?.show({
          message: '保存成功',
          type: 'success',
        });
        setTimeout(() => {
          tabStore.goAppCenter();
        }, 500);
      }
    })
    .catch(err => {
      utoastRef.value?.show({
        message: '保存失败',
        type: 'info',
      });
    })
    .finally(() => {
      appCenterStore.fetchLoadMyApps();
    });
}

function handleChooseAvatar() {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      const tempFilePaths = res.tempFilePaths;
      // @ts-ignore - 处理tempFilePaths类型问题
      tempFilePaths.forEach((filePath: string) => {
        uploadFile(filePath);
      });
    },
    fail: async err => {
      console.log('chooseImage fail', err, `------errmsg`, err.errMsg);
      if (
        ['chooseImage:fail No filming permission', 'chooseImage:fail No Permission'].includes(
          err.errMsg
        )
      ) {
        const permissionResult = await checkPermission('camera');
        console.log('执行了吗????????');
        permissionModalShow.value = true;
        permissionModalTitle.value = '提示';
        permissionModalContent.value =
          '您拒绝了相机权限，部分功能可能无法使用。是否前往设置开启权限？';
        permissionOpenSettings.value = permissionResult.openSettings;
      }
    },
  });
}

// 上传文件
const uploadFile = (filePath: string) => {
  const baseUrl = getBaseUrl();
  uni.uploadFile({
    url: `${baseUrl}/huayun-ai/system/file/public/upload`,
    header: {
      Authorization: uni.getStorageSync('token'),
    },
    filePath: filePath,
    name: 'file',
    success: uploadFileRes => {
      // @ts-ignore - parse返回值类型
      const result = JSON.parse(uploadFileRes.data);
      if (result.data.fileUrl) {
        form.value.avatarUrl = result.data.fileUrl;
      }
    },
  });
};
// 权限弹窗事件处理
function handlePermissionCancel() {
  permissionModalShow.value = false;
}

function handlePermissionConfirm() {
  permissionModalShow.value = false;
  console.log('前往设置, permissionOpenSettings:', permissionOpenSettings.value);

  if (permissionOpenSettings.value && typeof permissionOpenSettings.value === 'function') {
    // 直接调用permissionOpenSettings函数
    try {
      permissionOpenSettings.value();
    } catch (e) {
      console.error('调用permissionOpenSettings错误:', e);
      // 如果调用失败，尝试直接调用系统API
      try {
        // #ifdef APP-PLUS
        const isIOS = plus.os.name === 'iOS';
        if (isIOS) {
          try {
            // 对于iOS 10+，使用新的API
            const UIApplication = plus.ios.import('UIApplication');
            const application = UIApplication.sharedApplication();
            const NSURL = plus.ios.import('NSURL');
            const settingURL = NSURL.URLWithString('app-settings:');

            // 检查iOS版本
            const iosVersion = plus.os.version || '9.0';
            if (parseInt(iosVersion) >= 10) {
              // iOS 10+ 使用新方法
              console.log('使用iOS 10+方法打开设置');
              const options = plus.ios.newObject('NSDictionary');
              application.openURL_options_completionHandler(settingURL, options, null);
              plus.ios.deleteObject(options);
            } else {
              // iOS 9及以下使用旧方法
              application.openURL(settingURL);
            }

            plus.ios.deleteObject(settingURL);
            plus.ios.deleteObject(NSURL);
            plus.ios.deleteObject(application);
          } catch (iosError) {
            console.error('iOS原生方法打开设置失败:', iosError);
            // 备选方案
            plus.runtime.openURL('app-settings:');
          }
        } else {
          // Android
          plus.runtime.openURL(`package:${plus.runtime.appid}`);
        }
        // #endif

        // #ifndef APP-PLUS
        uni.openSetting({
          success: res => {
            console.log('打开设置成功:', res);
          },
          fail: err => {
            console.error('打开设置失败:', err);
            uni.showToast({ title: '无法打开设置页面', icon: 'none' });
          },
        });
        // #endif
      } catch (e2) {
        console.error('备选打开设置方式错误:', e2);
        uni.showToast({ title: '无法打开设置页面', icon: 'none' });
      }
    }
  } else {
    console.error('permissionOpenSettings不是函数或为null');
    // 如果permissionOpenSettings不是函数，直接尝试打开设置
    try {
      // #ifdef APP-PLUS
      const isIOS = plus.os.name === 'iOS';
      if (isIOS) {
        try {
          // 对于iOS 10+，使用新的API
          const UIApplication = plus.ios.import('UIApplication');
          const application = UIApplication.sharedApplication();
          const NSURL = plus.ios.import('NSURL');
          const settingURL = NSURL.URLWithString('app-settings:');

          // 检查iOS版本
          const iosVersion = plus.os.version || '9.0';
          if (parseInt(iosVersion) >= 10) {
            // iOS 10+ 使用新方法
            console.log('使用iOS 10+方法打开设置');
            const options = plus.ios.newObject('NSDictionary');
            application.openURL_options_completionHandler(settingURL, options, null);
            plus.ios.deleteObject(options);
          } else {
            // iOS 9及以下使用旧方法
            application.openURL(settingURL);
          }

          plus.ios.deleteObject(settingURL);
          plus.ios.deleteObject(NSURL);
          plus.ios.deleteObject(application);
        } catch (iosError) {
          console.error('iOS原生方法打开设置失败:', iosError);
          // 备选方案
          plus.runtime.openURL('app-settings:');
        }
      } else {
        // Android
        plus.runtime.openURL(`package:${plus.runtime.appid}`);
      }
      // #endif

      // #ifndef APP-PLUS
      uni.openSetting({
        success: res => {
          console.log('打开设置成功:', res);
        },
        fail: err => {
          console.error('打开设置失败:', err);
          uni.showToast({ title: '无法打开设置页面', icon: 'none' });
        },
      });
      // #endif
    } catch (e) {
      console.error('直接打开设置页面错误:', e);
      uni.showToast({ title: '无法打开设置页面', icon: 'none' });
    }
  }
}
</script>

<template>
  <!-- 权限弹窗组件 -->
  <LKpermissionModal
    v-model:show="permissionModalShow"
    :title="permissionModalTitle"
    :content="permissionModalContent"
    cancel-text="取消"
    confirm-text="前往设置"
    @cancel="handlePermissionCancel"
    @confirm="handlePermissionConfirm"
  />
  <LkToast ref="utoastRef"></LkToast>
  <view class="container">
    <u-navbar :autoBack="true" placeholder bgColor="#F7F7FD">
      <template #center>
        <text class="navbar-title">编辑应用</text>
      </template>
    </u-navbar>
    <view class="content">
      <!-- 头像 -->
      <view class="avatar-container">
        <view class="avatar">
          <up-image
            v-if="isLoadingAiAvatar"
            :src="aiAnimationSrc"
            lazy-load
            :fade="true"
            duration="450"
            width="100%"
            height="100%"
          >
            <template v-slot:loading>
              <u-loading-icon></u-loading-icon>
            </template>
          </up-image>

          <up-image
            lazy-load
            v-else
            :src="form.avatarUrl || defaultAvatarSrc"
            @click="handleChooseAvatar"
            :fade="true"
            duration="450"
            width="100%"
            height="100%"
          >
            <template v-slot:loading>
              <u-loading-icon></u-loading-icon>
            </template>
          </up-image>
          <!-- <image v-if="isLoadingAiAvatar" :src="aiAnimationSrc" lazy-load />
          <image
            lazy-load
            v-else
            :src="form.avatarUrl || defaultAvatarSrc"
            @click="handleChooseAvatar"
          /> -->
        </view>
        <view class="ai-created">
          <image
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/8a1e6c046e614ea32e8847cb6fa9eecf.svg"
          ></image>
          <view class="ai-created-text" @click="generateAvatar">{{
            isLoadingAiAvatar ? 'AI正在生成中' : 'AI生成'
          }}</view>
        </view>
      </view>

      <!-- 应用名称 -->
      <view class="app-name">
        <view class="app-name-text">应用名称 <text class="required">*</text></view>
        <up-input
          v-model="form.name"
          class="app-name-input"
          placeholder="请输入内容"
          border="surround"
        ></up-input>
        <!-- 应用介绍 -->
        <view class="app-description">
          <view class="app-description-text">应用介绍 </view>
          <up-textarea
            v-model="form.intro"
            class="app-description-textarea"
            placeholder="请输入内容"
            count
            maxlength="500"
          ></up-textarea>
        </view>
      </view>
    </view>

    <LkButton
      class="confirm-create-btn"
      type="primary"
      size="large"
      shape="round"
      :disabled="isSaveBtnDisabled"
      @click="handleSaveApp"
      >保存</LkButton
    >
  </view>
</template>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f6f6fc;
  padding: 0 16 * 2rpx;

  .confirm-create-btn {
    margin-top: auto;
    margin-bottom: 30 * 2rpx;
  }

  .navbar-title {
    color: #1d2129;
    font-size: 17 * 2rpx;
    font-weight: 600;
  }
}

.content {
  .avatar-container {
    position: relative;
  }
  .avatar {
    position: relative;
    width: 112 * 2rpx;
    height: 112 * 2rpx;
    border-radius: 50%;
    background-color: #fff;
    border: 5 * 2rpx solid #fff;
    margin: 0 auto;
    margin-top: 24 * 2rpx;
    margin-bottom: 20 * 2rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .ai-created {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 12 * 2rpx;
    border-radius: 50 * 2rpx;
    padding: 2 * 2rpx 12 * 2rpx;
    background: linear-gradient(90deg, #58aeff 0%, #9f71ff 46.5%, #ff9ed4 100%);
    height: 30 * 2rpx;
    image {
      width: 14 * 2rpx;
      height: 14 * 2rpx;
    }
  }

  .required {
    color: #ff4d4f;
  }
  .app-description {
    margin-top: 16 * 2rpx;
  }

  .app-name-text,
  .app-description-text {
    color: #1d2129;
    font-size: 16 * 2rpx;
    font-weight: 500;
    margin-bottom: 10 * 2rpx;
  }

  .app-name-input {
    height: 56px;
    padding: 16px !important;
    background-color: #fff;
    border-radius: 12px;
    border: none;
  }

  .app-description-textarea {
    height: 225px;
    padding: 16px !important;
    padding-bottom: 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    border: none;
  }
  ::v-deep .u-textarea__field {
    height: 100% !important;
  }

  ::v-deep .uni-textarea-textarea,
  ::v-deep .uni-input-input {
    color: #1d2129;
  }
}
</style>
