<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useUserStore } from '@/store/userStore';
import { useSystemStore } from '@/store/systemStore';
import {
  getSmsCode,
  getAuthValid,
  loginByPassword,
  login,
  getTenantListByJsCode,
  resetPassword,
  bindPhoneToWechat,
  getTenantListByUnionId,
  resetFirstPassword,
  appleLogin,
} from '@/api/auth';
import CryptoJS from 'crypto-js';
import { serviceImageData } from '@/components/LkServiceImage/data';
import { LoginType } from '@/constants/auth';
import PasswordSetting from './PasswordSetting.vue';
import PhoneBinding from './PhoneBinding.vue';
import PrivacyPolicyModal from '@/components/PrivacyPolicyModal/index.vue';
import { useTabStore } from '@/store/tabStore';
import { TabEnum } from '@/constants';
import type { ResetFirstPasswordParams, TenantInfo, WechatLoginResponse } from '@/types/api/auth';
import { commonAppList } from '@/api/userCommon';
import { HeaderNav } from '@/components/LkNavBar';
import { SimpleRouter } from '@/router';

const userStore = useUserStore();
const systemStore = useSystemStore();

// 导航方法
async function back() {
  try {
    // 返回到模式选择页面
    await uni.redirectTo({
      url: '/pages/mode-entry/index?type=login',
    });
  } catch (error) {
    console.error('返回操作失败:', error);
    await uni.navigateBack();
  }
}

function toForgotPassword() {
  uni.navigateTo({
    url: '/pages-subpackages/auth-pkg/forgot-password/index',
  });
}

function toTerms() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/user-agreement/index',
  });
}

function toPrivacy() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/privacy-policy/index',
  });
}

// 登录类型
const loginType = ref(LoginType.PASSWORD);

// 登录表单数据
const loginForm = reactive({
  phone: '',
  code: '',
  username: '',
  password: '',
});

// 登录状态
const isLoading = ref(false);
const codeTip = ref('');
const uCode = ref<any>(null);
const slideCodeRef = ref();
const bindPhoneNumber = ref('');
const protocolAgreed = ref(false);
const uToastRef = ref();

// 密码显示/隐藏状态
const showPassword = ref(false);

// 密码设置弹窗引用
const passwordSettingRef = ref();

// 手机号绑定弹窗引用
const phoneBindingRef = ref();

// 隐私协议弹窗引用
const privacyPolicyRef = ref();

// 保存微信登录的code，用于后续绑定
const wechatAuthInfo = ref<any>(null);

// 保存苹果登录的信息，用于后续绑定
const appleAuthInfo = ref<{
  openid: string;
  access_token: string;
} | null>(null);

// 检测是否为iOS设备
const isIOS = ref(false);

// 新增样式配置
const inputStyle = {
  height: '92rpx',
  background: '#FFFFFF',
  borderRadius: '100rpx',
  padding: '0 48rpx',
  fontSize: '32rpx',
};

const placeholderStyle = {
  color: '#AEAEB2',
  fontSize: '26rpx',
  fontWeight: '400',
};

const buttonStyle = {
  height: '84rpx',
  borderRadius: '100rpx',
  fontSize: '32rpx',
  fontWeight: '600',
  background: '#7D4DFF',
  marginBottom: '40rpx',
  border: 'none',
};

// 切换密码显示状态
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value;
}

// 切换登录方式
function switchLoginType(type: LoginType) {
  loginType.value = type;
  loginForm.code = '';
}

// 获取短信验证码
async function getSmsCodeFn() {
  if (!uCode.value.canGetCode) return;

  if (!loginForm.phone || !/^1\d{10}$/.test(loginForm.phone)) {
    uToastRef.value.show({
      message: '请输入正确的手机号',
      type: 'info',
    });
    return;
  }

  try {
    const slideParams = await slideCodeRef.value.open();
    await getSmsCode({
      mobile: loginForm.phone,
      ...slideParams,
    });

    uToastRef.value.show({
      message: '验证码已发送',
      type: 'success',
    });
    uCode.value.start();
  } catch (error: any) {
    uCode.value.reset();
  }
}

// 处理登录
async function handleLogin() {
  if (!protocolAgreed.value) {
    uToastRef.value.show({
      message: '请先选择用户协议',
      type: 'info',
    });
    return;
  }

  if (loginType.value === LoginType.PHONE) {
    if (!loginForm.phone || !/^1\d{10}$/.test(loginForm.phone)) {
      uToastRef.value.show({
        message: '请输入正确的手机号',
        type: 'info',
      });
      return;
    }
    if (!loginForm.code) {
      uToastRef.value.show({
        message: '请输入验证码',
        type: 'info',
      });
      return;
    }
  } else {
    if (!loginForm.username || !/^1\d{10}$/.test(loginForm.username)) {
      uToastRef.value.show({
        message: '请输入正确的手机号',
        type: 'info',
      });
      return;
    }

    if (!loginForm.password) {
      uToastRef.value.show({
        message: '请输入密码',
        type: 'info',
      });
      return;
    }
  }

  isLoading.value = true;
  try {
    let tenantList;

    if (loginType.value === LoginType.PHONE) {
      // const slideParams = await slideCodeRef.value.open();

      tenantList = await getAuthValid({
        mobile: loginForm.phone,
        code: loginForm.code,
        // ...slideParams,
      });
    } else {
      tenantList = await loginByPassword({
        account: loginForm.username,
        password: CryptoJS.SHA256(loginForm.password).toString(CryptoJS.enc.Hex),
      });
    }

    loginByTenantList(tenantList);
  } catch (error: any) {
    console.log(error, 'error');

    uToastRef.value.show({
      message: error.msg || '登录失败',
      type: 'info',
    });
  } finally {
    isLoading.value = false;
  }
}

// 处理密码修改成功
const handlePasswordSuccess = async (params: {
  type: 'wechat' | 'phone' | 'apple';
  encryptedPassword: string;
}) => {
  const { type, encryptedPassword } = params;
  try {
    const params: ResetFirstPasswordParams = {
      password: encryptedPassword,
      password1: encryptedPassword,
    };

    if (type === 'wechat') {
      // params.account = bindPhoneNumber.value;
      // params.phone = bindPhoneNumber.value;
      params.unionId = wechatAuthInfo.value.unionid;
    }

    if (type === 'phone') {
      params.phone = loginForm.phone;
      params.account = loginForm.username;
    }

    if (type === 'apple') {
    }

    // 这里需要调用修改密码的API
    await resetFirstPassword(params);

    uToastRef.value.show({
      message: '密码修改成功,请重新登录',
      type: 'success',
    });

    if (type === 'phone') {
      loginForm.password = '';
    }
  } catch (error: any) {
    uToastRef.value.show({
      message: error.msg || '密码修改失败',
      type: 'info',
    });
  }
};

// 处理密码设置失败/关闭
const handlePasswordFail = () => {
  // 清空登录表单信息
  loginForm.code = '';

  // 清空第三方登录信息
  wechatAuthInfo.value = null;
  appleAuthInfo.value = null;

  if (userStore.userInfo) {
    userStore.logout();
  }

  uToastRef.value.show({
    message: '密码设置已取消，请重新登录',
    type: 'info',
  });
};

// 处理微信登录
const handleWechatLogin = async () => {
  if (!protocolAgreed.value) {
    uToastRef.value.show({
      message: '请先选择用户协议',
      type: 'info',
    });
    return;
  }
  // #ifdef APP-PLUS
  const authProvider = 'weixin' as 'weixin';
  uni.getProvider({
    service: 'oauth',
    success: async function (res: any) {
      if (~res.provider.indexOf(authProvider)) {
        uni.login({
          provider: authProvider,
          success: async function (loginRes: any) {
            // 兼容不同平台，可能是 loginRes.authResult 或 loginRes
            const authResult = loginRes.authResult || loginRes;
            await handleWechatAuthResult(authResult);
          },
          fail: function (err) {
            uToastRef.value.show({
              message: '微信登录失败',
              type: 'info',
            });
            console.error('微信登录失败：', err);
          },
        });
      } else {
        uToastRef.value.show({
          message: '当前环境不支持微信登录',
          type: 'info',
        });
      }
    },
  });
  // #endif

  // #ifdef H5
  // H5环境下模拟微信登录返回
  const mockWechatRes = {
    access_token:
      '93_WpRR4yB3hfrgwazSL-A9AQRomliLulgPp_hGmlNHDtfKelnznuvqHaUPw2ETp2tCritSWZCUxUnqMfvqp1k8XfILSjq7yxkCerBouEr9ndo',
    expires_in: 7200,
    refresh_token:
      '93_ndfouR85MEqSgkFV7U64hK_XQbH42IKTYJgaqa-wb-JdOPXgbC0BNnziIEoydNhJpimwAS3mL4i8kViL2N4eAylbKwxj0Zo7NSk0A0W76e0',
    openid: 'orbnQ672P0PaBZWLezDRPJlr8T04',
    scope: 'snsapi_userinfo',
    unionid: 'oevblvliWvD3vbpMtFevJxz68GZE',
  };
  await handleWechatAuthResult(mockWechatRes);
  // #endif
};

// 处理苹果登录
const handleAppleLogin = async () => {
  if (!protocolAgreed.value) {
    uToastRef.value.show({
      message: '请先选择用户协议',
      type: 'info',
    });
    return;
  }

  // #ifdef APP-PLUS
  const authProvider = 'apple' as 'apple';
  uni.getProvider({
    service: 'oauth',
    success: async function (res: any) {
      if (~res.provider.indexOf(authProvider)) {
        uni.login({
          provider: authProvider,
          success: async function (loginRes: any) {
            try {
              const authResult = loginRes.authResult || loginRes;
              await handleAppleAuthResult(authResult);
            } catch (error: any) {
              uToastRef.value.show({
                message: error.msg || '苹果登录失败',
                type: 'info',
              });
            }
          },
          fail: function (err) {
            uToastRef.value.show({
              message: '苹果登录失败',
              type: 'info',
            });
            console.error('苹果登录失败：', err);
          },
        });
      } else {
        uToastRef.value.show({
          message: '当前环境不支持苹果登录',
          type: 'info',
        });
      }
    },
    fail: function (err) {
      uToastRef.value.show({
        message: '苹果登录失败',
        type: 'info',
      });
    },
  });
  // #endif

  // #ifdef H5
  // H5环境下模拟苹果登录返回
  const mockAppleRes = {
    access_token:
      'eyJraWQiOiJVYUlJRlkyZlc0IiwiYWxnIjoiUlMyNTYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XPuPhUBKX1i9FdMsPEGCTGDVXtxnCQjvOrQODhyw_Uhk_TdZNDoh-Glf5MKUKjRTyQgIWabSPutQfr0LeurGfsix4zN_AYdjrcVtLxvVKvwMiY5qbjQ8bSMP3_Q8RiJZA38BiUkmwjHErjhxxsqWV7moAEE644snVvDJWlp1cqSfouputHVWO1G6qooacoPQUldu8K7ltKRXbXBOVr7XMZkwfI0kpW0dewaHrVm5Rps0d1-6BjdhKO9MTzsUVwrRg1UXDTHsV75aU7SxlRJFWhnJ4OfHJGlKegupVYwJuF4OY4Y7VKIaZs8DS7gczgKLsuvaHY50I9unQA7_qwlZag',
    openid: '001511.c0e94b1076ae42c4a77831cba8ac08ca.0943',
  };
  await handleAppleAuthResult(mockAppleRes);
  // #endif
};

// 处理手机号绑定成功
const handlePhoneBindingSuccess = async (bindingData: {
  phone: string;
  code: string;
  type: 'wechat' | 'apple';
}) => {
  try {
    if (bindingData.type == 'wechat') {
      const { unionid, openid, access_token } = wechatAuthInfo.value || {};
      if (!unionid || !openid || !access_token) {
        uToastRef.value.show({
          message: '微信授权信息缺失，请重新登录',
          type: 'info',
        });
        return;
      }
      bindPhoneNumber.value = bindingData.phone;

      const tenantList = await getTenantListByUnionId({
        phone: bindingData.phone,
        code: bindingData.code,
        unionId: unionid,
        openId: openid,
        accessToken: access_token,
      });

      if (tenantList?.length) {
        uToastRef.value.show({
          message: '手机号绑定成功',
          type: 'success',
        });
        loginByTenantList(tenantList, 'wechat');
      } else {
        uToastRef.value.show({
          message: '绑定手机号未完成注册，请联系管理员',
          type: 'info',
        });
      }
    } else {
      //苹果二次登陆
      const { openid, access_token } = appleAuthInfo.value || {};
      if (!openid || !access_token) {
        uToastRef.value.show({
          message: '微信授权信息缺失，请重新登录',
          type: 'info',
        });
        return;
      }
      bindPhoneNumber.value = bindingData.phone;

      const tenantList = await appleLogin({
        phone: bindingData.phone,
        code: bindingData.code,
        accessToken: access_token,
      });

      if (tenantList?.length) {
        uToastRef.value.show({
          message: '手机号绑定成功',
          type: 'success',
        });
        loginByTenantList(tenantList, 'apple');
      } else {
        uToastRef.value.show({
          message: '绑定手机号未完成注册，请联系管理员',
          type: 'info',
        });
      }
    }
  } catch (error: any) {
    uToastRef.value.show({
      message: error.msg || '手机号绑定失败',
      type: 'info',
    });
  }
};

const handleWechatAuthResult = async (authResult: {
  unionid: string;
  openid: string;
  access_token: string;
}) => {
  const { unionid, openid, access_token } = authResult;
  if (!unionid || !openid || !access_token) {
    uToastRef.value.show({
      message: '微信授权信息不完整',
      type: 'info',
    });
    return;
  }

  // 存储微信授权信息，方便后续绑定手机号等操作
  wechatAuthInfo.value = authResult;

  try {
    // 调用后端微信登录接口
    const tenantList = await getTenantListByUnionId({
      unionId: unionid,
      openId: openid,
      accessToken: access_token,
    });

    if (tenantList && tenantList.length) {
      await loginByTenantList(tenantList, 'wechat');
    } else {
      // 未绑定手机号，弹窗绑定手机号
      phoneBindingRef.value?.open({
        type: 'wechat',
      });
    }
  } catch (error: any) {
    uToastRef.value.show({
      message: error.msg || '微信登录失败',
      type: 'info',
    });
  }
};

// 处理苹果授权结果
const handleAppleAuthResult = async (authResult: { access_token: string; openid: string }) => {
  const { access_token, openid } = authResult;

  if (!access_token) {
    uToastRef.value.show({
      message: '苹果授权信息不完整',
      type: 'info',
    });
    return;
  }

  // 存储苹果授权信息，方便后续绑定手机号等操作
  appleAuthInfo.value = authResult;

  try {
    // 调用后端苹果登录接口
    const tenantList = await appleLogin({
      accessToken: access_token,
      openid,
    });
    if (tenantList && tenantList.length) {
      await loginByTenantList(tenantList, 'apple');
    } else {
      // 未绑定手机号，弹窗绑定手机号
      phoneBindingRef.value?.open({
        type: 'apple',
      });
    }
  } catch (error: any) {
    uToastRef.value.show({
      message: error.msg || '苹果登录失败',
      type: 'info',
    });
  }
};

const loginSuccessRedirect = async () => {
  setTimeout(async () => {
    try {
      console.log('登录成功，开始处理路由跳转...');

      // 登录成功后设置隐私协议同意状态
      uni.setStorageSync('hasAgreedPrivacy', true);
      console.log('已设置隐私协议同意状态');

      // 使用简化的路由管理器处理登录成功
      const result = await SimpleRouter.loginSuccess();

      if (result.success) {
        console.log(`登录后跳转成功`);
      } else {
        console.error('登录后跳转失败:', result.message);
        // 跳转失败时，尝试默认页面
        await uni.navigateTo({ url: '/pages/index/index' });
      }
    } catch (error) {
      console.error('登录成功处理失败:', error);
      // 出错时跳转到默认页面
      await uni.navigateTo({ url: '/pages/index/index' });
    }
  }, 1000);
};

const loginByTenantList = async (tenantList: TenantInfo[], loginSource?: 'wechat' | 'apple') => {
  try {
    const userInfo = await login({
      accessKey: tenantList.find(item => item.isDefault)?.accessKey || tenantList[0].accessKey,
    });
    userStore.loginSuccess(userInfo, tenantList);

    uToastRef.value.show({
      message: '登录成功',
      type: 'success',
    });
    // 随便调用一个接口，用于判断是否账号激活
    await commonAppList();
    loginSuccessRedirect();
  } catch (error: any) {
    if (error.code == 405) {
      // 405 账号未激活,需要设置密码
      uToastRef.value.show({
        message: '账号未激活，请先设置密码',
        type: 'info',
      });
      passwordSettingRef.value?.open({
        type: loginSource || 'phone',
      });
    } else {
      uToastRef.value.show({
        message: error.msg || '登录失败',
        type: 'info',
      });
    }
  }
};

const disabledLogin = computed(() => {
  if (loginType.value === LoginType.PHONE) {
    return !loginForm.phone || !/^1\d{10}$/.test(loginForm.phone) || !loginForm.code;
  } else {
    return !loginForm.username || !/^1\d{10}$/.test(loginForm.username) || !loginForm.password;
  }
});

// 检查是否首次进入
const checkFirstVisit = () => {
  const hasAgreedPrivacy = uni.getStorageSync('hasAgreedPrivacy');
  if (!hasAgreedPrivacy) {
    // 首次进入，显示隐私协议弹窗
    setTimeout(() => {
      privacyPolicyRef.value?.open();
    }, 500);
  }
};

// 处理隐私协议确认
const handlePrivacyConfirm = () => {
  // 用户同意隐私协议
  protocolAgreed.value = true;
  // 注意：hasAgreedPrivacy 将在登录成功后设置
  uToastRef.value.show({
    message: '感谢您同意隐私协议',
    type: 'success',
  });
};

// 处理隐私协议取消
const handlePrivacyCancel = () => {
  // 用户不同意隐私协议
  protocolAgreed.value = false;
  uToastRef.value.show({
    message: '您需要同意隐私协议才能使用本应用',
    type: 'info',
  });
};

// 检测设备类型
const checkDeviceType = () => {
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  isIOS.value = systemInfo.platform === 'ios';
  // #endif

  // #ifdef H5
  const userAgent = navigator.userAgent;
  isIOS.value = /iPhone|iPad|iPod/i.test(userAgent);
  // #endif

  // #ifdef MP-WEIXIN
  const wechatSystemInfo = uni.getSystemInfoSync();
  isIOS.value = wechatSystemInfo.platform === 'ios';
  // #endif
};

// 页面加载时检查首次访问
onMounted(() => {
  checkFirstVisit();
  checkDeviceType();
});
</script>

<template>
  <view class="login">
    <!-- 返回按钮 -->
    <HeaderNav
      title="切换身份"
      :style="{
        backgroundColor: 'transparent',
        position: 'absolute',
        top: '0',
        left: '0',
        zIndex: '1',
        '--status-bar-padding-top': 'calc(var(--status-bar-height) + 14px)',
        paddingTop: 'var(--status-bar-padding-top)',
      }"
      @back="back"
    >
      <template #right>
        <LkServiceImage name="loginLogo" class="login-logo" mode="aspectFit"></LkServiceImage>
      </template>
    </HeaderNav>

    <!-- 背景图片 -->
    <view class="background">
      <LkServiceImage name="loginBg" class="bg-image" mode="aspectFill"></LkServiceImage>
    </view>

    <view class="login-content">
      <!-- Logo区域 -->
      <view class="header-wrap">
        <view class="content"> </view>
        <view
          class="slogan"
          style="
            display: flex;
            align-items: flex-end;
            justify-content: center;
            margin-bottom: 10rpx;
          "
        >
          <LkServiceImage name="loginIpImage" class="logo" mode="aspectFit"></LkServiceImage>

          <text style="padding-right: 150rpx">智驱世界</text>
        </view>
        <view class="slogan">共创美好未来</view>
      </view>

      <!-- 登录区域 -->
      <!-- <textarea
        v-if="appleAuthInfo"
        name=""
        id=""
        :value="JSON.stringify(appleAuthInfo)"
      ></textarea> -->
      <view class="login-area">
        <!-- 手机号登录 -->
        <template v-if="loginType === LoginType.PHONE">
          <view class="login-form">
            <view class="form-item">
              <u-input
                v-model="loginForm.phone"
                type="number"
                maxlength="11"
                placeholder="请输入手机号"
                fontSize="32rpx"
                :customStyle="inputStyle"
                :placeholderStyle="placeholderStyle"
                border="none"
              ></u-input>
            </view>

            <view class="form-item">
              <u-input
                v-model="loginForm.code"
                type="number"
                maxlength="6"
                placeholder="请输入验证码"
                fontSize="32rpx"
                :customStyle="inputStyle"
                :placeholderStyle="placeholderStyle"
                border="none"
              >
                <template #suffix>
                  <up-code
                    ref="uCode"
                    @change="codeTip = $event"
                    seconds="60"
                    changeText="Xs后重发"
                  ></up-code>
                  <view class="get-code-btn" @click="getSmsCodeFn">{{
                    codeTip || '获取验证码'
                  }}</view>
                </template>
              </u-input>
            </view>
          </view>
        </template>

        <!-- 密码登录 -->
        <template v-else>
          <view class="login-form">
            <view class="form-item">
              <u-input
                v-model="loginForm.username"
                type="number"
                maxlength="11"
                fontSize="32rpx"
                placeholder="请输入手机号"
                :customStyle="inputStyle"
                :placeholderStyle="placeholderStyle"
                border="none"
              ></u-input>
            </view>

            <view class="form-item">
              <u-input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                fontSize="32rpx"
                :customStyle="inputStyle"
                :placeholderStyle="placeholderStyle"
                border="none"
              >
                <template #suffix>
                  <view class="password-toggle" @click="togglePasswordVisibility">
                    <image v-if="showPassword" src="/static/common/open_eye.svg" class="eye-icon" />
                    <image v-else src="/static/common/close_eye.svg" class="eye-icon" />
                  </view>
                </template>
              </u-input>
            </view>
            <view class="forgot-pwd">
              <view @click="toForgotPassword">忘记密码</view>
            </view>
          </view>
        </template>

        <!-- 登录按钮 -->
        <view class="btn-area">
          <u-button
            type="primary"
            @click="handleLogin"
            :loading="isLoading"
            :disabled="disabledLogin"
            :customStyle="buttonStyle"
            >登录</u-button
          >
        </view>

        <!-- 协议 -->
        <view class="protocol">
          <view class="checkbox" @click="protocolAgreed = !protocolAgreed">
            <view class="checkbox-inner" :class="{ checked: protocolAgreed }">
              <u-icon v-if="protocolAgreed" name="checkmark" color="#FFFFFF" size="24rpx"></u-icon>
            </view>
          </view>
          <text class="protocol-text">我已阅读并同意</text>
          <text class="protocol-link" @click="toTerms">《用户协议》</text>
          <text class="protocol-text">与</text>
          <text class="protocol-link" @click="toPrivacy">《隐私政策》</text>
        </view>

        <!-- 其他登录方式 -->
        <view class="other-login">
          <text class="other-title">其他登录方式</text>
          <view class="other-icons">
            <view
              class="icon-item"
              v-if="loginType === LoginType.PASSWORD"
              @click="switchLoginType(LoginType.PHONE)"
            >
              <LkServiceImage name="phoneSelectIcon" mode="aspectFit"></LkServiceImage>
            </view>
            <view
              class="icon-item"
              v-if="loginType === LoginType.PHONE"
              @click="switchLoginType(LoginType.PASSWORD)"
            >
              <LkServiceImage name="passwordSelectIcon" mode="aspectFit"></LkServiceImage>
            </view>
            <view class="icon-item" @click="handleWechatLogin">
              <LkServiceImage name="wechatSelectIcon" mode="aspectFit"></LkServiceImage>
            </view>
            <view class="icon-item" @click="handleAppleLogin">
              <LkServiceImage name="appleSelectIcon" mode="aspectFit"></LkServiceImage>
            </view>
          </view>
        </view>
      </view>
      <!-- 密码设置弹窗 -->
      <PasswordSetting
        ref="passwordSettingRef"
        @success="handlePasswordSuccess"
        @fail="handlePasswordFail"
      />

      <!-- 手机号绑定弹窗 -->
      <PhoneBinding ref="phoneBindingRef" @success="handlePhoneBindingSuccess" />

      <!-- 隐私协议弹窗 -->
      <PrivacyPolicyModal
        ref="privacyPolicyRef"
        @confirm="handlePrivacyConfirm"
        @cancel="handlePrivacyCancel"
      />

      <!-- 滑块验证码组件 -->
      <LkSlideCode ref="slideCodeRef"></LkSlideCode>
      <!-- Toast 提示组件 -->
      <LkToast ref="uToastRef"></LkToast>
    </view>
  </view>
</template>

<style lang="scss">
.login {
  height: 100vh;
  background: #f1f3f6;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .login-logo {
    width: 158rpx;
    height: 64rpx;
  }
  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .bg-image {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }

    .bg-overlay {
      width: 100%;
      height: 584rpx;
      position: absolute;
      bottom: 0;
    }
  }

  .header-wrap {
    padding: 50rpx 104rpx 0rpx;
    position: relative;
    z-index: 1;
    text-align: center;
    // 小屏幕设备调整padding-top

    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 40rpx;
    }

    .logo {
      width: 150rpx;
      height: 150rpx;
    }

    .slogan {
      font-size: 50rpx;
      color: #280868;
      text-align: center;
      font-weight: bold;
      letter-spacing: 1.6rpx;
    }
  }

  .login-area {
    position: relative;
    z-index: 1;
    padding: 0 60rpx;
    margin-top: 60rpx;

    .login-form {
      .form-item:not(:last-child) {
        margin-bottom: 32rpx;
      }
      .form-item:nth-child(2) {
        margin-bottom: 20rpx !important;
      }
      .get-code-btn {
        font-size: 30rpx;
        color: #7d4dff !important;
      }
      .password-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        cursor: pointer;

        .eye-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
      .forgot-pwd {
        display: flex;
        justify-content: flex-end;
        font-size: 28rpx;
        color: #7d4dff;
        align-items: center;
      }
    }

    .btn-area {
      margin-top: 60rpx;
      margin-bottom: 30rpx;
    }

    .protocol {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40rpx 0;

      .checkbox {
        width: 36rpx;
        height: 36rpx;
        margin-right: 16rpx;

        &-inner {
          width: 100%;
          height: 100%;
          border: 2rpx solid #aaa;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          &.checked {
            background: #7d4dff;
            border-color: #7d4dff;
          }
        }
      }

      .protocol-text {
        font-size: 26rpx;
        color: #000000;
      }

      .protocol-link {
        font-size: 26rpx;
        color: #3d7fff;
      }
    }

    .other-login {
      margin-top: 80rpx;
      text-align: center;

      .other-title {
        display: inline-block;
        font-size: 24rpx;
        color: #414141;
        margin-bottom: 28rpx;
      }

      .other-icons {
        display: flex;
        justify-content: center;

        .icon-item {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 26rpx;

          image {
            width: 80rpx;
            height: 80rpx;
          }
        }
      }
    }
  }
}

.back-icon {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #606060;
}
</style>
