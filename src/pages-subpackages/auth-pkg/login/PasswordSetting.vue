<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import CryptoJS from 'crypto-js';

const visible = ref(false);
const isLoading = ref(false);
const uToastRef = ref();
const type = ref<'phone' | 'wechat'>('phone');

// 键盘相关状态
const keyboardHeight = ref(0);
const isKeyboardShow = ref(false);

// 密码显示/隐藏状态
const showPassword = ref(false);
const showConfirmPassword = ref(false);

const form = reactive({
  password: '',
  confirmPassword: '',
});

// 样式配置
const inputStyle = {
  height: '96rpx',
  background: '#F2F3F5',
  borderRadius: '16rpx',
  padding: '0 32rpx',
  fontSize: '32rpx',
};

const placeholderStyle = {
  color: '#86909C',
  fontSize: '28rpx',
  fontWeight: '400',
};

const buttonStyle = {
  width: '320rpx',
  height: '84rpx',
};

// 动态计算弹窗样式
const popupStyle = computed(() => {
  if (isKeyboardShow.value && keyboardHeight.value > 0) {
    // 键盘弹起时，调整弹窗位置
    // 根据键盘高度动态调整，确保输入框可见
    const adjustHeight = keyboardHeight.value;
    return {
      transform: `translateY(-${adjustHeight}px)`,
      transition: 'transform 0.3s ease-in-out',
    };
  }
  return {};
});

// 表单验证
const isValid = computed(() => {
  if (!form.password || !form.confirmPassword) return false;

  const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/;
  return passwordPattern.test(form.password) && form.password === form.confirmPassword;
});

// 暴露方法
const emit = defineEmits(['success', 'close', 'fail']);

// 键盘事件处理
const onKeyboardHeightChange = (res: any) => {
  keyboardHeight.value = res.height;
  isKeyboardShow.value = res.height > 0;
};

// 监听键盘事件
onMounted(() => {
  uni.onKeyboardHeightChange(onKeyboardHeightChange);
});

onUnmounted(() => {
  uni.offKeyboardHeightChange(onKeyboardHeightChange);
});

// 切换密码显示状态
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// 切换确认密码显示状态
const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

// 打开弹窗
const open = (params: { type: 'phone' | 'wechat' }) => {
  type.value = params.type;
  visible.value = true;
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  // 重置键盘状态
  keyboardHeight.value = 0;
  isKeyboardShow.value = false;
  emit('close');
};

// 重置表单
const reset = () => {
  form.password = '';
  form.confirmPassword = '';
  showPassword.value = false;
  showConfirmPassword.value = false;
};

// 处理重置按钮点击
const handleReset = () => {
  reset();
};

// 处理关闭
const handleClose = () => {
  close();
  reset();
  emit('fail');
};

// 处理确认
const handleConfirm = async () => {
  if (!isValid.value) {
    uToastRef.value.show({
      message: '请检查密码格式是否正确',
      type: 'info',
    });
    return;
  }

  isLoading.value = true;
  try {
    // 发送加密后的密码
    const encryptedPassword = CryptoJS.SHA256(form.password).toString(CryptoJS.enc.Hex);
    emit('success', {
      type: type.value,
      encryptedPassword: encryptedPassword,
    });
    close();
    reset();
  } catch (error: any) {
    uToastRef.value.show({
      message: error.msg || '修改失败',
      type: 'info',
    });
  } finally {
    isLoading.value = false;
  }
};

// 输入框聚焦处理
const handleInputFocus = () => {
  // 延迟一点时间，确保键盘高度已经获取到
  setTimeout(() => {
    if (keyboardHeight.value === 0) {
      // 如果没有获取到键盘高度，使用默认值
      isKeyboardShow.value = true;
    }
  }, 300);
};

// 输入框失焦处理
const handleInputBlur = () => {
  // 延迟重置，避免快速切换输入框时的闪烁
  setTimeout(() => {
    if (keyboardHeight.value === 0) {
      isKeyboardShow.value = false;
    }
  }, 100);
};

// 暴露组件方法
defineExpose({
  open,
  close,
  reset,
});
</script>

<script lang="ts">
export default {
  name: 'PasswordSetting',
};
</script>

<template>
  <u-popup
    :show="visible"
    @close="handleClose"
    mode="bottom"
    round="12"
    :closeable="false"
    :custom-style="popupStyle"
    :safe-area-inset-bottom="!isKeyboardShow"
  >
    <view class="password-setting" :class="{ 'keyboard-active': isKeyboardShow }">
      <!-- 标题 -->
      <view class="header">
        <text class="title">密码设置</text>
      </view>

      <!-- 提示文字 -->
      <view class="tips">
        为保障您的账号安全，首次登录请修改您的密码（密码 8 至 16
        位，包含大小写字母和数字的组合，可以输入特殊符号）
      </view>

      <!-- 表单 -->
      <view class="form">
        <view class="form-item">
          <u-input
            v-model="form.password"
            :type="showPassword ? 'text' : 'password'"
            :customStyle="inputStyle"
            :placeholderStyle="placeholderStyle"
            placeholder="请输入新密码"
            fontSize="32rpx"
            border="none"
            :adjust-position="false"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          >
            <template #suffix>
              <view class="password-toggle" @click="togglePasswordVisibility">
                <image v-if="showPassword" src="/static/common/open_eye.svg" class="eye-icon" />
                <image v-else src="/static/common/close_eye.svg" class="eye-icon" />
              </view>
            </template>
          </u-input>
        </view>

        <view class="form-item">
          <u-input
            v-model="form.confirmPassword"
            :type="showConfirmPassword ? 'text' : 'password'"
            :customStyle="inputStyle"
            :placeholderStyle="placeholderStyle"
            placeholder="请再次输入新密码"
            fontSize="32rpx"
            border="none"
            :adjust-position="false"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          >
            <template #suffix>
              <view class="password-toggle" @click="toggleConfirmPasswordVisibility">
                <image
                  v-if="showConfirmPassword"
                  src="/static/common/open_eye.svg"
                  class="eye-icon"
                />
                <image v-else src="/static/common/close_eye.svg" class="eye-icon" />
              </view>
            </template>
          </u-input>
        </view>
      </view>

      <!-- 按钮组 -->
      <view class="button-group">
        <LkButton
          type="neutral"
          size="large"
          class="reset-button button"
          shape="round"
          @click="handleReset"
          :customStyle="buttonStyle"
          >重置</LkButton
        >

        <LkButton
          type="primary"
          size="large"
          class="confirm-button button"
          shape="round"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="!isValid"
          :customStyle="buttonStyle"
          >确认修改</LkButton
        >
      </view>
    </view>
  </u-popup>

  <!-- Toast 提示组件 -->
  <LkToast ref="uToastRef"></LkToast>
</template>

<style lang="scss">
.password-setting {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  transition: all 0.3s ease-in-out;

  &.keyboard-active {
    // 键盘激活时的样式调整
    padding-bottom: 16rpx;

    .tips {
      margin-bottom: 24rpx;
    }

    .button-group {
      margin-top: 32rpx;
    }
  }

  .header {
    text-align: center;
    margin-bottom: 24rpx;

    .title {
      font-size: 34rpx;
      font-weight: 600;
      color: #1d2129;
    }
  }

  .tips {
    font-size: 28rpx;
    color: #4e5969;
    line-height: 40rpx;
    margin-bottom: 32rpx;
    transition: margin-bottom 0.3s ease-in-out;
  }

  .form {
    .form-item {
      margin-bottom: 32rpx;

      .password-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        cursor: pointer;

        .eye-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  .button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 48rpx;
    padding: 0 32rpx;
    transition: margin-top 0.3s ease-in-out;

    .button {
      width: 48% !important;
    }
  }
}
</style>
