<script setup lang="ts">
import { computed } from 'vue';

interface SelectOption {
  value: string | number;
  text: string;
}

interface Props {
  options: SelectOption[];
  keyword: string;
  selectedValue?: string | number;
  emptyText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  selectedValue: '',
  emptyText: '暂无数据',
});

const emit = defineEmits<{
  select: [option: SelectOption];
}>();

// 计算过滤后的选项
const filteredOptions = computed(() => {
  if (!props.keyword) {
    return props.options;
  }
  return props.options.filter(option =>
    option.text.toLowerCase().includes(props.keyword.toLowerCase())
  );
});

const handleOptionClick = (option: SelectOption) => {
  emit('select', option);
};
</script>

<template>
  <view class="option-list">
    <!-- 选项列表 -->
    <view
      v-for="option in filteredOptions"
      :key="option.value"
      class="option-item"
      :class="{ 'is-selected': option.value === selectedValue }"
      @click="handleOptionClick(option)"
    >
      <text class="option-text">{{ option.text }}</text>
      <view v-if="option.value === selectedValue" class="selected-icon">
        <text class="icon">✓</text>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view v-if="filteredOptions.length === 0" class="empty-state">
      <text class="empty-text">{{ emptyText }}</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.option-list {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.2s ease;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f8f9fa;
  }

  &.is-selected {
    background: rgba(125, 77, 255, 0.1);
    color: rgb(125, 77, 255);
  }
}

.option-text {
  flex: 1;
  font-size: 14px;
  color: inherit;
  line-height: 1.4;
}

.selected-icon {
  margin-left: 8px;

  .icon {
    font-size: 16px;
    color: rgb(125, 77, 255);
    font-weight: bold;
  }
}

.empty-state {
  padding: 20px;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  color: #999;
}
</style>
