<template>
  <view>
    <up-navbar bgColor="transparent" placeholder>
      <template #left>
        <up-icon name="arrow-left" size="40rpx" @tap="handleBack" />
      </template>
      <template #center>
        <LkText :lines="1" ellipsis size="large">同步练习</LkText>
      </template>
      <template #right>
        <view class="grade-filter" @click="showGradeSelector = true">
          <view class="grade-info">
            <view class="grade-name">
              {{ gradeValue.gradeName || '' }}
              <text class="grade-version">（{{ gradeValue.versionName || '' }})</text>
            </view>
          </view>
          <LkSvg
            width="24px"
            height="24px"
            src="/static/learning-resource/down.svg"
            class="arrow-icon"
          />
        </view>
      </template>
    </up-navbar>

    <view class="subject-section">
      <view class="subject-content">
        <!-- 学科Tab区 -->
        <scroll-view scroll-x="true" class="subject-scroll">
          <LkTabGroup
            v-model="currentSubject"
            :tabs="subjectTabs"
            @update:modelValue="handleSubjectChange"
          />
        </scroll-view>
        <view class="difficulty-select-wrapper">
          <uni-data-select
            class="difficulty-select"
            v-model="currentDifficulty"
            :localdata="difficultyOptions"
            placeholder="选择难度"
            @change="handleDifficultyChange"
          />
        </view>
      </view>
    </view>

    <LkGradeSelector
      :show="showGradeSelector"
      :value="gradeValue"
      @confirm="handleGradeConfirm"
      @close="handleGradeClose"
    />

    <!-- 单元展示区 -->
    <view class="unit-section">
      <uni-collapse accordion>
        <uni-collapse-item
          v-for="(unit, index) in units"
          :key="unit.id"
          :title="unit.name"
          :open="openUnitIndex === index"
          @change="handleUnitChange(index)"
        >
          <view class="unit-content">
            <!-- 知识点列表 -->
            <view
              v-for="knowledgePoint in unit.knowledgePoints"
              :key="knowledgePoint.id"
              class="knowledge-point-item"
            >
              <text class="knowledge-point-name">{{ knowledgePoint.name }}</text>
              <button class="practice-button" @click="startPractice(knowledgePoint)">
                开始练习
              </button>
            </view>
          </view>
        </uni-collapse-item>
      </uni-collapse>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import LkGradeSelector from '@/components/LkGradeSelector/index.vue';

const handleBack = () => {
  uni.navigateBack();
};

// 单元相关数据和方法
const openUnitIndex = ref(-1); // 当前展开的单元索引，-1表示没有展开的单元

// 模拟单元和知识点数据
const units = ref([
  {
    id: 1,
    name: '单元一',
    knowledgePoints: [
      { id: 1, name: '知识点1' },
      { id: 2, name: '知识点2' },
      { id: 3, name: '知识点3' },
    ],
  },
  {
    id: 2,
    name: '单元二',
    knowledgePoints: [
      { id: 4, name: '知识点4' },
      { id: 5, name: '知识点5' },
    ],
  },
  {
    id: 3,
    name: '单元三',
    knowledgePoints: [
      { id: 6, name: '知识点6' },
      { id: 7, name: '知识点7' },
      { id: 8, name: '知识点8' },
      { id: 9, name: '知识点9' },
    ],
  },
]);

// 单元切换事件
const handleUnitChange = (index: number) => {
  if (openUnitIndex.value === index) {
    // 如果点击的是已经展开的单元，则收起
    openUnitIndex.value = -1;
  } else {
    // 否则展开点击的单元
    openUnitIndex.value = index;
  }
};

// 开始练习事件
const startPractice = (knowledgePoint: any) => {
  console.log('开始练习:', knowledgePoint);
  // 这里可以添加开始练习的逻辑
};

// 年级选择器相关状态
const showGradeSelector = ref(false);
const gradeValue = ref({
  gradeName: '',
  gradeId: 0,
  versionName: '',
  versionId: 0,
  semesterId: 0,
  textbookVersionId: 0,
  textbookVolumeId: 0,
});

// 年级选择确认事件
const handleGradeConfirm = (value: {
  gradeName: string;
  versionName: string;
  semesterId: number;
  textbookVersionId: number;
  textbookVolumeId: number;
}) => {
  gradeValue.value = {
    ...gradeValue.value,
    ...value,
  };
  showGradeSelector.value = false;
  // 可以在这里处理确认后的逻辑
  console.log('选择的年级信息：', value);
};

// 年级选择关闭事件
const handleGradeClose = () => {
  showGradeSelector.value = false;
};

// 学科Tab相关数据和方法
const currentSubject = ref(0);

// 模拟学科Tab数据
const subjectTabs = ref([
  { value: 0, label: '全部' },
  { value: 1, label: '语文' },
  { value: 2, label: '数学' },
  { value: 3, label: '英语' },
  { value: 4, label: '物理' },
  { value: 5, label: '化学' },
  { value: 6, label: '生物' },
  { value: 7, label: '历史' },
  { value: 8, label: '地理' },
  { value: 9, label: '政治' },
]);

// 学科切换事件
const handleSubjectChange = (subject: number) => {
  currentSubject.value = subject;
  console.log('切换学科:', subject);
};

// 难度选择相关数据和方法
const currentDifficulty = ref(0);

// 模拟难度选项数据
const difficultyOptions = ref([
  { value: 0, text: '全部难度' },
  { value: 1, text: '简单' },
  { value: 2, text: '中等' },
  { value: 3, text: '困难' },
]);

// 难度切换事件
const handleDifficultyChange = (difficulty: number) => {
  currentDifficulty.value = difficulty;
  console.log('切换难度:', difficulty);
};
</script>

<style scoped lang="scss">
.grade-filter {
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
  height: auto;
  min-height: 60rpx; /* 减小最小高度 */
  background-color: #f5f5f5; /* 淡灰色背景 */
  padding: 6rpx 12rpx; /* 减小内边距 */

  &:active {
    transform: scale(0.98);
  }

  .grade-info {
    display: flex;
    align-items: center;
    flex: 1;

    .grade-name {
      font-size: 20rpx; /* 进一步调小字体 */
      font-weight: 500; /* 调整字重 */
      color: #1e293b;
      white-space: nowrap;
      display: flex;
      align-items: center;
    }

    .grade-version {
      font-size: 18rpx; /* 进一步调小字体 */
      color: #64748b;
      white-space: nowrap;
    }
  }

  .arrow-icon {
    flex-shrink: 0;
    opacity: 0.6;
    transition: transform 0.3s ease;
  }
}

.subject-section {
  padding: 20rpx 30rpx;
}

.subject-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.subject-scroll {
  flex: 1;
  min-width: 0; /* 确保可以收缩 */
}

.difficulty-select-wrapper {
  width: 200rpx;
  min-width: 200rpx;
  flex-shrink: 0; /* 防止收缩 */
  margin-left: 20rpx;
}

.difficulty-select {
  width: 100%;
  height: 60rpx; /* 设置固定高度 */

  :deep(.uni-select) {
    height: 60rpx;
    line-height: 60rpx;
  }

  :deep(.uni-select__input-box) {
    height: 60rpx;
    line-height: 60rpx;
  }
}

.unit-section {
  padding: 20rpx 30rpx;
}

.unit-content {
  padding: 20rpx 0;
}

.knowledge-point-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.knowledge-point-name {
  font-size: 28rpx;
  color: #333;
}

.practice-button {
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}
</style>
