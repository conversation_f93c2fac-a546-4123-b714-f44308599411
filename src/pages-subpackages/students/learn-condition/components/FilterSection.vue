<template>
  <view class="filter-section">
    <view class="filter-row">
      <!-- 学期筛选 -->
      <view class="filter-item" @tap="handleSemesterSelect">
        <view class="filter-value">
          <text class="filter-text">{{ currentSemesterLabel }}</text>
          <LkSvg width="16px" height="16px" src="/static/common/solid_up.svg" />
        </view>
      </view>

      <!-- 日期范围筛选 -->
      <view class="filter-item" @tap="handleDateRangeSelect" style="margin-left: 10px">
        <view class="filter-value">
          <text class="filter-text">{{ dateRangeText }}</text>
          <LkSvg width="16px" height="16px" src="/static/common/solid_up.svg" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  /** 当前学期标签 */
  currentSemesterLabel: string;
  /** 日期范围文本 */
  dateRangeText: string;
}

interface Emits {
  (e: 'semesterSelect'): void;
  (e: 'dateRangeSelect'): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

// 处理学期选择
const handleSemesterSelect = () => {
  emit('semesterSelect');
};

// 处理日期范围选择
const handleDateRangeSelect = () => {
  emit('dateRangeSelect');
};
</script>

<style lang="scss" scoped>
.filter-section {
  margin: 20rpx 30rpx;
}

.filter-row {
  display: flex;
}

.filter-item {
  padding: 15rpx 20rpx;
  background: #fff;
  border-radius: 8px;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 0;
}

.filter-value {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.filter-text {
  font-size: 28rpx;
  color: #1e293b;
  font-weight: 500;
  flex: 8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
