<template>
  <view class="analysis-card">
    <!-- 卡片标题 -->
    <view class="card-title">
      <LkSvg src="/static/study/titleBg.svg" class="card-title-bg" />
      <view class="card-title-text" :style="titleStyle">{{ title }}</view>
      <view class="card-title-space"></view>
    </view>

    <!-- 学科选择器 -->
    <view v-if="showSubjectSelector" class="card-select">
      <uni-data-select v-model="selectedSubject" :localdata="subjectOptions"></uni-data-select>
    </view>

    <!-- 卡片内容 -->
    <view class="card-content">
      <slot />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  /** 卡片标题 */
  title: string;
  /** 是否显示学科选择器 */
  showSubjectSelector?: boolean;
  /** 学科选项 */
  subjectOptions?: Array<{ value: number; text: string }>;
  /** 当前选中的学科 */
  currentSubject?: number;
  /** 标题样式 */
  titleStyle?: Record<string, string>;
}

interface Emits {
  (e: 'subjectChange', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  showSubjectSelector: false,
  subjectOptions: () => [],
  currentSubject: 0,
  titleStyle: () => ({}),
});

const emit = defineEmits<Emits>();

// 双向绑定学科选择
const selectedSubject = computed({
  get: () => props.currentSubject,
  set: value => {
    emit('subjectChange', String(value));
  },
});
</script>

<style lang="scss" scoped>
.analysis-card {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  margin: 40rpx 30rpx;
  position: relative;
}

.card-title {
  position: relative;
  left: 0px;
  top: -15px;
  width: 120px;
  height: 40px;

  .card-title-bg {
    width: 120px;
    height: 40px;
  }

  .card-title-text {
    position: absolute;
    top: 6px;
    left: 13px;
    font-size: 21px;
    color: #fff;
    font-weight: 500;
  }

  .card-title-space {
    position: absolute;
    height: 5px;
    border-radius: 999px 999px 0px 0px;
    background: #2f00ac;
    width: 10px;
    top: 0px;
    right: 0px;
  }
}

.card-select {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 100px;
}

.card-content {
  // padding: 0 10px;
}
</style>
