<template>
  <view class="chart-section">
    <!-- 标题和切换器 -->
    <view class="chart-header" v-if="showHeader">
      <view class="chart-title">{{ title }}</view>
      <up-subsection
        v-if="showSwitcher"
        :list="switcherOptions"
        :current="currentIndex"
        @change="handleSwitcherChange"
        style="width: 120px"
        activeColor="#000"
      />
    </view>

    <!-- 图表容器 -->
    <view class="chart-wrapper" :style="chartWrapperStyle">
      <transition name="chart-fade" mode="out-in">
        <qiun-data-charts
          :key="`${chartType}-${currentIndex}`"
          :type="chartType"
          :opts="chartOpts"
          :chartData="chartData"
          :tooltipFormat="tooltipFormat"
        />
      </transition>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import qiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
import useDeviceDetection from '@/hooks/useDeviceDetection';

interface Props {
  /** 图表标题 */
  title?: string;
  /** 图表类型 */
  chartType: 'area' | 'line' | 'radar' | 'ring';
  /** 图表配置 */
  chartOpts: any;
  /** 图表数据 */
  chartData: any;
  /** 是否显示头部 */
  showHeader?: boolean;
  /** 是否显示切换器 */
  showSwitcher?: boolean;
  /** 切换器选项 */
  switcherOptions?: Array<{ name: string; value: string }>;
  /** 当前选中的切换器索引 */
  currentIndex?: number;
  /** tooltip格式化方法名 */
  tooltipFormat?: string;
}

interface Emits {
  (e: 'switcherChange', index: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showHeader: true,
  showSwitcher: false,
  switcherOptions: () => [],
  currentIndex: 0,
  tooltipFormat: undefined,
});

const emit = defineEmits<Emits>();

// 设备检测
const { isMobile, isTablet } = useDeviceDetection();

// 判断是否为空数据
const isEmpty = computed(() => {
  if (!props.chartData) return true;

  if (props.chartType === 'radar' || props.chartType === 'ring') {
    return (
      !props.chartData.series ||
      props.chartData.series.length === 0 ||
      !props.chartData.series[0].data ||
      props.chartData.series[0].data.length === 0
    );
  }

  return (
    !props.chartData.categories ||
    props.chartData.categories.length === 0 ||
    !props.chartData.series ||
    props.chartData.series.length === 0
  );
});

// 根据图表类型和设备类型计算图表容器样式
const chartWrapperStyle = computed(() => {
  let height = '300px'; // 默认高度

  // 根据图表标题和设备类型调整尺寸
  if (props.title === '知识点掌握情况') {
    // 知识点掌握情况：手机端尺寸要小一点，平板端保持不变
    height = isMobile.value ? '300px' : '300px';
  } else if (props.title === '错误高频区分析') {
    // 错误高频区分析：手机端尺寸要高一些，平板端保持不变
    height = isMobile.value ? '300px' : '300px';
  }

  return {
    height,
    width: '100%',
    position: 'relative' as const,
  };
});

// 处理切换器变化
const handleSwitcherChange = (index: number) => {
  emit('switcherChange', index);
};
</script>

<style lang="scss" scoped>
.chart-section {
  margin-bottom: 20rpx;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.chart-title {
  font-size: 18px;
  color: #000;
  position: relative;
  margin-left: 12px;
  font-weight: 500;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    height: 12px;
    width: 6px;
    background: #7d4dff;
    border-radius: 10px;
    transform: translateY(-50%);
  }
}

.chart-wrapper {
  position: relative;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: #f8f9fa;
  border-radius: 8px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

/* 图表切换过渡动画 */
.chart-fade-enter-active {
  transition: opacity 0.15s ease-in;
}

.chart-fade-leave-active {
  transition: opacity 0.1s ease-out;
}

.chart-fade-enter-from,
.chart-fade-leave-to {
  opacity: 0;
}
</style>
