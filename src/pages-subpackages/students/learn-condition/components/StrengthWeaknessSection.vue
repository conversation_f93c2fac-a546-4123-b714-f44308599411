<template>
  <view class="strength-weakness-section">
    <view class="section-title">学科优势与薄弱分析</view>

    <up-row gutter="10">
      <!-- 优势学科 -->
      <up-col
        :span="strengthSubjects.length > 0 && weaknessSubjects.length <= 0 ? 12 : 6"
        v-if="strengthSubjects.length > 0"
      >
        <view class="subject-card strength-card">
          <span class="subject-card-title strength-title">优势学科</span>
          <template v-for="subject in strengthSubjects" :key="subject">
            <view class="subject-card-content strength-content">
              <view>{{ subject }}</view>
            </view>
          </template>
        </view>
      </up-col>

      <!-- 薄弱学科 -->
      <up-col
        :span="weaknessSubjects.length > 0 && strengthSubjects.length <= 0 ? 12 : 6"
        v-if="weaknessSubjects.length > 0"
      >
        <view class="subject-card weakness-card">
          <span class="subject-card-title weakness-title">薄弱学科</span>
          <template v-for="subject in weaknessSubjects" :key="subject">
            <view class="subject-card-content weakness-content">
              <view>{{ subject }}</view>
            </view>
          </template>
        </view>
      </up-col>
    </up-row>

    <!-- 学科表现分析 -->
    <view class="analysis-card">
      <span class="analysis-card-title">学科表现分析</span>
      <view class="analysis-content">
        <LKMarkdown v-if="analysisText" :source="analysisText" />
        <view v-else class="no-data">暂无数据</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import LKMarkdown from '@/components/LKMarkdown/index.vue';

interface Props {
  /** 学科优势与薄弱数据 */
  data: {
    优势学科?: string[];
    薄弱学科?: string[];
    学科表现分析?: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
});

// 优势学科列表
const strengthSubjects = computed(() => props.data.优势学科 || []);

// 薄弱学科列表
const weaknessSubjects = computed(() => props.data.薄弱学科 || []);

// 分析文本
const analysisText = computed(() => props.data.学科表现分析 || '');
</script>

<style lang="scss" scoped>
.strength-weakness-section {
  // padding: 0 10px;
}

.section-title {
  font-size: 18px;
  color: #000;
  position: relative;
  margin-left: 12px;
  margin-bottom: 10px;
  font-weight: 500;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    height: 12px;
    width: 6px;
    background: #7d4dff;
    border-radius: 10px;
    transform: translateY(-50%);
  }
}

.subject-card {
  border-radius: 12px;
  padding: 10px 12px;
  position: relative;
}

.strength-card {
  background: #e3f9ee;
}

.weakness-card {
  background: #fff2ef;
}

.subject-card-title {
  font-size: 16px;
  color: #fff;
  font-weight: 500;
  border-radius: 12px 0px 20px 0px;
  position: relative;
  padding: 5px 10px;
  top: -5px;
  left: -12px;
}

.strength-title {
  background: #0bc68d;
}

.weakness-title {
  background: #ef5148;
}

.subject-card-content {
  border-radius: 10px;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  margin-top: 10px;
  font-weight: 600;
}

.strength-content {
  color: #0bc68d;
}

.weakness-content {
  color: #ef5148;
}

.analysis-card {
  background: #f7f9ff;
  border-radius: 12px;
  padding: 10px 12px;
  margin-top: 10px;
  position: relative;
}

.analysis-card-title {
  background: #e2e7fe;
  color: #7d4dff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px 0px 20px 0px;
  position: relative;
  padding: 5px 10px;
  top: -5px;
  left: -12px;
}

.analysis-content {
  margin-top: 6px;
  margin-left: -10px;
}

.no-data {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px 0;
}
</style>
