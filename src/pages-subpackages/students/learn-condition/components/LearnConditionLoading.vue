<template>
  <view
    v-if="show"
    class="learn-condition-loading"
    :class="[
      `size-${size}`,
      {
        fullscreen: fullscreen,
        overlay: overlay,
      },
    ]"
    :style="customStyle"
  >
    <!-- 加载动画 -->
    <view class="loading-content">
      <!-- 自定义SVG动画 -->
      <view class="loading-custom">
        <LkSvg
          src="/static/study/loading_condition.svg"
          :width="iconSize"
          :height="iconSize"
          customClass="custom-loading-icon"
        />
      </view>

      <!-- 加载文字 -->
      <text v-if="text" class="loading-text">{{ text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { CSSProperties } from 'vue';
import LkSvg from '@/components/svg/index.vue';

// 定义组件属性
interface Props {
  // 是否显示加载状态
  show?: boolean;
  // 加载提示文字
  text?: string;
  // 尺寸：xs、small、medium、large、xl、xxl
  size?: 'xs' | 'small' | 'medium' | 'large' | 'xl' | 'xxl';
  // 是否全屏显示
  fullscreen?: boolean;
  // 是否显示遮罩层
  overlay?: boolean;
  // 自定义样式
  customStyle?: CSSProperties;
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  show: true,
  text: '加载中...',
  size: 'medium',
  fullscreen: false,
  overlay: false,
  customStyle: () => ({}),
});

// 计算图标尺寸
const iconSize = computed(() => {
  switch (props.size) {
    case 'xs':
      return '32rpx';
    case 'small':
      return '48rpx';
    case 'large':
      return '88rpx';
    case 'xl':
      return '120rpx';
    case 'xxl':
      return '160rpx';
    default:
      return '68rpx'; // medium 默认使用 68rpx，与原始 SVG 尺寸匹配
  }
});
</script>

<style lang="scss" scoped>
.learn-condition-loading {
  display: flex;
  align-items: center;
  justify-content: center;

  // 全屏模式
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
  }

  // 遮罩层模式
  &.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  // 尺寸设置
  &.size-xs {
    .loading-text {
      font-size: 20rpx;
      margin-top: 12rpx;
    }
  }

  &.size-small {
    .loading-text {
      font-size: 24rpx;
      margin-top: 16rpx;
    }
  }

  &.size-medium {
    .loading-text {
      font-size: 28rpx;
      margin-top: 20rpx;
    }
  }

  &.size-large {
    .loading-text {
      font-size: 32rpx;
      margin-top: 24rpx;
    }
  }

  &.size-xl {
    .loading-text {
      font-size: 36rpx;
      margin-top: 28rpx;
    }
  }

  &.size-xxl {
    .loading-text {
      font-size: 42rpx;
      margin-top: 32rpx;
    }
  }

  // 自定义SVG动画
  .loading-custom {
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(.custom-loading-icon) {
      animation: spin 2s linear infinite;
    }
  }

  // 加载文字
  .loading-text {
    font-size: 28rpx;
    color: #86909c;
    text-align: center;
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
