import { ref, computed } from 'vue';
import type {
  SemestersItem,
  ClassRankTrend,
  SubjectScoreTrendItem,
  KnowledgeAnalysis,
} from '@/types/students/learnCondition';
import {
  getClassRankTrend,
  getKnowledgeAnalysis,
  getSemesters,
  getSubjects,
  getSubjectScoreTrend,
} from '@/api/students/learnCondition';
import {
  transformSemesterOptions,
  getCurrentSemesterOption,
  formatDateRange,
  type SemesterOption,
} from '../utils';

/**
 * 学习状态数据管理 Composable
 * 负责管理所有API调用、数据状态和加载状态
 */
export function useLearnConditionData() {
  // ===== 基础状态 =====
  const loading = ref(false);
  const error = ref<string>('');

  // ===== 详细加载状态 =====
  const loadingStates = ref({
    semesters: false,
    subjects: false,
    classRankTrend: false,
    subjectScoreTrend: false,
    knowledgeAnalysis: false,
  });

  // 计算是否有任何接口在加载中
  const isAnyLoading = computed(() => {
    return Object.values(loadingStates.value).some(state => state);
  });

  // ===== 学期相关状态 =====
  const semesterOptions = ref<SemesterOption[]>([]);
  const currentSemester = ref<SemesterOption>({
    id: '',
    createTime: '',
    updateTime: '',
    isDeleted: 0,
    tenantId: '',
    year: '',
    type: 1,
    startDate: '',
    endDate: '',
    isCurrent: 0,
    semesterId: 0,
    semesterName: '',
    value: '',
    label: '',
  });

  // ===== 日期范围状态 =====
  const currentDateRange = ref({
    startDate: '',
    endDate: '',
    displayText: '',
  });

  // ===== 学科相关状态 =====
  const subjectOptions = ref<{ value: number; text: string }[]>([]);
  const currentSubject = ref<number>(0);

  // ===== 图表数据状态 =====
  const classRankData = ref<ClassRankTrend>({
    monthRankingTrend: [],
    weekRankingTrend: [],
    totalCount: 0,
  });

  const subjectScoreData = ref<SubjectScoreTrendItem[]>([]);
  const knowledgeAnalysisData = ref<KnowledgeAnalysis>({
    correctRateList: [],
    wrongRateList: [],
  });

  // ===== 计算属性 =====
  const dateRangeText = computed(() => currentDateRange.value.displayText);

  // ===== API调用方法 =====

  /**
   * 获取学期列表
   */
  const fetchSemesters = async () => {
    try {
      loadingStates.value.semesters = true;
      error.value = '';

      const res = await getSemesters();
      // 处理接口返回空数据的情况
      const transformedOptions = res && Array.isArray(res) ? transformSemesterOptions(res) : [];

      semesterOptions.value = transformedOptions;

      // 设置当前学期
      const currentOption = getCurrentSemesterOption(transformedOptions);
      if (currentOption) {
        currentSemester.value = currentOption;
        updateDateRange(currentOption);
      }

      return transformedOptions;
    } catch (err) {
      error.value = '获取学期列表失败';
      console.error('获取学期列表失败:', err);
      throw err;
    } finally {
      loadingStates.value.semesters = false;
    }
  };

  /**
   * 获取学科列表
   */
  const fetchSubjects = async () => {
    try {
      loadingStates.value.subjects = true;
      error.value = '';

      const res = await getSubjects({
        semesterId: Number(currentSemester.value.id),
        startDate: currentDateRange.value.startDate,
        endDate: currentDateRange.value.endDate,
      });

      subjectOptions.value = res.map(item => ({
        value: Number(item.id),
        text: item.name,
      }));

      // 设置默认学科 - 保持用户选择的学科状态
      if (subjectOptions.value.length > 0) {
        // 检查当前选中的学科是否在新的学科列表中
        const currentSubjectExists = subjectOptions.value.some(
          option => option.value === currentSubject.value
        );

        // 只有在当前学科不存在或者是初始状态时才重置为第一个学科
        if (!currentSubjectExists || currentSubject.value === 0) {
          currentSubject.value = subjectOptions.value[0].value;
        } else {
          console.log('fetchSubjects - 保持当前学科:', currentSubject.value);
        }
      }

      return subjectOptions.value;
    } catch (err) {
      error.value = '获取学科列表失败';
      console.error('获取学科列表失败:', err);
      throw err;
    } finally {
      loadingStates.value.subjects = false;
    }
  };

  /**
   * 获取班级排名趋势数据
   */
  const fetchClassRankTrend = async () => {
    try {
      loadingStates.value.classRankTrend = true;
      error.value = '';

      const res = await getClassRankTrend({
        semesterId: Number(currentSemester.value.id),
        startDate: currentDateRange.value.startDate,
        endDate: currentDateRange.value.endDate,
      });

      classRankData.value = res;
      return res;
    } catch (err) {
      error.value = '获取班级排名趋势失败';
      console.error('获取班级排名趋势失败:', err);
      throw err;
    } finally {
      loadingStates.value.classRankTrend = false;
    }
  };

  /**
   * 获取单科成绩趋势数据
   */
  const fetchSubjectScoreTrend = async () => {
    try {
      loadingStates.value.subjectScoreTrend = true;
      error.value = '';

      const res = await getSubjectScoreTrend({
        semesterId: Number(currentSemester.value.id),
        startDate: currentDateRange.value.startDate,
        endDate: currentDateRange.value.endDate,
      });

      subjectScoreData.value = res;
      return res;
    } catch (err) {
      error.value = '获取单科成绩趋势失败';
      console.error('获取单科成绩趋势失败:', err);
      throw err;
    } finally {
      loadingStates.value.subjectScoreTrend = false;
    }
  };

  /**
   * 获取知识点分析数据
   */
  const fetchKnowledgeAnalysis = async () => {
    try {
      loadingStates.value.knowledgeAnalysis = true;
      error.value = '';

      const res = await getKnowledgeAnalysis({
        semesterId: Number(currentSemester.value.id),
        subjectId: currentSubject.value,
        startDate: currentDateRange.value.startDate,
        endDate: currentDateRange.value.endDate,
      });

      knowledgeAnalysisData.value = res;
      return res;
    } catch (err) {
      error.value = '获取知识点分析失败';
      console.error('获取知识点分析失败:', err);
      throw err;
    } finally {
      loadingStates.value.knowledgeAnalysis = false;
    }
  };

  // ===== 数据更新方法 =====

  /**
   * 更新当前学期
   */
  const updateCurrentSemester = (semesterId: string) => {
    const semester = semesterOptions.value.find(item => item.value === semesterId);
    if (semester) {
      currentSemester.value = semester;
      updateDateRange(semester);
    }
  };

  /**
   * 更新日期范围
   */
  const updateDateRange = (dateRange: { startDate: string; endDate: string }) => {
    currentDateRange.value = {
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      displayText: formatDateRange(dateRange.startDate, dateRange.endDate),
    };
  };

  /**
   * 更新当前学科
   */
  const updateCurrentSubject = (subjectId: number) => {
    currentSubject.value = subjectId;
  };

  /**
   * 初始化所有数据
   */
  const initializeData = async () => {
    try {
      // 首先获取学期列表
      const semesterList = await fetchSemesters();

      // 如果学期列表为空，则不继续调用其他接口
      if (!semesterList || semesterList.length === 0) {
        console.log('学期列表为空，跳过其他接口调用');
        return;
      }

      // 学期列表不为空，继续获取其他数据
      await fetchSubjects();
      await Promise.all([
        fetchClassRankTrend(),
        fetchSubjectScoreTrend(),
        fetchKnowledgeAnalysis(),
      ]);
    } catch (err) {
      console.error('初始化数据失败:', err);
      throw err;
    }
  };

  /**
   * 学期切换后刷新数据
   * 重新获取学科列表和所有依赖时间的数据接口
   */
  const refreshDataAfterSemesterChange = async () => {
    try {
      // 学期切换需要重新获取学科列表
      await fetchSubjects();

      // 并行获取所有依赖时间的数据接口
      await Promise.all([
        fetchClassRankTrend(),
        fetchSubjectScoreTrend(),
        fetchKnowledgeAnalysis(),
      ]);
    } catch (err) {
      console.error('学期切换后数据刷新失败:', err);
      throw err;
    }
  };

  /**
   * 日期范围变化后刷新数据
   * 只调用依赖时间范围的数据接口，不包括学期列表和学科列表
   */
  const refreshDataAfterDateRangeChange = async () => {
    try {
      // 并行获取所有依赖时间的数据接口
      await Promise.all([
        fetchClassRankTrend(),
        fetchSubjectScoreTrend(),
        fetchKnowledgeAnalysis(),
      ]);
    } catch (err) {
      console.error('日期范围变化后数据刷新失败:', err);
      throw err;
    }
  };

  return {
    // 状态
    loading,
    error,
    loadingStates,
    isAnyLoading,
    semesterOptions,
    currentSemester,
    currentDateRange,
    subjectOptions,
    currentSubject,
    classRankData,
    subjectScoreData,
    knowledgeAnalysisData,

    // 计算属性
    dateRangeText,

    // 方法
    fetchSemesters,
    fetchSubjects,
    fetchClassRankTrend,
    fetchSubjectScoreTrend,
    fetchKnowledgeAnalysis,
    updateCurrentSemester,
    updateDateRange,
    updateCurrentSubject,
    initializeData,
    refreshDataAfterSemesterChange,
    refreshDataAfterDateRangeChange,
  };
}
