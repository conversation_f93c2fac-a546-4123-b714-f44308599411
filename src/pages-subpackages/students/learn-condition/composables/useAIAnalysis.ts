import { ref, computed } from 'vue';
import ai from '@/common/ai';
import type { ClassRankTrend, SubjectScoreTrendItem, KnowledgeAnalysis } from '@/types/students/learnCondition';

/**
 * AI分析 Composable
 * 负责管理AI分析请求和结果状态
 */
export function useAIAnalysis() {
  // ===== AI分析结果状态 =====
  const generalDiscussion = ref<string>('');
  const knowledgeMastery = ref<string>('');
  const highFreqErrorAreas = ref<string>('');
  const subjectStrengthsWeaknesses = ref<any>({});
  
  // ===== 加载状态 =====
  const aiLoading = ref(false);
  const aiError = ref<string>('');

  // ===== 详细AI加载状态 =====
  const aiLoadingStates = ref({
    generalDiscussion: false,
    subjectStrengthsWeaknesses: false,
    knowledgeMastery: false,
    highFreqErrorAreas: false,
  });

  // 计算是否有任何AI请求在进行中
  const isAnyAILoading = computed(() => {
    return Object.values(aiLoadingStates.value).some(state => state);
  });

  // ===== 请求取消控制 =====
  const abortSignals = new Map<string, any>();

  /**
   * 通用AI聊天请求方法
   */
  const chatOnce = async (businessType: string, content: any): Promise<string> => {
    // 取消之前的同类型请求
    const existingSignal = abortSignals.get(businessType);
    if (existingSignal) {
      existingSignal.abort();
    }

    // 创建新的取消信号
    const signal = ai.AbortSignal();
    abortSignals.set(businessType, signal);

    try {
      aiLoading.value = true;
      aiError.value = '';

      const { responseText } = await ai.fetch({
        url: '/huayun-tool/api/chat/commonBusiness',
        data: {
          businessType,
          messages: [
            {
              role: 'user',
              hideInUI: false,
              content: JSON.stringify(content),
            },
          ],
        },
        onMessage: () => {},
        abortSignal: signal,
      });

      // 请求成功后移除信号
      abortSignals.delete(businessType);

      return responseText || '';
    } catch (error: any) {
      // 如果是主动取消的请求，不设置错误状态
      if (error.name !== 'AbortError') {
        aiError.value = `AI分析请求失败: ${error.message}`;
        console.error('AI分析请求失败:', error);
      }
      abortSignals.delete(businessType);
      throw error;
    } finally {
      aiLoading.value = false;
    }
  };

  /**
   * 获取学生学习分析总览
   */
  const fetchGeneralDiscussion = async (
    classRankingTrend: ClassRankTrend,
    studentSubjectScoreTrend: SubjectScoreTrendItem[],
    knowledgeAnalysis: KnowledgeAnalysis
  ) => {
    try {
      aiLoadingStates.value.generalDiscussion = true;
      const result = await chatOnce('studentLearningAnalysisOverview', {
        classRankingTrend: { ...classRankingTrend },
        studentSubjectScoreTrend: { ...studentSubjectScoreTrend },
        KnowledgeAnalysis: { ...knowledgeAnalysis },
      });

      generalDiscussion.value = result;
      return result;
    } catch (error) {
      console.error('获取总体分析失败:', error);
      throw error;
    } finally {
      aiLoadingStates.value.generalDiscussion = false;
    }
  };

  /**
   * 获取学科优势与薄弱分析
   */
  const fetchSubjectStrengthsWeaknesses = async (
    classRankingTrend: ClassRankTrend,
    studentSubjectScoreTrend: SubjectScoreTrendItem[],
    knowledgeAnalysis: KnowledgeAnalysis
  ) => {
    try {
      aiLoadingStates.value.subjectStrengthsWeaknesses = true;
      const result = await chatOnce('subjectStrengthsWeaknesses', {
        classRankingTrend: { ...classRankingTrend },
        studentSubjectScoreTrend: { ...studentSubjectScoreTrend },
        KnowledgeAnalysis: { ...knowledgeAnalysis },
      });

      const parsedResult = JSON.parse(result);
      subjectStrengthsWeaknesses.value = parsedResult.data || {};
      return parsedResult.data;
    } catch (error) {
      console.error('获取学科优势与薄弱分析失败:', error);
      throw error;
    } finally {
      aiLoadingStates.value.subjectStrengthsWeaknesses = false;
    }
  };

  /**
   * 获取知识点掌握分析
   */
  const fetchKnowledgeMastery = async (correctRateList: KnowledgeAnalysis['correctRateList']) => {
    try {
      aiLoadingStates.value.knowledgeMastery = true;
      const result = await chatOnce('knowledgeMastery', {
        data: correctRateList,
      });

      knowledgeMastery.value = result;
      return result;
    } catch (error) {
      console.error('获取知识点掌握分析失败:', error);
      throw error;
    } finally {
      aiLoadingStates.value.knowledgeMastery = false;
    }
  };

  /**
   * 获取高频错误区域分析
   */
  const fetchHighFreqErrorAreas = async (wrongRateList: KnowledgeAnalysis['wrongRateList']) => {
    try {
      aiLoadingStates.value.highFreqErrorAreas = true;
      const result = await chatOnce('highFreqErrorAreas', {
        data: wrongRateList,
      });

      highFreqErrorAreas.value = result;
      return result;
    } catch (error) {
      console.error('获取高频错误区域分析失败:', error);
      throw error;
    } finally {
      aiLoadingStates.value.highFreqErrorAreas = false;
    }
  };

  /**
   * 批量获取所有AI分析
   */
  const fetchAllAnalysis = async (
    classRankingTrend: ClassRankTrend,
    studentSubjectScoreTrend: SubjectScoreTrendItem[],
    knowledgeAnalysis: KnowledgeAnalysis
  ) => {
    try {
      // 并行执行所有AI分析请求
      await Promise.allSettled([
        fetchGeneralDiscussion(classRankingTrend, studentSubjectScoreTrend, knowledgeAnalysis),
        fetchSubjectStrengthsWeaknesses(classRankingTrend, studentSubjectScoreTrend, knowledgeAnalysis),
        fetchKnowledgeMastery(knowledgeAnalysis.correctRateList),
        fetchHighFreqErrorAreas(knowledgeAnalysis.wrongRateList),
      ]);
    } catch (error) {
      console.error('批量获取AI分析失败:', error);
      throw error;
    }
  };

  /**
   * 取消所有进行中的AI请求
   */
  const cancelAllRequests = () => {
    abortSignals.forEach((signal: any, businessType: string) => {
      signal.abort();
      console.log(`已取消AI请求: ${businessType}`);
    });
    abortSignals.clear();
  };

  /**
   * 重置所有AI分析结果
   */
  const resetAnalysisResults = () => {
    generalDiscussion.value = '';
    knowledgeMastery.value = '';
    highFreqErrorAreas.value = '';
    subjectStrengthsWeaknesses.value = {};
    aiError.value = '';
  };

  return {
    // 状态
    generalDiscussion,
    knowledgeMastery,
    highFreqErrorAreas,
    subjectStrengthsWeaknesses,
    aiLoading,
    aiError,
    aiLoadingStates,
    isAnyAILoading,

    // 方法
    chatOnce,
    fetchGeneralDiscussion,
    fetchSubjectStrengthsWeaknesses,
    fetchKnowledgeMastery,
    fetchHighFreqErrorAreas,
    fetchAllAnalysis,
    cancelAllRequests,
    resetAnalysisResults,
  };
}
