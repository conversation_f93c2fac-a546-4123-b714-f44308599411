import { ref, computed, watch, type Ref } from 'vue';
import type {
  ClassRankTrend,
  SubjectScoreTrendItem,
  KnowledgeAnalysis,
} from '@/types/students/learnCondition';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';

/**
 * 图表数据处理 Composable
 * 负责图表数据转换、配置管理和状态控制
 */
export function useChartData(classRankData?: Ref<ClassRankTrend>) {
  // ===== 设备检测 =====
  const { deviceType, screenWidth } = useDeviceDetection();

  // ===== 趋势类型状态 =====
  const currentClassRankTrendType = ref<'week' | 'month'>('week');
  const currentSubjectTrendType = ref<'week' | 'month'>('week');

  // ===== 响应式横轴数量计算 =====
  /**
   * 根据设备类型和屏幕宽度动态计算横轴显示的数据点数量
   * @returns 适合当前设备的横轴数据点数量
   */
  const getResponsiveMaxPoints = computed(() => {
    const currentDeviceType = deviceType.value;
    const currentScreenWidth = screenWidth.value;

    switch (currentDeviceType) {
      case 'mobile':
        // 手机端：4-6个点，根据屏幕宽度细分
        if (currentScreenWidth <= 375) {
          return 4; // 小屏手机（iPhone SE等）
        } else if (currentScreenWidth <= 414) {
          return 5; // 中等手机（iPhone 12等）
        } else {
          return 6; // 大屏手机（iPhone 12 Pro Max等）
        }

      case 'tablet':
        // 平板端：6-12个点，根据屏幕宽度细分
        if (currentScreenWidth <= 768) {
          return 6; // 小平板（iPad mini, 7-8寸平板）
        } else if (currentScreenWidth <= 1024) {
          return 8; // 中等平板（iPad, 9-10寸平板）
        } else if (currentScreenWidth <= 1366) {
          return 10; // 大平板（iPad Pro 11寸, Surface Pro）
        } else {
          return 12; // 超大平板（iPad Pro 12.9寸, Surface Studio）
        }

      case 'desktop':
        // 桌面端：10-16个点
        if (currentScreenWidth <= 1440) {
          return 10; // 小桌面屏幕
        } else if (currentScreenWidth <= 1920) {
          return 12; // 标准桌面屏幕
        } else {
          return 16; // 4K及以上大屏
        }

      default:
        return 8; // 默认值，兼容未知设备
    }
  });

  // ===== 图表配置 =====
  const baseChartOpts = {
    padding: [8, 25, 0, 0],
    enableScroll: false,
    dataPointShapeType: 'hollow',
    dataLabel: false,
  };

  // 班级排名图表配置
  const classRankChartOpts = computed(() => {
    // 使用totalCount作为min值，如果没有数据则使用默认值16
    const minValue = classRankData?.value?.totalCount ?? 16;

    return {
      ...baseChartOpts,
      color: ['#7D4DFF'],
      xAxis: {
        title: currentClassRankTrendType.value === 'week' ? '周次' : '月份',
      },
      yAxis: {
        showTitle: true,
        gridType: 'dash',
        dashLength: 10,
        splitNumber: 8,
        data: [
          {
            min: minValue,
            max: 0,
            title: '排名',
          },
        ],
      },
      extra: {
        area: {
          type: 'straight',
          opacity: 0.3,
          addLine: true,
          width: 3,
          gradient: true,
        },
        // tooltip: {
        //   showBox: false,
        //   showArrow: false,
        //   legendShow: false,
        //   splitLine: false,
        //   showCategory: false,
        // },
      },
    };
  });

  // 单科成绩图表配置
  const subjectChartOpts = computed(() => ({
    ...baseChartOpts,
    color: ['#7D4DFF', '#4DD8FF', '#FFB24D'],
    xAxis: {
      title: currentSubjectTrendType.value === 'week' ? '周次' : '月份',
      disableGrid: true,
    },
    yAxis: {
      showTitle: true,
      gridType: 'dash',
      dashLength: 10,
      splitNumber: 5,
      data: [
        {
          unit: '%',
          min: 0,
          max: 100,
          title: '正确率',
        },
      ],
    },
    extra: {
      line: {
        type: 'straight',
        width: 3,
        activeType: 'hollow',
      },
      // tooltip: {
      //   showBox: false,
      //   showArrow: false,
      //   legendShow: false,
      //   splitLine: false,
      //   showCategory: false,
      // },
    },
  }));

  // 知识点雷达图配置
  const knowledgeRadarOpts = ref({
    color: ['#4DD8FF'],
    padding: [0, 0, 0, 0],
    dataLabel: false,
    enableScroll: false,
    dataPointShapeType: 'hollow',
    legend: {
      show: true,
      position: 'bottom',
    },
    extra: {
      radar: {
        gridType: 'radar',
        gridColor: '#E7E7E7',
        gridCount: 3,
        opacity: 0.3,
        max: 100,
        border: true,
        borderWidth: 3,
        borderColor: '#E7E7E7',
        radius: 80,
        axisLabel: true,
      },
    },
  });

  // 错误分析圆环图配置
  const errorAnalysisOpts = ref({
    rotate: false,
    rotateLock: false,
    color: ['#03B9DB', '#7D4DFF', '#4DD8FF', '#FFB24D', '#FF4D4D'],
    padding: [0, 0, 0, 0],
    dataLabel: true,
    enableScroll: false,
    legend: {
      show: true,
      position: 'bottom',
    },
    title: {
      name: '收益率',
      fontSize: 15,
      color: 'transparent',
    },
    subtitle: {
      name: '70%',
      fontSize: 25,
      color: 'transparent',
    },
    extra: {
      ring: {
        offsetAngle: -90,
        labelWidth: 10,
        border: false,
        customRadius: 80,
      },
    },
  });

  // ===== 数据转换方法 =====

  /**
   * 对数据进行平均采样，根据设备类型动态调整横轴数据点数量
   * @param originalData 原始数据数组
   * @param maxPoints 最大显示点数，如果不传则使用响应式计算的值
   * @returns 采样后的数据数组
   */
  const sampleData = <T>(originalData: T[], maxPoints?: number): T[] => {
    // 如果没有传入maxPoints，则使用响应式计算的值
    const actualMaxPoints = maxPoints ?? getResponsiveMaxPoints.value;

    if (originalData.length <= actualMaxPoints) {
      return originalData;
    }

    const sampledData: T[] = [];
    const step = originalData.length / actualMaxPoints;

    for (let i = 0; i < actualMaxPoints; i++) {
      const index = Math.round(i * step);
      // 确保不超出数组边界
      const actualIndex = Math.min(index, originalData.length - 1);
      sampledData.push(originalData[actualIndex]);
    }

    return sampledData;
  };

  /**
   * 转换班级排名趋势数据为图表数据
   */
  const convertClassRankTrendToChartData = (data: ClassRankTrend) => {
    if (!data) {
      return {
        categories: [],
        series: [
          {
            name: '班级排名',
            legendShape: 'line',
            data: [],
          },
        ],
      };
    }

    const trendData =
      currentClassRankTrendType.value === 'week' ? data.weekRankingTrend : data.monthRankingTrend;

    if (!trendData || trendData.length === 0) {
      return {
        categories: [],
        series: [
          {
            name: '班级排名',
            legendShape: 'line',
            data: [],
          },
        ],
      };
    }

    // 对数据进行采样，确保不超过8个点
    const sampledTrendData = sampleData(trendData as any[]);

    const categories = sampledTrendData.map(item => {
      if (currentClassRankTrendType.value === 'week') {
        return `第${(item as any).week}周`;
      } else {
        return `第${(item as any).month}月`;
      }
    });

    const rankData = sampledTrendData.map(item => (item as any).classAvgRank);

    return {
      categories,
      series: [
        {
          name: '班级排名',
          legendShape: 'line',
          data: rankData,
        },
      ],
    };
  };

  /**
   * 转换单科成绩趋势数据为图表数据
   */
  const convertSubjectScoreTrendToChartData = (data: SubjectScoreTrendItem[]) => {
    if (!data || data.length === 0) {
      return {
        categories: [],
        series: [],
      };
    }

    const firstItem = data[0];
    const trendData =
      currentSubjectTrendType.value === 'week'
        ? firstItem.weekRankingTrendList
        : firstItem.monthRankingTrendList;

    if (!trendData || trendData.length === 0) {
      return {
        categories: [],
        series: [],
      };
    }

    // 对数据进行采样，确保不超过8个点
    const sampledTrendData = sampleData(trendData as any[]);

    const categories = sampledTrendData.map(item => {
      if (currentSubjectTrendType.value === 'week') {
        return `第${(item as any).week}周`;
      } else {
        return `第${(item as any).month}月`;
      }
    });

    const series = data.map(subjectItem => {
      const subjectTrendData =
        currentSubjectTrendType.value === 'week'
          ? subjectItem.weekRankingTrendList
          : subjectItem.monthRankingTrendList;

      // 对每个学科的数据也进行采样
      const sampledSubjectData = sampleData(subjectTrendData as any[]);

      return {
        name: subjectItem.subjectName,
        data: sampledSubjectData.map(item => (item as any).correctRate),
      };
    });

    return {
      categories,
      series,
    };
  };

  /**
   * 转换知识点掌握情况数据为雷达图数据
   */
  const convertCorrectRateToRadarData = (correctRateList: KnowledgeAnalysis['correctRateList']) => {
    if (!correctRateList || correctRateList.length === 0) {
      return {
        categories: [],
        series: [
          {
            name: '知识点掌握率(%)',
            legendShape: 'line',
            data: [],
          },
        ],
      };
    }

    const categories = correctRateList.map(item => item.pointName);
    const data = correctRateList.map(item => item.rate);

    return {
      categories,
      series: [
        {
          name: '知识点掌握率(%)',
          legendShape: 'line',
          data,
        },
      ],
    };
  };

  /**
   * 转换错误高频区数据为圆环图数据
   */
  const convertWrongRateToRingData = (wrongRateList: KnowledgeAnalysis['wrongRateList']) => {
    if (!wrongRateList || wrongRateList.length === 0) {
      return {
        series: [
          {
            data: [],
          },
        ],
      };
    }

    // 按错误率降序排序，取前6条数据
    const sortedData = wrongRateList
      .sort((a, b) => b.rate - a.rate) // 按rate降序排序
      .slice(0, 6); // 只取前6条

    const data = sortedData.map(item => ({
      name: item.pointName,
      value: item.rate,
    }));

    return {
      series: [
        {
          data,
        },
      ],
    };
  };

  // ===== 趋势类型切换方法 =====

  /**
   * 切换班级排名趋势类型
   */
  const switchClassRankTrendType = (type: 'week' | 'month') => {
    currentClassRankTrendType.value = type;
  };

  /**
   * 切换单科成绩趋势类型
   */
  const switchSubjectTrendType = (type: 'week' | 'month') => {
    currentSubjectTrendType.value = type;
  };

  return {
    // 状态
    currentClassRankTrendType,
    currentSubjectTrendType,

    // 响应式配置
    getResponsiveMaxPoints,

    // 图表配置
    classRankChartOpts,
    subjectChartOpts,
    knowledgeRadarOpts,
    errorAnalysisOpts,

    // 数据转换方法
    convertClassRankTrendToChartData,
    convertSubjectScoreTrendToChartData,
    convertCorrectRateToRadarData,
    convertWrongRateToRingData,

    // 趋势类型切换方法
    switchClassRankTrendType,
    switchSubjectTrendType,
  };
}
