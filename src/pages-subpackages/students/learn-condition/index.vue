<template>
  <view class="learn-condition">
    <up-navbar :title="`${currentUserInfo?.username}-学情分析`" bgColor="transparent" placeholder>
      <template #left>
        <up-icon name="arrow-left" size="24px" color="#000" @tap="handleBack" />
      </template>
    </up-navbar>

    <scroll-view scroll-y class="content-container">
      <!-- 空状态 -->
      <view v-if="shouldShowEmpty" class="empty-container">
        <MyEmpty
          text="暂无学期数据，请联系管理员"
          :image-width="'200rpx'"
          :image-height="'200rpx'"
        />
      </view>

      <!-- 正常内容 -->
      <template v-else>
        <!-- 时间筛选区 -->
        <FilterSection
          :current-semester-label="currentSemester.label"
          :date-range-text="dateRangeText"
          @semester-select="handleSemesterSelect"
          @date-range-select="handleDateRangeSelect"
        />

        <!-- 总体分析卡片 -->
        <AnalysisCard title="总体分析">
          <!-- 总论 -->
          <view class="section-content">
            <view class="section-title">总论</view>
            <view class="conclusion-content">
              <LKMarkdown v-if="generalDiscussion" :source="generalDiscussion" />
              <view v-else>暂无数据</view>
            </view>
            <up-divider lineColor="#E7E7E7" />
          </view>

          <!-- 班级排名波动 -->
          <view class="section-content">
            <ChartContainer
              :key="classRankChartKey"
              title="班级排名波动"
              chart-type="area"
              :chart-opts="classRankChartOpts"
              :chart-data="classRankChartData"
              :show-switcher="true"
              :switcher-options="classSections"
              :current-index="currentClassRankIndex"
              tooltip-format="classRankTooltip"
              @switcher-change="handleClassRankChange"
            />
            <up-divider lineColor="#E7E7E7" />
          </view>

          <!-- 单科成绩变化趋势 -->
          <view class="section-content">
            <ChartContainer
              :key="subjectChartKey"
              title="单科成绩变化趋势"
              chart-type="line"
              :chart-opts="subjectChartOpts"
              :chart-data="subjectChartData"
              :show-switcher="true"
              :switcher-options="classSections"
              :current-index="currentSubjectTrendIndex"
              tooltip-format="subjectScoreTooltip"
              @switcher-change="handleIndividualChange"
            />
            <up-divider lineColor="#E7E7E7" />
          </view>

          <!-- 学科优势与薄弱分析 -->
          <view class="section-content">
            <StrengthWeaknessSection :data="subjectStrengthsWeaknesses" />
          </view>
        </AnalysisCard>

        <!-- 知识点分析卡片 -->
        <AnalysisCard
          title="知识点分析"
          :title-style="{ fontSize: '18px', left: '10px', top: '8px' }"
          :show-subject-selector="true"
          :subject-options="subjectOptions"
          :current-subject="currentSubject"
          @subject-change="handleSubjectChange"
          style="margin-bottom: 10px !important"
        >
          <!-- 知识点掌握情况 -->
          <view class="section-content">
            <!-- 当知识点掌握情况只有一条数据时显示空状态 -->
            <template v-if="shouldShowKnowledgeEmpty">
              <view class="chart-title">知识点掌握情况</view>
              <MyEmpty text="暂无数据" :image-width="'120rpx'" :image-height="'120rpx'" />
            </template>
            <!-- 正常显示图表 -->
            <template v-else>
              <ChartContainer
                title="知识点掌握情况"
                chart-type="radar"
                :chart-opts="knowledgeRadarOpts"
                :chart-data="knowledgeRadarData"
              />
            </template>

            <view class="analysis-section">
              <span class="analysis-section-title">知识点分析</span>
              <view class="analysis-section-content">
                <LKMarkdown v-if="knowledgeMastery" :source="knowledgeMastery" />
                <view v-else>暂无数据</view>
              </view>
            </view>
            <up-divider lineColor="#E7E7E7" />
          </view>

          <!-- 错误高频区分析 -->
          <view class="section-content">
            <!-- 当错误高频区分析只有一条数据时显示空状态 -->
            <template v-if="shouldShowErrorAnalysisEmpty">
              <view class="chart-title">错误高频区分析</view>
              <MyEmpty text="暂无数据" :image-width="'120rpx'" :image-height="'120rpx'" />
            </template>
            <!-- 正常显示图表 -->
            <template v-else>
              <ChartContainer
                title="错误高频区分析"
                chart-type="ring"
                :chart-opts="errorAnalysisOpts"
                :chart-data="errorAnalysisData"
                tooltip-format="errorAnalysisTooltip"
              />
            </template>

            <view class="analysis-section">
              <span class="analysis-section-title">错误类型分析</span>
              <view class="analysis-section-content">
                <LKMarkdown v-if="highFreqErrorAreas" :source="highFreqErrorAreas" />
                <view v-else>暂无数据</view>
              </view>
            </view>
          </view>
        </AnalysisCard>
        <view class="safe-area-inset-bottom"></view>
      </template>
    </scroll-view>

    <LkSvg
      width="280rpx"
      height="280rpx"
      src="/static/study/learn_condition.svg"
      customClass="bg-icon"
    />

    <!-- 选择器弹窗 -->
    <LkSelectPopupList
      ref="semesterPopupRef"
      :list="semesterOptions"
      :default-value="currentSemester.value"
      title="选择学期"
      closeOnClickOverlay
      @confirm="handleSemesterConfirm"
    />

    <LkDateRangePicker
      ref="dateRangePickerRef"
      :startDate="currentDateRange.startDate"
      :endDate="currentDateRange.endDate"
      title="选择日期区间"
      @confirm="handleDateRangeConfirm"
    />

    <!-- 全局提示组件 -->
    <LkToast ref="toastRef" />

    <!-- 全局Loading组件 -->
    <LearnConditionLoading
      :show="showGlobalLoading"
      :text="loadingText"
      size="xl"
      fullscreen
      overlay
    />
  </view>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';

import LkSelectPopupList from '@/components/LkSelectPopupList/index.vue';
import LkDateRangePicker from '@/components/LkDateRangePicker/index.vue';
import LkToast from '@/components/LkToast/index.vue';
import LKMarkdown from '@/components/LKMarkdown/index.vue';
import MyEmpty from '@/components/MyEmpty/index.vue';
import LearnConditionLoading from './components/LearnConditionLoading.vue';

// 导入组件
import FilterSection from './components/FilterSection.vue';
import AnalysisCard from './components/AnalysisCard.vue';
import ChartContainer from './components/ChartContainer.vue';
import StrengthWeaknessSection from './components/StrengthWeaknessSection.vue';

// 导入composables
import { useLearnConditionData } from './composables/useLearnConditionData';
import { useChartData } from './composables/useChartData';
import { useAIAnalysis } from './composables/useAIAnalysis';

import { useUserStore } from '@/store/userStore';

const userStore = useUserStore();
const currentUserInfo = computed(() => userStore.userInfo);

// ===== 组件引用 =====
const semesterPopupRef = ref();
const dateRangePickerRef = ref();
const toastRef = ref();

// ===== 使用Composables =====
const {
  loading,
  error,
  loadingStates,
  isAnyLoading,
  semesterOptions,
  currentSemester,
  currentDateRange,
  subjectOptions,
  currentSubject,
  classRankData,
  subjectScoreData,
  knowledgeAnalysisData,
  dateRangeText,
  updateCurrentSemester,
  updateDateRange,
  updateCurrentSubject,
  fetchKnowledgeAnalysis,
  initializeData,
  refreshDataAfterSemesterChange,
  refreshDataAfterDateRangeChange,
} = useLearnConditionData();

const {
  currentClassRankTrendType,
  currentSubjectTrendType,
  classRankChartOpts,
  subjectChartOpts,
  knowledgeRadarOpts,
  errorAnalysisOpts,
  convertClassRankTrendToChartData,
  convertSubjectScoreTrendToChartData,
  convertCorrectRateToRadarData,
  convertWrongRateToRingData,
  switchClassRankTrendType,
  switchSubjectTrendType,
} = useChartData(classRankData);

const {
  generalDiscussion,
  knowledgeMastery,
  highFreqErrorAreas,
  subjectStrengthsWeaknesses,
  aiLoading,
  aiError,
  aiLoadingStates,
  isAnyAILoading,
  fetchAllAnalysis,
  fetchKnowledgeMastery,
  fetchHighFreqErrorAreas,
  cancelAllRequests,
} = useAIAnalysis();

// ===== 本地状态 =====
const classSections = ref([
  { name: '周趋势', value: 'week' },
  { name: '月趋势', value: 'month' },
]);

const currentClassRankIndex = ref(0);
const currentSubjectTrendIndex = ref(0);

// 添加强制重新渲染的控制变量，解决安卓兼容性问题
const classRankChartKey = ref(Date.now());
const subjectChartKey = ref(Date.now());

// ===== 计算属性 =====
// 判断是否显示空状态
const shouldShowEmpty = computed(() => {
  return (
    !loadingStates.value.semesters && (!semesterOptions.value || semesterOptions.value.length <= 0)
  );
});

// 班级排名图表数据 - 包含趋势类型依赖
const classRankChartData = computed(() => {
  // 显式依赖趋势类型，确保切换时重新计算
  const trendType = currentClassRankTrendType.value;
  return convertClassRankTrendToChartData(classRankData.value);
});

// 单科成绩图表数据 - 包含趋势类型依赖
const subjectChartData = computed(() => {
  // 显式依赖趋势类型，确保切换时重新计算
  const trendType = currentSubjectTrendType.value;
  return convertSubjectScoreTrendToChartData(subjectScoreData.value);
});

// 知识点雷达图数据
const knowledgeRadarData = computed(() => {
  return convertCorrectRateToRadarData(knowledgeAnalysisData.value.correctRateList);
});

// 错误分析圆环图数据
const errorAnalysisData = computed(() => {
  return convertWrongRateToRingData(knowledgeAnalysisData.value.wrongRateList);
});

// 判断知识点掌握情况是否应该显示空状态（数据不足时）
const shouldShowKnowledgeEmpty = computed(() => {
  const correctRateList = knowledgeAnalysisData.value.correctRateList;
  return !correctRateList || correctRateList.length <= 1;
});

// 判断错误高频区分析是否应该显示空状态（数据不足时）
const shouldShowErrorAnalysisEmpty = computed(() => {
  const wrongRateList = knowledgeAnalysisData.value.wrongRateList;
  return !wrongRateList || wrongRateList.length <= 1;
});

// ===== 全局Loading状态 =====
// 计算是否显示全局loading
const showGlobalLoading = computed(() => {
  return isAnyLoading.value || isAnyAILoading.value;
});

// 获取当前loading文本
const loadingText = computed(() => {
  if (loadingStates.value.semesters) return '获取学期数据中...';
  if (loadingStates.value.subjects) return '获取学科数据中...';
  if (loadingStates.value.classRankTrend) return '获取班级排名数据中...';
  if (loadingStates.value.subjectScoreTrend) return '获取成绩趋势数据中...';
  if (loadingStates.value.knowledgeAnalysis) return '获取知识点分析中...';
  if (aiLoadingStates.value.generalDiscussion) return 'AI分析总体情况中...';
  if (aiLoadingStates.value.subjectStrengthsWeaknesses) return 'AI分析学科优劣中...';
  if (aiLoadingStates.value.knowledgeMastery) return 'AI分析知识掌握中...';
  if (aiLoadingStates.value.highFreqErrorAreas) return 'AI分析错误类型中...';
  return '加载中...';
});

// ===== 事件处理方法 =====

/**
 * 处理学科变化
 */
const handleSubjectChange = async (value: string) => {
  try {
    updateCurrentSubject(parseInt(value));

    // 获取知识点分析数据
    const knowledgeData = await fetchKnowledgeAnalysis();

    // 学科切换后，需要重新调用知识点掌握情况和错误高频区分析的智能体
    if (knowledgeData) {
      await Promise.allSettled([
        fetchKnowledgeMastery(knowledgeData.correctRateList),
        fetchHighFreqErrorAreas(knowledgeData.wrongRateList),
      ]);
    }
  } catch (error) {
    console.error('学科切换后数据刷新失败:', error);
    toastRef.value?.show({
      message: '学科切换失败',
      type: 'error',
    });
  }
};

/**
 * 处理班级排名趋势切换
 */
const handleClassRankChange = (value: number) => {
  // 立即更新索引，避免UI延迟
  currentClassRankIndex.value = value;
  const trendType = classSections.value[value].value as 'week' | 'month';

  // 切换数据并强制重新渲染图表，解决安卓兼容性问题
  switchClassRankTrendType(trendType);
  classRankChartKey.value = Date.now();
};

/**
 * 处理单科成绩趋势切换
 */
const handleIndividualChange = (value: number) => {
  // 立即更新索引，避免UI延迟
  currentSubjectTrendIndex.value = value;
  const trendType = classSections.value[value].value as 'week' | 'month';

  // 切换数据并强制重新渲染图表，解决安卓兼容性问题
  switchSubjectTrendType(trendType);
  subjectChartKey.value = Date.now();
};

/**
 * 处理学期选择
 */
const handleSemesterSelect = () => {
  semesterPopupRef.value?.open();
};

/**
 * 处理日期范围选择
 */
const handleDateRangeSelect = () => {
  dateRangePickerRef.value?.open();
};

/**
 * 处理学期确认
 */
const handleSemesterConfirm = async (value: string) => {
  try {
    updateCurrentSemester(value);
    // 学期切换需要重新获取学科列表和所有依赖时间的数据
    await refreshDataAfterSemesterChange();

    // 只有在有学期数据时才重新获取AI分析
    if (semesterOptions.value && semesterOptions.value.length > 0) {
      await fetchAllAnalysis(
        classRankData.value,
        subjectScoreData.value,
        knowledgeAnalysisData.value
      );
    }
  } catch (error) {
    console.error('学期切换后数据刷新失败:', error);
    toastRef.value?.show({
      message: '数据刷新失败',
      type: 'error',
    });
  }
};

/**
 * 处理日期范围确认
 */
const handleDateRangeConfirm = async (result: { startDate: string; endDate: string }) => {
  try {
    updateDateRange(result);
    // 日期范围变化只需要刷新依赖时间的数据，不需要重新获取学科列表
    await refreshDataAfterDateRangeChange();

    // 只有在有学期数据时才重新获取AI分析
    if (semesterOptions.value && semesterOptions.value.length > 0) {
      await fetchAllAnalysis(
        classRankData.value,
        subjectScoreData.value,
        knowledgeAnalysisData.value
      );
    }
  } catch (error) {
    console.error('日期范围变更后数据刷新失败:', error);
    toastRef.value?.show({
      message: '数据刷新失败',
      type: 'error',
    });
  }
};

/**
 * 返回上一页
 */
const handleBack = () => {
  uni.navigateBack();
};

// ===== 生命周期 =====
/**
 * 组件挂载时初始化数据
 */
onMounted(async () => {
  try {
    await initializeData();

    // 只有在有学期数据时才获取AI分析
    if (semesterOptions.value && semesterOptions.value.length > 0) {
      await fetchAllAnalysis(
        classRankData.value,
        subjectScoreData.value,
        knowledgeAnalysisData.value
      );
    } else {
      console.log('学期数据为空，跳过AI分析');
    }
  } catch (error) {
    console.error('初始化失败:', error);
    toastRef.value?.show({
      message: '数据加载失败',
      type: 'error',
    });
  }
});

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
  cancelAllRequests();
});
</script>
<style lang="scss" scoped>
.learn-condition {
  position: relative;
  height: 100vh;
  background: linear-gradient(180deg, #e5e9fd -2.59%, #fff 87.32%);
}

.bg-icon {
  position: absolute;
  right: 0;
  top: 0;
  color: #d9ddfc;
}
.content-container {
  flex: 1;
  height: calc(100vh - 44px - var(--status-bar-height));
  position: relative;
  z-index: 1;
}

.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400rpx;
}

.filter-row {
  display: flex;
  margin: 20rpx 30rpx;

  .filter-item {
    padding: 15rpx 20rpx;
    background: #fff;
    border-radius: 8px;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 0;

    .filter-value {
      display: flex;
      align-items: center;
      gap: 10rpx;

      .filter-text {
        font-size: 28rpx;
        color: #1e293b;
        font-weight: 500;
        flex: 8;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.learn-condition-card {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  margin: 40rpx 30rpx;
  position: relative;
  .learn-condition-card-title {
    position: relative;
    left: 0px;
    top: -15px;
    width: 120px;
    height: 40px;
    .learn-condition-card-title-bg {
      width: 120px;
      height: 40px;
    }
    .learn-condition-card-title-text {
      position: absolute;
      top: 6px;
      left: 13px;
      font-size: 21px;
      color: #fff;
      font-weight: 500;
    }
    .learn-condition-card-title-space {
      position: absolute;
      height: 5px;
      border-radius: 999px 999px 0px 0px;
      background: #2f00ac;
      width: 10px;
      top: 0px;
      right: 0px;
    }
  }
}

.learn-condition-title {
  font-size: 18px;
  color: #000;
  position: relative;
  margin-left: 12px;
  font-weight: 500;
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    height: 12px;
    width: 6px;
    background: #7d4dff;
    border-radius: 10px;
    transform: translateY(-50%);
  }
}

.learn-condition-conclusion {
  border-radius: 12px;
  background: linear-gradient(
    107deg,
    rgba(230, 213, 255, 0.6) -18.84%,
    rgba(213, 240, 255, 0.6) 113.54%
  );
  padding: 10px 12px;
  justify-content: center;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  width: 100%;
}

.learn-condition-subsection {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.learn-condition-strength-sub {
  border-radius: 12px;
  background: #e3f9ee;
  padding: 10px 12px;
  position: relative;
  .learn-condition-strength-sub-title {
    font-size: 16px;
    color: #fff;
    font-weight: 500;
    border-radius: 12px 0px 20px 0px;
    background: #0bc68d;
    position: relative;
    padding: 5px 10px;
    top: -5px;
    left: -12px;
  }
}

.learn-condition-strength-sub-content {
  border-radius: 10px;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  margin-top: 10px;
  color: #0bc68d;
  font-weight: 600;
}

.learn-condition-card-select {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 100px;
}

.chart-container {
  width: 100%;
  height: 300px;
  margin: 20rpx 0;
}

.section-content {
  /* padding: 0 10px; */
}

.section-title {
  font-size: 18px;
  color: #000;
  position: relative;
  margin-left: 12px;
  margin-bottom: 10px;
  font-weight: 500;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    height: 12px;
    width: 6px;
    background: #7d4dff;
    border-radius: 10px;
    transform: translateY(-50%);
  }
}

.conclusion-content {
  border-radius: 12px;
  background: linear-gradient(
    107deg,
    rgba(230, 213, 255, 0.6) -18.84%,
    rgba(213, 240, 255, 0.6) 113.54%
  );
  padding: 10px 12px;
  justify-content: center;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  width: 100%;
}

.analysis-section {
  background: #f7f9ff;
  border-radius: 12px;
  padding: 10px 12px;
  margin-top: 10px;
  position: relative;
}

.analysis-section-title {
  background: #e2e7fe;
  color: #7d4dff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px 0px 20px 0px;
  position: relative;
  padding: 5px 10px;
  top: -5px;
  left: -12px;
}

.analysis-section-content {
  margin-top: 6px;
  margin-left: -10px;
}

.chart-title {
  font-size: 18px;
  color: #000;
  position: relative;
  margin-left: 12px;
  margin-bottom: 10px;
  font-weight: 500;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    height: 12px;
    width: 6px;
    background: #7d4dff;
    border-radius: 10px;
    transform: translateY(-50%);
  }
}
</style>
