import dayjs from 'dayjs';
import type { SemestersItem } from '@/types/students/learnCondition';

/**
 * 获取最近一段时间范围的日期
 * @param type 时间范围类型：0-最近一周 1-最近一个月
 * @returns 包含开始日期和结束日期的对象，格式为YYYY-MM-DD
 */
export function getDateRangeRecent(type: number): { startDate: string; endDate: string } {
  const today = dayjs();
  let startDate: dayjs.Dayjs;
  if (type === 0) {
    // 最近一周（当前日期到6天前，共7天）
    startDate = today.subtract(6, 'day');
  } else {
    // 最近一个月（当前日期到30天前）
    startDate = today.subtract(30, 'day').add(1, 'day'); // 减30天再加1天等于从29天前开始
  }

  return {
    startDate: startDate.format('YYYY-MM-DD'),
    endDate: today.format('YYYY-MM-DD'),
  };
}

/**
 * 格式化日期显示
 * @param startDate
 * @param endDate
 * @returns
 */
export function formatDateRange(startDate: string, endDate: string): string {
  const formatDate = (dateStr: string): string => {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}.${month}.${day}`;
  };
  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
}

/**
 * 扩展学期项类型，添加选择器需要的字段
 */
export type SemesterOption = SemestersItem & {
  value: string;
  label: string;
};

/**
 * 转换学期数据为选择器选项
 */
export function transformSemesterOptions(semesters: SemestersItem[]): SemesterOption[] {
  return semesters.map(item => ({
    ...item,
    startDate: dayjs(item.startDate).format('YYYY-MM-DD'),
    endDate: dayjs(item.endDate).format('YYYY-MM-DD'),
    semesterId: parseInt(item.id),
    semesterName: `${item.year}学年${item.type === 1 ? '上' : '下'}学期`,
    value: item.id,
    label: `${item.year}学年${item.type === 1 ? '上' : '下'}学期`,
  }));
}

/**
 * 获取当前学期
 */
export function getCurrentSemesterOption(options: SemesterOption[]): SemesterOption | null {
  return options.find(item => item.isCurrent === 1) || options[0] || null;
}
