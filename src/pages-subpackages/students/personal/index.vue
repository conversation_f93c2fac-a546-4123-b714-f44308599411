<template>
  <LkToast ref="utoastRef"></LkToast>
  <view class="user">
    <view class="header">
      <view class="header-message" @tap="handleNoticeClick">
        <template v-if="unreadNotificationCount > 0">
          <uni-badge
            :text="unreadNotificationCount > 99 ? '99+' : unreadNotificationCount.toString()"
            :inverted="false"
            type="error"
            size="small"
            :absolute="true"
            :offset="[2, 2]"
          >
            <LkSvg width="24px" height="24px" src="/static/study/notice.svg" />
          </uni-badge>
        </template>
        <template v-else>
          <LkSvg width="24px" height="24px" src="/static/study/notice.svg" />
        </template>
      </view>

      <view class="user-parent">
        <template v-if="isLogin">
          <view class="user-parent-box user-parent-login">
            <image
              src="https://huayun-ai-obs-public.huayuntiantu.com/4d0b829d9c01d213ecf1f304610a4a85.png"
            ></image>
            <view class="user-parent-info">
              <view class="user-parent-info-name u-line-1" @tap.stop="handleGotoLogin">登录</view>
              <view class="user-parent-info-account"></view>
            </view>
          </view>
        </template>
        <template v-else>
          <view class="user-parent-box">
            <image
              class="user-avatar"
              shape="circle"
              :src="userInfo?.avatar"
              @click="chooseAvatar"
            ></image>
            <view class="user-parent-info">
              <view class="user-parent-info-name u-line-1">{{ userInfo?.username }}</view>
              <view class="user-parent-info-account">{{
                isStudent
                  ? `${userInfo?.gradeName}${userInfo?.clazzName} | ${userInfo?.account}`
                  : userInfo?.phone
              }}</view>
            </view>
          </view>
        </template>

        <view class="user-edit" v-if="!isLogin">
          <view class="edit-btn" @click="handleGotoModify"> 编辑 </view>
        </view>
      </view>
    </view>
    <!-- 将所有内容包装在一个固定高度的不可滚动容器中 -->
    <view class="user-content-wrapper">
      <view class="user-link-bottom user-link-bottom-top">
        <view class="user-link-bottom-item" @click="handleGotoDialogueRecord">
          <image
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/ed4e21700d5552664f9c7c39cfb7d0e9.svg"
          />
          <text>对话记录</text>
          <image
            class="user-link-bottom-item-arrow"
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
          />
        </view>
        <view class="user-link-bottom-item" @click="handleGotoMynotes">
          <image
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/fa2110ce5e9600d20a40913d53780053.svg"
          />
          <text>我的笔记</text>
          <image
            class="user-link-bottom-item-arrow"
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
          />
        </view>
        <view class="user-link-bottom-item" @click="handleGotoMyCollection">
          <image
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/306071cbf3bc2f4ef5fba75cc25aa946.svg"
          />
          <text>我的收藏</text>
          <image
            class="user-link-bottom-item-arrow"
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
          />
        </view>
        <view class="user-link-bottom-item" @click="handleGotoUserFeedback">
          <image
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3ebcdf2bdf612793fcba3a392faf129d.svg"
          />
          <text>帮助反馈</text>
          <image
            class="user-link-bottom-item-arrow"
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
          />
        </view>
        <view class="user-link-bottom-item" @click="handleGotoSetting">
          <image
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/be94ce0f1fd77f567d401a5a3be1cb1f.svg"
          />
          <text>设置</text>
          <image
            class="user-link-bottom-item-arrow"
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
          />
        </view>
      </view>
    </view>

    <u-safe-bottom></u-safe-bottom>

    <!-- 权限弹窗组件 -->
    <LKpermissionModal
      v-model:show="permissionModalShow"
      :title="permissionModalTitle"
      :content="permissionModalContent"
      cancel-text="取消"
      confirm-text="前往设置"
      @cancel="handlePermissionCancel"
      @confirm="handlePermissionConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useUserStore } from '@/store/userStore';
import { getUser, updateUser } from '@/api/my-pkg/modify';
import { onLoad, onShow, onHide } from '@dcloudio/uni-app';
import { checkPermission } from '@/utils/permission';
import { getBaseUrl } from '@/common/ai/url';
import LKpermissionModal from '@/components/LKpermissionModal/index.vue';
import { RouteConfigManager } from '@/router/config';
import { getUnreadNotificationCount } from '@/api/notification';

const userStore = useUserStore();
console.log(userStore.getUserInfo);

const isLogin = computed(() => userStore.getUserInfo === null);
const utoastRef = ref();
const { safeAreaInsets } = uni.getSystemInfoSync();
const isStudent = ref(uni.getStorageSync('userMode') === 'student');
const userInfo = ref({
  avatar: '',
  username: '',
  phone: '',
  gender: 1, // 默认为男性
  account: '',
});

// 标记是否需要在页面显示时重试图片选择
const shouldRetryImageSelection = ref(false);
const avatarLoading = ref(false);

// 权限弹窗相关状态
const permissionModalShow = ref(false);
const permissionModalTitle = ref('');
const permissionModalContent = ref('');
const permissionOpenSettings = ref<any>(null);

// 未读通知数量
const unreadNotificationCount = ref(0);
// 定时器
let notificationTimer: number | null = null;

onShow(async () => {
  fetchGetUser();

  // 检查是否需要重试图片选择
  if (shouldRetryImageSelection.value) {
    shouldRetryImageSelection.value = false;

    try {
      // 重新检查权限
      const permissionResult = await checkPermission('camera');

      if (permissionResult.granted) {
        // 权限已授予，自动重试图片选择
        chooseImageFunc();
      } else {
        // 权限仍未授予，提示用户
        uni.showToast({
          title: '权限未开启，无法选择图片',
          icon: 'none',
        });
      }
    } catch (error) {
      console.error('重试权限检查失败:', error);
      // 如果权限检查失败，使用原有的方式重试
      chooseImageFunc();
    }
  }
});
const handleNoticeClick = () => {
  console.log('handleNoticeClick');
  uni.navigateTo({
    url: '/pages-subpackages/students/notice/index',
  });
};

const fetchUnreadNotificationCount = async () => {
  try {
    const response = await getUnreadNotificationCount();
    unreadNotificationCount.value = response || 0;
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    unreadNotificationCount.value = 0;
  }
};

// 启动定时器
const startNotificationTimer = () => {
  // 先清除可能存在的定时器
  stopNotificationTimer();

  // 设置每5秒执行一次
  notificationTimer = setInterval(() => {
    fetchUnreadNotificationCount();
  }, 5000);
};

// 停止定时器
const stopNotificationTimer = () => {
  if (notificationTimer) {
    clearInterval(notificationTimer);
    notificationTimer = null;
  }
};

// 页面卸载时清理定时器
onUnmounted(() => {
  stopNotificationTimer();
});

// 页面隐藏时停止定时器
onHide(() => {
  stopNotificationTimer();
});

// 页面显示时重新启动定时器
onShow(() => {
  fetchUnreadNotificationCount();
  startNotificationTimer();
});

// 选择图片
const chooseAvatar = async () => {
  try {
    // #ifdef APP-PLUS
    // 特殊处理 iOS 相机权限
    if (plus.os.name === 'iOS') {
      console.log('iOS平台处理相机权限');
      try {
        // 导入AVCaptureDevice类
        const AVCaptureDevice = (plus.ios as any).import('AVCaptureDevice');
        const AVMediaTypeVideo = 'vide'; // iOS中AVMediaTypeVideo的实际值

        // 检查当前权限状态
        const authStatus = AVCaptureDevice.authorizationStatusForMediaType(AVMediaTypeVideo);
        console.log('iOS相机权限状态:', authStatus);

        if (authStatus === 3) {
          // 已授权 (3)，直接选择图片
          console.log('相机权限已授权');
          plus.ios.deleteObject(AVCaptureDevice);
          chooseImageFunc();
          return;
        } else if (authStatus === 0) {
          // 未决定 (0)，请求权限
          console.log('相机权限未决定，请求权限');

          // 使用Promise包装原生回调
          const granted = await new Promise<boolean>(resolve => {
            // 尝试多种可能的方法名
            try {
              if (typeof AVCaptureDevice.requestAccessForMediaType === 'function') {
                console.log('使用requestAccessForMediaType请求权限');
                AVCaptureDevice.requestAccessForMediaType(AVMediaTypeVideo, (granted: boolean) => {
                  console.log('权限请求结果:', granted);
                  resolve(granted);
                });
              } else if (typeof AVCaptureDevice.requestAccess === 'function') {
                console.log('使用requestAccess方法');
                AVCaptureDevice.requestAccess(AVMediaTypeVideo, (granted: boolean) => {
                  console.log('权限请求结果:', granted);
                  resolve(granted);
                });
              } else {
                console.log('没有找到请求权限的方法，尝试使用uni.authorize');
                plus.ios.deleteObject(AVCaptureDevice);
                uni.authorize({
                  scope: 'scope.camera',
                  success: () => resolve(true),
                  fail: () => resolve(false),
                });
              }
            } catch (e) {
              console.error('请求权限错误:', e);
              // 使用uni.chooseImage作为最后的尝试
              uni.chooseImage({
                count: 1,
                sourceType: ['camera'],
                success: () => resolve(true),
                fail: () => resolve(false),
              });
              resolve(false);
            }
          });

          plus.ios.deleteObject(AVCaptureDevice);

          if (granted) {
            // 用户授予了权限
            chooseImageFunc();
            return;
          }
        }

        // 如果权限被拒绝(2)或受限(1)，或请求被拒绝，显示弹窗引导用户到设置中开启
        plus.ios.deleteObject(AVCaptureDevice);
        showCameraPermissionDialog();
      } catch (e) {
        console.error('iOS相机权限处理错误:', e);
        // 出错时回退到通用方式
        handlePermissionWithCommonWay();
      }
      return;
    } else {
      // Android平台
      try {
        // 直接请求Android相机权限
        const status = await new Promise<boolean>(resolve => {
          plus.android.requestPermissions(
            ['android.permission.CAMERA'],
            result => {
              console.log('Android相机权限结果:', JSON.stringify(result));
              if (result.granted && result.granted.length > 0) {
                resolve(true);
              } else {
                resolve(false);
              }
            },
            () => resolve(false)
          );
        });

        if (status) {
          chooseImageFunc();
          return;
        } else {
          showCameraPermissionDialog();
          return;
        }
      } catch (e) {
        console.error('Android相机权限检查错误:', e);
        // 如果原生API失败，回退到通用方式
        handlePermissionWithCommonWay();
      }
    }
    // #endif

    // 非iOS平台或降级处理
    handlePermissionWithCommonWay();
  } catch (error) {
    console.error('权限处理失败:', error);
    // 出现异常时直接尝试选择图片
    chooseImageFunc();
  }
};

// 使用通用方式处理权限
const handlePermissionWithCommonWay = async () => {
  try {
    // 使用项目的权限工具类进行权限检查
    const permissionResult = await checkPermission('camera');

    if (permissionResult.granted) {
      // 权限已授予，直接选择图片
      chooseImageFunc();
    } else {
      // 权限未授予，显示权限说明弹窗
      showCameraPermissionDialog(permissionResult);
    }
  } catch (error) {
    console.error('通用权限检查失败:', error);
    chooseImageFunc();
  }
};

// 显示相机权限对话框
const showCameraPermissionDialog = (permissionResult?: any) => {
  permissionModalTitle.value = permissionResult?.details?.deniedTitle || '相机权限未开启';
  permissionModalContent.value =
    permissionResult?.details?.deniedMessage ||
    '检测到手机设置中未对APP开启相机权限，请先在手机设置开启。';
  permissionOpenSettings.value = permissionResult?.openSettings || null;
  permissionModalShow.value = true;
};

// 权限弹窗事件处理
function handlePermissionCancel() {
  permissionModalShow.value = false;
}

function handlePermissionConfirm() {
  permissionModalShow.value = false;
  // 设置重试标记，当用户从设置页面返回时会自动重试
  shouldRetryImageSelection.value = true;

  // 调起设置页面
  try {
    if (permissionOpenSettings.value && typeof permissionOpenSettings.value === 'function') {
      permissionOpenSettings.value();
    } else {
      // #ifdef MP
      uni.openSetting();
      // #endif

      // #ifdef APP-PLUS
      // 针对iOS和Android使用不同的设置URL
      if (plus.os.name === 'iOS') {
        try {
          // 对于iOS 10+，使用新的API
          const UIApplication = (plus.ios as any).import('UIApplication');
          const application = UIApplication.sharedApplication();
          const NSURL = (plus.ios as any).import('NSURL');
          const settingURL = NSURL.URLWithString('app-settings:');

          // 检查iOS版本
          const iosVersion = plus.os.version || '9.0';
          if (parseInt(iosVersion) >= 10) {
            // iOS 10+ 使用新方法
            console.log('使用iOS 10+方法打开设置');
            const options = plus.ios.newObject('NSDictionary');
            application.openURL_options_completionHandler(settingURL, options, null);
            plus.ios.deleteObject(options);
          } else {
            // iOS 9及以下使用旧方法
            application.openURL(settingURL);
          }

          plus.ios.deleteObject(settingURL);
          plus.ios.deleteObject(NSURL);
          plus.ios.deleteObject(application);
        } catch (iosError) {
          console.error('iOS原生方法打开设置失败:', iosError);
          // 备选方案
          plus.runtime.openURL('app-settings:');
        }
      } else {
        // Android
        plus.runtime.openURL(`package:${plus.runtime.appid}`);
      }
      // #endif

      // #ifdef H5
      uni.showToast({
        title: '请在浏览器设置中开启相机权限',
        icon: 'none',
      });
      // #endif
    }
  } catch (settingErr) {
    console.error('打开设置页面失败:', settingErr);
    uni.showToast({
      title: '无法打开设置页面，请手动前往设置开启权限',
      icon: 'none',
    });
  }
}

// 选择图片的具体实现函数
const chooseImageFunc = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      const tempFilePaths = res.tempFilePaths;
      // @ts-ignore - 处理tempFilePaths类型问题
      tempFilePaths.forEach((filePath: string) => {
        uploadFile(filePath);
      });
    },
    fail: err => {
      console.log('chooseImage fail', err);
      if (err.errMsg && err.errMsg.includes('fail No Permission')) {
        uni.showToast({
          title: '请给予摄像头权限',
          icon: 'none',
        });

        // 设置重试标记，当用户从设置页面返回时会自动重试
        shouldRetryImageSelection.value = true;

        // 如果权限被拒绝，提示用户前往设置页面开启权限
        setTimeout(() => {
          uni.showModal({
            title: '提示',
            content: '您拒绝了相机权限，部分功能可能无法使用。是否前往设置开启权限？',
            confirmText: '去设置',
            cancelText: '取消',
            success: modalRes => {
              if (modalRes.confirm) {
                // #ifdef MP
                uni.openSetting();
                // #endif

                // #ifdef APP-PLUS
                if (plus.os.name === 'iOS') {
                  // iOS打开设置的特殊URL
                  plus.runtime.openURL('app-settings:');
                } else {
                  // Android打开应用设置
                  plus.runtime.openURL('app-settings://');
                }
                // #endif

                // #ifdef H5
                uni.showToast({
                  title: '请在浏览器设置中开启相机权限',
                  icon: 'none',
                });
                // #endif
              }
            },
          });
        }, 500);
      }
    },
  });
};

// 上传文件
const uploadFile = (filePath: string) => {
  avatarLoading.value = true;
  const baseUrl = getBaseUrl();
  uni.uploadFile({
    url: `${baseUrl}/huayun-ai/system/file/public/upload`,
    header: {
      Authorization: uni.getStorageSync('token'),
    },
    filePath: filePath,
    name: 'file',
    success: uploadFileRes => {
      // @ts-ignore - parse返回值类型
      const result = JSON.parse(uploadFileRes.data);
      console.log('上传', result);
      if (result.data.fileUrl) {
        userInfo.value.avatar = result.data.fileUrl;
        // 自动保存用户信息
        saveUserInfo();
      }
    },
    complete: () => {
      avatarLoading.value = false;
    },
  });
};

// 保存用户信息
const saveUserInfo = () => {
  updateUser({
    userId: userStore.getUserInfo?.userId,
    username: userInfo.value.username,
    phone: userInfo.value.phone,
    avatar: userInfo.value.avatar,
    gender: userInfo.value.gender,
  }).then(() => {
    utoastRef.value?.show({
      message: '保存成功',
      type: 'success',
    });
    // 更新用户信息
    fetchGetUser();
  });
};

// 跳转修改
function handleGotoModify() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/modify/index',
  });
}

// 跳转对话记录
function handleGotoDialogueRecord() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/studentHistory/index',
  });
}

// 跳转回收站
function handleGotoRecycleBin() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/recycleBin/index',
  });
}

// 跳转用户反馈
function handleGotoUserFeedback() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/feedback/feedback',
  });
}

// 跳转设置
function handleGotoSetting() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/setting/index',
  });
}

// 我的笔记
function handleGotoMynotes() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/notes/index',
  });
}

// 我的收藏
function handleGotoMyCollection() {
  uni.navigateTo({
    url: '/pages-subpackages/students/learning-resource/my-collection',
  });
}

// 登录
function handleGotoLogin() {
  uni.redirectTo({
    url: RouteConfigManager.getLoginPage(),
  });
}

function fetchGetUser() {
  getUser({ id: userStore.getUserInfo?.userId! }).then(res => {
    userInfo.value = res;
  });
}
</script>

<style lang="scss" scoped>
.user {
  position: fixed; /* 使用fixed定位而不是relative */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, #f3f7ff 0%, rgba(255, 255, 255, 0) 100%);
  background-repeat: no-repeat;
  background-size: 100% 500rpx;
  background-position: top;
  width: 100vw;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 禁止滚动 */
  background: url(https://huayun-ai-obs-public.huayuntiantu.com/5fb32bec6d7b521c5f12c42909d15102.png)
    0px 0px / 100% 100% no-repeat #f7f7f7;
  padding-top: var(--status-bar-height);
}

.user * {
  padding: 0;
  margin: 0;
  line-height: 1em;
}

.header {
  box-sizing: border-box;
  background-size: 750rpx 538rpx;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-color: transparent;
  z-index: 999;
}

.header-message {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 40rpx;
  margin-right: 32rpx;

  image {
    width: 48rpx;
    height: 48rpx;
  }
}

.header-title {
  text-align: center;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
  font-size: 34rpx;
  color: #000000;
}

.user-parent {
  margin-top: 80rpx;
  padding: 0 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-parent-login {
  padding: 0;
  font-size: 40rpx;
}

.user-parent image {
  flex-shrink: 0;
  border-radius: 50%;
  margin-left: 10rpx;
  margin-right: 44rpx;
  width: 135rpx;
  height: 135rpx;
  box-shadow:
    0rpx 14rpx 23rpx 0rpx rgba(0, 0, 0, 0.05),
    0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  background: #ffffff;
  border: 4rpx solid #fff;
}

.user-avatar {
  cursor: pointer;
}

.user-parent-box {
  display: flex;
  align-items: center;
}

.user-parent-info-name {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #000000;
}

.user-parent-info-account {
  margin-top: 12rpx;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #6a6a6d;
}

.user-edit {
  width: 104rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.21);
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
}

.user-content {
  flex: 1;
  overflow: hidden;
  z-index: 999;
}

.user-link-top {
  padding: 60rpx 30rpx 0 30rpx;
  display: flex;
  justify-content: space-around;
}

.user-link-top-item {
  width: 156rpx;
}

.user-link-top-item view {
  text-align: center;
}

.user-link-top-item view:first-child {
  font-size: 38rpx;
  font-weight: 600;
  color: #2f2f2f;
}

.user-link-top-item view:last-child {
  margin-top: 8rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333;
}

.user-link-middle {
  padding: 60rpx 30rpx 0 30rpx;
  display: flex;
  justify-content: space-between;
}

.user-link-middle-item {
  width: 334rpx;
  height: 144rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-sizing: border-box;
  display: inline-flex;
  justify-content: center;
  flex-direction: column;
}

.user-link-middle-item view:first-child {
  padding-left: 20rpx;
  position: relative;
  font-size: 30rpx;
  font-weight: 500;
  color: #333330;
  /* background: url(../../static/login/login_03.png) no-repeat right 12rpx center/40rpx 40rpx; */
}

.user-link-middle-item view:first-child::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 2px;
  height: 15px;
  background-color: #fff;
  border-radius: 0 2rpx 2rpx 0;
}

.user-link-middle-item view:last-child {
  padding-top: 20rpx;
  padding-left: 20rpx;
  font-size: 24rpx;
  color: #aaa;
  font-weight: 400;
}

.user {
  .user-link-bottom-top {
    margin-top: 27 * 2rpx;
    margin-bottom: 0;
  }
}

.user-link-bottom {
  margin: 24rpx 32rpx 32rpx 32rpx;
  background-color: #fff;
  border-radius: 24rpx;
}

.user-link-bottom-item-arrow {
  width: 24 * 2rpx;
  height: 24 * 2rpx;
  margin: 0 !important;
  margin-left: auto !important;
}

.user-link-bottom-item {
  margin: 8rpx 32rpx;
  margin-right: 0;
  padding-right: 32rpx;
  height: 112rpx;
  display: flex;
  align-items: center;
  /* background: url(https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg)
    no-repeat right/40rpx 40rpx; */
  border-top: 1px solid #eee;
}

.user-link-bottom-item:first-child {
  border-top: none;
}

.user-link-bottom-item image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.user-link-bottom-item text {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.9);
}

.user-content-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden; /* 禁止滚动 */
}
</style>
