<script setup lang="ts">
import { ref, onMounted, shallowRef } from 'vue';
import StudyCenter from '../study/index.vue';
import AppCenter from '../center/index.vue';
import PersonalCenter from '../personal/index.vue';
import { useTabStore } from '@/store/tabStore';

const tabStore = useTabStore();

// 移除本地Tab配置，现在使用全局tabStore

// 组件映射表 - 直接存储组件引用
const tabComponents: Record<number, any> = {
  0: StudyCenter,
  1: AppCenter,
  2: PersonalCenter,
};

// 当前激活的tab组件 - 使用shallowRef避免组件对象被深度响应式处理
const currentTab = shallowRef(tabComponents[0]);

// 当前激活的tab索引
const activeTabIndex = ref(0);

// Tab点击处理
const handleTabClick = (index: number) => {
  if (index === tabStore.activeTabIndex) return;

  console.log('切换到Tab:', index);

  // 更新当前组件引用
  currentTab.value = tabComponents[index];

  // 更新全局TabStore的激活索引
  tabStore.switchTab(index);

  // 同步本地索引
  activeTabIndex.value = index;
};

// 初始化页面
onMounted(() => {
  tabStore.initializeTabBar();
});
</script>

<template>
  <scroll-view
    class="students-container"
    scroll-y
    :show-scrollbar="false"
    :style="
      tabStore.activeTabIndex === 0 && {
        backgroundColor: '#fff',
        backgroundImage:
          'url(https://huayun-ai-obs-public.huayuntiantu.com/af8e099f-e740-43ef-aa04-52c9dc02eda5.png)',
        backgroundSize: '100% 40%',
        backgroundRepeat: 'no-repeat',
      }
    "
  >
    <view class="page-wrapper">
      <component :is="currentTab" />
    </view>

    <view class="custom-tabbar-wrap safe-area-inset-bottom" v-if="tabStore.showTabBar">
      <u-row justify="space-around" customStyle="height: 100rpx;margin-top: 10rpx;">
        <u-col
          span="4"
          v-for="(item, index) in tabStore.tabItems"
          :key="index"
          style="display: flex; justify-content: center; align-items: center"
          @click="handleTabClick(index)"
        >
          <view class="tabbar-item" :class="{ active: tabStore.activeTabIndex === index }">
            <image
              class="tabbar-icon"
              :src="tabStore.activeTabIndex === index ? item.selectedIconPath : item.iconPath"
            />
            <text
              class="tabbar-text"
              :class="{ visible: tabStore.activeTabIndex === index }"
              v-if="tabStore.activeTabIndex === index"
              >{{ item.text }}</text
            >
          </view>
        </u-col>
      </u-row>
    </view>
  </scroll-view>
</template>

<style scoped lang="scss">
.students-container {
  background: #fff;
  width: 100%;
  height: 100vh;
  position: relative;
}

.page-wrapper {
  width: 100%;
  // min-height: 100vh;
  padding-bottom: 160rpx; /* 为底部Tabbar预留空间 */
  // padding-top: var(--status-bar-height); /* 顶部安全区域 */
}

.platform-ios {
  // height: calc(100vh - 170rpx);
  height: 100vh;
  //padding-bottom: 170rpx;
  .custom-tabbar-wrap {
    height: 160rpx;
  }
}

.platform-android {
  height: 100vh;
  // padding-bottom: 140rpx;
  // height: calc(100vh - 140rpx);
  .custom-tabbar-wrap {
    height: 130rpx;
  }
}

/* 自定义TabBar样式 */
.custom-tabbar-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-left: 20rpx;
  padding-right: 20rpx;
  z-index: 999; /* 增加z-index值，确保始终在最上层 */
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    /* 减小背景高度 */
    background-color: #fff;
    border-radius: 40rpx 40rpx 0px 0px; /* 减小圆角 */
    box-shadow: 0px -2px 9px 0px rgba(0, 0, 0, 0.07);
    z-index: -1;
  }

  .tabbar-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6rpx; /* 减小间隔 */
    border-radius: 66rpx; /* 减小圆角 */
    min-width: 50rpx; /* 减小最小宽度 */
    max-width: 200rpx; /* 减小最小宽度 */
    min-height: 80rpx;
    transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
    position: relative;
    overflow: hidden;

    &.active {
      background: #f4f1fd;
      /* 默认活跃状态内边距适用于Android和H5 */
      padding: 12rpx 24rpx;
      /* iOS特定的活跃状态内边距调整 */
      @supports (-webkit-touch-callout: none) {
        padding: 8rpx 16rpx; /* iOS特定的较小内边距 */
      }

      .tabbar-icon {
        transform: scale(1.05); /* 减小变换效果 */
      }
    }
  }

  .tabbar-icon {
    /* 默认图标大小适用于Android和H5 */
    width: 40rpx;
    height: 40rpx;
    transition: transform 0.3s ease;
  }

  .tabbar-text {
    /* 默认字体大小适用于Android和H5 */
    font-size: 26rpx;
    color: #613eea;
    font-weight: 500;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(-10rpx);

    &.visible {
      opacity: 1;
      transform: translateX(0);
    }
  }
}
</style>
