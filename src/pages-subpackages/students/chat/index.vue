<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, h } from 'vue';
import { useRoute } from 'vue-router';
import { useChatStore } from '@/store/chatStore';
import {
  initChatItem,
  updateChatItem,
  removeChatItem,
  updateChatTitle,
  getParseResult,
  getAppContextDetail,
  chatSearchDataset,
  filesParseImageOcr,
  getTenantApp,
} from '@/api/chat';
import dayjs from '../../../utils/day';
import ai from '@/common/ai';
import { respDims, getSafeArea } from '@/utils/respDims';
import MessageInput from '@/components/chat/MessageInput.vue';
import MessageList from '@/components/chat/MessageList_student.vue';
import ChatFlowStep from '@/components/workflow/ChatFlowStep.vue';
import { HeaderNav } from '@/components/LkNavBar';
import { nanoid } from '@/utils/nanoid/index';
import LkSvg from '@/components/svg/index.vue';
import { onLoad, onBackPress } from '@dcloudio/uni-app';
import { resolveUrl } from '@/common/ai/url';
import GaoChatSSEClient from '@/components/GaoChatSSEClient.vue';
import { useTabStore } from '@/store/tabStore';

// 添加注释说明SSE和Web客户端处理的差异
/**
 * 聊天实现说明：
 *
 * 本应用统一使用SSE客户端进行聊天实现：
 * - 所有环境（Web、安卓、iOS等）都使用GaoChatSSEClient组件进行流式通信
 * - 消息发送：通过chatSSEClientRef.value.startChat发送请求
 * - 消息接收：通过handleSseMessage处理流式响应
 * - 聊天终止：通过chatSSEClientRef.value.stopChat终止流
 */

// 定义聊天类型枚举
const ChatTypeEnum = {
  GENERATE_TITLE: '1', // 生成聊天标题
  RICH_TEXT_AI: '2', // 富文本编辑器AI功能
  GENERATE_STRUCTURED_PROMPT: '3', // 生成结构化提示词
  GENERATE_OUTLINE: '4', // 生成大纲内容
  KNOWLEDGE_SEARCH: '5', // 知识库搜索
  TEST_QUALITY_ANALYSIS: '6', // 测试质量分析
  MIND_MAP: '7', // 思维导图
  AI_CREATE_APP: '8', // AI创建简易应用
  GENERATE_AVATAR: '9', // 生成应用头像
  CUSTOM_OPTIMIZE_PROMPT: '10', // 自定义优化提示词
  TEACHER_MESSAGE: '11', // 教师寄语
  PERSONAL_KNOWLEDGE_ANALYSIS: '12', // 个人知识点分析
};

// 定义一个兼容的 findLastIndex 方法，在不支持原生方法的浏览器中使用
function findLastIndexCompat(array, predicate) {
  for (let i = array.length - 1; i >= 0; i--) {
    if (predicate(array[i], i, array)) {
      return i;
    }
  }
  return -1;
}

// 状态管理
const chatStore = useChatStore();
const route = useRoute();

// 响应式状态
const quickTitle = ref(null);
const innerChatId = ref('');
const abortSignal = ref(null);
const messages = ref([]);
const scrollIntoView = ref(null);
const resultTitle = ref('');
const tenantData = ref({});
const sideBarMessage = ref({});
const uploadLimit = ref({});
const waitForInit = ref(false);
const selectedFlowStep = ref(null);
const welcomeText = ref('');
const appAvatarUrl = ref('');
const otherTenant = ref(null);
const messageInputRef = ref(null);
const messageListRef = ref(null);
const appTitle = ref('');
const isResponseLoading = ref(false);
const isParsingBackground = ref(false);
const isParsingFiles = ref(false);
const isParsingImage = ref(false);
const isParsingDocument = ref(false);
const isMessageEditVisible = ref(false);
const isDislikeModalVisible = ref(false); // 新增：点踩反馈弹窗显示状态
const finalAppId = ref('');
const file = ref(null);
// 添加SSE客户端引用
const chatSSEClientRef = ref(null);
// 添加键盘状态管理
const isKeyboardVisible = ref(false);
// 添加上传组件展开状态管理
const isUploadExpanded = ref(false);
// 添加安全区域高度管理
const safeAreaTop = ref(0);

// 添加一个变量来存储最后处理的AI消息ID
const lastFinishedAiMessageId = ref(null);

// 计算属性
const chatting = computed(() => {
  const hasMessages = messages.value.length > 0;
  const lastMessage = hasMessages ? messages.value[messages.value.length - 1] : null;
  const isLastMessageUnfinished = lastMessage && lastMessage.status !== 'finish';
  const isLoading = isResponseLoading.value;

  // 如果有未完成的消息或者正在加载响应，则认为正在聊天
  const result = (hasMessages && isLastMessageUnfinished) || isLoading;

  console.log('chatting 计算属性:', {
    hasMessages,
    lastMessage: lastMessage ? { obj: lastMessage.obj, status: lastMessage.status } : null,
    isLastMessageUnfinished,
    isLoading,
    result,
  });

  return result;
});

// 获取URL参数
// 在安卓APP中useRoute可能会返回undefined，改用onLoad生命周期获取参数
const chatId = ref('');
const appId = ref('');
const chatType = ref('');
const chatSubjectId = ref('');
const routeAppId = ref(''); // 新增：存储路由传入的appId
const routeFileObj = ref(null); // 新增：存储路由传入的fileObj
const messageType = ref('');
const tabStore = useTabStore();
const routeParams = ref({});
// 初始化数据
watch(
  () => chatId.value,
  val => {
    if (val !== innerChatId.value) {
      init();
    }
  },
  { immediate: true }
);
watch(
  () => messages.value,
  val => {
    console.log('66666messages', val);
  }
);

// 监听 MessageEdit 的显示状态
watch(
  () => messageListRef.value?.showEditModal,
  newVal => {
    console.log('MessageEdit 状态变化:', newVal);
    isMessageEditVisible.value = !!newVal;
  },
  { deep: true, immediate: true }
);

// 监听 点踩反馈弹窗 的显示状态
watch(
  () => messageListRef.value?.showDislikeModal,
  newVal => {
    console.log('点踩反馈弹窗状态变化:', newVal);
    isDislikeModalVisible.value = !!newVal;
  },
  { deep: true, immediate: true }
);

// 初始化安全区域
function initSafeArea() {
  try {
    const systemInfo = uni.getSystemInfoSync();
    const safeArea = getSafeArea();

    // 对于安卓设备，使用状态栏高度作为顶部安全区域
    if (systemInfo.platform === 'android') {
      safeAreaTop.value = systemInfo.statusBarHeight || 0;
    } else {
      // iOS设备使用安全区域的top值
      safeAreaTop.value = safeArea.top || systemInfo.statusBarHeight || 0;
    }

    console.log('安全区域初始化完成:', {
      platform: systemInfo.platform,
      safeAreaTop: safeAreaTop.value,
      statusBarHeight: systemInfo.statusBarHeight,
      safeArea: safeArea,
    });
  } catch (error) {
    console.error('初始化安全区域失败:', error);
    // 设置默认值
    safeAreaTop.value = 20;
  }
}

// 初始化方法
function init() {
  stopChat();
  if (messageInputRef.value && typeof messageInputRef.value.reset === 'function') {
    messageInputRef.value.reset();
  }
  innerChatId.value = chatId.value;
  messages.value = [];
  if (JSON.parse(uni.getStorageSync('userInfo')).tenantId) {
    waitForInit.value = false;
    JSON.parse(uni.getStorageSync('userInfo')).account && getInitChatItem();
  } else {
    waitForInit.value = true;
  }
}

// 获取租户数据
async function fetchTenantData() {
  try {
    // 如果有路由传入的appId，直接使用

    await getTenantId();

    // getTenantId已经更新了tenantData和appId，直接初始化
    init();
  } catch (error) {
    console.error('获取租户数据失败:', error);
    // 降级处理：使用本地存储的selectApp
    const fallbackAppId = uni.getStorageSync('selectApp').id;
    if (fallbackAppId) {
      tenantData.value = {
        id: fallbackAppId,
      };
      appId.value = fallbackAppId;
      console.log('降级使用本地存储的appId:', fallbackAppId);
      init();
    }
  }
}

// 在页面加载时获取参数
onLoad(options => {
  console.log('options', options);

  // 初始化安全区域
  initSafeArea();

  routeParams.value = options;
  chatId.value = options.chatId || '';
  appId.value = options.appId || '';
  chatType.value = options.chatType || '';
  chatSubjectId.value = options.chatSubjectId || '';

  // 获取从OptionTool传来的参数
  routeAppId.value = options.appId || '';
  messageType.value = options.chatType || '';

  // 处理fileObj参数 - 需要先decodeURIComponent再JSON.parse
  if (options.fileObj) {
    console.log('fileObj', options.fileObj);

    try {
      // 先解码URI组件，再解析JSON
      const decodedFileObj = decodeURIComponent(options.fileObj);
      const fileObj = JSON.parse(decodedFileObj);
      console.log('fileObj2222222', fileObj);
      routeFileObj.value = {
        fileKey: fileObj.fileKey,
        fileName: fileObj.fileName,
        fileSize: fileObj.fileSize,
        fileType: fileObj.fileType,
        fileUrl: fileObj.fileUrl,
        type: 'file',
        id: fileObj.id,
        uploadTime: fileObj.uploadTime,
      };
    } catch (error) {
      console.error('解析fileObj参数失败:', error);
      routeFileObj.value = null;
    }
  }

  // 设置文件和消息类型
  if (routeFileObj.value) {
    file.value = routeFileObj.value;
  }
  if (chatType.value) {
    messageType.value = chatType.value;
  }

  // 获取参数后立即初始化数据
  fetchTenantData();

  nextTick(() => {
    refeshQuickBar();
  });
});

// 获取当前使用的tenantAppId
function getCurrentTenantAppId() {
  // 优先使用从getTenantApp获取的id
  if (tenantData.value && tenantData.value.id) {
    return tenantData.value.id;
  }
  // 其次使用路由传入的appId
  if (appId.value) {
    return appId.value;
  }
  // 最后降级使用本地存储或用户信息
  return uni.getStorageSync('selectApp').id || JSON.parse(uni.getStorageSync('userInfo')).tenantId;
}

function getTenantId() {
  return getTenantApp({
    type: chatType.value || ChatTypeEnum.GENERATE_TITLE,
  })
    .then(res => {
      if (res) {
        // 更新tenantId和appId
        const tenantAppData = res;
        console.log('getTenantApp返回数据:', tenantAppData);

        tenantData.value = {
          id: tenantAppData.id,
          tenantId: tenantAppData.tenantId,
          finalAppId: tenantAppData.finalAppId,
          name: tenantAppData.name,
          avatarUrl: tenantAppData.avatarUrl,
        };

        // 更新appId变量
        if (!routeAppId.value) {
          appId.value = tenantAppData.id;
        }

        console.log('更新后的tenantData:', tenantData.value);
        console.log('更新后的appId:', appId.value);

        return tenantAppData.tenantId;
      }
      return JSON.parse(uni.getStorageSync('userInfo')).tenantId;
    })
    .catch(error => {
      console.error('获取租户应用失败:', error);
      return JSON.parse(uni.getStorageSync('userInfo')).tenantId;
    });
}

// 初始化聊天记录
async function getInitChatItem(scroll = true) {
  return initChatItem({
    tenantAppId: getCurrentTenantAppId(),
    chatId: chatId.value || '',
  })
    .then(async res => {
      welcomeText.value = res.app?.chatConfig?.welcomeText;
      appAvatarUrl.value = res.appAvatarUrl;
      finalAppId.value = res.finalAppId;
      appTitle.value = res.app?.name;
      uploadLimit.value = res.app.chatConfig.fileSelectConfig || {
        canSelectImg: false,
        canSelectFile: false,
        maxFiles: 0,
      };

      // 处理历史记录数据
      messages.value = res.history.map(it => {
        return {
          ...it,
          dataId: it.dataId,
          status: 'finish',
          createTimeValue: dayjs(it.createTime).valueOf(),
          chatAppAvatarUrl: it.chatAppAvatarUrl,
          obj: it.obj,
          feedbackType: it.feedbackType,
          customFeedback: it.customFeedback,
          value: it.value,
        };
      });

      // 确保对话是一问一答的形式
      const formattedMessages = [];
      for (let i = 0; i < messages.value.length; i++) {
        const currentMessage = messages.value[i];
        formattedMessages.push(currentMessage);
        if (
          currentMessage.obj === 'Human' &&
          i + 1 < messages.value.length &&
          messages.value[i + 1].obj === 'AI'
        ) {
          formattedMessages.push(messages.value[i + 1]);
          i++; // 跳过下一个AI回复
        }
      }

      messages.value = formattedMessages;

      if (scroll) {
        scrollToBottom(200);
        scrollToBottom(1000);
      }
    })
    .catch(error => {
      // 检查是否是无权限操作错误
      if (error && error.code === 400 && error.msg && error.msg.includes('无权限操作')) {
        uni.showToast({
          title: '无权限访问此聊天',
          icon: 'none',
          duration: 2000,
        });
        // 延迟返回上一页，让用户能看到提示
        setTimeout(() => {
          handleBack();
        }, 1500);
      } else {
        // 其他错误处理
        uni.showToast({
          title: '获取聊天记录失败',
          icon: 'none',
          duration: 2000,
        });
      }
    });
}

// // 侧边栏数据处理
// function sideBarData(newData) {
//   if (newData && newData.type == 999) {
//     refeshQuickBar();
//     quickStep();
//   } else {
//     sideBarMessage.value = newData;
//     refeshQuickBar();
//   }
// }

// 刷新快速操作栏
function refeshQuickBar() {
  let flowList = uni.getStorageSync('flowList');
  if (flowList.length > 0) {
    let minSortItem = flowList.reduce((minItem, currentItem) => {
      return currentItem.sort < minItem.sort ? currentItem : minItem;
    });
    quickTitle.value = minSortItem.quickTitle ? minSortItem.quickTitle : null;
  } else {
    quickTitle.value = null;
  }
}

// 快速步骤操作
function quickStep() {
  let flowList = uni.getStorageSync('flowList');
  let step = {};
  if (flowList.length > 0) {
    step = flowList.filter(it => it.quickTitle == quickTitle.value)[0];
  }
  if (step.inputContent || step.proContent) {
    step.timestamp = new Date().getTime();
    otherTenant.value = null;
    selectedFlowStep.value = step;
  } else {
    otherTenant.value = step;
    clearOtherTenant(true);
  }
}

// 清除其他租户
function clearOtherTenant(value) {
  let flowList = uni.getStorageSync('flowList');
  if (flowList.length > 0 && value) {
    chatStore.setFlowList(flowList.filter(it => it.sort != otherTenant.value.sort));
  } else {
    chatStore.removeFlowList();
    otherTenant.value = null;
  }
  quickTitle.value = null;
}

// 滚动到底部
function scrollToBottom(delay = 20) {
  setTimeout(() => {
    scrollIntoView.value =
      scrollIntoView.value === 'scroll-bottom1' ? 'scroll-bottom2' : 'scroll-bottom1';
  }, delay);
}

// 设置输入内容
function setInput(input) {
  if (messageInputRef.value && typeof messageInputRef.value.setInput === 'function') {
    messageInputRef.value.setInput(input);
  } else {
    console.log('无法设置输入内容：messageInputRef.value.setInput不是函数');
  }
}

// 处理键盘状态变化
function handleKeyboardChange(visible) {
  isKeyboardVisible.value = visible;
  console.log('键盘状态变化:', visible);
}

// 处理上传组件展开状态变化
function handleUploadExpandChange(expanded) {
  isUploadExpanded.value = expanded;
  console.log('上传组件展开状态变化:', expanded);
}

// 处理输入框获取焦点
function handleInputFocus() {
  // 关闭MessageList中的longpress-popup
  if (messageListRef.value && typeof messageListRef.value.closeLongPressPopup === 'function') {
    messageListRef.value.closeLongPressPopup();
  }
}

// 处理滚动事件
function handleScroll() {
  // 滚动时关闭MessageList中的longpress-popup
  if (messageListRef.value && typeof messageListRef.value.closeLongPressPopup === 'function') {
    messageListRef.value.closeLongPressPopup();
  }
}

// 更新聊天内容
function updateContent(val) {
  startChat({
    content: val,
  });
}

// 删除消息
function onDeleteMessage(messageToDelete) {
  const indexToDelete = messages.value.findIndex(msg => msg.dataId === messageToDelete.dataId);

  if (indexToDelete === -1) {
    console.warn('Message to delete not found in local list.');
    return;
  }

  const idsToMarkForDeletion = new Set();
  idsToMarkForDeletion.add(messageToDelete.dataId);

  // Check if the message to delete is part of a Human-AI pair
  if (messageToDelete.obj === ai.ChatRoleEnum.human) {
    const nextMessageIndex = indexToDelete + 1;
    if (
      nextMessageIndex < messages.value.length &&
      messages.value[nextMessageIndex].obj === ai.ChatRoleEnum.ai
    ) {
      idsToMarkForDeletion.add(messages.value[nextMessageIndex].dataId);
      if (messages.value[nextMessageIndex].status !== 'finish') {
        stopChat('cancel');
      }
    }
  } else if (messageToDelete.obj === ai.ChatRoleEnum.ai) {
    if (messageToDelete.status !== 'finish') {
      stopChat('cancel');
    }
    const prevMessageIndex = indexToDelete - 1;
    if (prevMessageIndex >= 0 && messages.value[prevMessageIndex].obj === ai.ChatRoleEnum.human) {
      idsToMarkForDeletion.add(messages.value[prevMessageIndex].dataId);
    }
  }

  if (idsToMarkForDeletion.size === 0) {
    return; // Should not happen if messageToDelete.dataId was added
  }

  // Call API to remove messages from server
  if (innerChatId.value) {
    // Only attempt server deletion if there's a chat session ID
    idsToMarkForDeletion.forEach(id => {
      removeChatItem({
        chatId: innerChatId.value,
        contentId: id, // API expects contentId for the message
        tenantAppId: getCurrentTenantAppId(),
      })
        .then(() => {
          console.log(`Message ${id} removed from server.`);
        })
        .catch(error => {
          console.error(`Error removing message ${id} from server:`, error);
          // Optionally, notify user or handle error (e.g., revert local deletion if critical)
        });
    });
  } else {
    console.warn(
      'No innerChatId, skipping server deletion. Messages will be removed locally only.'
    );
  }

  // Update local messages list
  messages.value = messages.value.filter(msg => !idsToMarkForDeletion.has(msg.dataId));
}

// 停止聊天
function stopChat(reason) {
  console.log('stopChat 被调用，原因:', reason);
  console.log('当前 chatting 状态:', chatting.value);
  console.log('chatSSEClientRef.value 存在:', !!chatSSEClientRef.value);
  console.log('abortSignal.value 存在:', !!abortSignal.value);

  // 获取当前正在发送的用户消息内容，用于返回到输入框
  let userMessageContent = '';

  // 查找最后一条用户消息
  if (messages.value.length > 0) {
    // 从后往前查找最后一条用户消息
    for (let i = messages.value.length - 1; i >= 0; i--) {
      const message = messages.value[i];
      if (message.obj === ai.ChatRoleEnum.human) {
        // 从消息的value数组中提取文本内容
        const textItem = message.value?.find(item => item.type === 'text');
        if (textItem && textItem.text && textItem.text.content) {
          userMessageContent = textItem.text.content;
        }
        break;
      }
    }
  }

  // 所有环境都使用SSE客户端停止聊天
  if (chatSSEClientRef.value) {
    try {
      console.log('尝试调用 SSE 客户端的 stopChat 方法');
      chatSSEClientRef.value.stopChat();
      console.log('已停止SSE客户端聊天');
    } catch (error) {
      console.error('停止SSE客户端聊天时发生错误:', error);
    }
    abortSignal.value = null;
  } else if (abortSignal.value) {
    // 保留此代码作为备用，以防SSE客户端未初始化
    try {
      console.log('使用传统 AbortController 停止聊天');
      // 确保传递字符串参数
      abortSignal.value.abort(reason || 'user_canceled');
      console.log('已停止传统客户端聊天');
    } catch (error) {
      console.error('停止传统客户端聊天时发生错误:', error);
    }
    abortSignal.value = null;
  } else {
    console.log('没有活动的聊天信号可以停止');
  }

  // 立即更新正在生成的AI消息状态
  const lastMessage = messages.value[messages.value.length - 1];
  if (lastMessage && lastMessage.obj === ai.ChatRoleEnum.ai && lastMessage.status !== 'finish') {
    // 如果AI消息有内容，保留它并设置为完成状态
    if (lastMessage.value && lastMessage.value.length > 0) {
      const hasTextContent = lastMessage.value.some(
        item =>
          item.type === 'text' && item.text && item.text.content && item.text.content.trim() !== ''
      );

      if (hasTextContent) {
        lastMessage.status = 'finish';
        // 保存已经生成的内容
        const textContent =
          lastMessage.value.find(item => item.type === 'text')?.text?.content || '';
        if (textContent && innerChatId.value) {
          updateChatItem({
            dataId: lastMessage.dataId,
            content: textContent,
            value: JSON.stringify(lastMessage.value),
          }).catch(error => {
            console.error('保存中断的聊天内容失败:', error);
          });
        }
      } else {
        // 如果AI消息没有文本内容，删除AI消息和对应的人类消息
        messages.value.pop(); // 删除AI消息
        if (
          messages.value.length > 0 &&
          messages.value[messages.value.length - 1].obj === ai.ChatRoleEnum.human
        ) {
          messages.value.pop(); // 删除对应的人类消息
        }
      }
    } else {
      // 如果AI消息没有内容，删除AI消息和对应的人类消息
      messages.value.pop(); // 删除AI消息
      if (
        messages.value.length > 0 &&
        messages.value[messages.value.length - 1].obj === ai.ChatRoleEnum.human
      ) {
        messages.value.pop(); // 删除对应的人类消息
      }
    }
  }

  // 将用户消息内容返回到输入框
  if (userMessageContent) {
    console.log('将用户消息内容返回到输入框:', userMessageContent);
    setInput(userMessageContent);
  }

  // 重置所有加载状态
  console.log('重置所有加载状态');
  isResponseLoading.value = false;
  isParsingBackground.value = false;
  isParsingFiles.value = false;
  isParsingImage.value = false;
  isParsingDocument.value = false;

  // 滚动到底部
  scrollToBottom();
}
const accessFileUrlWithFilename = (fileUrl, filename) => {
  if (!fileUrl.includes('filename')) {
    if (fileUrl.includes('?')) {
      fileUrl = fileUrl + `&filename=${filename.replace(/ /g, '')}`;
    } else {
      fileUrl = fileUrl + `?filename=${filename.replace(/ /g, '')}`;
    }
  }

  return fileUrl;
};
// 开始聊天
async function startChat(input) {
  if (chatting.value) {
    return Promise.reject();
  }

  // 发送消息后立即清空路由参数（保留appId）
  clearRouteParams();

  scrollToBottom();

  const content = input.content;
  const uploadList = input.uploadList || [];
  const saveChatId = chatId.value;
  const isNewChat = !innerChatId.value;
  const currentChatId = innerChatId.value || (await nanoid());
  abortSignal.value?.abort();
  const signal = ai.AbortSignal();
  abortSignal.value = signal;
  const humanDataId = await nanoid();
  const aiDataId = await nanoid();
  const currentTime = dayjs().valueOf(); // 获取当前时间戳

  console.log('uploadListuploadList', uploadList);

  const newMessages = [
    ...messages.value,
    {
      dataId: humanDataId,
      obj: ai.ChatRoleEnum.human,
      chatAppId: getCurrentTenantAppId(),
      isLike: 0,
      value: [
        ...(uploadList
          ?.filter(item => item.type === 'file')
          .map(file => ({
            type: 'file',
            file: {
              type: 'file',
              name: file.fileName || '',
              size: file.fileSize || 0,
              fileType: file.fileType || '',
              fileContent: file.fileContent || '',
              url: accessFileUrlWithFilename(file.fileUrl, file.fileName),
              fileId: file.fileKey || '',
            },
          })) || []),
        ...(uploadList
          ?.filter(item => item.type === 'image_url')
          .map(image => ({
            type: 'file',
            file: {
              type: 'image',
              url: accessFileUrlWithFilename(image.fileUrl, image.fileName),
            },
          })) || []),
        ...(chatStore.hiddenContent
          ? [
              {
                type: 'prompt',
                prompt: {
                  content: chatStore.hiddenContent,
                },
              },
            ]
          : []),
        ...(content
          ? [
              {
                type: 'text',
                text: {
                  content: content,
                },
              },
            ]
          : []),
      ],
      status: 'finish',
      createTimeValue: currentTime, // 添加 createTimeValue
    },
    {
      dataId: aiDataId,
      obj: ai.ChatRoleEnum.ai,
      chatAppId: getCurrentTenantAppId(),
      isLike: 0,
      value: [], // 修改为空数组以支持新的onMessage逻辑
      status: 'loading',
      createTimeValue: currentTime + 1, // AI消息时间略晚于用户消息，确保顺序
    },
  ];

  messages.value = newMessages;

  const onMessage = ({ text = '', status, name, event, reasoningText }) => {
    const aiMessage = messages.value.find(it => it.dataId === aiDataId);
    if (name === '生成图片' && status === 'running') {
      isParsingImage.value = true;
    } else if (name === '文档解析' && status === 'running') {
      isParsingDocument.value = true;
    }
    if (!aiMessage) {
      signal.abort('cancel'); // 使用startChat作用域内的signal
      return;
    }

    if (event === 'answer' && reasoningText) {
      const lastValue = aiMessage.value[aiMessage.value.length - 1];
      if (lastValue?.type === 'reasoning') {
        lastValue.reasoning.content += reasoningText;
      } else {
        aiMessage.value.push({
          type: 'reasoning',
          reasoning: {
            content: reasoningText,
          },
        });
      }
      isResponseLoading.value = true;
    }
    if ((event === 'answer' || event === 'fastAnswer') && text) {
      const lastValue = aiMessage.value[aiMessage.value.length - 1];
      if (lastValue?.type === 'text') {
        lastValue.text.content += text;
      } else {
        aiMessage.value.push({
          type: 'text',
          text: {
            content: text,
          },
        });
      }
      isResponseLoading.value = true;
    }

    if (name) {
      aiMessage.status = status;
      aiMessage.moduleName = name;
    }

    if (!reasoningText) {
      isResponseLoading.value = false;
    }

    scrollToBottom();
  };

  const humanIndex = findLastIndexCompat(messages.value, it => it.dataId === humanDataId);
  const humanMessage = messages.value[humanIndex];

  uploadList.forEach(item => {
    item.fileUrl = accessFileUrlWithFilename(item.fileUrl, item.fileName);
    // 初始化content字段，后续在解析时会填充
    item.content = '';
  });

  // 定义 fileKeys 数组
  const fileKeys = uploadList.map(item => item.fileKey);

  let uploadListArray = [];
  // 背景知识解析文件内容
  let quotedRef = [];
  let searchSelectedRef = [];
  isParsingBackground.value = true;
  try {
    console.log('appId.value', appId.value);
    console.log('uni.getStorageSync("userInfo")', uni.getStorageSync('userInfo'));
    const appContextDetail = await getAppContextDetail({
      tenantAppId: getCurrentTenantAppId(),
    }).catch(error => {
      console.error('获取应用上下文失败:', error);
      return null;
    });

    if (appContextDetail?.files?.length) {
      const validFiles = appContextDetail.files.filter(item => item.authority !== 'Invalid');
      quotedRef = await Promise.all(
        validFiles.map(async item => {
          try {
            const res = await getParseResult({ fileKey: item.fileKey });
            return {
              fileContent: res.fileContent,
              fileName: item.fileName,
            };
          } catch (error) {
            console.error('解析文件失败:', error);
            return { fileContent: '', fileName: '' };
          }
        })
      );
      // 过滤掉解析失败的文件
      quotedRef = quotedRef.filter(item => item.fileContent && item.fileName);
    }

    if (appContextDetail?.spaces?.length) {
      const validSpaces = appContextDetail.spaces.filter(item => item.authority !== 'Invalid');
      if (validSpaces.length) {
        try {
          searchSelectedRef =
            (await chatSearchDataset({
              messages: [
                {
                  dataId: humanDataId,
                  content: content,
                  role: 'user',
                },
              ],
              spaceIds: validSpaces.map(item => item.spaceId),
            })) || [];
        } catch (error) {
          console.error('搜索数据集失败:', error);
          searchSelectedRef = [];
        }
      }
    }
  } catch (error) {
    console.error('背景知识解析失败:', error);
  }
  isParsingBackground.value = false;

  // 解析上传的文件
  let parseResult = undefined;
  if (uploadList.length > 0) {
    isParsingFiles.value = true;
    try {
      // 准备解析图片和文件
      let promises = [];

      // 筛选图片和文件
      let images = uploadList.filter(it => it.type === 'image_url');
      let files = uploadList.filter(it => it.type === 'file');

      // 解析文件
      if (files.length > 0) {
        // 创建文件解析Promise数组
        const filePromises = files.map(async file => {
          try {
            console.log('开始解析文件:', file.fileName);
            const res = await getParseResult({ fileKey: file.fileKey });

            // 更新文件内容
            file.content = res.fileContent || '';
            file.fileContent = res.fileContent || '';

            // 更新uploadList中对应的项
            for (let i = 0; i < uploadList.length; i++) {
              if (uploadList[i].fileKey === file.fileKey) {
                console.log('文件内容已更新:', file.fileName);
                uploadList[i].content = res.fileContent || '';
                uploadList[i].fileContent = res.fileContent || '';
                break;
              }
            }

            return {
              FileContent: res.fileContent,
              FileName: file.fileName,
              // FileType: file.fileType,
              // FileUrl: file.fileUrl,
            };
          } catch (error) {
            console.error('文件解析失败:', file.fileName, error);
            return null;
          }
        });

        // 等待所有文件解析完成
        const fileResults = await Promise.all(filePromises);
        const validResults = fileResults.filter(res => res !== null);

        if (validResults.length > 0) {
          parseResult = {
            UploadedFileParsingResult: {
              Files: validResults,
            },
          };
        }
      }

      // 处理图片OCR
      if (images.length > 0) {
        try {
          console.log('开始OCR识别图片:', images.length, '张');
          const ocrResult = await filesParseImageOcr({
            files: images.map(img => img.fileUrl),
            tenantAppId: getCurrentTenantAppId(),
            chatId: currentChatId,
          });

          if (
            ocrResult &&
            ocrResult.UploadedFileParsingResult &&
            ocrResult.UploadedFileParsingResult.Files
          ) {
            // 更新图片OCR内容
            ocrResult.UploadedFileParsingResult.Files.forEach((file, index) => {
              if (index < images.length && file.FileContent) {
                const imgUrl = images[index].fileUrl;

                // 更新uploadList中对应的项
                for (let i = 0; i < uploadList.length; i++) {
                  if (uploadList[i].fileUrl && uploadList[i].fileUrl.includes(imgUrl)) {
                    console.log('图片OCR内容已更新:', uploadList[i].fileName || '未命名图片');
                    uploadList[i].content = file.FileContent;
                    uploadList[i].fileContent = file.FileContent;
                    break;
                  }
                }
              }
            });

            // 合并到parseResult
            if (!parseResult) {
              parseResult = ocrResult;
            } else {
              parseResult.UploadedFileParsingResult.Files = [
                ...parseResult.UploadedFileParsingResult.Files,
                ...ocrResult.UploadedFileParsingResult.Files,
              ];
            }
          }
        } catch (error) {
          console.error('OCR识别失败:', error);
        }
      }
    } catch (error) {
      console.error('文件解析过程出错:', error);
    } finally {
      isParsingFiles.value = false;
    }
  }

  // 检查文件内容是否已更新
  console.log('文件解析后的uploadList:');
  uploadList.forEach((item, index) => {
    console.log(
      `[${index}] ${item.fileName || item.fileKey}: content长度=${(item.content || '').length}`
    );
  });

  const hiddenContent = chatStore.hiddenContent;

  if (hiddenContent) {
    tempContentArray.push({
      type: 'prompt',
      content: hiddenContent,
    });
    chatStore.removeHiddenContent();
  }

  // 构建uploadListArray - 确保将文件内容包含在内
  if (uploadList.length > 0) {
    uploadListArray = uploadList.map(item => {
      if (item.type === 'image_url') {
        return {
          type: 'image_url',
          image_url: {
            url: item.fileUrl,
          },
        };
      } else if (item.type === 'file') {
        return {
          type: 'file_url',
          name: item.fileName,
          url: item.fileUrl,
          fileId: item.fileKey,
          content: item.content || item.fileContent || '',
        };
      }
    });
  }

  let tempContentArray = [];

  if (uploadListArray.length + tempContentArray.length > 0) {
    tempContentArray = uploadListArray.concat(tempContentArray);
    tempContentArray.push({
      type: 'text',
      text: parseResult
        ? `\`\`\` parseFiles\n${JSON.stringify(parseResult)} \n\`\`\` \n${content}`
        : content,
    });
  }
  const targetMessages = messages.value[messages.value.length - 2].value;
  let chatAppId = getCurrentTenantAppId();
  if (
    otherTenant.value &&
    otherTenant.value.tenantAppId &&
    Number(otherTenant.value.tenantAppId) > 0
  ) {
    chatAppId = otherTenant.value.tenantAppId;
    let flowList = uni.getStorageSync('flowList');
    if (flowList.length > 0) {
      chatStore.setFlowList(flowList.filter(it => it.sort != otherTenant.value.sort));
    }
    otherTenant.value = null;
    refeshQuickBar();
  }
  const requestData = {
    chatAppId: chatAppId,
    value: JSON.stringify(targetMessages),
    content: input.content,
    fileKeys: fileKeys,
    messages: [
      {
        dataId: humanDataId,
        role: 'user',
        content: tempContentArray.length > 0 ? tempContentArray : input.content,
      },
    ],
    responseChatItemId: aiDataId,
    variables: {
      cTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    },
    tenantAppId: getCurrentTenantAppId(),
    chatId: currentChatId,
    detail: true,
    stream: true,
    quotedRef:
      quotedRef.length && quotedRef.every(item => item.fileContent && item.fileName)
        ? quotedRef
        : undefined,
    searchSelectedRef: searchSelectedRef.length ? searchSelectedRef : undefined,
    rawParseResult: parseResult || undefined,
  };

  try {
    if (chatSSEClientRef.value) {
      // 使用SSE客户端的startChat方法
      chatSSEClientRef.value.startChat({
        url: resolveUrl('/huayun-ai/app/chat/completions'),
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: uni.getStorageSync('token'),
        },
        body: requestData,
        aiDataId: aiDataId,
        currentChatId: currentChatId,
        isNewChat: isNewChat,
        saveChatId: saveChatId,
        humanDataId: humanDataId,
        timestamp: currentTime,
      });

      console.log('SSE请求已发送, chatId:', currentChatId);
    } else {
      // 如果SSE客户端引用不可用，显示错误
      uni.showToast({
        title: '聊天客户端未初始化，请重试',
        icon: 'none',
        duration: 2000,
      });

      // 清理已添加的消息
      const aiIndex = findLastIndexCompat(messages.value, it => it.dataId === aiDataId);
      const humanIndex = findLastIndexCompat(messages.value, it => it.dataId === humanDataId);

      if (aiIndex >= 0) {
        messages.value.splice(aiIndex, 1);
      }
      if (humanIndex >= 0) {
        messages.value.splice(humanIndex, 1);
      }
    }
  } catch (error) {
    // 处理请求失败的情况
    const aiIndex = findLastIndexCompat(messages.value, it => it.dataId === aiDataId);
    const humanIndex = findLastIndexCompat(messages.value, it => it.dataId === humanDataId);

    if (aiIndex >= 0) {
      messages.value.splice(aiIndex, 1);
    }
    if (humanIndex >= 0) {
      messages.value.splice(humanIndex, 1);
    }

    // 显示错误提示
    uni.showToast({
      title: '发送消息失败，请重试',
      icon: 'none',
      duration: 2000,
    });
  }
}

// SSE客户端事件处理方法
const handleSseOpen = response => {
  console.log('SSE连接已打开', response);
  isResponseLoading.value = true;
};

const handleSseMessage = msg => {
  try {
    if (msg.data === '[DONE]') {
      // 结束流式传输
      const aiIndex = findLastIndexCompat(
        messages.value,
        it => it.obj === ai.ChatRoleEnum.ai && it.status !== 'finish'
      );
      if (aiIndex >= 0) {
        messages.value[aiIndex].status = 'finish';
        // 存储最后处理的AI消息ID
        lastFinishedAiMessageId.value = messages.value[aiIndex].dataId;
        console.log('收到[DONE]，存储AI消息ID:', lastFinishedAiMessageId.value);
      } else {
        console.warn('收到[DONE]但未找到未完成的AI消息');
      }
      return;
    }

    // 解析data字段（它是一个JSON字符串）
    let parsedData;
    if (typeof msg.data === 'string') {
      try {
        parsedData = JSON.parse(msg.data);
      } catch (e) {
        console.error('解析msg.data失败:', e);
        return;
      }
    } else {
      parsedData = msg.data;
    }

    // 提取文本内容和其他必要信息
    let text = '';
    let status = 'running';
    let name = '';
    let reasoningText = '';
    let event = msg.event || 'answer';

    if (parsedData.choices && parsedData.choices.length > 0) {
      // 从choices[0].delta.content获取文本
      text = parsedData.choices[0].delta?.content || '';
      // 检查是否有reasoning部分
      if (parsedData.choices[0].delta?.reasoning_content) {
        reasoningText = parsedData.choices[0].delta.reasoning_content;
      }

      // 检查是否有模块名称
      if (parsedData.choices[0].delta?.module) {
        name = parsedData.choices[0].delta.module.name || '';
        status = parsedData.choices[0].delta.module.status || 'running';
      }
    }

    // 特殊模块状态处理
    if (name === '生成图片' && status === 'running') {
      isParsingImage.value = true;
    } else if (name === '文档解析' && status === 'running') {
      isParsingDocument.value = true;
    }

    // 构建与原有onMessage兼容的事件对象
    const eventData = {
      text,
      status,
      name,
      event,
      reasoningText,
    };

    // 查找当前正在生成回复的AI消息
    const aiIndex = findLastIndexCompat(
      messages.value,
      it => it.obj === ai.ChatRoleEnum.ai && it.status !== 'finish'
    );
    if (aiIndex >= 0) {
      const aiMessage = messages.value[aiIndex];
      // 记录当前处理的消息ID
      lastFinishedAiMessageId.value = aiMessage.dataId;

      // 处理推理文本
      if (eventData.reasoningText) {
        const lastValue = aiMessage.value[aiMessage.value.length - 1];
        if (lastValue?.type === 'reasoning') {
          lastValue.reasoning.content += eventData.reasoningText;
        } else {
          aiMessage.value.push({
            type: 'reasoning',
            reasoning: {
              content: eventData.reasoningText,
            },
          });
        }
        isResponseLoading.value = true;
      }

      // 处理回复文本
      if (eventData.text) {
        const lastValue = aiMessage.value[aiMessage.value.length - 1];
        if (lastValue?.type === 'text') {
          lastValue.text.content += eventData.text;
        } else {
          aiMessage.value.push({
            type: 'text',
            text: {
              content: eventData.text,
            },
          });
        }
        isResponseLoading.value = true;
      }

      // 更新消息状态和模块名称
      if (eventData.name) {
        aiMessage.status = eventData.status;
        aiMessage.moduleName = eventData.name;
      }

      // 滚动到底部
      scrollToBottom();
    }
  } catch (error) {
    console.error('处理SSE消息时出错:', error);
  }
};

const handleSseError = error => {
  console.error('SSE连接错误:', error);

  // 重置相关状态
  isResponseLoading.value = false;
  isParsingBackground.value = false;
  isParsingFiles.value = false;
  isParsingImage.value = false;
  isParsingDocument.value = false;

  // 更新最后一条AI消息的状态为错误
  const aiIndex = findLastIndexCompat(
    messages.value,
    it => it.obj === ai.ChatRoleEnum.ai && it.status !== 'finish'
  );
  if (aiIndex >= 0) {
    const aiMessage = messages.value[aiIndex];
    aiMessage.status = 'error';

    // 可以选择显示错误消息
    aiMessage.value.push({
      type: 'text',
      text: {
        content: '抱歉，连接出现问题，请重试。',
      },
    });
  }

  // 显示错误提示
  uni.showToast({
    title: '连接出现问题，请重试',
    icon: 'none',
    duration: 2000,
  });
};

const handleSseFinish = () => {
  console.log('SSE连接已完成');

  // 重置状态
  isResponseLoading.value = false;
  isParsingBackground.value = false;
  isParsingFiles.value = false;
  isParsingImage.value = false;
  isParsingDocument.value = false;

  let aiIndex = -1;
  let aiMessage = null;

  // 首先尝试使用lastFinishedAiMessageId查找
  if (lastFinishedAiMessageId.value) {
    console.log('使用lastFinishedAiMessageId查找消息:', lastFinishedAiMessageId.value);
    aiIndex = messages.value.findIndex(msg => msg.dataId === lastFinishedAiMessageId.value);

    if (aiIndex >= 0) {
      console.log('通过ID找到消息，索引:', aiIndex);
      aiMessage = messages.value[aiIndex];
    } else {
      console.warn('通过ID未找到消息');
    }
  }

  // 如果通过ID未找到，尝试其他方式
  if (aiIndex < 0) {
    // 查找最近的AI消息 - 不再使用status作为查找条件
    aiIndex = findLastIndexCompat(messages.value, it => it.obj === ai.ChatRoleEnum.ai);

    // 如果没找到，尝试查找最后一条消息
    if (aiIndex < 0 && messages.value.length > 0) {
      console.log('使用最后一条消息作为候选');
      aiIndex = messages.value.length - 1;
    }

    if (aiIndex >= 0) {
      aiMessage = messages.value[aiIndex];
    }
  }

  console.log('找到消息索引:', aiIndex, '消息总数:', messages.value.length);

  if (aiMessage) {
    console.log('找到的消息:', {
      dataId: aiMessage.dataId,
      obj: aiMessage.obj,
      status: aiMessage.status,
      valueLength: aiMessage.value?.length || 0,
    });

    // 确保状态为finish
    aiMessage.status = 'finish';

    // 获取文本内容
    const textContent = aiMessage.value.find(item => item.type === 'text')?.text?.content || '';
    console.log('对话完成，准备保存聊天记录，文本内容长度:', textContent.length);

    // 从SSE客户端接收的参数中获取chatId
    const sseParams = chatSSEClientRef.value?.getParams();
    console.log('SSE参数:', sseParams);

    // 决定使用哪个chatId
    let chatIdToUse = innerChatId.value;

    // 如果没有innerChatId，尝试使用SSE参数中的currentChatId
    if (!chatIdToUse && sseParams && sseParams.currentChatId) {
      console.log('使用SSE参数中的currentChatId:', sseParams.currentChatId);
      chatIdToUse = sseParams.currentChatId;
      innerChatId.value = chatIdToUse; // 更新innerChatId

      // 如果是新对话，生成标题
      if (sseParams.isNewChat && sseParams.saveChatId === chatId.value) {
        // 查找人类消息
        const humanIndex = findLastIndexCompat(
          messages.value,
          it => it.obj === ai.ChatRoleEnum.human
        );
        if (humanIndex >= 0) {
          const humanMessage = messages.value[humanIndex];
          console.log('生成对话标题');
          chatOnce(humanMessage, aiMessage, chatIdToUse);
        }
      }
    }

    // 保存聊天记录 - 不再依赖于innerChatId检查
    if (aiMessage.dataId && textContent) {
      console.log('保存聊天记录:', {
        dataId: aiMessage.dataId,
        content: textContent.substring(0, 50) + '...',
        chatId: chatIdToUse || '未指定',
      });

      // 准备保存参数
      const saveParams = {
        dataId: aiMessage.dataId,
        content: textContent,
        value: JSON.stringify(aiMessage.value),
      };

      // 执行保存 - API可能不需要chatId参数，所以去掉这部分判断
      updateChatItem(saveParams)
        .then(() => {
          console.log('聊天记录保存成功');

          // 成功保存后，如果有chatId但innerChatId为空，设置innerChatId
          if (!innerChatId.value && chatIdToUse) {
            innerChatId.value = chatIdToUse;
            console.log('保存成功后设置innerChatId:', innerChatId.value);
          }

          // 重置lastFinishedAiMessageId
          lastFinishedAiMessageId.value = null;
        })
        .catch(error => {
          console.error('保存聊天记录失败:', error);
          // 显示错误提示
          uni.showToast({
            title: '保存聊天记录失败',
            icon: 'none',
            duration: 2000,
          });
        });
    } else {
      console.warn('缺少保存聊天记录所需的数据:', {
        hasDataId: !!aiMessage.dataId,
        hasContent: !!textContent,
      });

      // 即使不保存也重置ID
      lastFinishedAiMessageId.value = null;
    }
  } else {
    console.warn('未找到需要完成的AI消息，消息数组长度:', messages.value.length);
    lastFinishedAiMessageId.value = null;
  }
};

// 清空路由传入的一次性参数（保留appId）
function clearRouteParams() {
  // 清空文件相关参数
  routeFileObj.value = null;
  messageType.value = '';
  chatType.value = '';

  file.value = null;

  // 重置消息类型为默认值（如果需要的话）
  // messageType.value = '';

  if (messageInputRef.value && typeof messageInputRef.value.clearRouteFile === 'function') {
    messageInputRef.value.clearRouteFile();
  } else if (messageInputRef.value && typeof messageInputRef.value.reset === 'function') {
    // 如果没有clearRouteFile方法，尝试使用reset方法
    messageInputRef.value.reset();
  }
}

// 更新消息
function onUpdateMessage(message) {
  updateChatItem(message).then(() => {
    const found = messages.value.find(it => it.dataId == message.dataId);
    found && Object.assign(found, message);
  });
}

// 重试消息
function onRetryMessage(message) {
  stopChat('cancel');

  let index = messages.value.findIndex(it => it.dataId == message.dataId);
  while (index >= 0 && messages.value[index].obj == ai.ChatRoleEnum.ai) {
    index--;
  }

  const humanMessageToRetry =
    message.obj === ai.ChatRoleEnum.human ? message : messages.value[index];

  const content =
    message.content !== undefined
      ? message.content
      : humanMessageToRetry.value.reduce((acc, item) => {
          if (item.type === 'text' && item.text?.content) {
            return acc + item.text.content;
          }
          if (item.type === 'prompt' && item.prompt?.content) {
            return acc + item.prompt.content;
          }
          return acc;
        }, '');

  const uploadList = humanMessageToRetry.value.reduce((acc, item) => {
    if (item.type === 'file' && item.file) {
      if (item.file.type === 'image') {
        acc.push({
          type: 'image_url',
          fileUrl: item.file.url,
          fileName: item.file.name,
        });
      } else if (item.file.type === 'file') {
        acc.push({
          type: 'file',
          fileName: item.file.name,
          fileUrl: item.file.url.split('?')[0],
          fileKey: item.file.fileId,
          fileContent: item.file.fileContent,
          fileSize: item.file.size,
          fileType: item.file.fileType,
        });
      }
    }
    return acc;
  }, []);

  if (index >= 0) {
    const humanIndex = index;
    const dataIds = messages.value
      .slice(humanIndex, humanIndex + 2)
      .filter(it => it.dataId)
      .map(it => it.dataId);
    dataIds.forEach(id => {
      removeChatItem({
        chatId: innerChatId.value,
        contentId: id,
        tenantAppId: getCurrentTenantAppId(),
      });
    });
    messages.value.splice(humanIndex, 2);
  }

  if (index < 0) {
    return;
  }

  startChat({
    content,
    uploadList,
  });
}

// 一次性聊天
function chatOnce(humanMessage, aiMessage, chatId) {
  console.log('开始生成对话标题, chatId:', chatId);
  // 重置标题
  resultTitle.value = '';

  const titlePromptTemplate = `
  目标：
  -通过一轮对话内容生成一个简短的标题。

  回答要求：
  - 标题应该与对话内容一致，且与对话内容相关。
  - 标题不带标点符号。
  - 标题不带任何解释性前缀。
  - 标题长度少于20个字。

  下面是一个对话内容：

  问题:'''{{question}}'''

  回答:'''{{answer}}'''
  `;

  const signal = ai.AbortSignal();
  let fetchPromise;

  // 提取问题和回答文本
  const questionText =
    humanMessage?.value?.map(it => (it.type === 'text' ? it.text.content : '')).join('') || '';
  const answerText =
    aiMessage?.value?.map(it => (it.type === 'text' ? it.text.content : '')).join('') || '';

  console.log('问题文本长度:', questionText.length);
  console.log('回答文本长度:', answerText.length);

  try {
    fetchPromise = ai.fetch({
      url: '/huayun-ai/client/chat/once',
      data: {
        messages: [
          {
            role: 'user',
            content: titlePromptTemplate
              .replace('{{question}}', questionText)
              .replace('{{answer}}', answerText),
          },
        ],
        chatId: chatId,
        tenantAppId: getCurrentTenantAppId(),
        detail: true,
        stream: true,
        variables: {
          cTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        },
      },
      onMessage: event => {
        // 处理接收到的消息
        const messageData = event.text;
        resultTitle.value = resultTitle.value + (messageData || '');
        console.log('生成标题片段:', messageData);
      },
      abortSignal: signal,
    });
  } catch (error) {
    console.error('chatOnce请求初始化错误:', error);
    fetchPromise = Promise.reject(error);
  }

  fetchPromise
    .catch(error => {
      console.error('chatOnce请求出错:', error);
    })
    .finally(() => {
      try {
        const finalTitle = resultTitle.value || '新对话';
        console.log('保存对话标题:', finalTitle, 'chatId:', chatId);

        // 确保使用正确的chatId更新标题
        const chatIdToUse = innerChatId.value || chatId;

        updateChatTitle({
          chatId: chatIdToUse,
          title: finalTitle,
        })
          .then(() => {
            console.log('对话标题更新成功');
          })
          .catch(error => {
            console.error('更新对话标题失败:', error);
            // 尝试再次保存
            setTimeout(() => {
              console.log('尝试再次保存标题...');
              updateChatTitle({
                chatId: chatIdToUse,
                title: finalTitle,
              }).catch(e => {
                console.error('再次保存标题也失败:', e);
              });
            }, 1000);
          });
      } catch (error) {
        console.error('处理对话标题更新时出错:', error);
      }
    });
}

// 生命周期钩子
onMounted(() => {
  // 由于参数获取已经移到onLoad中处理，这里只需要做一些额外的初始化工作
  // console.log('组件已挂载，当前参数:', {
  //   file: file.value,
  //   messageType: messageType.value,
  //   routeAppId: routeAppId.value
  // });
});

onUnmounted(() => {
  stopChat('cancel');
});

// 添加一个新的共享函数来处理导航逻辑
const handleNavigateBack = () => {
  const from = routeParams.value?.from;
  console.log(`页面从${from}跳转`);
  // 处理特殊情况
  switch (from) {
    case 'create-app-success':
      {
        tabStore.activeTabIndex = 1;
        uni.reLaunch({ url: '/pages/index/index' });
      }
      return true;

    default:
      {
        console.log('执行返回操作');
        // 获取当前页面栈
        const pages = getCurrentPages();

        // 如果页面栈中只有一个页面，则跳转到首页而不是退出应用
        if (pages.length <= 1) {
          console.log('页面栈中只有一个页面，跳转到首页');
          uni.switchTab({
            url: '/pages/index/index',
          });
          return true;
        }

        // 执行返回操作
        uni.navigateBack({
          fail: () => {
            uni.switchTab({
              url: '/pages/index/index',
            });
          },
        });
      }
      return true;
  }
};

// 添加系统返回键处理-只兼容安卓,IOS系统返回已经禁止
onBackPress(event => {
  console.log('event-1111111111111', event, routeParams.value);
  if (event.from === 'backbutton') {
    // const from = routeParams.value?.from;
    return handleNavigateBack();
  } else {
    return false;
  }
});

const handleBack = () => {
  // const from = routeParams.value?.from;
  return handleNavigateBack();
};

const handleRefresh = () => {
  // 刷新聊天记录
  if (innerChatId.value) {
    init();
  }
};

const handleShare = () => {
  const currentTenantAppId = getCurrentTenantAppId();
  uni.navigateTo({
    url: '/pages-subpackages/chat-pkg/share/index?tenantAppId=' + currentTenantAppId,
    params: {
      tenantAppId: currentTenantAppId,
    },
  });
};
const newChat = () => {
  if (JSON.parse(uni.getStorageSync('userInfo')).account) {
    chatId.value = '';
    // 刷新页面，开始新对话
    init();
  } else {
    uni.redirectTo({
      url: '/pages/login/login',
    });
  }
};
</script>

<template>
  <view
    class="chat-container"
    :style="{
      backgroundImage:
        'url(https://huayun-ai-obs-public.huayuntiantu.com/d8eff78e-4c35-4b55-8bf7-e0d0ecfcf653.png?filename=bg_chat.png)',
      backgroundAttachment: 'fixed',
      backgroundSize: '100% auto',
      backgroundPosition: 'bottom',
    }"
  >
    <!-- 添加一个顶部安全区域占位 -->
    <view class="safe-area-top" :style="{ height: safeAreaTop + 'px' }"></view>

    <view class="custom-header-nav">
      <view class="header-nav__left" @click="handleBack">
        <u-icon name="arrow-left" size="20" color="#1D2129"></u-icon>
      </view>

      <view class="chat-header-title">
        <LkSvg class="chat-header-title-icon" src="/static/learning-resource/ai-logo.svg" />
        <text class="chat-header-title-text">AI智能答疑</text>
      </view>
      <view class="header-nav__right"> </view>
    </view>

    <view class="chat-messages">
      <scroll-view
        class="message-list-scroll"
        scroll-y
        :scroll-into-view="scrollIntoView"
        @scroll="handleScroll"
      >
        <MessageList
          ref="messageListRef"
          :finalAppId="finalAppId"
          :list="messages"
          :isResponseLoading="isResponseLoading"
          :isParsingBackground="isParsingBackground"
          :isParsingFiles="isParsingFiles"
          :isParsingImage="isParsingImage"
          :isParsingDocument="isParsingDocument"
          :welcomeText="welcomeText"
          :appAvatarUrl="appAvatarUrl"
          @updateMessage="onUpdateMessage"
          @retryMessage="onRetryMessage"
          @updateContent="updateContent"
          @deleteMessage="onDeleteMessage"
          :resource="chatStore.toolGoChatParams.resource"
        />

        <view id="scroll-bottom1" class="scroll-bottom"></view>
        <view id="scroll-bottom2" class="scroll-bottom"></view>
      </scroll-view>

      <view class="input-container">
        <view v-if="chatting" class="stop-chat" @click="stopChat()" @touchstart="stopChat()">
          停止生成
        </view>
        <MessageInput
          ref="messageInputRef"
          :sideBarMessage="sideBarMessage"
          :chatting="chatting"
          :uploadLimit="uploadLimit"
          :file="routeFileObj"
          :messageType="messageType"
          :isMessageEditVisible="isMessageEditVisible || isDislikeModalVisible"
          @scrollToBottom="scrollToBottom(100)"
          @send="startChat"
          @keyboardChange="handleKeyboardChange"
          @uploadExpandChange="handleUploadExpandChange"
          @inputFocus="handleInputFocus"
        />
        <view
          class="chat-footer"
          v-show="
            !isKeyboardVisible &&
            !isUploadExpanded &&
            !isMessageEditVisible &&
            !isDislikeModalVisible
          "
        >
          <view class="ai-notice-text">内容由AI生成，请核查重要信息</view>
        </view>
      </view>
    </view>

    <!-- 添加gao-ChatSSEClient组件 -->
    <gao-chat-s-s-e-client
      ref="chatSSEClientRef"
      @onOpen="handleSseOpen"
      @onMessage="handleSseMessage"
      @onError="handleSseError"
      @onFinish="handleSseFinish"
    />
  </view>
</template>

<style scoped>
/* 顶部安全区域占位 */
.safe-area-top {
  width: 100%;
  background-color: #fff;
  /* 高度通过JavaScript动态设置，兼容安卓和iOS */
}

/* 自定义导航栏样式 */
.custom-header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  background-color: #fff;
  position: relative;
}

.header-nav__left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  cursor: pointer;
}

.header-nav__right {
  display: flex;
  align-items: center;
  width: 44px;
  height: 44px;
}

.header-nav__refresh {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  cursor: pointer;
}

.chat-header-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-header-title-icon {
  width: 24px;
  height: 24px;
}

.chat-header-title-text {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-repeat: no-repeat;
  background-color: #fff;
}

.chat-messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.message-list-scroll {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  position: relative;
  height: 700rpx;
}

.scroll-bottom {
  height: 0;
}

.input-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.stop-chat {
  display: inline-flex;
  padding: 5px 12px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.stop-chat:active {
  transform: scale(0.95);
  opacity: 0.7;
}

.stop-chat {
  border-radius: 16rpx;
  background: rgba(0, 0, 0, 0.4);
  font-size: 28rpx;
  color: #fff;
  position: absolute;
  bottom: 100%;
  margin-bottom: 14rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

.quick-bar {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
  margin-left: 32rpx;
}

.quick-step {
  display: inline;
  background-color: #ffffff;
  color: #3c6dff;
  border: 1rpx solid #3c6dff;
  padding: 12rpx 48rpx;
  font-size: 28rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

.quick-title {
  display: flex;
  align-items: center;
  color: #a9acb1;
  font-size: 22rpx;
  padding-left: 8rpx;
}

.other-tenant {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18rpx;
  margin-left: 32rpx;
  background-color: #f9f9f9;
}

.tenant-left {
  display: flex;
  align-items: center;
}

.tenant-label {
  color: #909399 !important;
}

.tenant-name {
  display: flex;
  align-items: center;
  margin: 0 12rpx;
}

.tenant-avatar {
  width: 32rpx;
  height: 32rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
}

.tenant-title {
  color: #111824;
  font-size: 1.1rem;
  font-weight: bold;
  margin-left: 12rpx;
}

.tenant-close {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}

.arrow-leftward::before {
  content: '←';
  font-size: 12rpx;
}

.close-icon::before {
  content: '×';
  font-size: 16rpx;
}

.custom-icon {
  margin-left: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-footer {
  padding: 28rpx 0 59rpx 0;
  position: relative;
  z-index: 999;
}
.ai-notice-text {
  color: var(--text-icon-text-3, #86909c);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 32rpx;
}

/* iOS 键盘防滚动样式 - 强制防止页面被键盘顶起 */
body.ios-keyboard-active {
  position: fixed !important;
  width: 100% !important;
  height: 100vh !important;
  overflow: hidden !important;
  touch-action: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -webkit-overflow-scrolling: auto !important;
}

html.ios-keyboard-active {
  overflow: hidden !important;
  touch-action: none !important;
  -webkit-overflow-scrolling: auto !important;
}

/* 确保聊天容器在iOS键盘激活时的表现 */
.chat-container.ios-keyboard-active {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100vh !important;
  overflow: hidden !important;
  touch-action: none !important;
  -webkit-overflow-scrolling: auto !important;
  overscroll-behavior: none !important;
}

/* 防止iOS Safari的橡皮筋效果 */
.chat-container {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;
}

/* 当键盘激活时，禁用所有滚动 */
.ios-keyboard-active * {
  -webkit-overflow-scrolling: auto !important;
  overflow: hidden !important;
}

/* 强制禁用iOS键盘导致的页面滚动 */
.ios-keyboard-active {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100vh !important;
  overflow: hidden !important;
  touch-action: none !important;
  -webkit-overflow-scrolling: auto !important;
  overscroll-behavior: none !important;
}

/* 确保输入框在键盘激活时正确定位 */
.ios-keyboard-active .message-input-inner {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
}
</style>
