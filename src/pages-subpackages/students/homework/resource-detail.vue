<template>
  <MyContainer bgColor="#F7F7FD" safeTop safeBottom :loading="isLoading">
    <view class="resource-detail">
      <!-- 顶部导航栏 -->
      <u-navbar
        title="资源详情"
        :autoBack="true"
        bgColor="#ffffff"
        color="#1D2129"
        placeholder
        height="88rpx"
      >
      </u-navbar>

      <!-- 内容区域 -->
      <!-- 资源信息 -->
      <view class="resource-info">
        <!-- 文件图标和标题 -->
        <view class="info-header">
          <view class="doc-icon-wrapper">
            <image class="document-icon" :src="fileIcon" mode="aspectFit"></image>
          </view>
          <view class="title-container">
            <view class="title">{{ resourceTitle }}</view>
            <view v-if="resourceVip" class="vip-tag">
              <text>精</text>
            </view>
          </view>
        </view>

        <!-- 分类标签 -->
        <view class="category-row">
          <view v-for="(tag, index) in resourceTags" :key="index" class="category-tag">
            <text>{{ tag }}</text>
          </view>
        </view>

        <!-- 分隔线 -->
        <view class="separator-line"></view>

        <!-- 文件信息 -->
        <view class="file-info-row">
          <view class="file-info-item">
            <image
              class="info-icon file-icon"
              src="/static/homework/file_more_lines.svg"
              mode="aspectFit"
            >
            </image>
            <text class="info-text">{{ resourceSize }}</text>
          </view>
          <text class="separator">|</text>
          <view class="file-info-item">
            <image
              class="info-icon date-icon"
              src="/static/homework/time_line.svg"
              mode="aspectFit"
            >
            </image>
            <text class="info-text">{{ formatDate }}</text>
          </view>
          <view class="school-tag">{{ schoolName }}</view>
        </view>
      </view>

      <!-- 内容预览区域 - 可滚动的富文本内容 -->
      <view class="content-scroll">
        <view class="content-container">
          <!-- 使用mp-html渲染富文本内容 -->
          <mp-html :content="htmlContent" :domain="domain" />
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="action-bar">
        <view class="collect-btn" @click="toggleCollect">
          <uni-icons
            :type="isCollected ? 'star-filled' : 'star'"
            size="24"
            :color="isCollected ? '#FFB23E' : '#4E5969'"
          ></uni-icons>
          <text>{{ isCollected ? '已收藏' : '收藏' }}</text>
        </view>
        <button class="action-button" @click="downloadResource">沉浸阅读</button>
      </view>
    </view>
  </MyContainer>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { MyContainer } from './components';
import { onLoad } from '@dcloudio/uni-app';

// 页面参数
const resourceId = ref('');
const resourceTitle = ref('');
const resourceSize = ref('');
const resourceType = ref('');
const resourceTags = ref<string[]>([]);
const schoolName = ref('学校');
const uploadTime = ref('');
const resourceVip = ref(true); // 是否为VIP资源
const isCollected = ref(false); // 是否收藏
const isLoading = ref(false);

// Mock数据
interface ResourceData {
  id: string;
  title: string;
  size: string;
  type: string;
  tags: string[];
  school: string;
  uploadTime: string;
  isVip: boolean;
}

const mockResourceData: Record<string, ResourceData> = {
  '1': {
    id: '1',
    title: '2024-2025七年级上册初中数学有理数知识点详解教案',
    size: '386KB',
    type: 'doc',
    tags: ['数学', '教案', '七年级'],
    school: '学校',
    uploadTime: '2025-04-03',
    isVip: true,
  },
  '2': {
    id: '2',
    title: '高中物理力学综合练习题及答案',
    size: '2.4MB',
    type: 'pdf',
    tags: ['物理', '练习题', '高中'],
    school: '第一中学',
    uploadTime: '2025-03-15',
    isVip: false,
  },
  '3': {
    id: '3',
    title: '小学语文必背古诗词大全',
    size: '1.2MB',
    type: 'ppt',
    tags: ['语文', '古诗词', '小学'],
    school: '实验小学',
    uploadTime: '2025-02-28',
    isVip: true,
  },
};

// 模拟API调用
const fetchResourceDetail = (id: string) => {
  isLoading.value = true;

  // 模拟网络延迟
  setTimeout(() => {
    const resourceData = mockResourceData[id] || mockResourceData['1'];

    // 设置数据
    resourceTitle.value = resourceData.title;
    resourceSize.value = resourceData.size;
    resourceType.value = resourceData.type;
    resourceTags.value = resourceData.tags;
    schoolName.value = resourceData.school;
    uploadTime.value = resourceData.uploadTime;
    resourceVip.value = resourceData.isVip;

    isLoading.value = false;
  }, 500);
};

// 文件图标
const fileIcon = computed(() => {
  if (resourceType.value === 'pdf') {
    return '/static/fileTypeIcon/pdf.svg';
  } else if (resourceType.value === 'word' || resourceType.value === 'doc') {
    return '/static/fileTypeIcon/doc.svg';
  } else if (resourceType.value === 'ppt') {
    return '/static/fileTypeIcon/ppt.svg';
  } else {
    return '/static/fileTypeIcon/unknown.svg';
  }
});

// 格式化日期
const formatDate = computed(() => {
  if (!uploadTime.value) return '';
  // 返回格式为 YYYY-MM-DD
  return uploadTime.value;
});

// 富文本内容
const htmlContent = ref(`
<div>
    <div style="padding: 20rpx; background-color: #f5f7fa;">
        <h2 style="font-size: 36rpx; font-weight: bold; margin-bottom: 20rpx;">第一章 有理数</h2>
        <div style="background-color: #ffffff; padding: 30rpx; border-radius: 16rpx; margin-bottom: 30rpx;">
            <h3 style="font-size: 32rpx; font-weight: 500; margin-bottom: 20rpx;">1.1 正数与负数</h3>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">①正数：大于0的数叫正数。（根据需要，有时在正数前面也加上"+"）</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">②负数：在以前学过的0以外的数前面加上负号"-"的数叫负数。与正数具有相反意义。</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">③0既不是正数也不是负数。0是正数与负数的分界线。</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">④整数：正整数、0和负整数统称为整数。</p>
        </div>
        <div style="background-color: #ffffff; padding: 30rpx; border-radius: 16rpx; margin-bottom: 30rpx;">
            <h3 style="font-size: 32rpx; font-weight: 500; margin-bottom: 20rpx;">1.2 有理数</h3>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">①有理数：整数和分数统称为有理数。</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">②有理数的性质：</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx; padding-left: 20rpx;">- 有理数可以表示为两个整数的比，即 p/q 的形式，其中 q≠0</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx; padding-left: 20rpx;">- 有理数在数轴上对应着确定的点</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx; padding-left: 20rpx;">- 有理数集是无限的，并且在任意两个不同的有理数之间，总能找到无数个有理数</p>
        </div>
        <div style="background-color: #ffffff; padding: 30rpx; border-radius: 16rpx; margin-bottom: 30rpx;">
            <h3 style="font-size: 32rpx; font-weight: 500; margin-bottom: 20rpx;">1.3 有理数的加减法</h3>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">①同号两数相加：绝对值相加，取相同的符号</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">②异号两数相加：绝对值相减，取绝对值较大数的符号</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">③两数相减：减去一个数等于加上这个数的相反数</p>
        </div>
        <div style="background-color: #ffffff; padding: 30rpx; border-radius: 16rpx; margin-bottom: 30rpx;">
            <h3 style="font-size: 32rpx; font-weight: 500; margin-bottom: 20rpx;">1.4 有理数的乘除法</h3>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">①乘法法则：同号得正，异号得负，绝对值相乘</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">②除法法则：同号得正，异号得负，绝对值相除</p>
            <p style="font-size: 28rpx; line-height: 1.6; margin-bottom: 16rpx;">③乘法分配律：a(b+c) = ab+ac</p>
        </div>
    </div>
</div>
`);

// 设置mp-html的domain
const domain = ref('');

// 获取页面参数
onLoad(options => {
  // 获取资源ID，默认使用ID 1
  const id = options?.query?.id || '1';
  resourceId.value = id;

  // 调用模拟API获取资源详情
  fetchResourceDetail(id);
});

// 收藏/取消收藏
function toggleCollect() {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '已收藏',
      icon: 'none',
      duration: 1500,
    });
  } else {
    uni.showToast({
      title: '已取消收藏',
      icon: 'none',
      duration: 1500,
    });
  }
}

// 返回上一页
function goBack() {
  uni.navigateBack();
}

// 沉浸阅读
function downloadResource() {
  uni.showToast({
    title: '进入沉浸阅读模式',
    icon: 'none',
  });
}
</script>

<style lang="scss" scoped>
.resource-detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .resource-info {
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    background-color: #fff;
    flex-shrink: 0;

    .info-header {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24rpx;

      .doc-icon-wrapper {
        width: 68rpx;
        height: 68rpx;
        background-color: #e6f5ff;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        flex-shrink: 0;

        .document-icon {
          width: 100%;
          height: 100%;
        }
      }

      .title-container {
        flex: 1;
        position: relative;

        .title {
          font-size: 36rpx;
          font-weight: 500;
          color: #303133;
          line-height: 1.3;
          margin-right: 40rpx;
          padding-right: 8rpx;
        }

        .vip-tag {
          position: absolute;
          right: 0;
          top: 6rpx;
          width: 40rpx;
          height: 40rpx;
          background-color: #ffb23e;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          text {
            color: #ffffff;
            font-size: 22rpx;
            font-weight: 500;
          }
        }
      }
    }

    .category-row {
      display: flex;
      gap: 16rpx;
      margin-bottom: 10rpx;
      flex-wrap: wrap;

      .category-tag {
        height: 40rpx;
        padding: 10rpx 16rpx;
        background-color: #f3f6fd;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          font-size: 24rpx;
          color: #84888f;
          text-align: center;
          letter-spacing: 0.5%;
        }
      }
    }

    .file-info-row {
      display: flex;
      align-items: center;

      .file-info-item {
        display: flex;
        align-items: center;

        .info-icon {
          width: 36rpx;
          height: 36rpx;
          margin-right: 8rpx;
        }

        .info-text {
          font-size: 28rpx;
          color: #86909c;
        }
      }

      .separator {
        margin: 0 20rpx;
        color: #86909c;
        font-size: 28rpx;
      }

      .school-tag {
        margin-left: auto;
        background: #e8f7ff;
        color: #3491fa;
        font-size: 28rpx;
        padding: 2rpx 16rpx;
        border-radius: 999px;
      }
    }
  }

  .separator-line {
    width: 100%;
    height: 1rpx;
    background-color: #f3f3f3;
    margin: 18rpx 0;
  }

  .content-scroll {
    flex: 1;
    background-color: #ffffff;
    margin-top: 20rpx;
    overflow-y: auto;

    .content-container {
      padding: 20rpx 40rpx;
      min-height: 100%;
    }
  }

  .action-bar {
    flex-shrink: 0;
    padding: 20rpx 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    box-shadow: 0 -8rpx 16rpx rgba(0, 0, 0, 0.05);

    .collect-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 88rpx;

      text {
        font-size: 24rpx;
        color: #4e5969;
        margin-top: 4rpx;

        &.active-text {
          color: #ffb23e;
        }
      }
    }

    .action-button {
      width: 75%;
      height: 88rpx;
      border-radius: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #7d4dff;
      border: none;
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 600;
    }
  }
}
</style>
