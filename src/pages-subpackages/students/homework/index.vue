<template>
  <MyDialogProvider>
    <view class="page">
      <view class="homework-container">
        <u-navbar :autoBack="false" bgColor="#fff" placeholder height="88rpx" @leftClick="goBack">
          <template #center>
            <text class="navbar-title">我的作业</text>
          </template>
          <template #right>
            <view class="navbar-right-btn" @tap="goToSelfHomework">自主作业</view>
          </template>
        </u-navbar>

        <!-- 搜索框 - 使用LkSearch组件 -->
        <view class="search-box">
          <LkSearch
            v-model="searchKeyword"
            placeholder="请输入作业名称"
            @search="search"
            @clear="clearSearch"
          />
        </view>

        <!-- 标签栏 -->
        <view class="tabs-container">
          <MyTabs v-model:currentIndex="tabCurrent" :tabItems="statusList" @change="onTabChange" />
        </view>

        <!-- 筛选区域 - 使用FilterItem组件 -->
        <view class="filter-area">
          <FilterItem
            :text="getSelectedLabel('semester', semesterValue)"
            defaultText="全部学期"
            @click="openFilterPopup('semester')"
          />
          <FilterItem
            :text="getSelectedLabel('subject', subjectValue)"
            defaultText="全部学科"
            @click="openFilterPopup('subject')"
          />
          <FilterItem
            :text="getSelectedLabel('type', typeValue)"
            defaultText="全部类型"
            @click="openFilterPopup('type')"
          />
        </view>

        <!-- 作业列表 -->
        <view class="page-list-wrapper">
          <z-paging
            ref="pagingRef"
            v-model="homeworkList"
            :fixed="false"
            :auto="true"
            :refresher-enabled="true"
            :safe-area-inset-bottom="true"
            :loading-more-enabled="true"
            :empty-view-text="'暂无作业数据'"
            :empty-view-img="emptyIcon"
            @query="queryList"
            @onRefresh="onRefresh"
            @onRestore="onRestore"
          >
            <uni-row :gutter="20">
              <uni-col
                :xs="24"
                :sm="24"
                :md="24"
                :lg="24"
                v-for="(item, index) in homeworkList"
                :key="index"
              >
                <HomeworkCard :item="item" @click="goToDetail(item)" />
              </uni-col>
            </uni-row>
          </z-paging>
        </view>
      </view>
      <!-- 自主作业弹窗 -->
      <popup-self-homework ref="selfHomeworkPopupRef" @success="handleSubmitSuccess" />

      <!-- 筛选弹窗 -->
      <PopupSemester ref="semesterPopupRef" @confirm="handleSemesterConfirm" />
      <PopupSubject ref="subjectPopupRef" @confirm="handleSubjectConfirm" />
      <PopupType ref="typePopupRef" @confirm="handleTypeConfirm" />
    </view>
  </MyDialogProvider>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import {
  STATUS_LIST,
  HomeworkType,
  HomeworkAnswerType,
  HomeworkTaskType,
} from '@/constants/students/homework';
import PopupSelfHomework from './components/PopupSelfHomework/index.vue';
import MyTabs from '@/components/MyTabs';
import { LkNavBar } from '@/components/LkNavBar';
import LkSearch from '@/components/LkSearch/index.vue';
import LkSelectList from '@/components/LkSelectList/index.vue';
import HomeworkCard from './components/HomeworkCard/index.vue';
import { convertToFrontendItem } from '@/pages-subpackages/students/homework/utils';
import { MyDialogProvider, showDialog, showDialogDanger } from '@/components/MyDialog';
import FilterItem from './components/FilterItem/index.vue';
import { PopupSemester, PopupSubject, PopupType } from './components';
import { getHomeworkPage } from '@/api/students/homework';
import type { HomeworkPageItem } from '@/types/students/homework';
import { HomeworkStatus } from '@/constants/students/homework';
import emptyIcon from '@/static/common/empty.svg';
import { onShow } from '@dcloudio/uni-app';

// 定义TabItem类型@
interface TabItem {
  label: string;
  value: string | number;
}

// 状态列表
const statusList = [{ label: '全部', value: '' }, ...STATUS_LIST];

// 标签栏数据
const tabCurrent = ref<string | number>(0);

// 自主作业弹窗引用
const selfHomeworkPopupRef = ref();

// 筛选弹窗引用
const semesterPopupRef = ref();
const subjectPopupRef = ref();
const typePopupRef = ref();

// z-paging 引用
const pagingRef = ref();

// 多选配置项
const filterConfig = {
  semester: {
    multiSelect: false, // 学期不支持多选
    showAll: true, // 显示"全部学期"选项
  },
  subject: {
    multiSelect: true, // 学科支持多选
    showAll: true, // 显示"全部学科"选项
  },
  type: {
    multiSelect: true, // 类型支持多选
    showAll: true, // 显示"全部类型"选项
  },
};

// 筛选值 - 根据多选配置调整类型
const semesterValue = ref<string | number>(''); // 学期默认单选，使用字符串
const subjectValue = ref<(string | number)[]>([]); // 学科多选，使用数组
const typeValue = ref<(string | number)[]>([]); // 类型多选，使用数组

// 搜索关键词
const searchKeyword = ref('');

// 作业列表数据
const homeworkList = ref<HomeworkPageItem[]>([]);

// 初始化数据
onShow(() => {
  // z-paging 自动模式会在组件挂载后自动加载数据
  // 这里可以处理页面显示时的逻辑
});

// z-paging 查询列表数据
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const result = await getHomeworkPage({
      current: pageNo,
      size: pageSize,
      searchKey: searchKeyword.value,
      semesterId: semesterValue.value,
      subjectIds: subjectValue.value.map(item => Number(item)),
      status: statusList[tabCurrent.value as number].value,
      aliases: typeValue.value.map(item => String(item)),
    });

    // 数据重构，将后端作业类型转成前端作业类型
    const newList = result.records.map((item: any) => {
      return convertToFrontendItem(item);
    });

    // z-paging 完成加载
    pagingRef.value?.complete(newList);
  } catch (error) {
    console.error('获取作业列表失败:', error);
    // 加载失败
    pagingRef.value?.complete(false);
  }
};

// 下拉刷新
const onRefresh = () => {
  // z-paging 会自动调用 queryList 重新获取第一页数据
};

// 从其他页面返回时恢复
const onRestore = () => {
  // 可以在这里处理页面恢复逻辑
};

// 刷新数据
const refreshData = async () => {
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

// 返回上一页
const goBack = () => {
  uni.redirectTo({
    url: '/pages-subpackages/students/index/index',
  });
};

// 跳转到自主作业
const goToSelfHomework = () => {
  // 打开自主作业弹窗
  selfHomeworkPopupRef.value.open({
    // id: '4',
    // editMode: true,
  });
};

// 处理自主作业提交成功
const handleSubmitSuccess = () => {
  // console.log('自主作业提交成功，刷新列表');
  // 刷新页面数据
  refreshData();
};

// 打开特定类型的筛选弹窗
const openFilterPopup = (type: string) => {
  switch (type) {
    case 'semester':
      semesterPopupRef.value.open({
        semester: semesterValue.value,
      });
      break;
    case 'subject':
      const subjectDefaultValues = Array.isArray(subjectValue.value)
        ? subjectValue.value
        : [subjectValue.value];
      console.log('index.vue打开学科筛选，传递defaultValues:', subjectDefaultValues);
      subjectPopupRef.value.open({
        multiSelect: filterConfig.subject.multiSelect,
        showAll: filterConfig.subject.showAll,
        defaultValues: subjectDefaultValues,
      });
      break;
    case 'type':
      typePopupRef.value.open({
        multiSelect: filterConfig.type.multiSelect,
        showAll: filterConfig.type.showAll,
        type: typeValue.value,
      });
      break;
  }
};

// 处理学期确认
const handleSemesterConfirm = (value: (string | number)[]) => {
  // 学期是单选，但组件返回数组，取第一个值
  semesterValue.value = value.length > 0 ? value[0] : '';
  refreshData();
};

// 处理学科确认
const handleSubjectConfirm = (value: (string | number)[]) => {
  // 学科是多选，组件返回数组
  subjectValue.value = value;
  refreshData();
};

// 处理类型确认
const handleTypeConfirm = (value: (string | number)[]) => {
  // 类型是多选，组件返回数组
  typeValue.value = value;
  refreshData();
};

// 获取筛选选项的当前选中标签
const getSelectedLabel = (type: 'semester' | 'subject' | 'type', value: any) => {
  if (type === 'semester') {
    return semesterPopupRef.value ? semesterPopupRef.value.getLabelByValue(value) : '全部学期';
  }
  if (type === 'subject') {
    return subjectPopupRef.value ? subjectPopupRef.value.getLabelByValue(value) : '全部学科';
  }
  return typePopupRef.value ? typePopupRef.value.getLabelByValue(value) : '全部类型';
};

// 标签栏点击
const onTabChange = (index: number | string, item: TabItem) => {
  console.log('标签切换:', index, item);
  refreshData();
};

// 搜索
const search = () => {
  refreshData();
};

// 跳转到详情页
const goToDetail = (item: HomeworkPageItem) => {
  switch (item.status) {
    case HomeworkStatus.TO_BE_SUBMITTED:
      // 待提交作业
      if (item.isSelfHomework) {
        // 自主作业
        // 待完成...
        return;
      }
      uni.navigateTo({
        url: `/pages-subpackages/students/homework/detail?id=${item.homeworkId}`,
      });
      break;
    case HomeworkStatus.NOT_SUBMITTED:
      // 未提交作业
      if (item.isSelfHomework) {
        // 自主作业
        return;
      }
      uni.navigateTo({
        url: `/pages-subpackages/students/homework/detail?id=${item.homeworkId}`,
      });
      break;
    case HomeworkStatus.TO_BE_CORRECTED:
      if (item.isSelfHomework) {
        // 自主作业
        showDialog({
          content: '待批改，请稍等片刻',
        });
        console.log('自主作业待批改', item);
        return;
      }
      uni.navigateTo({
        url: `/pages-subpackages/students/homework/detail?id=${item.homeworkId}`,
      });
      break;
    case HomeworkStatus.TO_BE_REVISED:
      if (item.isSelfHomework) {
        return;
      }
      uni.navigateTo({
        url: `/pages-subpackages/students/homework/detail?id=${item.homeworkId}`,
      });
      break;
    case HomeworkStatus.CORRECTING:
      if (item.isSelfHomework) {
        // 自主作业
        showDialog({
          content: '批改中，请稍等片刻',
        });
        return;
      }
      // 批改中作业,作业提交成功提示页
      uni.navigateTo({
        url: `/pages-subpackages/students/homework/submit-success?id=${item.homeworkId}&title=${item.name}&subTitle=${item.subjectName}`,
      });
      break;
    case HomeworkStatus.COMPLETED:
      let type = '';
      let id = item.homeworkId;
      if (item.isSelfHomework == 1) {
        // 自主作业
        type = 'self';
      } else if (item.taskType == HomeworkTaskType.WRITING) {
        type = 'writing';
      } else {
        type = 'single';
      }

      if (type == 'writing') {
        let submitId = '';
        if (item.submitList.length > 0) {
          submitId = item.submitList[0].id;
        }
        uni.navigateTo({
          url: `/pages-subpackages/students/homework/score?id=${id}&stuHomeworkId=${item.id}&type=${type}&title=${item.name}&subTitle=${item.subjectName}&submitId=${submitId}`,
        });
      } else {
        uni.navigateTo({
          url: `/pages-subpackages/students/homework/score?id=${id}&stuHomeworkId=${item.id}&type=${type}&title=${item.name}&subTitle=${item.subjectName}`,
        });
      }

      break;
    case HomeworkStatus.CORRECTION_FAILED:
      // 批改失败
      if (item.isSelfHomework) {
        // 自主作业 弹框可编辑修改重新提交自主作业
        selfHomeworkPopupRef.value.open({
          id: item.id,
          editMode: true,
        });

        return;
      }
      break;
  }
};

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = '';
  refreshData();
};
</script>

<style lang="scss" scoped>
.navbar-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #000;
}

/* 容器样式 */
.homework-container {
  height: 100vh;
  background-color: #f7f7fd;
  width: 100%;
  overflow: hidden;
  /* 禁止横向滚动 */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 自定义右侧按钮 */
.navbar-right-btn {
  font-size: 28rpx;
  color: #7d4dff;
  padding: 10rpx 0;
}

/* 筛选区域 */
.filter-area {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  border-bottom: 1rpx solid #f0f0f0;
  justify-content: space-between;
  align-items: center;
  gap: 15rpx;
}

/* 搜索框 */
.search-box {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  // margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.page-list-wrapper {
  margin-top: 10rpx;
  flex: 1;
  overflow: hidden;
  padding: 0rpx 20rpx 20rpx 20rpx;
}

/* z-paging 样式调整 */
:deep(.z-paging-content) {
  height: 100%;
}

:deep(.z-paging-container) {
  height: 100%;
}

/* uni-row样式调整 */
:deep(.uni-row) {
  margin: 10rpx 0;
  padding: 10rpx;
}

:deep(.uni-col) {
  // padding-bottom: 20rpx;
}

.tabs-container {
  // padding: 0 30rpx;
}
</style>
