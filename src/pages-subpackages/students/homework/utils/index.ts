import {
  BACKEND_TO_FRONTEND_TYPE_MAP,
  HomeworkStatus,
  HomeworkType,
  NEED_ESTIMATE_TIME_STATUS,
  NEED_ESTIMATE_TIME_TYPE,
  STATUS_LABEL,
  FRONTEND_TYPE_LABEL,
  QUESTION_TYPE_LABELS,
  QuestionType,
  SubjectType,
  GradeType,
} from '@/constants/students/homework';
import { FileType, FILE_TYPE_PATTERNS } from '@/constants/students/homework';

/**
 * 判断当前作业状态是否需要显示预计批改时长
 * @param status 作业状态
 * @param type 作业类型
 * @returns 是否需要显示预计批改时长
 */
export function needShowEstimateTime(status: HomeworkStatus, type: HomeworkType): boolean {
  return NEED_ESTIMATE_TIME_STATUS.includes(status) && NEED_ESTIMATE_TIME_TYPE.includes(type);
}

/**
 * 获取作业状态对应的描述信息
 * @param status 作业状态
 * @returns 状态描述
 */
export function getStatusDescription(status: HomeworkStatus): string {
  const descriptions = {
    [HomeworkStatus.TO_BE_SUBMITTED]: '接收到教师端已发布的作业，在截止时间内未提交作业',
    [HomeworkStatus.TO_BE_CORRECTED]: '已完成作业并提交，待教师端或AI批改',
    [HomeworkStatus.CORRECTING]: '提交后教师正在批改或AI正在批改',
    [HomeworkStatus.COMPLETED]: '教师端或AI已完成批改',
    [HomeworkStatus.TO_BE_REVISED]: '提交的作业存在错误，教师端发起了订正作业，待提交订正作业',
    [HomeworkStatus.NOT_SUBMITTED]: '在作业截止时间内未提交，或订正作业截止时间未提交',
    [HomeworkStatus.CORRECTION_FAILED]: '自主作业进入AI批改环节，AI批改返回失败',
  };

  return descriptions[status as keyof typeof descriptions] || '';
}

/**
 * 判断订正作业状态
 * 判断当前作业完成后的状态
 * @param needCorrection 是否需要批改
 * @returns 订正后状态
 */
export function getRevisionNextStatus(needCorrection: boolean): HomeworkStatus {
  return needCorrection ? HomeworkStatus.TO_BE_CORRECTED : HomeworkStatus.COMPLETED;
}

/**
 * 获取前端作业类型
 * @param backendType 后端作业类型
 * @returns 前端作业类型
 */
export function getFrontendType(backendType: string): HomeworkType {
  return BACKEND_TO_FRONTEND_TYPE_MAP[backendType] || HomeworkType.CLASS;
}

/**
 * 获取作业状态显示名称
 * @param status 作业状态
 * @returns 作业状态显示名称
 */
export function getStatusLabel(status: HomeworkStatus) {
  return STATUS_LABEL[status as keyof typeof STATUS_LABEL];
}

/**
 * 获取作业类型名称
 * @param type 后端作业类型
 * @returns 作业类型名称
 */
export function getHomeworkTypeLabel(type: string) {
  const frontendType = getFrontendType(type);
  return FRONTEND_TYPE_LABEL[frontendType] || frontendType;
}

/**
 * 对后端返回的作业对象进行重构
 * @param item 后端作业对象
 * @returns 重构后的作业对象
 */
export function convertToFrontendItem(item: any) {
  return {
    ...item,
    selfHomeworkFiles: [...item.selfHomeworkFiles],
    frontendType: getFrontendType(item.type),
    frontendLabel: getHomeworkTypeLabel(item.type),
    frontendStatusLabel: getStatusLabel(item.status),
  };
}

// 题项字段兼容工具
export function getOptionText(option: any): string {
  return option.content || option.label || option.text || option.title || '';
}

// 根据题型枚举获取对应的中文文本
export function getQuestionTypeLabel(type: string): string {
  return QUESTION_TYPE_LABELS[type] || type;
}

// 检查blank是否有parts属性的类型守卫
export function hasPartsProperty(blank: any) {
  return blank && Array.isArray(blank.parts);
}

/**
 * 根据题目ID查找题目在数组中的索引
 * @param id 题目ID
 * @param questions 题目数组
 * @returns 题目索引，如果未找到则返回-1
 */
export function findQuestionIndexById(id: string, questions: any[]): number {
  // 先尝试直接查找题目ID
  const directIndex = questions.findIndex(q => q.id === id);
  if (directIndex !== -1) {
    return directIndex;
  }

  // 如果直接查找失败，尝试在子题目中查找
  for (let i = 0; i < questions.length; i++) {
    const question = questions[i];
    if (question.subQuestions && Array.isArray(question.subQuestions)) {
      const subQuestion = question.subQuestions.find((sq: any) => sq.id === id);
      if (subQuestion) {
        // 找到了子题目，返回父题目的索引
        return i;
      }
    }
  }

  // 未找到题目
  return -1;
}

/**
 * 将数字转换为汉字
 * @param num 数字
 * @returns 汉字
 */
export function numberToChinese(num: number | string): string {
  const chinese = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  return num
    .toString()
    .split('')
    .map(n => chinese[parseInt(n)])
    .join('');
}

/**
 * 判断某题是否已作答（答题卡/进度通用）
 * @param question 题目对象
 * @param answer 答案对象
 * @param allAnswers 所有答案对象（用于子题判断）
 * @returns 是否已作答
 */
export function isQuestionAnswered(question: any, answer: any, allAnswers?: any[]): boolean {
  if (!answer) return false;

  // 检查是否有子题，如果有子题则递归判断
  if (
    question.subQuestions &&
    Array.isArray(question.subQuestions) &&
    question.subQuestions.length > 0
  ) {
    // 如果有子题，要求全部子题都作答才算作已作答
    return question.subQuestions.every((subQuestion: any) => {
      // 首先尝试从所有答案中查找子题的答案
      let subAnswer = allAnswers?.find((ans: any) => ans.questionId === subQuestion.id);

      // 如果没找到，尝试从当前答案的 answer 对象中查找子题答案
      if (!subAnswer && answer?.answer && typeof answer.answer === 'object') {
        const subAnswerData = answer.answer[subQuestion.id];
        if (subAnswerData) {
          subAnswer = { questionId: subQuestion.id, answer: subAnswerData };
        }
      }

      return isQuestionAnswered(subQuestion, subAnswer, allAnswers);
    });
  }

  // 填空题
  if (question.type === QuestionType.FILL_BLANK || question.type === QuestionType.CLOZE) {
    const answerData = answer.answer;
    if (!answerData) return false;

    if (Array.isArray(answerData)) {
      // 检查答案数量是否与 blank 数量一致
      const isConsistent = checkBlankAnswerConsistency(question, answer);
      if (!isConsistent) {
        // console.log('填空题答案数量与 blank 数量不一致', {
        //   blankCount: getBlankCount(question.blanks || []),
        //   answerCount: answerData.length,
        //   questionId: question.id,
        // });
        return false;
      }

      // 每个空都不能为空，才是已作答
      const isAnswered = answerData.every(item => !isEmptyValue(item));
      return isAnswered;
    }
    return !isEmptyValue(answerData);
  }

  // 多选题：只要回答了一个选项就算已作答
  if (question.type === QuestionType.MULTIPLE) {
    const answerData = answer.answer;
    if (!answerData || !Array.isArray(answerData)) return false;

    // 过滤掉空值，获取实际选择的选项数量
    const selectedCount = answerData.filter(item => item && item.trim() !== '').length;

    // 只要选择了至少一个选项，就算作已作答
    return selectedCount > 0;
  }

  // 单选题
  if (question.type === QuestionType.SINGLE) {
    const answerData = answer.answer;
    return !isEmptyValue(answerData);
  }

  // 判断题
  if (question.type === QuestionType.TRUE_FALSE) {
    const answerData = answer.answer;
    return !isEmptyValue(answerData);
  }

  // 简答题
  if (question.type === QuestionType.SHORT_ANSWER) {
    const answerData = answer.answer;
    if (!answerData) return false;

    // 检查是否存在绘图数据或者附件
    if (answerData.drawing?.data || (answerData.files && answerData.files.length > 0)) {
      return true;
    }

    return false;
  }

  // 其他题型：检查是否有答案内容
  const answerData = answer.answer;
  if (typeof answerData === 'string') {
    return answerData.trim() !== '';
  }
  if (Array.isArray(answerData)) {
    return answerData.some(item => item && String(item).trim() !== '');
  }
  if (typeof answerData === 'object' && answerData !== null) {
    return Object.keys(answerData).length > 0;
  }

  return false;
}

/**
 * 判断文件类型
 * @param fileType 文件类型字符串
 * @returns FileType 枚举值
 */
export function getFileType(fileType: string): FileType {
  for (const type in FILE_TYPE_PATTERNS) {
    const patterns = FILE_TYPE_PATTERNS[type as keyof typeof FILE_TYPE_PATTERNS];
    if (patterns.some(pattern => fileType.toLowerCase().includes(pattern))) {
      return type as FileType;
    }
  }
  return FileType.OTHER;
}

/**
 * 判断文件是否为特定类型
 * @param fileType 文件类型字符串
 * @param type 要检查的类型
 * @returns boolean
 */
export function checkFileType(
  fileType: string,
  type: 'image' | 'document' | 'pdf' | 'audio'
): boolean {
  const fileTypeEnum = getFileType(fileType);
  switch (type) {
    case 'image':
      return fileTypeEnum === FileType.IMAGE;
    case 'document':
      return fileTypeEnum === FileType.DOCUMENT;
    case 'pdf':
      return fileTypeEnum === FileType.PDF;
    case 'audio':
      return fileTypeEnum === FileType.AUDIO;
    default:
      return false;
  }
}

/**
 * 判断是否为空值，包括对象，数组，字符串
 */
export function isEmptyValue(value: any): boolean {
  if (value === null || value === undefined) return true;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'string') return value.trim() === '';
  return Object.keys(value).length === 0;
}

/**
 * 提取 blanks 里面 parts 中 type 为 'blank' 的对象数量
 * @param blanks 填空题的 blanks 数组
 * @returns blank 对象的总数量
 */
export function getBlankCount(blanks: any[]): number {
  if (!Array.isArray(blanks)) {
    return 0;
  }

  let totalBlankCount = 0;

  blanks.forEach(blank => {
    if (blank && Array.isArray(blank.parts)) {
      const blankParts = blank.parts.filter((part: any) => part && part.type === 'blank');
      totalBlankCount += blankParts.length;
    }
  });

  return totalBlankCount;
}

/**
 * 检查填空题答案数量是否与 blank 数量一致
 * @param question 题目对象
 * @param answer 答案对象
 * @returns 是否一致
 *
 * @example
 * // 题目数据结构示例
 * const question = {
 *   id: 'question-1',
 *   type: 'fill_blank',
 *   blanks: [
 *     {
 *       id: '0',
 *       parts: [
 *         { type: 'text', value: '这是一个' },
 *         { type: 'blank', index: 0 },
 *         { type: 'text', value: '的句子，还有另一个' },
 *         { type: 'blank', index: 1 }
 *       ]
 *     }
 *   ]
 * };
 *
 * const answer = {
 *   questionId: 'question-1',
 *   answer: ['填空', '空白']  // 用户回答数组
 * };
 *
 * // 检查一致性
 * const isConsistent = checkBlankAnswerConsistency(question, answer);
 * console.log(isConsistent); // true，因为有2个blank，用户回答了2个答案
 */
export function checkBlankAnswerConsistency(question: any, answer: any): boolean {
  // 检查基本参数
  if (!question || !question.blanks || !Array.isArray(question.blanks)) {
    return false;
  }

  if (!answer || !answer.answer) {
    return false;
  }

  // 提取 blanks 中 type 为 'blank' 的对象数量
  const blankCount = getBlankCount(question.blanks);

  // 获取用户答案数组
  const userAnswers = Array.isArray(answer.answer) ? answer.answer : [];

  // 检查数量是否一致
  return blankCount === userAnswers.length;
}

/**
 * 年级类型和学科分类，决定AI助手类型
 */
export const getAiAssistantType = (gradeType: any, subjectType: any) => {
  let type = 0;

  // 小学低年级 + 文科 = 14
  if (gradeType == GradeType.PRIMARY_LOW && subjectType == SubjectType.ARTS) {
    type = 14;
  }
  // 小学低年级 + 理科 = 15
  else if (gradeType == GradeType.PRIMARY_LOW && subjectType == SubjectType.SCIENCE) {
    type = 15;
  }
  // 小学高年级 + 文科 = 16
  else if (gradeType == GradeType.PRIMARY_HIGH && subjectType == SubjectType.ARTS) {
    type = 16;
  }
  // 小学高年级 + 理科 = 17
  else if (gradeType == GradeType.PRIMARY_HIGH && subjectType == SubjectType.SCIENCE) {
    type = 17;
  }
  // 初中 + 文科 = 18
  else if (gradeType == GradeType.MIDDLE && subjectType == SubjectType.ARTS) {
    type = 18;
  }
  // 初中 + 理科 = 19
  else if (gradeType == GradeType.MIDDLE && subjectType == SubjectType.SCIENCE) {
    type = 19;
  }
  // 高中 + 文科 = 20
  else if (gradeType == GradeType.SENIOR_HIGH && subjectType == SubjectType.ARTS) {
    type = 20;
  }
  // 高中 + 理科 = 21
  else if (gradeType == GradeType.SENIOR_HIGH && subjectType == SubjectType.SCIENCE) {
    type = 21;
  }
  return type;
};
