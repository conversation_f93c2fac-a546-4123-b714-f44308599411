/**
 * 专门处理后端与前端结构转换
 */
import { AnswerType, HomeworkTaskType, QuestionType } from '@/constants/students/homework';
import type {
  FrontQuestion,
  FrontQuestionOption,
  FrontUserAnswersMap,
} from '@/types/students/homework';
import type { BatchInfo } from '@/store/homeworkStore';
import { numberToChinese } from '.';
import { useHomeworkStore } from '@/store/homeworkStore';

/**
 * 序列化答案，后端需要的是字符串，前端需要的是数组
 */
function serializeAnswer(answer: any) {
  if (typeof answer === 'string') {
    return answer;
  }
  return JSON.stringify(answer);
}

/**
 * 反序列化答案，后端需要的是数组，前端需要的是字符串
 */
function deserializeAnswer(answer: any) {
  if (typeof answer === 'string') {
    return JSON.parse(answer);
  }
  return answer;
}

/**
 * 通用题目列表转换函数
 * @param questionList 题目列表
 * @param parentIndex 父题索引（可选）
 * @param isChild 是否为子题
 * @returns 转换后的题目数组
 */
function convertQuestionList(
  questionList: any[],
  parentIndex?: string,
  isChild: boolean = false
): FrontQuestion[] {
  // //console.log('=== convertQuestionList 开始 ===');
  // //console.log('questionList length:', questionList?.length);
  // //console.log('parentIndex:', parentIndex);
  // //console.log('isChild:', isChild);

  if (!questionList || !Array.isArray(questionList)) {
    //console.log('questionList 为空或不是数组，返回空数组');
    return [];
  }

  let resultQuestions = questionList.map((item: any, index: number) => {
    // //console.log('=== 开始处理题目 ===');
    // //console.log('isChild:', isChild);
    // //console.log('parentIndex:', parentIndex);
    // //console.log('index:', index);
    // //console.log('item', item);
    // 生成前端题目ID
    const frontId = parentIndex ? `question-${parentIndex}-${index + 1}` : `question-${index + 1}`;

    // 判断是否有子题，决定取数据的方式（兼容后端两种结构：外层 children 与 question.children）
    let questionData: any;
    let hasChildren = false;
    let childrenArray: any[] = [];

    const isWrapper = !!item?.question; // 是否为包含 question 字段的包裹结构
    if (isWrapper) {
      // 常规结构：item = { question, children }
      questionData = item.question;
      childrenArray = Array.isArray(questionData?.children)
        ? questionData.children
        : Array.isArray(item.children)
          ? item.children
          : [];
    } else {
      // 嵌套结构：item 本身就是 question（来自 question.children）
      questionData = item;
      childrenArray = Array.isArray(questionData?.children) ? questionData.children : [];
    }
    hasChildren = Array.isArray(childrenArray) && childrenArray.length > 0;

    if (!questionData) {
      console.error(`❌ 第${index + 1}题数据为空，跳过处理:`, {
        题目索引: index,
        题目序号: item.questionOrder || '未知',
        是否包裹结构: isWrapper,
        问题: '无法提取questionData',
        原始数据结构分析: {
          有question字段: !!item?.question,
          有stem字段: !!item?.stem,
          有answerType字段: !!item?.answerType,
          有id字段: !!item?.id,
          所有字段: Object.keys(item || {}),
        },
        完整原始数据: item,
      });
      return null;
    }
    //console.log('questionData', questionData);
    //console.log('hasChildren', hasChildren);

    // 通用属性
    const baseQuestion: any = {
      id: frontId,
      index: isChild ? index + 1 : item.questionOrder || index + 1,
      title: questionData.stem || '',
      score: isChild ? item.score || 0 : item.score || 0,
      analysis: questionData.analysis || '',
      backendData: item, // 始终保存完整的原始数据
      subQuestions: [],
    };

    // 检查是否有 children 字段，如果有就递归处理子题
    if (hasChildren) {
      const currentParentIndex = isChild
        ? `${parentIndex}-${index + 1}`
        : String(item.questionOrder || index + 1);

      // 递归处理子题
      //console.log('开始递归处理子题，children:', childrenArray);
      const subQuestions = convertQuestionList(childrenArray, currentParentIndex, true);
      //console.log('子题处理结果:', subQuestions);
      baseQuestion.subQuestions = subQuestions;
    }

    // 根据题型转换
    let questionTypeCode = questionData.questionTypeCode;
    console.log(`📝 题目${index + 1}原始题型信息:`, {
      'questionData.questionTypeCode': questionData.questionTypeCode,
      'questionData.answerType': questionData.answerType,
      'AnswerType.CHOICE': AnswerType.CHOICE,
      是否为选择题: questionData.answerType == AnswerType.CHOICE,
      有子题: baseQuestion.subQuestions && baseQuestion.subQuestions.length > 0,
    });

    if (baseQuestion.subQuestions && baseQuestion.subQuestions.length > 0) {
      questionTypeCode = QuestionType.ANY_TYPE;
      console.log(`📝 题目${index + 1}有子题，强制设为万能题型`);
    }

    //console.log('题型代码:', questionTypeCode, 'isChild:', isChild);
    const currentParentIndex = isChild
      ? `${parentIndex}-${index + 1}`
      : String(item.questionOrder || index + 1);

    // 特殊处理写作题
    if (!isChild) {
      const homeworkStore = useHomeworkStore();
      if (homeworkStore.backendData.taskType === HomeworkTaskType.WRITING) {
        questionData.questionTypeName = '写作题';
        return {
          ...baseQuestion,
          type: QuestionType.WRITING,
        };
      }
    }

    // 如果是选择题，根据答案数量智能判断题型
    if (questionData.answerType == AnswerType.CHOICE) {
      console.log(`🔍 选择题${index + 1}分析 - 根据答案数量判断题型:`, {
        answerType: questionData.answerType,
        answer: questionData.answer,
        options: questionData.options,
      });

      // 解析答案数组，判断答案数量
      let answerCount = 0;
      let isJudgeQuestion = false;

      try {
        if (questionData.answer) {
          const answerArr = JSON.parse(questionData.answer);
          if (Array.isArray(answerArr)) {
            answerCount = answerArr.length;
            console.log(`📊 题目${index + 1}答案数量: ${answerCount}, 答案内容:`, answerArr);
          }
        }

        // 检查是否为判断题（通过选项内容判断）（！暂时不要判断题）
        if (questionData.options) {
          // const optionsArray = JSON.parse(questionData.options);
          // if (Array.isArray(optionsArray) && optionsArray.length === 2) {
          //   const optionTexts = optionsArray.map(opt => String(opt).toLowerCase());
          //   isJudgeQuestion = optionTexts.some(text =>
          //     text.includes('正确') || text.includes('错误') ||
          //     text.includes('√') || text.includes('×') ||
          //     text.includes('true') || text.includes('false') ||
          //     text.includes('对') || text.includes('错')
          //   );
          // }
        }
      } catch (error) {
        console.warn(`题目${index + 1}答案解析失败:`, error);
        answerCount = 1; // 默认为单选
      }

      // 根据答案数量和选项内容智能判断题型
      if (isJudgeQuestion) {
        console.log(`✅ 题目${index + 1}识别为判断题（归属单选题处理）`);
        questionData.options = JSON.stringify([
          { id: '0', content: '正确' },
          { id: '1', content: '错误' },
        ]);
        return convertSingleChoiceQuestion(baseQuestion, questionData, currentParentIndex);
      } else if (answerCount === 1) {
        console.log(`✅ 题目${index + 1}识别为单选题（答案数量: ${answerCount}）`);
        return convertSingleChoiceQuestion(baseQuestion, questionData, currentParentIndex);
      } else if (answerCount > 1) {
        console.log(`✅ 题目${index + 1}识别为多选题（答案数量: ${answerCount}）`);
        return convertMultipleChoiceQuestion(baseQuestion, questionData, currentParentIndex);
      } else {
        // 答案数量为0或无法解析时的兜底处理
        console.warn(`⚠️ 题目${index + 1}答案数量为${answerCount}，默认处理为单选题`);
        return convertSingleChoiceQuestion(baseQuestion, questionData, currentParentIndex);
      }
    } else if (questionData.answerType == AnswerType.FILL_BLANK) {
      // 填空题
      return convertFillBlankQuestion(baseQuestion, questionData, currentParentIndex);
    } else if (questionData.answerType == AnswerType.ANSWER) {
      // 解答题
      return convertShortAnswerQuestion(baseQuestion, questionData, currentParentIndex);
    } else {
      // 万能题型处理
      console.warn(
        `未知的答题类型: ${questionData.answerType}, 题目${index + 1}, 使用万能题型处理`
      );
      return convertAnyTypeQuestion(baseQuestion, questionData, currentParentIndex);
    }

    // 如果上面的逻辑都没有返回，说明有问题
    console.error(`题目${index + 1}转换失败，所有分支都没有匹配`, {
      answerType: questionData.answerType,
      questionTypeCode: questionTypeCode,
      questionData: questionData,
    });
    return null;
  });

  // 过滤掉null值，避免后续处理出错
  const originalLength = resultQuestions.length;
  const nullIndexes: number[] = [];

  // 记录哪些位置是null
  resultQuestions.forEach((question, index) => {
    if (question === null || question === undefined) {
      nullIndexes.push(index);
    }
  });

  resultQuestions = resultQuestions.filter(question => question !== null && question !== undefined);
  const filteredLength = resultQuestions.length;

  if (originalLength !== filteredLength) {
    console.warn(`⚠️ 题目转换过程中过滤掉了 ${originalLength - filteredLength} 个无效题目`, {
      原始数量: originalLength,
      有效数量: filteredLength,
      无效数量: originalLength - filteredLength,
      无效题目位置: nullIndexes.map(i => `第${i + 1}题`).join(', '),
      输入数据长度: questionList?.length || 0,
    });

    // 显示原始输入数据中对应位置的题目信息
    console.log('📋 无效题目详细信息:');
    nullIndexes.forEach(index => {
      const originalItem = questionList[index];
      console.log(`第${index + 1}题:`, {
        originalItem,
        questionOrder: originalItem?.questionOrder || '未知',
        hasQuestion: !!originalItem?.question,
        questionId: originalItem?.question?.id || originalItem?.id || '无ID',
        stem: originalItem?.question?.stem || originalItem?.stem || '无题干',
        answerType: originalItem?.question?.answerType || originalItem?.answerType || '未知类型',
      });
    });
    console.log('--------------------------------');
  }

  // 根据questionOrder排序，升序

  resultQuestions.sort((a, b) => a.backendData.questionOrder - b.backendData.questionOrder);
  // debugger
  console.log('题目转换完成:', {
    输入题目数量: questionList?.length || 0,
    输出题目数量: resultQuestions.length,
    是否为子题: isChild,
  });

  return resultQuestions;
}

/**
 * 验证常量是否正确导入
 */
function validateConstants() {
  const issues: string[] = [];

  if (!AnswerType || typeof AnswerType !== 'object') {
    issues.push('AnswerType 常量未正确导入');
  } else {
    if (AnswerType.CHOICE === undefined) issues.push('AnswerType.CHOICE 未定义');
    if (AnswerType.FILL_BLANK === undefined) issues.push('AnswerType.FILL_BLANK 未定义');
    if (AnswerType.ANSWER === undefined) issues.push('AnswerType.ANSWER 未定义');
  }

  if (!QuestionType || typeof QuestionType !== 'object') {
    issues.push('QuestionType 常量未正确导入');
  } else {
    if (QuestionType.SINGLE === undefined) issues.push('QuestionType.SINGLE 未定义');
    if (QuestionType.MULTIPLE === undefined) issues.push('QuestionType.MULTIPLE 未定义');
    if (QuestionType.TRUE_FALSE === undefined) issues.push('QuestionType.TRUE_FALSE 未定义');
    if (QuestionType.FILL_BLANK === undefined) issues.push('QuestionType.FILL_BLANK 未定义');
    if (QuestionType.SHORT_ANSWER === undefined) issues.push('QuestionType.SHORT_ANSWER 未定义');
    if (QuestionType.ANY_TYPE === undefined) issues.push('QuestionType.ANY_TYPE 未定义');
  }

  if (issues.length > 0) {
    console.error('🚨 常量验证失败:', issues);
    console.log('当前 AnswerType:', AnswerType);
    console.log('当前 QuestionType:', QuestionType);
    return false;
  }

  console.log('✅ 常量验证通过');
  return true;
}

/**
 * 分析题目数据结构，识别潜在问题
 */
function analyzeQuestionData(questionList: any[]) {
  console.log('🔍 数据结构分析');

  const problems: any[] = [];

  questionList.forEach((item, index) => {
    const analysis = {
      index: index + 1,
      questionOrder: item?.questionOrder || '未知',
      hasWrapper: !!item?.question,
      hasDirect: !!(item?.stem || item?.answerType),
      problems: [] as string[],
    };

    // 检查数据结构问题
    if (!item?.question && !item?.stem) {
      analysis.problems.push('既没有question字段，也没有直接的stem字段');
    }

    if (!item?.question && !item?.answerType) {
      analysis.problems.push('既没有question字段，也没有直接的answerType字段');
    }

    if (!item?.questionOrder && !item?.question?.id && !item?.id) {
      analysis.problems.push('缺少唯一标识（questionOrder、question.id、id都为空）');
    }

    if (item?.question === null) {
      analysis.problems.push('question字段为null');
    }

    if (item?.question === undefined && Object.keys(item || {}).length === 0) {
      analysis.problems.push('整个item对象为空');
    }

    // 检查题型相关问题
    if (item?.question) {
      if (!item.question.answerType && !item.question.questionTypeCode) {
        analysis.problems.push('question对象缺少answerType和questionTypeCode字段');
      }
      if (!item.question.stem) {
        analysis.problems.push('question对象缺少stem字段（题干）');
      }
    }

    // 检查数据类型问题
    if (item && typeof item !== 'object') {
      analysis.problems.push(`item不是对象类型，而是${typeof item}`);
    }

    if (analysis.problems.length > 0) {
      problems.push({
        ...analysis,
        rawData: item,
      });
    }

    // 显示每题的基本信息
    const status = analysis.problems.length > 0 ? '❌' : '✅';
    console.log(`${status} 题目${index + 1}:`, {
      questionOrder: analysis.questionOrder,
      hasWrapper: analysis.hasWrapper,
      hasDirect: analysis.hasDirect,
      problems: analysis.problems.length > 0 ? analysis.problems : '无问题',
      keys: Object.keys(item || {}),
    });
  });

  if (problems.length > 0) {
    console.log('⚠️ 发现问题的题目详情:');
    problems.forEach(problem => {
      console.error(`第${problem.index}题的问题:`, problem);
    });
  }

  return problems;
}

/**
 * 将后端题目结构转换为前端题目结构
 * @param questionList 后端返回的题目数据列表
 * @returns 前端题目结构
 */
export function convertBackendToFrontQuestion(questionList: any[]): FrontQuestion[] {
  console.log('🔄 开始转换题目数据');

  // 首先验证常量是否正确导入
  const constantsValid = validateConstants();
  if (!constantsValid) {
    console.error('❌ 常量验证失败，转换可能出现问题');
  }

  console.log('📥 输入数据概览:', {
    总数量: questionList?.length || 0,
    数据类型: Array.isArray(questionList) ? 'Array' : typeof questionList,
  });

  // 先分析数据结构，识别问题
  if (questionList && Array.isArray(questionList)) {
    const problems = analyzeQuestionData(questionList);
    if (problems.length > 0) {
      console.warn(`⚠️ 预检查发现 ${problems.length} 个题目可能有问题，将在转换过程中跳过`);
    }
  }

  // 根据questionOrder排序，升序
  questionList.sort((a, b) => a.questionOrder - b.questionOrder);

  const list = convertQuestionList(questionList);

  console.log('📤 转换结果:', {
    输出数量: list.length,
    成功率: `${((list.length / (questionList?.length || 1)) * 100).toFixed(1)}%`,
  });

  // debugger
  return list;
}

/**
 * 转换单选题
 * @param baseQuestion 基本题目信息
 * @param questionData 后端题目数据
 * @returns 前端单选题结构
 */
function convertSingleChoiceQuestion(
  baseQuestion: any,
  questionData: any,
  parentIndex?: string
): FrontQuestion {
  // 解析选项
  const options = parseOptions(questionData.options);

  // 解析标准答案
  let answer = '';
  try {
    if (questionData.answer) {
      const answerArr = JSON.parse(questionData.answer);
      if (Array.isArray(answerArr) && answerArr.length > 0) {
        // 后端答案是选项的索引，需要转换为选项ID
        const answerIndex = parseInt(answerArr[0]);
        if (!isNaN(answerIndex) && answerIndex >= 0 && answerIndex < options.length) {
          answer = options[answerIndex].id;
        }
      }
    }
  } catch (error) {
    console.error('解析单选题标准答案失败:', error);
  }

  return {
    ...baseQuestion,
    type: QuestionType.SINGLE,
    options,
    answer,
  };
}

/**
 * 转换多选题
 * @param baseQuestion 基本题目信息
 * @param questionData 后端题目数据
 * @returns 前端多选题结构
 */
function convertMultipleChoiceQuestion(
  baseQuestion: any,
  questionData: any,
  parentIndex?: string
): FrontQuestion {
  // 解析选项
  const options = parseOptions(questionData.options);

  // 解析标准答案
  let answer: string[] = [];
  let maxSelectCount = 0; // 最大选择数量
  try {
    if (questionData.answer) {
      const answerArr = JSON.parse(questionData.answer);
      if (Array.isArray(answerArr)) {
        // 将后端答案索引转换为选项ID
        answer = answerArr
          .map(index => {
            const answerIndex = parseInt(index);
            if (!isNaN(answerIndex) && answerIndex >= 0 && answerIndex < options.length) {
              return options[answerIndex].id;
            }
            return '';
          })
          .filter(Boolean);

        // 根据后端反序列化后的answer长度判断maxSelectCount
        // 注意：这是后端告诉的逻辑，可能不够规范，仅供参考
        maxSelectCount = answerArr.length;
      }
    }
  } catch (error) {
    console.error('解析多选题标准答案失败:', error);
  }

  return {
    ...baseQuestion,
    type: QuestionType.MULTIPLE,
    options,
    answer,
    maxSelectCount, // 添加最大选择数量
  };
}

/**
 * 转换判断题
 * @param baseQuestion 基本题目信息
 * @param questionData 后端题目数据
 * @returns 前端判断题结构
 */
function convertTrueFalseQuestion(
  baseQuestion: any,
  questionData: any,
  parentIndex?: string
): FrontQuestion {
  // 判断题固定选项
  const options: FrontQuestionOption[] = [
    { id: '√', content: '正确' },
    { id: '×', content: '错误' },
  ];

  // 解析标准答案
  let answer = '';
  try {
    if (questionData.answer) {
      const answerArr = JSON.parse(questionData.answer);
      if (Array.isArray(answerArr) && answerArr.length > 0) {
        // 判断题答案通常是 "√" 或 "×"
        const answerValue = answerArr[0];
        if (answerValue === '√' || answerValue === '正确' || answerValue === true) {
          answer = options[0].id; // 正确
        } else if (answerValue === '×' || answerValue === '错误' || answerValue === false) {
          answer = options[1].id; // 错误
        }
      }
    }
  } catch (error) {
    console.error('解析判断题标准答案失败:', error);
  }

  return {
    ...baseQuestion,
    type: QuestionType.TRUE_FALSE,
    options,
    answer,
  };
}

/**
 * 转换简答题（支持递归子题）
 * @param baseQuestion 基本题目信息
 * @param questionData 后端题目数据
 * @param parentIndex 父题的index（用于生成唯一id，可选）
 * @returns 前端简答题结构
 */
function convertShortAnswerQuestion(
  baseQuestion: any,
  questionData: any,
  parentIndex?: string
): FrontQuestion {
  // 解析标准答案
  let answer = {
    drawing: {},
    files: [],
  };

  return {
    ...baseQuestion,
    type: QuestionType.SHORT_ANSWER,
    answer,
  };
}

/**
 * 转换填空题
 * @param baseQuestion 基本题目信息
 * @param questionData 后端题目数据
 * @param parentIndex 父题的index（用于生成唯一id，可选）
 * @returns 前端填空题结构
 */
function convertFillBlankQuestion(
  baseQuestion: any,
  questionData: any,
  parentIndex?: string
): FrontQuestion {
  // 构建 blanks 的内置函数
  function buildBlanksFromStem(stem: string, baseId: string): any[] {
    if (!stem) return [];

    const blanks: any[] = [];
    let blankIndex = 0;

    // 使用正则表达式匹配 <!--BA-->...<!--EA--> 标记
    const blankPattern = /<!--BA-->(.*?)<!--EA-->/g;

    // 先检查是否包含分段标记（1）、（2）等
    const segmentPattern = /（(\d+)）/g;
    const segmentMatches = Array.from(stem.matchAll(segmentPattern));

    if (segmentMatches.length > 0) {
      // 有分段标记，按分段处理
      let lastIndex = 0;

      segmentMatches.forEach((segmentMatch, segmentIndex) => {
        const segmentNumber = segmentMatch[1];
        const segmentStart = segmentMatch.index!;
        const segmentEnd = segmentStart + segmentMatch[0].length;

        // 获取当前分段的内容（从分段标记后到下一个分段标记前）
        let contentEnd = stem.length;
        if (segmentIndex + 1 < segmentMatches.length) {
          contentEnd = segmentMatches[segmentIndex + 1].index!;
        }

        const content = stem.substring(segmentEnd, contentEnd).trim();

        if (content) {
          const parts: any[] = [];
          let partIndex = 0;
          let contentLastIndex = 0;

          // 在内容中查找填空标记
          const contentMatches = Array.from(content.matchAll(blankPattern));

          if (contentMatches.length > 0) {
            // 有填空标记，解析为 parts
            contentMatches.forEach(match => {
              const matchStart = match.index!;
              const matchEnd = matchStart + match[0].length;

              // 添加填空前的文本
              if (matchStart > contentLastIndex) {
                const textBefore = content
                  .substring(contentLastIndex, matchStart)
                  .replace(/<br?\/?>/g, ' ');
                if (textBefore.trim()) {
                  parts.push({ type: 'text', value: textBefore });
                }
              }

              // 添加填空
              parts.push({ type: 'blank', index: partIndex });
              partIndex++;

              contentLastIndex = matchEnd;
            });

            // 添加最后一个填空后的文本
            if (contentLastIndex < content.length) {
              const textAfter = content.substring(contentLastIndex);
              if (textAfter.trim()) {
                parts.push({ type: 'text', value: textAfter });
              }
            }
          } else {
            // 没有填空标记，整个内容作为文本
            parts.push({ type: 'text', value: content });
          }

          // const blankId = `${baseId}-${segmentNumber}`;
          blanks.push({
            // id: blankId,
            parts: parts,
          });
        }
      });
    } else {
      // 没有分段标记，按单个 blank 处理
      const parts: any[] = [];
      let lastIndex = 0;
      let partIndex = 0;

      // 解析填空标记
      const matches = Array.from(stem.matchAll(blankPattern));

      if (matches.length > 0) {
        matches.forEach(match => {
          const matchStart = match.index!;
          const matchEnd = matchStart + match[0].length;

          // 添加填空前的文本
          if (matchStart > lastIndex) {
            const textBefore = stem.substring(lastIndex, matchStart);
            if (textBefore.trim()) {
              parts.push({ type: 'text', value: textBefore });
            }
          }

          // 添加填空
          parts.push({ type: 'blank', index: partIndex });
          partIndex++;

          lastIndex = matchEnd;
        });

        // 添加最后一个填空后的文本
        if (lastIndex < stem.length) {
          const textAfter = stem.substring(lastIndex);
          if (textAfter.trim()) {
            parts.push({ type: 'text', value: textAfter });
          }
        }

        const blankId = `${baseId}-${blankIndex + 1}`;
        blanks.push({
          id: blankId,
          parts: parts,
        });
      }
    }

    return blanks;
  }

  // 使用内置函数构建 blanks
  const blanks = buildBlanksFromStem(questionData.stem, baseQuestion.id);

  let answer: any[] = [];
  try {
    // 填空题暂时不解析答案，目前根据智能体解析答案
    // answer = deserializeAnswer(questionData.answer);
  } catch (error) {
    console.error('解析填空题标准答案失败:', error);
  }
  // debugger

  // title 取 stem ‘(1)’之前内内容，没有的话就返回整个stem
  let title = questionData.stem;
  if (title.includes('（')) {
    title = title.split('（')[0].trim();
  }

  return {
    ...baseQuestion,
    type: QuestionType.FILL_BLANK,
    title,
    answer,
    blanks,
  };
}

/**
 * 转换万能题型（支持递归子题）
 * @param baseQuestion 基本题目信息
 * @param questionData 后端题目数据
 * @param parentIndex 父题的index（用于生成唯一id，可选）
 * @param subQuestions 子题数组（递归生成，默认空数组）
 * @returns 前端万能题型结构
 */
function convertAnyTypeQuestion(
  baseQuestion: any,
  questionData: any,
  parentIndex?: string
): FrontQuestion {
  // 万能题型没有固定的答案格式，直接使用原始答案
  let answer = questionData.answer || '';

  return {
    ...baseQuestion,
    type: QuestionType.ANY_TYPE,
    answer,
  };
}

/**
 * 解析选项字符串为选项数组
 * @param optionsStr 选项字符串
 * @returns 选项数组
 */
function parseOptions(optionsStr: string): FrontQuestionOption[] {
  if (!optionsStr) return [];

  try {
    // 尝试解析JSON字符串
    const optionsArray = JSON.parse(optionsStr);
    if (!Array.isArray(optionsArray)) return [];

    // 转换为前端选项格式
    return optionsArray.map((option: string, index: number) => ({
      // id: `option-${index + 1}`,
      id: option,
      content: option,
    }));
  } catch (error) {
    console.error('解析选项失败:', error);
    return [];
  }
}

interface RequestItem {
  /** 题目ID 主键不重复 */
  id: string;
  /** 题目ID 会重复 */
  questionId: string;
  /** 学生答案 */
  studentAnswer: string | string[];
  /** 附件 */
  files?: any[];
  /** 画板文件key 用于提交画板数据 */
  fileKey?: any;
  /** 子题 递归处理 */
  children?: RequestItem[];
}
/**
 * 用户答案转后端提交格式（用于作业提交/保存等场景）
 * @param questions 题目列表
 * @param userAnswers 用户答案map
 * @returns 后端提交格式数组
 */
export function convertUserAnswersToRequestList(
  questions: any[],
  userAnswers: FrontUserAnswersMap
): any[] {
  //console.log('convertUserAnswersToRequestList', questions, userAnswers);
  const userAnswersList = Object.values(userAnswers);
  const requestList = userAnswersList.map(userAnswer => {
    const question = getQuestion(questions, userAnswer.questionId);
    return parseUserAnswer(question, userAnswer);
  });

  function parseUserAnswer(question: any, userAnswer: any) {
    // if (!question) return;

    const requestItem: RequestItem = {
      id: question?.backendData?.question?.submitQuestion?.id || null,
      questionId: question?.backendData?.questionId || question?.backendData?.id,
      studentAnswer: '',
    };

    // 如果userAnswer.answer是对象， 过滤出question-开头的数据 那么判断是有子题
    const questionKeys = Object.keys(userAnswer?.answer || {}).filter(key =>
      key.includes('question-')
    );

    if (questionKeys.length > 0 && question.type != QuestionType.FILL_BLANK) {
      const children = questionKeys.map((key: any) => {
        const childQuestion = getQuestion(questions, key);
        const childRequestItem = parseUserAnswer(childQuestion, userAnswer?.answer[key]);
        return childRequestItem;
      });
      requestItem.children = children;
    } else {
      let studentAnswer = '';
      // 核心逻辑：将前端userAnswer.answer转换为后端需要的格式
      const newUserAnswer = userAnswer?.answer || userAnswer;

      if (question.type == QuestionType.MULTIPLE || question.type == QuestionType.SINGLE) {
        // 根据userAnswer.answer的值转换为数组
        // 如果是字符串，那么将字符串的值找到对应的下标，存在一个或者多个选项都放在[]里
        // 如果是数组遍历数组，将数组中的值转换为下标，存在一个或者多个选项都放在[]里
        // 用json.stringify转换为数组
        if (typeof newUserAnswer === 'string') {
          const answerIndex = question.options.findIndex(
            (option: any) => option.id === newUserAnswer
          );
          answerIndex != -1 && (studentAnswer = serializeAnswer([answerIndex.toString()]));
        } else if (Array.isArray(newUserAnswer)) {
          const answerArray: any[] = [];
          newUserAnswer.forEach((answer: any) => {
            const answerIndex = question.options.findIndex((option: any) => option.id === answer);
            answerIndex != -1 && answerArray.push(answerIndex.toString());
          });
          // 序列化数组
          studentAnswer = serializeAnswer(answerArray);
        }
      } else if (question.type == QuestionType.TRUE_FALSE) {
        studentAnswer = serializeAnswer([newUserAnswer]);
      } else if (question.type == QuestionType.FILL_BLANK) {
        // 填空题
        if (Array.isArray(userAnswer)) {
          studentAnswer = serializeAnswer(userAnswer);
        } else if (Array.isArray(userAnswer.answer)) {
          studentAnswer = serializeAnswer(userAnswer.answer);
        }
      } else if (question.type == QuestionType.SHORT_ANSWER) {
        debugger;
        //  如果有画板对象且对象里面有数据，那么需要将画板对象里面的数据转换为字符串
        if (newUserAnswer?.drawing && newUserAnswer?.drawing?.data) {
          studentAnswer = serializeAnswer({
            data: newUserAnswer.drawing?.data,
          });
          requestItem.fileKey = newUserAnswer?.drawing?.imageFile?.fileKey;
        } else if (newUserAnswer?.answer?.drawing) {
          studentAnswer = serializeAnswer({
            data: newUserAnswer.answer.drawing?.data,
          });
          requestItem.fileKey = newUserAnswer?.answer?.drawing?.imageFile?.fileKey;
        }
      }

      // 如果有附件，转换成后端需要的格式
      if (newUserAnswer?.files && newUserAnswer?.files?.length > 0) {
        requestItem.files = newUserAnswer.files.map((attachment: any) => ({
          id: attachment.id,
          fileKey: attachment.fileKey,
        }));
      }

      requestItem.studentAnswer = studentAnswer;
    }
    return requestItem;
  }
  // 循环对象判断是否有chidlren，如果有需要提取出来 父级不要，得到一个扁平化的数据
  const flatRequestList: any[] = [];

  requestList.forEach(item => {
    if (item.children) {
      flatRequestList.push(...item.children);
    } else {
      flatRequestList.push(item);
    }
  });

  //console.log('flatRequestList', flatRequestList);

  return flatRequestList;
}

/**
 * 递归查找题目
 * @param questions 题目列表
 * @param questionId 题目ID
 * @returns 题目
 */
function getQuestion(questions: any[], questionId: string) {
  function findQuestion(questions: any[]): any | undefined {
    for (const q of questions) {
      if (q.id === questionId) return q;
      if (q.subQuestions && Array.isArray(q.subQuestions)) {
        const found = findQuestion(q.subQuestions);
        if (found) return found;
      }
    }
    return undefined;
  }
  return findQuestion(questions);
}
/**
 * 解析后端studentAnswer为前端userAnswers结构
 */
export function parseStudentAnswer(frontQ: any, studentAnswer: any): any {
  if (frontQ.type === QuestionType.MULTIPLE) {
    if (typeof studentAnswer === 'string') {
      try {
        const answerArr = JSON.parse(studentAnswer);
        if (Array.isArray(answerArr)) {
          // 多选题：后端存储的是下标，需要转换为选项ID
          const newAnswerArr = answerArr.map(index => {
            const answerIndex = parseInt(index);
            if (!isNaN(answerIndex) && answerIndex >= 0 && answerIndex < frontQ.options.length) {
              return frontQ.options[answerIndex].id;
            }
            return index;
          });
          return newAnswerArr;
        }
        return [];
      } catch {
        return [];
      }
    }
    return Array.isArray(studentAnswer) ? studentAnswer : [];
  }
  if (frontQ.type === QuestionType.FILL_BLANK || frontQ.type === QuestionType.CLOZE) {
    if (typeof studentAnswer === 'string') {
      try {
        // debugger
        return deserializeAnswer(studentAnswer);
      } catch {
        return [];
      }
    }
    return Array.isArray(studentAnswer) ? studentAnswer : [];
  }
  if (frontQ.type === QuestionType.SINGLE || frontQ.type === QuestionType.TRUE_FALSE) {
    // 单选题/判断题：后端存储的是下标，需要转换为选项ID
    if (typeof studentAnswer === 'string') {
      try {
        const answerArr = deserializeAnswer(studentAnswer);
        if (Array.isArray(answerArr) && answerArr.length > 0) {
          const answerIndex = parseInt(answerArr[0]);
          if (!isNaN(answerIndex) && answerIndex >= 0 && answerIndex < frontQ.options.length) {
            return frontQ.options[answerIndex].id;
          }
        }
        return answerArr[0] || '';
      } catch {
        return studentAnswer;
      }
    }
    if (Array.isArray(studentAnswer) && studentAnswer.length > 0) {
      const answerIndex = parseInt(studentAnswer[0]);
      if (!isNaN(answerIndex) && answerIndex >= 0 && answerIndex < frontQ.options.length) {
        return frontQ.options[answerIndex].id;
      }
      return studentAnswer[0];
    }
    return studentAnswer;
  }

  if (frontQ.type === QuestionType.SHORT_ANSWER) {
    const submitQuestion =
      frontQ.backendData?.question?.submitQuestion || frontQ.backendData?.submitQuestion;
    const files = submitQuestion?.files?.map((file: any) => ({
      ...file.file,
      id: file.id,
    }));
    const correct = submitQuestion?.correctResult === 1;

    // 简答题：根据后端files和studentAnswer结合前端结构
    let answerData: any = {
      files,
      correct,
      drawing: {},
    };

    const studentAnswer = submitQuestion?.studentAnswer;
    // 解析studentAnswer 画板数据存储在studentAnswer中
    if (studentAnswer) {
      answerData.drawing = Object.assign({}, deserializeAnswer(studentAnswer));
      answerData.drawing.imageFile = submitQuestion?.file || {};
      answerData.drawing.imageUrl = submitQuestion?.file?.fileUrl || '';
    }

    return answerData;
  }

  if (frontQ.type === QuestionType.ANY_TYPE) {
    // 万能题型：根据子题类型进行递归解析
    if (Array.isArray(frontQ.subQuestions) && frontQ.subQuestions.length > 0) {
      // 如果有子题，递归解析每个子题
      const subAnswers: any = {};
      frontQ.subQuestions.forEach((subQuestion: any) => {
        // console.log('subQuestion', subQuestion);
        const subStudentAnswer = subQuestion.backendData?.question?.submitQuestion?.studentAnswer;
        subAnswers[subQuestion.id] = parseStudentAnswer(subQuestion, subStudentAnswer);
      });
      // debugger
      return subAnswers;
    } else {
      // 没有子题，直接返回原始答案
      return studentAnswer;
    }
  }

  // 其它题型直接返回
  return studentAnswer;
}

/**
 * 后端submitList转前端BatchInfo[]
 */
export function convertBackendSubmitListToBatches(
  submitList: any[],
  questions: any[]
): BatchInfo[] {
  // 如果 submitList 为空，初始化所有题目的用户回答数据
  if (!submitList || submitList.length === 0) {
    const userAnswers: FrontUserAnswersMap = {};

    function buildDefaultAnswer(question: any): any {
      const hasSub = Array.isArray(question.subQuestions) && question.subQuestions.length > 0;
      if (!hasSub) return getDefaultAnswerByType(question.type);
      const subAnswerMap: Record<string, any> = {};
      question.subQuestions.forEach((subQuestion: any) => {
        subAnswerMap[subQuestion.id] = buildDefaultAnswer(subQuestion);
      });
      return subAnswerMap;
    }

    // 仅初始化顶层题目到 userAnswers；子题放入父题的 answer 中
    function initUserAnswers(questionList: any[]) {
      questionList.forEach(question => {
        userAnswers[question.id] = {
          answer: buildDefaultAnswer(question),
          questionId: question.id,
          type: question.type,
          backendData: question.backendData,
        } as any;
      });
    }

    initUserAnswers(questions);

    // 创建初始批次
    const initialQuestions = questions.map((q: any) => ({
      questionId: q.id,
    }));

    return [
      {
        id: 'batch-0',
        name: '首次',
        userAnswers,
        questions: initialQuestions,
        files: [],
      },
    ];
  }

  // submitList 不为空
  const result = submitList.map((submit, idx) => {
    const userAnswers: FrontUserAnswersMap = {};

    // 当前订正的题目列表
    let newQuestions = submit.questionList.map((q: any) => ({
      questionId:
        questions.find((fq: any) => fq.backendData?.questionId == q.question?.id)?.id ||
        q.question?.id,
    }));

    let files: any[] = [];

    submit.questionList.forEach((q: any) => {
      const question = questions.find((fq: any) => fq.backendData?.questionId == q.question?.id);
      // 替换为当前订正题目信息
      question.backendData = q;

      // 判断如果有子题，那么需要将子题的backendData替换为当前订正题目信息
      if (question.subQuestions && question.subQuestions.length > 0) {
        question.subQuestions.forEach((subQuestion: any, index: number) => {
          subQuestion.backendData = q?.question?.children[index];
        });
      }

      if (question) {
        // 构建嵌套答案结构：子题答案嵌入父题的 answer 对象中
        let answer: any;
        if (question.subQuestions && question.subQuestions.length > 0) {
          // 有子题时，构建嵌套答案结构
          const subAnswerMap: Record<string, any> = {};
          question.subQuestions.forEach((subQuestion: any, index: number) => {
            const subQ = q?.question?.children?.[index];
            if (subQ) {
              subAnswerMap[subQuestion.id] = parseStudentAnswer(
                subQuestion,
                subQ?.submitQuestion?.studentAnswer
              );
            } else {
              subAnswerMap[subQuestion.id] = getDefaultAnswerByType(subQuestion.type);
            }
          });
          answer = subAnswerMap;
        } else {
          // 无子题时，直接解析答案
          answer = parseStudentAnswer(question, q.question?.submitQuestion?.studentAnswer);
        }

        userAnswers[question.id] = {
          answer,
          questionId: question.id,
          type: question.type,
          backendData: question.backendData,
        } as any;
      }
    });

    // 根据questionOrder排序
    newQuestions.sort((a: any, b: any) => a.questionOrder - b.questionOrder);

    // 纸质作业文件列表
    if (submit.paperFiles && submit.paperFiles.length > 0) {
      files = submit.paperFiles.map((file: any) => ({
        ...file.file,
        id: file.id,
      }));
    }

    return {
      id: submit.id || `batch-${idx}`,
      name: submit.isRevise ? `第${numberToChinese(idx)}次订正` : '首次',
      userAnswers,
      questions: newQuestions,
      files,
    } as any;
  });

  return result;
}

/**
 * 根据题目类型获取默认答案
 */
function getDefaultAnswerByType(questionType: string): any {
  switch (questionType) {
    case QuestionType.SINGLE:
    case QuestionType.TRUE_FALSE:
      return '';
    case QuestionType.MULTIPLE:
      return [];
    case QuestionType.FILL_BLANK:
      return [];
    case QuestionType.SHORT_ANSWER:
      return {
        drawing: {},
        files: [],
      };
    case QuestionType.ANY_TYPE:
      return {};
    default:
      return '';
  }
}
