<template>
  <MyDialogProvider>
    <MyContainer>
      <view class="detail-container">
        <!-- 导航栏 -->
        <u-navbar
          title=""
          :autoBack="false"
          bgColor="#7D4DFF"
          leftIconColor="#FFFFFF"
          placeholder
          height="88rpx"
          @leftClick="onBackClick"
          style="z-index: 1"
        >
          <template #center>
            <!-- 选项卡放入导航栏中间 -->
            <view class="navbar-tab-content">
              <view
                class="navbar-tab-item"
                :class="{ active: activeTab === 'answer' }"
                @click="activeTab = 'answer'"
              >
                答题
              </view>
              <view
                class="navbar-tab-item"
                :class="{ active: activeTab === 'resource' }"
                @click="activeTab = 'resource'"
              >
                辅助资料
              </view>
            </view>
          </template>
          <!-- 移除计时器，但保留模板插槽，以免破坏布局结构 -->
          <template #right>
            <!-- 计时器已移至进度位置 -->
          </template>
        </u-navbar>
        <view class="tab-content">
          <!-- 加载状态 -->
          <MyLoading v-if="isLoading" fullscreen type="spinner" />
          <template v-else>
            <!-- 由于小程序/uni-app等跨端环境不支持 <component :is="..."> 动态组件语法，
                这里改为条件渲染，保证兼容性和稳定性 -->
            <AnswerQuestion v-if="activeTab === 'answer'" />
            <ResourceList v-else-if="activeTab === 'resource'" />
          </template>
        </view>
      </view>
      <!-- AI助手 -->
      <view class="ai-assistant" v-if="showAiAssistant" @click="handleAiAssistantClick">
        <image
          class="ai-assistant-image"
          src="/static/homework/ai_assistant.png"
          mode="aspectFit"
        />
      </view>
    </MyContainer>
    <HomeworkGestureLeader v-if="showGestureGuide" @close="handleGestureClose" />
  </MyDialogProvider>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { AnswerQuestion, ResourceList } from './components/AnswerTabs';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import { useHomeworkStore, type BatchInfo } from '@/store/homeworkStore';
import { getQuestionsByType } from './mock/questions';
import { useTimer } from '@/hooks/useTimer';
import { useEventBus } from '@/hooks/useEventBus';
import {
  HomeworkEventNames,
  HomeworkAnswerType,
  HomeworkTaskType,
  QuestionType,
  GradeType,
  SubjectType,
} from '@/constants/students/homework';
import { MyContainer } from './components';
import { MyLoading } from '@/components/MyLoading';
import { MyDialogProvider } from '@/components/MyDialog';
import HomeworkNavBar from './components/HomeworkNavBar/index.vue';
import LkSvg from '@/components/svg/index.vue';
import { batchesDemo } from './mock/batch';
import { findQuestionIndexById, getAiAssistantType } from './utils';
import { getHomeworkDetail } from '@/api/students/homework';
import { convertBackendToFrontQuestion, convertBackendSubmitListToBatches } from './utils/convert';
import { HomeworkStatus } from '@/constants/students/homework';
import HomeworkGestureLeader from './components/HomeworkGestureLeader/index.vue';
import { validFinishGuide, finishGuide } from '@/api/students/homework';
import backendDetailDemo from './mock/backend_detail_demo.json';

type ActiveTabValue = 'answer' | 'resource';

const activeTab = ref<ActiveTabValue>('answer');
const homeworkStore = useHomeworkStore();
const eventBus = useEventBus();
const needLocateQuestionId = ref('');
const queryOptions = ref<any>({});
const autoSaveAnswer = computed(() => {
  return homeworkStore.autoSaveAnswer;
});
const showAiAssistant = computed(() => {
  // 学生练习中：作业待提交、未提交、待订正状态 ，学生在学生端进行在线答题过程中，可以与AI答疑助手交互
  // 学生提交作业后：作业状态为待批改、批改中、待确认，查看作业详情里面，学生在学生端可以与AI答疑助手交互
  // 教师确认批改后：作业状态已完成，查看作业详情里面，学生在学生端可以与AI答疑助手交互
  let show = false;
  // 暂时注释 ai 助理显示逻辑
  // if (
  //   (homeworkStore.status == HomeworkStatus.TO_BE_SUBMITTED ||
  //     homeworkStore.status == HomeworkStatus.TO_BE_REVISED ||
  //     homeworkStore.status == HomeworkStatus.TO_BE_CORRECTED) &&
  //   homeworkStore.backendData.enableAiAssistant == 1
  // ) {
  //   show = true;
  // }
  if (homeworkStore.backendData.enableAiAssistant == 1) {
    show = true;
  }
  return show;
});
// 是否显示手势提示,只有第一次进来才显示
const showGesture = computed(() => {
  const show =
    homeworkStore.backendData.taskType == HomeworkTaskType.ONLINE &&
    homeworkStore.backendData.submitList?.length == 0;
  return show;
});

// 手势引导显示控制：基于 showGesture 并结合后端指引完成状态
const showGestureGuide = ref(false);

const GUIDE_TYPE_DOING = 5; // 做题

// 评估是否显示手势引导
const evaluateGestureGuide = async () => {
  try {
    const finished = await validFinishGuide({ guideType: GUIDE_TYPE_DOING });
    // 如果完成手势引导，则设置状态
    if (finished) {
      homeworkStore.setIsFinishGestureGuide(true);
    }
    showGestureGuide.value = !finished;
  } catch (e) {
    // 接口异常时不阻塞主流程，按需展示
    showGestureGuide.value = true;
  }
};

const handleGestureClose = async () => {
  showGestureGuide.value = false;
  try {
    await finishGuide({ guideType: GUIDE_TYPE_DOING });
    homeworkStore.setIsFinishGestureGuide(true);
  } catch (e) {
    // 忽略上报错误
  }
};

// const defaultDialogOptions = computed(() => {
//   const width = uni.getSystemInfoSync().windowWidth;
//   if (width <= 740) {
//     return { width: '80vw' };
//   } else {
//     return { width: '60vw' };
//   }
// });

// 加载状态
const isLoading = ref(false);
// 获取作业详情
const fetchHomeworkDetail = async (options?: any) => {
  isLoading.value = true;
  try {
    let res = await getHomeworkDetail({
      id: homeworkStore.homeworkId,
    });
    // console.log(res);

    //#region 调试后端数据（后续项目上线删除）
    // res.status = 0;
    // res.taskType = HomeworkTaskType.WRITING;
    // res.deadlineTime = '2026-12-04 17:00:00';
    // res.status = HomeworkStatus.TO_BE_CORRECTED;
    // res.status = HomeworkStatus.COMPLETED;
    // res.enableAnswerVisible = 1; //  开启答案
    // res.enableAnswerVisible = 0; //  关闭答案
    // res.submitList = [];
    // res.questionList = [];
    // res = backendDetailDemo.data;
    // res.submitList[0].questionList[0].question.submitQuestion.files = [
    //   {
    //     "id": "68771",
    //     "file": {
    //       "id": "68771",
    //       "createTime": "",
    //       "updateTime": "",
    //       "isDeleted": null,
    //       "fileName": "1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png",
    //       "fileUrl": "https://huayun-obs-pre.hwzxs.com/d5f2883a8a2bed57fc0607cbcf4db663.png?AccessKeyId=QR9H4C35EOAGSCKMA1DG&Expires=1762219798&filename=1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png&response-content-disposition=inline%3B+filename%3D1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png&Signature=KtQDjapbvmBsnqqviaXjPb1WdWM%3D",
    //       "fileKey": "d5f2883a8a2bed57fc0607cbcf4db663.png",
    //       "fileSize": 195462,
    //       "fileJson": "{\"responseHeaders\":{\"connection\":\"keep-alive\",\"content-length\":\"0\",\"date\":\"2025-08-06 09:29:58\",\"etag\":\"\\\"8b73fa1efa7517e6bd564adea53d28a1\\\"\",\"id-2\":\"36AAAUgAIAABAAAQAAEAABAAAQAAEAABAAAaI=AAAAAAAAAAAAAAAAAAAAAAAAAA\",\"request-id\":\"000001987CFFD61C4984D981A36F4B5E\",\"version-id\":\"G00111987CFFD62B000049840031AE00\"},\"statusCode\":200,\"bucketName\":\"huayun-obs-pre\",\"objectKey\":\"d5f2883a8a2bed57fc0607cbcf4db663.png\",\"etag\":\"\\\"8b73fa1efa7517e6bd564adea53d28a1\\\"\",\"versionId\":\"G00111987CFFD62B000049840031AE00\",\"objectUrl\":\"https://huayun-obs-pre.obs.cn-south-1.myhuaweicloud.com:443/d5f2883a8a2bed57fc0607cbcf4db663.png\",\"objectStorageClass\":null,\"requestId\":\"000001987CFFD61C4984D981A36F4B5E\"}",
    //       "fileType": ".png",
    //       "videoId": ""
    //     }
    //   }
    // ]

    // res.submitList![0].paperFiles = [
    //   {
    //     id: '68771',
    //     file: {
    //       id: '68771',
    //       createTime: '',
    //       updateTime: '',
    //       isDeleted: null,
    //       fileName: '1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png',
    //       fileUrl:
    //         'https://huayun-obs-pre.hwzxs.com/d5f2883a8a2bed57fc0607cbcf4db663.png?AccessKeyId=QR9H4C35EOAGSCKMA1DG&Expires=1762219798&filename=1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png&response-content-disposition=inline%3B+filename%3D1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png&Signature=KtQDjapbvmBsnqqviaXjPb1WdWM%3D',
    //       fileKey: 'd5f2883a8a2bed57fc0607cbcf4db663.png',
    //       fileSize: 195462,
    //       fileJson:
    //         '{"responseHeaders":{"connection":"keep-alive","content-length":"0","date":"2025-08-06 09:29:58","etag":"\\"8b73fa1efa7517e6bd564adea53d28a1\\"","id-2":"36AAAUgAIAABAAAQAAEAABAAAQAAEAABAAAaI=AAAAAAAAAAAAAAAAAAAAAAAAAA","request-id":"000001987CFFD61C4984D981A36F4B5E","version-id":"G00111987CFFD62B000049840031AE00"},"statusCode":200,"bucketName":"huayun-obs-pre","objectKey":"d5f2883a8a2bed57fc0607cbcf4db663.png","etag":"\\"8b73fa1efa7517e6bd564adea53d28a1\\"","versionId":"G00111987CFFD62B000049840031AE00","objectUrl":"https://huayun-obs-pre.obs.cn-south-1.myhuaweicloud.com:443/d5f2883a8a2bed57fc0607cbcf4db663.png","objectStorageClass":null,"requestId":"000001987CFFD61C4984D981A36F4B5E"}',
    //       fileType: '.png',
    //       videoId: '',
    //     },
    //   },
    //   {
    //     id: '68771',
    //     file: {
    //       id: '68771',
    //       createTime: '',
    //       updateTime: '',
    //       isDeleted: null,
    //       fileName: '1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png',
    //       fileUrl:
    //         'https://huayun-obs-pre.hwzxs.com/d5f2883a8a2bed57fc0607cbcf4db663.png?AccessKeyId=QR9H4C35EOAGSCKMA1DG&Expires=1762219798&filename=1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png&response-content-disposition=inline%3B+filename%3D1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png&Signature=KtQDjapbvmBsnqqviaXjPb1WdWM%3D',
    //       fileKey: 'd5f2883a8a2bed57fc0607cbcf4db663.png',
    //       fileSize: 195462,
    //       fileJson:
    //         '{"responseHeaders":{"connection":"keep-alive","content-length":"0","date":"2025-08-06 09:29:58","etag":"\\"8b73fa1efa7517e6bd564adea53d28a1\\"","id-2":"36AAAUgAIAABAAAQAAEAABAAAQAAEAABAAAaI=AAAAAAAAAAAAAAAAAAAAAAAAAA","request-id":"000001987CFFD61C4984D981A36F4B5E","version-id":"G00111987CFFD62B000049840031AE00"},"statusCode":200,"bucketName":"huayun-obs-pre","objectKey":"d5f2883a8a2bed57fc0607cbcf4db663.png","etag":"\\"8b73fa1efa7517e6bd564adea53d28a1\\"","versionId":"G00111987CFFD62B000049840031AE00","objectUrl":"https://huayun-obs-pre.obs.cn-south-1.myhuaweicloud.com:443/d5f2883a8a2bed57fc0607cbcf4db663.png","objectStorageClass":null,"requestId":"000001987CFFD61C4984D981A36F4B5E"}',
    //       fileType: '.png',
    //       videoId: '',
    //     },
    //   },
    //   {
    //     id: '68771',
    //     file: {
    //       id: '68771',
    //       createTime: '',
    //       updateTime: '',
    //       isDeleted: null,
    //       fileName: '1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png',
    //       fileUrl:
    //         'https://huayun-obs-pre.hwzxs.com/d5f2883a8a2bed57fc0607cbcf4db663.png?AccessKeyId=QR9H4C35EOAGSCKMA1DG&Expires=1762219798&filename=1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png&response-content-disposition=inline%3B+filename%3D1754443798169_CA77B119-59F3-4C65-A65B-DA0C3888D4EE.png&Signature=KtQDjapbvmBsnqqviaXjPb1WdWM%3D',
    //       fileKey: 'd5f2883a8a2bed57fc0607cbcf4db663.png',
    //       fileSize: 195462,
    //       fileJson:
    //         '{"responseHeaders":{"connection":"keep-alive","content-length":"0","date":"2025-08-06 09:29:58","etag":"\\"8b73fa1efa7517e6bd564adea53d28a1\\"","id-2":"36AAAUgAIAABAAAQAAEAABAAAQAAEAABAAAaI=AAAAAAAAAAAAAAAAAAAAAAAAAA","request-id":"000001987CFFD61C4984D981A36F4B5E","version-id":"G00111987CFFD62B000049840031AE00"},"statusCode":200,"bucketName":"huayun-obs-pre","objectKey":"d5f2883a8a2bed57fc0607cbcf4db663.png","etag":"\\"8b73fa1efa7517e6bd564adea53d28a1\\"","versionId":"G00111987CFFD62B000049840031AE00","objectUrl":"https://huayun-obs-pre.obs.cn-south-1.myhuaweicloud.com:443/d5f2883a8a2bed57fc0607cbcf4db663.png","objectStorageClass":null,"requestId":"000001987CFFD61C4984D981A36F4B5E"}',
    //       fileType: '.png',
    //       videoId: '',
    //     },
    //   },
    // ];
    //#endregion

    // 设置当前作业后端数据
    homeworkStore.setBackendData(res);
    // 获取问题数据
    let questionList = [];

    // 获取问题数据
    if (res.submitList && res.submitList.length > 0) {
      questionList = res.submitList[0]?.questionList || [];
    } else {
      questionList = res?.questionList || [];
    }
    // 将后端问题数据转换为前端问题数据
    const questions = convertBackendToFrontQuestion(questionList);

    //  将订正数据转换为前端数据
    let batches = convertBackendSubmitListToBatches(res.submitList || [], questions);

    // 初始化作业
    homeworkStore.initHomework({
      homeworkId: homeworkStore.homeworkId,
      answerType: res.taskType,
      questions,
      batches,
    });

    // 定位题目
    locateQuestion();
  } catch (error) {
    console.error('获取作业详情失败:', error);
    // uni.showToast({
    //   title: '获取作业详情失败',
    //   icon: 'error',
    // });
  } finally {
    isLoading.value = false;
  }
};

const locateQuestion = () => {
  let locateQuestionId = queryOptions.value.locateQuestionId;
  // 检查是否有指定的题目ID，有则定位到该题目
  if (locateQuestionId) {
    // 根据后端问题 id 查询前端问题 id
    const frontQuestionId = homeworkStore.findFrontQuestionIdByBackendId(locateQuestionId);
    if (frontQuestionId) {
      locateQuestionId = frontQuestionId;
    }
    // 寻找题目在数组中的索引
    const questionIndex = findQuestionIndexById(locateQuestionId, homeworkStore.questions);
    if (questionIndex !== -1) {
      // 设置当前题目索引
      homeworkStore.setCurrentQuestionIndex(questionIndex);
      // 确保激活"答题"标签页
      activeTab.value = 'answer';
      console.log(`已定位到题目ID: ${locateQuestionId}, 题目索引: ${questionIndex}`);
    } else {
      console.warn(`未找到指定的题目ID: ${locateQuestionId}`);
    }
  }
};
/**
 * 点击AI助手
 */
const handleAiAssistantClick = () => {
  console.log('AI助手点击');
  uni.navigateTo({
    url: `/pages-subpackages/students/chat/index?chatType=${getAiAssistantType(homeworkStore.backendData?.gradeType, homeworkStore.backendData?.subjectType)}`,
  });
};

const onBackClick = () => {
  if (queryOptions.value.backHome) {
    uni.redirectTo({
      url: '/pages-subpackages/students/homework/index',
    });
  } else {
    uni.navigateBack();
  }
};

onLoad(async (options: any) => {
  queryOptions.value = options;
  console.log('queryOptions', queryOptions.value);
  // 从 url 获取作业ID
  const homeworkId = options.id || 'test-id';
  homeworkStore.homeworkId = homeworkId;

  // await fetchMockData();

  await fetchHomeworkDetail();
  // 拉取作业数据后评估是否展示引导
  evaluateGestureGuide();
});

onUnload(async () => {
  if (autoSaveAnswer.value) {
    // 退出前自动保存答题内容
    try {
      if (
        homeworkStore.status == HomeworkStatus.TO_BE_SUBMITTED ||
        homeworkStore.status == HomeworkStatus.NOT_SUBMITTED
      ) {
        console.log('退出前自动保存答题内容...');

        // 使用store中的公共保存逻辑，status=0表示答题更新
        const result = await homeworkStore.saveHomework({ status: 0 });

        if (result.success) {
          console.log('自动保存成功');
        } else {
          console.error('自动保存失败:', result.error);
        }
      }
    } catch (error) {
      console.error('退出前保存失败:', error);
    }
  }
  // 清空store
  homeworkStore.clearAll();
});
</script>
<style lang="scss" scoped>
@import './styles/index.scss';

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
}

.navbar-tab-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 380rpx;
  height: 58rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10rpx;
  padding: 4rpx;
}

.navbar-tab-item {
  flex: 1;
  height: 100%;
  border-radius: 10rpx;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    background: #ffffff;
    color: #7d4dff;
  }
}

.tab-content {
  flex: 1;
  overflow: hidden;
  // border-radius: 20px 20px 0px 0px;
  background: #fff;
  position: relative;
  z-index: 2;
  padding: 20rpx;
}

// AI助手
.ai-assistant {
  position: fixed;
  bottom: 409rpx;
  right: 30rpx;

  z-index: 99;

  display: flex;
  align-items: center;
  justify-content: center;

  .ai-assistant-image {
    width: 120rpx;
    height: 120rpx;
  }
}
</style>
