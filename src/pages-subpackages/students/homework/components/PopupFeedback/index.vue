<template>
  <my-popup ref="popupRef" type="bottom" title="纠错反馈" @close="close">
    <view class="popup-content">
      <view>
        <!-- 反馈类型选择 -->
        <view class="feedback-section">
          <text class="section-title">反馈类型</text>
          <uni-row class="feedback-options" :gutter="20">
            <uni-col :span="8">
              <view
                class="feedback-option-btn"
                :class="{ active: errorDimension === 1 }"
                @click="errorDimension = 1"
              >
                问题有误
              </view>
            </uni-col>
            <uni-col :span="8">
              <view
                class="feedback-option-btn"
                :class="{ active: errorDimension === 2 }"
                @click="errorDimension = 2"
              >
                答案有误
              </view>
            </uni-col>
            <uni-col :span="8">
              <view
                class="feedback-option-btn"
                :class="{ active: errorDimension === 3 }"
                @click="errorDimension = 3"
              >
                解析有误
              </view>
            </uni-col>
            <uni-col :span="8">
              <view
                class="feedback-option-btn"
                :class="{ active: errorDimension === 4 }"
                @click="errorDimension = 4"
              >
                其他
              </view>
            </uni-col>
          </uni-row>
        </view>

        <!-- 详细描述 -->
        <view class="feedback-section">
          <text class="section-title">详细描述</text>
          <view class="description-wrapper">
            <textarea
              class="description-textarea"
              v-model="feedbackDescription"
              placeholder="请描述您遇到的问题，我们会尽快处理"
              :maxlength="textLimit.max"
            />
            <text class="word-count">{{ feedbackDescription.length }}/{{ textLimit.max }}</text>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-button" @click="submitFeedback">
        <text class="submit-text">提交</text>
      </view>
    </view>
  </my-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import MyPopup from '@/components/MyPopup';
import { useHomeworkStore } from '@/store/homeworkStore';
import { addQuestionFeedback } from '@/api/students/homework';
import { isEmptyValue } from '../../utils';

const props = defineProps<{
  questionId: string;
  controller?: any;
}>();

const defaultStore = useHomeworkStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

// 弹出层引用
const popupRef = ref<any>(null);

const question = computed(() => {
  return controller.getQuestion(props.questionId);
});

// 错误维度（单选，1-题目错误，2-答案错误，3-解析错误，4-其他）
const errorDimension = ref(1);
const feedbackDescription = ref('');
const textLimit = ref({
  min: 2,
  max: 500,
});

// 打开弹出层
function open() {
  if (popupRef.value) {
    popupRef.value.open();
  }
}

// 关闭弹出层
function close() {
  // 重置表单
  resetForm();
}

// 重置表单
function resetForm() {
  errorDimension.value = 1;
  feedbackDescription.value = '';
}

// 提交反馈
const submitFeedback = async () => {
  // 验证是否选择了错误维度
  if (![1, 2, 3, 4].includes(errorDimension.value)) {
    uni.showToast({
      title: '请选择错误维度',
      icon: 'none',
    });
    return;
  }
  // 验证是否填写了描述
  if (!feedbackDescription.value.trim()) {
    uni.showToast({
      title: '请填写具体描述',
      icon: 'none',
    });
    return;
  }
  // 字数限制
  if (
    feedbackDescription.value.length < textLimit.value.min ||
    feedbackDescription.value.length > textLimit.value.max
  ) {
    uni.showToast({
      title: `字数限制${textLimit.value.min}-${textLimit.value.max}字`,
      icon: 'none',
    });
    return;
  }

  // 收集反馈数据
  const feedbackData = {
    questionId: question.value.backendData.questionId,
    errorDimension: errorDimension.value,
    errorDescription: feedbackDescription.value,
    // fileKeys: [] // 如有文件上传功能可补充
  };

  // TODO: 发送反馈数据到服务器
  console.log('提交纠错反馈:', feedbackData);

  try {
    await addQuestionFeedback(feedbackData);
    uni.showToast({
      title: '提交成功',
      icon: 'success',
    });
    // 关闭弹窗
    if (popupRef.value) {
      popupRef.value.close();
    }
  } catch (error) {
    console.error(error);
  }
};

// 对外暴露方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.popup-content {
  // padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  display: flex;
  flex-flow: column;
  justify-content: space-between;
}

.feedback-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

/* 错误类型选项 - 按钮样式 */
.feedback-options {
  margin-bottom: 20rpx;
}

.feedback-option-btn {
  padding: 16rpx 10rpx;
  border-radius: 100rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f2f3f5;
  border: 1px solid transparent;
  text-align: center;
  transition: all 0.3s;
  margin-bottom: 20rpx;

  &.active {
    background-color: #f3ecff;
    border-color: #7d4dff;
    color: #7d4dff;
  }
}

/* 具体描述 */
.description-wrapper {
  position: relative;
  // border: 1rpx solid #DCDCDC;
  border-radius: 16rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
}

.description-textarea {
  width: 100%;
  height: 240rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.word-count {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 提交按钮 */
.submit-button {
  background-color: #7d4dff;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;

  .submit-text {
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 500;
  }
}
</style>
