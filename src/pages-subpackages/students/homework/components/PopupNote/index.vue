<template>
  <my-popup ref="popupNoteRef" type="bottom" title="做笔记">
    <view class="popup-content">
      <view>
        <view class="section-title">笔记</view>
        <view class="textarea-wrapper">
          <textarea
            class="note-textarea"
            v-model="noteObj.note"
            placeholder=""
            :maxlength="textLimit.max"
          />
          <text class="word-count">{{ noteObj.note?.length ?? 0 }}/{{ textLimit.max }}</text>
        </view>
      </view>
      <view class="save-button" @click="saveNote">保存</view>
    </view>
  </my-popup>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import MyPopup from '@/components/MyPopup';
import { addStudentsNote, updateStudentsNote, getStudentsNote } from '@/api/students/homework';
import { useHomeworkStore } from '@/store/homeworkStore';
import { isEmptyValue } from '../../utils';

const props = defineProps<{
  questionId: string;
  controller?: any;
}>();

const defaultStore = useHomeworkStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

const question = computed(() => {
  return controller.getQuestion(props.questionId);
});
const noteObj = ref({
  noteId: '',
  note: '',
});
const popupNoteRef = ref<any>(null);
const textLimit = ref({
  min: 2,
  max: 500,
});

// 打开笔记弹出层
function open(data: any) {
  if (popupNoteRef.value) {
    noteObj.value = data;
    popupNoteRef.value.open();
  }
}

// 关闭笔记弹出层
function close() {
  noteObj.value.note = '';
  popupNoteRef.value.close();
}

// 保存笔记
async function saveNote() {
  if (
    noteObj.value.note.length < textLimit.value.min ||
    noteObj.value.note.length > textLimit.value.max
  ) {
    uni.showToast({
      title: `字数限制${textLimit.value.min}-${textLimit.value.max}字`,
      icon: 'none',
    });
    return;
  }
  try {
    if (noteObj.value.noteId) {
      // 更新
      const params = {
        id: noteObj.value.noteId,
        noteContent: noteObj.value.note,
      };
      await updateStudentsNote(params);
      controller.setQuestionNote(props.questionId, {
        note: noteObj.value.note,
        noteId: noteObj.value.noteId,
      });
    } else {
      // 新增
      const params = {
        homeworkId: controller.backendData.homeworkId,
        refId: question.value.backendData.questionId,
        noteContent: noteObj.value.note,
        type: 1,
      };
      await addStudentsNote(params);
      const noteRes = await getStudentsNote({
        refId: question.value.backendData.questionId,
        type: 1,
      });
      controller.setQuestionNote(props.questionId, {
        note: noteObj.value.note,
        noteId: noteRes.id,
      });
    }

    // emit('update:note', props.questionId, localNote.value);

    uni.showToast({ title: '保存成功', icon: 'success' });
    close();
  } catch (e) {
    // uni.showToast({ title: '保存失败', icon: 'none' });
    console.error(e);
  }
}

// 对外暴露方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.popup-content {
  // padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  display: flex;
  flex-flow: column;
  justify-content: space-between;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

/* 文本区域样式 */
.textarea-wrapper {
  position: relative;
  border-radius: 16rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  flex: 1;
  margin-bottom: 30rpx;
}

.note-textarea {
  width: 100%;
  height: 300rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.word-count {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.save-button {
  height: 88rpx;
  background-color: #7d4dff;
  color: #ffffff;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-top: 30rpx;
}
</style>
