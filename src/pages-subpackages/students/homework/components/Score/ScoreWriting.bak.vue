<template>
  <view class="score-writing">
    <!-- 内容区域（弹性布局） -->
    <view class="content-container">
      <!-- 绿色区域（作文显示）- 默认占更大空间 -->
      <view class="writing-area">
        <!-- 移除外层scroll-view，直接使用AnnotationWriting组件 -->
        <view class="writing-card">
          <!-- 使用AnnotationWriting组件替换原有的作文内容区域 -->
          <AnnotationWriting
            ref="annotationWritingRef"
            :data="annotationData"
            @annotationActivate="handleAnnotationActivate"
          />
        </view>
      </view>

      <!-- 蓝色区域（评价卡片） -->
      <view
        class="evaluation-area"
        :class="{ expanded: dragState.isExpanded }"
        :style="{ height: `${dragState.currentHeight}rpx` }"
        @touchstart="onCardTouchStart"
        @touchmove="onCardTouchMove"
        @touchend="onCardTouchEnd"
      >
        <!-- 滑动条指示器 - 可拖拽 -->
        <view
          class="drag-indicator"
          @click="toggleExpand"
          @touchstart.stop="onTouchStart"
          @touchmove.stop="onTouchMove"
          @touchend.stop="onTouchEnd"
        ></view>

        <!-- 评价标签页 -->
        <view class="tab-container">
          <view
            v-for="tab in tabs"
            :key="tab.value"
            class="tab-item"
            :class="{ active: activeTab === tab.value }"
            @click="onTabClick(tab)"
          >
            <text>{{ tab.label }}</text>
            <view class="tab-indicator" v-if="activeTab === tab.value"></view>
          </view>
        </view>

        <!-- 评价内容区 - 可滚动 -->
        <scroll-view scroll-y class="evaluation-content">
          <!-- 综合评价 -->
          <view class="evaluation-card" v-if="activeTab === 'comprehensive'">
            <!-- 综合评价内容 -->
            <view class="score-section">
              <view class="score-left">
                <view class="grade-title">评定档次</view>
                <view class="grade-level">二类文(上)</view>
                <view class="score-range">分数区间:75-89</view>
              </view>
              <view class="score-right">
                <view class="score-value">82</view>
                <view class="score-total">/100分</view>
              </view>
            </view>

            <!-- 档次评定理由 -->
            <view class="reason-header">
              <text>档次评定理由</text>
            </view>
            <view class="reason-content">
              该文章符合二类文标准的主要特征:中心明确,内容充实,结构完整,语言通顺,能够围绕主题展开论述.但思想深度和语言文采方面稍有欠缺,未能达到一类文标准.综合来看,在二类文档次中属于中上水平,故评为此档.
            </view>

            <!-- 总体评语 -->
            <view class="reason-header">
              <text>总体评语</text>
            </view>
            <view class="reason-content">
              本文中心思想明确，能够按照要求完成写作任务，结构较为完整，段落清晰，语言表达通顺，没有大的语法错误。亮点在于文章的逻辑性尚可，能够一步步展开论述。但文章在思想的深刻性上还不够，分析问题停留在表面，可以进一步思考。总体属于一篇稳定的二类文。
            </view>
          </view>

          <!-- 详细批注 -->
          <view class="evaluation-card" v-else-if="activeTab === 'detail'">
            <!-- 批注分页指示器 -->
            <scroll-view scroll-x class="annotation-pagination-scroll" show-scrollbar="false">
              <view class="annotation-pagination">
                <view
                  v-for="(item, index) in detailAnnoData"
                  :key="index"
                  class="page-btn"
                  :class="{ active: currentPage === item.number }"
                  @click="onDetailAnnoClick(item)"
                >
                  {{ item.number }}
                </view>
              </view>
            </scroll-view>

            <!-- 批注内容区域 -->
            <view class="annotation-content">
              <!-- 佳句赏析 -->
              <view class="annotation-item" v-if="currentPage === 1">
                <view class="annotation-label good-sentence"> 🌟 佳句赏析 </view>
                <view class="annotation-box good-sentence-box">
                  <text class="annotation-text"
                    >该句通过比喻（润滑剂、粘合剂）生动形象地阐述了恰当相处之道在社群关系中的重要作用，增强了语言的感染力和说服力。</text
                  >
                </view>
              </view>

              <!-- 优化建议 -->
              <view class="annotation-item" v-if="currentPage === 2">
                <view class="annotation-label suggestion"> ✨ 优化建议 </view>
                <view class="annotation-box suggestion-box">
                  <text class="annotation-text"
                    >这段论述可以进一步丰富例证，建议补充一两个具体的社会现象或历史事件作为佐证，使论点更有说服力。</text
                  >
                </view>
              </view>

              <!-- 删除内容 -->
              <!-- <view class="annotation-item" v-if="currentPage === 3">
                                <view class="annotation-label delete">
                                    ⛔️ 删除内容
                                </view>
                                <view class="annotation-box delete-box">
                                    <text class="annotation-text">此处内容与中心论点关联不大，建议删除或调整，以保持文章结构的紧凑性和逻辑性。</text>
                                </view>
                            </view> -->

              <!-- 插入内容 -->
              <!-- <view class="annotation-item" v-if="currentPage === 4">
                                <view class="annotation-label insert">
                                    ✍️ 插入内容
                                </view>
                                <view class="annotation-box insert-box">
                                    <text class="annotation-text">此处可以插入一段关于人际交往中换位思考重要性的论述，进一步深化文章主题。</text>
                                </view>
                            </view> -->

              <!-- 替换内容 -->
              <!-- <view class="annotation-item" v-if="currentPage === 5">
                                <view class="annotation-label replace">
                                    📖 替换内容
                                </view>
                                <view class="annotation-box replace-box">
                                    <text class="annotation-text">建议将这个例子替换为更贴近当代青少年生活的事例，使论证更有说服力和时代感。</text>
                                </view>
                            </view> -->
            </view>
          </view>

          <!-- 评分详情 -->
          <view class="evaluation-card score-details" v-else-if="activeTab === 'score'">
            <!-- 使用uni-collapse组件实现折叠面板 -->
            <uni-collapse
              class="custom-collapse"
              accordion
              @change="(val: number) => (currentOpen = val)"
            >
              <uni-collapse-item
                v-for="(item, index) in scoreDimensions"
                :key="item.id"
                :name="index"
              >
                <template v-slot:title>
                  <view class="score-item-header">
                    <text class="score-item-title">{{ item.title }}</text>
                    <text class="score-item-points">{{ item.score }}/{{ item.totalScore }}分</text>
                    <view class="score-item-arrow">
                      <LkSvg
                        :src="
                          index === currentOpen
                            ? '/static/homework/arrow_up.svg'
                            : '/static/homework/arrow_down.svg'
                        "
                        width="100%"
                        height="100%"
                      />
                    </view>
                  </view>
                </template>
                <view class="score-item-content" v-if="item.subItems && item.subItems.length > 0">
                  <view v-for="(subItem, subIndex) in item.subItems" :key="subIndex">
                    <view class="score-item-content-header">
                      <text>{{ subItem.title }}</text>
                      <text>{{ subItem.score }}/{{ subItem.totalScore }}</text>
                    </view>
                    <view class="score-item-divider"></view>
                    <text class="score-item-desc">{{ subItem.desc }}</text>
                  </view>
                </view>
              </uni-collapse-item>
            </uni-collapse>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { LkSvg } from '@/components';
// 引入AnnotationWriting组件
import AnnotationWriting, {
  type Annotation,
  type AnnotationDataItem,
} from '../../../homework/components/AnnotationWriting';
// 引入批注数据
import { annotationDataMock } from '../../../homework/mock/annotation';

// 页面数据
const subjectName = ref('语文');
const homeworkTitle = ref('第一次单元练习作业');
// 当前打开的折叠面板索引
const currentOpen = ref(-1);
// 批注组件引用
const annotationWritingRef = ref<InstanceType<typeof AnnotationWriting> | null>(null);
// 标签页数据
const activeTab = ref('comprehensive'); // 默认显示综合评价标签页
const tabs = [
  { label: '综合评价', value: 'comprehensive' },
  { label: '详细批注', value: 'detail' },
  { label: '评分详情', value: 'score' },
];
// 批注数据
const annotationData = ref<AnnotationDataItem[]>(annotationDataMock);

// 详细批注数据
const detailAnnoData = ref([
  {
    number: 1,
    annotationId: 'anno_001',
    title: '佳句赏析',
    type: 'great',
    content:
      '该句通过比喻（润滑剂、粘合剂）生动形象地阐述了恰当相处之道在社群关系中的重要作用，增强了语言的感染力和说服力。',
  },
  {
    number: 2,
    annotationId: 'anno_005',
    title: '优化建议',
    type: 'recommend',
    content:
      '这段论述可以进一步丰富例证，建议补充一两个具体的社会现象或历史事件作为佐证，使论点更有说服力。',
  },
  {
    number: 3,
    annotationId: 'anno_101',
    title: '删除内容',
    type: 'great',
    content: '此处内容与中心论点关联不大，建议删除或调整，以保持文章结构的紧凑性和逻辑性。',
  },
  {
    number: 4,
    annotationId: 'anno_102',
    title: '插入内容',
    type: 'recommend',
    content: '此处可以插入一段关于人际交往中换位思考重要性的论述，进一步深化文章主题。',
  },
]);

// 监听折叠面板状态变化
const onCollapseChange = (index: number) => {
  currentOpen.value = currentOpen.value === index ? -1 : index;
};

// 详细批注当前页码
const currentPage = ref(1);

// 设置当前批注页码
const setCurrentPage = (page: number) => {
  currentPage.value = page;
};

// 点击标签页
const onTabClick = (tab: { label: string; value: string }) => {
  activeTab.value = tab.value;

  // 如果切换到详细批注标签页
  if (tab.value === 'detail') {
    // 确保评价面板展开
    if (!dragState.isExpanded) {
      dragState.isExpanded = true;
    }

    // 激活当前页码对应的批注
    nextTick(() => {
      // 获取当前页码对应的批注
      const currentAnno = detailAnnoData.value.find(item => item.number === currentPage.value);
      if (currentAnno) {
        console.log('切换到详细批注标签页，激活批注:', currentAnno.annotationId);
        // 激活批注
        setTimeout(() => {
          annotationWritingRef.value?.activateAnnotationById(currentAnno.annotationId);
        }, 100);
      }
    });
  } else {
    // 如果切换到其他标签页，清除批注高亮
    nextTick(() => {
      // 清除批注高亮
      if (annotationWritingRef.value) {
        annotationWritingRef.value.clearActiveArea();
      }
    });
  }
};

// 处理批注激活事件
const handleAnnotationActivate = (annotation: Annotation, imageIndex: number) => {
  console.log('批注激活:', annotation, '图片索引:', imageIndex);

  // 切换到详细批注标签页
  activeTab.value = 'detail';

  // 确保评价面板展开
  if (!dragState.isExpanded) {
    dragState.isExpanded = true;
  }

  // 如果当前页码与批注序号相同，则不进行切换
  if (currentPage.value == annotation.number) return;

  // 根据批注序号设置当前页码
  if (annotation.number) {
    setCurrentPage(annotation.number);
  }

  scrollToAnnotation();
};

// 区域拖拽相关状态
const dragState = reactive({
  startY: 0,
  moveY: 0,
  lastY: 0,
  isDragging: false,
  minHeight: 300, // rpx - 收起状态高度
  maxHeight: 600, // rpx - 展开状态高度
  isExpanded: false, // 是否展开状态
  // 当前高度，初始为收起状态
  get currentHeight() {
    return this.isExpanded ? this.maxHeight : this.minHeight;
  },
});

// 触摸开始事件
const onTouchStart = (e: TouchEvent) => {
  const touch = e.touches[0] || e.changedTouches[0];
  dragState.startY = touch.clientY;
  dragState.isDragging = true;
};

// 触摸移动事件
const onTouchMove = (e: TouchEvent) => {
  if (!dragState.isDragging) return;

  const touch = e.touches[0] || e.changedTouches[0];
  const currentY = touch.clientY;
  const deltaY = dragState.startY - currentY; // 向上为正，向下为负

  // 仅记录移动距离，不立即更改高度
  dragState.moveY = deltaY;
};

// 触摸结束事件
const onTouchEnd = () => {
  if (!dragState.isDragging) return;

  // 判断移动方向和距离
  if (Math.abs(dragState.moveY) > 10) {
    // 移动超过阈值才触发切换
    if (dragState.moveY > 0) {
      // 向上拖动，展开卡片
      dragState.isExpanded = true;
    } else {
      // 向下拖动，收起卡片
      dragState.isExpanded = false;
    }
  }

  // 重置状态
  dragState.isDragging = false;
  dragState.moveY = 0;
};

// 在整个评价区域上添加拖拽事件，提升交互面积
const onCardTouchStart = onTouchStart;
const onCardTouchMove = onTouchMove;
const onCardTouchEnd = onTouchEnd;

// 点击方法切换展开/收起状态
const toggleExpand = () => {
  dragState.isExpanded = !dragState.isExpanded;
};

// 滚动到批注内容区域
const scrollToAnnotation = () => {
  // 使用uni-app的选择器API获取评价区域元素
  const query = uni.createSelectorQuery();
  query
    .select('.evaluation-area')
    .boundingClientRect(data => {
      if (data) {
        // 展开评价区域
        dragState.isExpanded = true;
        // 滚动到批注内容
        setTimeout(() => {
          const annotationQuery = uni.createSelectorQuery();
          annotationQuery
            .select('.annotation-content')
            .boundingClientRect(annoData => {
              // 确保annoData是单个元素而不是数组
              const topValue = Array.isArray(annoData) ? annoData[0]?.top || 0 : annoData?.top || 0;

              if (topValue !== undefined) {
                uni.pageScrollTo({
                  scrollTop: topValue,
                  duration: 300,
                });
              }
            })
            .exec();
        }, 300); // 等待展开动画完成
      }
    })
    .exec();
};

// 记录上次点击的批注页码和时间
const lastClickedAnno = reactive({
  number: 0,
  timestamp: 0,
});

// 点击详细批注页码
const onDetailAnnoClick = (item: any) => {
  console.log(`点击批注页码: ${item.number}, 批注ID: ${item.annotationId}`);

  // 防抖处理：如果短时间内重复点击同一个批注页码，则忽略
  const now = Date.now();
  const debounceTime = 500; // 防抖时间，单位毫秒
  if (lastClickedAnno.number === item.number && now - lastClickedAnno.timestamp < debounceTime) {
    console.log(
      `防抖处理：忽略重复点击批注页码 ${item.number}，间隔时间: ${now - lastClickedAnno.timestamp}ms`
    );
    return;
  }

  // 更新上次点击的批注信息
  lastClickedAnno.number = item.number;
  lastClickedAnno.timestamp = now;

  // 先切换到详细批注标签页
  activeTab.value = 'detail';

  // 设置当前页码
  currentPage.value = item.number;

  // 根据批注页码设置不同的延时
  // 页码3和4在第二张图片上，需要更长的延时
  const delay = item.number >= 3 ? 200 : 100;

  // 展开评价区域
  if (!dragState.isExpanded) {
    dragState.isExpanded = true;
  }

  // 等待标签页切换完成后再激活批注
  setTimeout(() => {
    // 调用AnnotationWriting组件的activateAnnotationById方法
    // 该方法内部会处理滚动到批注位置
    console.log(`激活批注: ${item.annotationId}, 页码: ${item.number}, 延时: ${delay}ms`);
    annotationWritingRef.value?.activateAnnotationById(item.annotationId);
  }, delay);
};

// 评分维度数据
const scoreDimensions = reactive([
  {
    id: 1,
    title: '内容与立意',
    score: 35,
    totalScore: 40,
    open: false,
    subItems: [
      {
        title: '语言流畅，用词准确',
        score: 12,
        totalScore: 15,
        desc: '该文章符合二类文标准的主要特征:中心明确,内容充实,结构完整,语言通顺,能够围绕主题展开论述.但思想深度和语言文采方面稍有欠缺,未能达到一类文标准.综合来看,在二类文档次中属于中上水平,故评为此档.',
      },
    ],
  },
  {
    id: 2,
    title: '结构与逻辑',
    score: 25,
    totalScore: 30,
    open: false,
    subItems: [
      {
        title: '结构合理',
        score: 15,
        totalScore: 20,
        desc: '文章结构较为完整，层次分明，有一定的逻辑性，但过渡不够自然流畅。',
      },
    ],
  },
  {
    id: 3,
    title: '语言与表达',
    score: 22,
    totalScore: 30,
    open: false,
    subItems: [
      {
        title: '表达准确',
        score: 10,
        totalScore: 15,
        desc: '语言表达基本准确，但缺乏灵活性和多样性，有些表述较为平淡。',
      },
    ],
  },
  {
    id: 4,
    title: '特殊扣分项',
    score: 0,
    totalScore: 0,
    open: false,
    subItems: [],
  },
]);

// 页面加载完成后初始化
onMounted(() => {
  // 设置默认显示综合评价标签页
  activeTab.value = 'comprehensive';

  // 设置默认页码为1
  currentPage.value = 1;

  // 展开评价面板
  dragState.isExpanded = true;
});
</script>

<style lang="scss" scoped>
.score-writing {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7fd;
  position: relative;
  overflow: hidden;
}

// 内容容器 - 弹性布局区域
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

// 绿色区域 - 作文显示区域
.writing-area {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  height: 100%;
}

.writing-card {
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #fff;
}

.writing-image-container {
  position: relative;
  width: 100%;
}

.writing-image {
  width: 100%;
  height: auto;
  display: block;
}

.annotation-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  /* 让触摸事件穿透到下方的图片 */
}

/* 批注序号点击区域样式 */
.annotation-numbers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.annotation-number-marker {
  position: absolute;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #d791ff 0%, #8547ff 100%);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translate(-50%, -50%);
  pointer-events: auto;
  /* 允许点击 */
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  border: 2px solid white;
  /* 添加白色边框使序号更加明显 */
}

.annotation-number-text {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

// 蓝色区域 - 评价卡片区域
.evaluation-area {
  background-color: #ffffff;
  border-radius: 28rpx 28rpx 0 0;
  box-shadow: 0px -1px 9px rgba(0, 0, 0, 0.14);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 5; // 确保在作文区域上层

  &.expanded {
    // 展开状态的特殊样式，如果需要
  }
}

// 滑动条指示器
.drag-indicator {
  width: 60rpx;
  height: 6rpx;
  background-color: #d9d9d9;
  border-radius: 100rpx;
  margin: 14rpx auto;
  cursor: pointer; // 指示可点击
  touch-action: none; // 阻止默认触摸行为
  position: relative;
  z-index: 10;

  &::before {
    content: '';
    position: absolute;
    top: -20rpx;
    left: -100rpx;
    right: -100rpx;
    height: 40rpx;
    // 扩大点击区域，但不显示
  }
}

// 标签页容器
.tab-container {
  display: flex;
  justify-content: space-around;
  padding: 0 32rpx;
  margin-bottom: 20rpx;
}

.tab-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
  flex: 1;

  text {
    font-size: 36rpx;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 400;
    text-align: center;
  }

  &.active {
    text {
      color: #303133;
      font-weight: 600;
    }
  }
}

.tab-indicator {
  width: 48rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #dc7eff 0%, #601cff 100%);
  border-radius: 999rpx;
  margin-top: 4rpx;
}

// 评价内容区
.evaluation-content {
  flex: 1;
  overflow: hidden;
}

.evaluation-card {
  padding: 20rpx 30rpx 30rpx;
  font-size: 30rpx;
  border-radius: 20rpx;
  margin: 0 30rpx 30rpx;

  &.score-details {
    padding: 0;
    background-color: transparent;
  }
}

// 分数区域样式已移到底部

.score-left {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.grade-title {
  font-size: 28rpx;
  color: #303133;
  margin-bottom: 8rpx;
}

.grade-level {
  font-size: 36rpx;
  color: #303133;
  font-weight: 600;
  margin-bottom: 6rpx;
}

.score-range {
  font-size: 28rpx;
  color: #303133;
  opacity: 0.9;
}

.score-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
}

.score-value {
  font-size: 68rpx;
  color: #000000;
  font-weight: 600;
  line-height: 1;
}

.score-total {
  font-size: 28rpx;
  color: #303133;
  opacity: 0.9;
  margin-top: 6rpx;
}

// 评定理由和评语区域
.reason-header {
  display: inline-flex; // 改为inline，使其宽度自适应内容
  align-items: center;
  background: linear-gradient(90deg, #ebe4ff 0%, #cbb8ff 100%);
  border-radius: 10px 0px;
  padding: 12rpx 20rpx;
  margin: 20rpx 0 16rpx;
}

.reason-header text {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
}

.reason-content {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.9);
  line-height: 1.8;
  background-color: #f9fafb;
  padding: 20rpx 16rpx;
  border-radius: 20rpx;
}

// 评分详情列表样式
.score-item-list {
  margin-top: 20rpx;
}

.score-item {
  margin-bottom: 14rpx;
}

.score-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  border: 1px solid #e7e7e7;
  border-radius: 8rpx;
  padding: 0 24rpx;
  height: 80rpx;
  box-sizing: border-box;
  position: relative;
}

.score-item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.score-item-points {
  font-size: 28rpx;
  color: #4e5969;
  margin-right: 50rpx;
  /* 为箭头留出空间 */
}

.score-item-arrow {
  position: absolute;
  right: 18rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.score-item-arrow image {
  width: 24rpx;
  height: 12rpx;
}

/* 展开时箭头旋转 */
:deep(.uni-collapse-item--open) .score-item-arrow image {
  transform: rotate(180deg);
}

/* 展开的内容区域样式 */
.score-item-content {
  background-color: #f9fafb;
  border-radius: 10rpx;
  margin-top: 10rpx;
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.score-item-content-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.score-item-content-header text {
  font-size: 28rpx;
  font-weight: 500;
  color: #1d2129;
}

.score-item-divider {
  height: 1px;
  background-color: #e7e7e7;
  margin: 10rpx 0;
}

.score-item-desc {
  font-size: 30rpx;
  line-height: 1.8;
  color: rgba(0, 0, 0, 0.9);
}

// 底部按钮
.score-btn {
  width: 343rpx;
  height: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  border: 1px solid #7d4dff;
  color: #7d4dff;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin: 30rpx 0;
  margin-left: auto;
  margin-right: auto;
}

// 占位内容样式
.placeholder-content {
  margin-top: 20rpx;
}

.placeholder-line {
  margin-bottom: 20rpx;
  line-height: 1.6;
}

// 评分详情相关样式
.custom-collapse {
  margin-top: 20rpx;
  border: none !important;
  background-color: transparent !important;

  :deep(.uni-collapse-item) {
    margin-bottom: 16rpx;
    border: none !important;
    background-color: transparent !important;

    .uni-collapse-item__title {
      padding: 0 !important;
      height: auto !important;
      background-color: transparent !important;
      border: none !important;

      &::before,
      &::after {
        display: none !important;
      }

      .uni-collapse-item__title-box {
        padding: 0 !important;
        width: 100% !important;
      }

      .uni-collapse-item__title-inner {
        width: 100% !important;
      }

      .uni-collapse-item__title-arrow {
        width: 30rpx !important;
        height: 30rpx !important;
        margin-right: 0 !important;
        display: none !important; // 隐藏原生箭头

        &.uni-collapse-item--animation {
          transform: rotate(180deg) !important;
        }
      }
    }

    .uni-collapse-item__wrap {
      background-color: transparent !important;
      border: none !important;

      .uni-collapse-item__wrap-content {
        padding: 0 !important;
      }
    }
  }
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(90deg, #ebe4ff 0%, #cbb8ff 100%);
  border-radius: 10px 0px;
  padding: 16rpx 24rpx;
  margin-bottom: 12rpx;
  width: 100%;
  box-sizing: border-box;
}

.dimension-left {
  display: flex;
  align-items: baseline;
}

.dimension-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-right: 8rpx;
}

.dimension-desc {
  font-size: 24rpx;
  color: #606266;
}

.dimension-right {
  display: flex;
  align-items: center;
}

.dimension-score {
  font-size: 36rpx;
  font-weight: 600;
  color: #303133;
}

.dimension-content {
  background-color: #f9fafb;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.score-item {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.score-item-title {
  font-size: 28rpx;
  color: #303133;
}

.score-item-value {
  font-size: 28rpx;
  color: #303133;
  font-weight: 500;
}

.score-comment {
  padding: 16rpx 0 0;
  display: flex;
  flex-direction: column;
}

.comment-title {
  font-size: 28rpx;
  color: #303133;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.comment-content {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}

.collapse-item {
  margin-bottom: 14rpx;
}

.custom-collapse {
  margin-top: 20rpx;
}

// 修改评分区域样式，使其匹配截图中的渐变背景
.score-section {
  display: flex;
  justify-content: space-between;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  border: none;
  // 横向线性渐变，从淡粉色到淡蓝色再到淡粉色
  background: linear-gradient(to right, #ffdce0, #d8f0ff, #fadee2);
}

.evaluation-card.score-details {
  padding: 0 30rpx;
}

/* 自定义uni-collapse样式 */
:deep(.uni-collapse) {
  background-color: transparent;
  border: none;
}

:deep(.uni-collapse-item) {
  margin-bottom: 16rpx;
}

:deep(.uni-collapse-item__title) {
  padding: 0 !important;
  height: auto !important;
  line-height: normal !important;
  background-color: transparent !important;
  border: none !important;
}

:deep(.uni-collapse-item__title-box) {
  padding: 0 !important;
  width: 100% !important;
}

:deep(.uni-collapse-item__title-inner) {
  width: 100% !important;
  padding: 0 !important;
  flex: 1;
}

:deep(.uni-collapse-item__title-arrow) {
  display: none !important;
  /* 隐藏原生箭头 */
}

:deep(.uni-collapse-item__wrap) {
  padding: 0 !important;
  border: none !important;
  background-color: transparent !important;
}

:deep(.uni-collapse-item__wrap-content) {
  padding: 0 !important;
}

/* 详细批注样式 */
.annotation-pagination-scroll {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 30rpx;
}

.annotation-pagination {
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  gap: 20rpx;
}

.page-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f2f3f5;
  color: #4e5969;
  font-size: 28rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.page-btn.active {
  background: linear-gradient(135deg, #d791ff 0%, #8547ff 100%);
  color: #ffffff;
  font-weight: 600;
}

.annotation-item {
  margin-bottom: 20rpx;
}

.annotation-label {
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10rpx;
  padding: 0 4rpx;
}

.good-sentence {
  color: #00b8ac;
}

.suggestion {
  color: #ff7a00;
}

.delete,
.insert,
.replace {
  color: #f05050;
}

.annotation-box {
  padding: 20rpx 14rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.good-sentence-box {
  background-color: #e1fffd;
}

.suggestion-box {
  background-color: #fff2e5;
}

.delete-box,
.insert-box,
.replace-box {
  background-color: #ffeeee;
}

.annotation-text {
  font-size: 15px;
  line-height: 1.47;
  color: rgba(0, 0, 0, 0.9);
}
</style>
