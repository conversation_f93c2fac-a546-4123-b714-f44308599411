<template>
  <view class="score-single">
    <!-- 订正选项卡 -->
    <CorrectionTabs
      v-model:currentIndex="currentTabIndex"
      :tabs="correctionTabs"
      @change="switchTab"
    />

    <!-- 成绩概览卡片 -->
    <view class="score-card">
      <!-- 进度图 -->
      <view class="score-arcbar">
        <qiun-data-charts
          type="arcbar"
          :opts="scoreArcOpts"
          :chartData="scoreArcData"
          background="none"
        />
        <!-- 标签容器 -->
        <view class="score-labels-box">
          <!-- 正确率 line -->
          <view class="right-line">
            <image src="/static/homework/chart/polyline_1.svg" />
          </view>
          <!-- 正确率 label -->
          <text class="right-label-text">正确率:{{ resData.correctRate }}%</text>
          <!-- 得分 line -->
          <view class="score-line">
            <image src="/static/homework/chart/polyline_2.svg" />
          </view>
          <!-- 得分 label -->
          <text class="score-label-text">得分:{{ resData.score }}分</text>
          <!-- 满分 label  -->
          <text class="full-label-text">满分:{{ resData.totalScore }}分</text>
        </view>
      </view>
    </view>
    <!-- 做题情况卡片 -->
    <MySectionCard title="做题情况">
      <view class="stats-container">
        <uni-row :gutter="20">
          <uni-col :span="8" v-for="(item, index) in statsItems" :key="index">
            <view class="stats-group">
              <view class="stats-item">
                <view class="stats-num">{{ item.value }}</view>
                <view class="stats-unit" v-if="item.unit">{{ item.unit }}</view>
              </view>
              <view class="stats-label">{{ item.label }}</view>
            </view>
          </uni-col>
        </uni-row>
      </view>
    </MySectionCard>

    <!-- 智能分析卡片 -->
    <MySectionCard title="智能分析">
      <!-- 雷达图 -->
      <view class="radar-chart">
        <qiun-data-charts
          v-if="radarData?.categories && radarData?.categories.length > 0"
          type="radar"
          :opts="radarOpts"
          :chartData="radarData"
        />
        <!-- 暂无数据 -->
        <MyEmpty v-else />
      </view>

      <!-- AI分析内容 -->
      <view class="ai-analysis">
        <text class="ai-content" v-html="aiAnalysis"></text>
        <!-- 推荐练习按钮 -->
        <view class="recommend-btn">
          <button class="btn btn-outline" @click="goToRecommendExercises">推荐练习</button>
        </view>
      </view>
    </MySectionCard>
    <MyLoading v-if="loading" fullscreen type="spinner" overlay />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed, watch } from 'vue';
import { getHomeworkScoreDetail, getStudentSubmitList } from '@/api/students/homework';
import MySectionCard from '../MySectionCard/index.vue';
import { numberToChinese } from '../../utils';
import { getChatCommonBusiness } from '@/api/students/homework';
import MyLoading from '@/components/MyLoading';
import CorrectionTabs from '../CorrectionTabs/index.vue';
import MyEmpty from '@/components/MyEmpty';

// 接收作业ID属性
const props = defineProps({
  homeworkId: {
    type: String,
    default: '',
  },
  stuHomeworkId: {
    type: String,
    default: '',
  },
});

// 后端返回数据
const resData = ref<any>({
  correctRate: 0,
  score: 0,
  totalScore: 0,
  correctCount: 0,
  wrongCount: 0,
  answeredCount: 0,
  unansweredCount: 0,
  timeSecond: 0,
});

const loading = ref(false);
const aiAnalysis = ref('');

// 格式化秒数为 HH:mm:ss 格式
const formatSecondsToHHMMSS = (seconds: number) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  const pad = (n: number) => n.toString().padStart(2, '0');
  if (h > 0) {
    return `${pad(h)}:${pad(m)}:${pad(s)}`;
  } else {
    return `${pad(m)}:${pad(s)}`;
  }
};

// 统计项数据
const statsItems = computed(() => [
  { value: resData.value.correctCount, unit: '题', label: '答对' },
  { value: resData.value.wrongCount, unit: '题', label: '答错' },
  { value: resData.value.answeredCount, unit: '题', label: '已答' },
  { value: resData.value.unansweredCount, unit: '题', label: '未答' },
  { value: formatSecondsToHHMMSS(resData.value.timeSecond), unit: '', label: '用时' },
]);

// 进度图配置
const scoreArcOpts = ref({
  padding: [5, 5, 5, 5],
  title: {
    name: '0分',
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    offsetY: -5,
  },
  subtitle: false,
  color: ['#7D4DFF', '#FFAF37'],
  extra: {
    arcbar: {
      type: 'circle',
      width: 14,
      backgroundColor: '#E9E9E9',
      startAngle: 1.5,
      endAngle: 0.25,
      gap: 5,
      direction: 'ccw',
      lineCap: 'square',
    },
  },
});

// 进度图数据
const scoreArcData = ref({
  // series: [
  //   {
  //     name: '正确率',
  //     data: 0,
  //   },
  //   {
  //     name: '得分率',
  //     data: 0,
  //   },
  // ],
});

// 雷达图配置
const radarOpts = ref({
  color: ['#7D4DFF'],
  padding: [5, 5, 5, 5],
  dataLabel: false,
  legend: {
    show: false,
  },
  extra: {
    radar: {
      gridType: 'radar',
      gridColor: '#7D4DFF',
      gridCount: 3,
      opacity: 0.5,
      max: 100,
    },
  },
});

// 雷达图数据
const radarData = ref({
  categories: [] as any[],
  series: [] as any[],
});

interface CorrectionTab {
  name: string;
  id: string;
}

// 订正选项卡数据
const correctionTabs = ref<CorrectionTab[]>([]);

const currentTabIndex = ref(0);
const currentTab = computed(() => correctionTabs.value[currentTabIndex.value]);

// 页面加载时获取成绩数据
onMounted(() => {
  console.log('加载单次成绩单数据，作业ID:', props.homeworkId);
  if (props.homeworkId) {
    fetchStudentSubmitList();
  }
});

const fetchStudentSubmitList = async () => {
  try {
    loading.value = true;
    const res = await getStudentSubmitList({ id: props.stuHomeworkId });
    console.log('学生提交记录列表:', res);
    correctionTabs.value = res.map((item: any) => ({
      name: item.reviseCount > 0 ? `第${numberToChinese(item.reviseCount)}次订正` : '首次',
      id: String(item.submitId),
    }));

    // 默认选择最后一个
    currentTabIndex.value = correctionTabs.value.length - 1;
    fetchHomeworkScoreDetail();
  } catch (error) {
    console.error('获取学生提交记录列表失败:', error);
  } finally {
    loading.value = false;
  }
};

const fetchHomeworkScoreDetail = async () => {
  try {
    // uni.showLoading({ title: '加载中...' });
    const res = await getHomeworkScoreDetail({ id: currentTab.value.id });
    console.log('作业成绩详情:', res);

    if (res) {
      // 更新成绩数据
      resData.value = res;

      // 更新进度图标题
      scoreArcOpts.value.title.name = res.score + '分';

      // 更新进度图数据
      scoreArcData.value = {
        series: [
          {
            name: '正确率',
            data: res.correctRate / 100,
          },
          {
            name: '得分率',
            data: res.score / res.totalScore,
          },
        ],
      };

      // 更新雷达图数据（如果接口返回了知识点掌握程度数据）
      if (res.pointItems && Array.isArray(res.pointItems)) {
        const categories: string[] = [];
        const data: number[] = [];

        res.pointItems.forEach((point: any) => {
          categories.push(point.pointName || '');
          data.push(point.correctRate);
        });

        if (categories.length > 0) {
          radarData.value = {
            categories,
            series: [
              {
                name: '掌握程度',
                data,
              },
            ],
          };
        }
      }

      fetchKnowledgeMasteryCommonBusiness();
    }
  } catch (error) {
    console.error('获取作业成绩详情失败:', error);
  } finally {
    uni.hideLoading();
  }
};

const fetchKnowledgeMasteryCommonBusiness = async () => {
  const res = await getChatCommonBusiness({
    businessType: 'knowledgeMastery',
    messages: [
      {
        dataId: 't7exj6h1q1ilth678fnih7',
        role: 'user',
        hideInUI: false,
        content: JSON.stringify({
          data: resData.value.pointItems.map((item: any) => ({
            pointName: item.pointName,
            rate: item.correctRate,
          })),
        }),
      },
    ],
    stream: false,
  });

  if (res && res.choices && res.choices.length > 0) {
    let aiAnalysisText = '';
    res.choices.forEach((choice: any) => {
      choice.message.content.forEach((item: any) => {
        if (item.type === 'text') {
          aiAnalysisText += item.text.content;
        }
      });
    });
    aiAnalysis.value = aiAnalysisText;
  } else {
    aiAnalysis.value = '暂无分析数据';
  }

  // console.log('知识掌握度智能体业务:', res);
};

// 切换订正选项卡
const switchTab = (index: number, tab?: any) => {
  currentTabIndex.value = index;
  // 根据选中的选项卡来加载不同的成绩数据
  console.log('切换到选项卡:', correctionTabs.value[index].name);
  // 如果有订正记录ID，可以再次调用接口获取对应的订正记录数据
  if (props.homeworkId) {
    // 这里可以传递额外参数，例如订正记录的ID或索引
    fetchHomeworkScoreDetail();
  }
};

// 方法：前往推荐练习
const goToRecommendExercises = () => {
  uni.navigateTo({
    url: '/pages-subpackages/students/error-book/recommend-contact?homeworkId=' + props.homeworkId,
  });
};
</script>

<style lang="scss" scoped>
.score-single {
  display: flex;
  flex-direction: column;
  background-color: #f7f7fd;
  padding: 16rpx 16rpx 16rpx 16rpx;
  // min-height: 100vh;
}

// 成绩卡片样式
.score-card {
  margin-top: 20rpx;
  margin-bottom: 30rpx;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 30rpx;
  box-shadow: 0 0 15px rgba(237, 237, 237, 0.62);
  border: 1px solid #f1f1f1;
  display: flex;
  justify-content: center;
}

.score-arcbar {
  position: relative;
  width: 600rpx;
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20rpx 0;
}

.score-labels-box {
}

.right-line {
  position: absolute;
  left: -5rpx;
  top: 50rpx;

  image {
    width: 204rpx;
    height: 22rpx;
  }
}

.score-line {
  position: absolute;
  right: 20rpx;
  top: 30rpx;

  image {
    width: 280rpx;
    height: 36rpx;
  }
}

.right-label-text {
  position: absolute;
  left: 15rpx;
  top: 30rpx;
  font-size: 22rpx;
  color: #333333;
  white-space: nowrap;
  line-height: 22rpx;
}

.score-label-text {
  position: absolute;
  right: 35rpx;
  top: -3rpx;
  font-size: 22rpx;
  color: #333333;
}

.full-label-text {
  position: absolute;
  right: 35rpx;
  top: 40rpx;
  font-size: 22rpx;
  color: #333333;
}

.total-score-label {
  right: 0;
  top: 60rpx;
}

.label-box {
  display: flex;
  align-items: center;
  height: 22rpx;
  width: auto;
}

.label-text {
  font-size: 22rpx;
  color: #333333;
  white-space: nowrap;
  line-height: 22rpx;
}

.label-line {
  // width: 80rpx;
  // height: auto;
}

.correct-rate-line {
  background-color: #7d4dff;
  margin-left: 5rpx;
}

.score-line {
  // background-color: #FFAF37;
  margin-right: 5rpx;
}

.score-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #7d4dff;
}

.score-total {
  font-size: 26rpx;
  color: #333;
  margin-left: 4rpx;
}

// 统计数据项样式
.stats-container {
  // padding: 20rpx 0rpx;
}

.stats-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  position: relative;
}

// 添加右侧分隔线
.uni-col:not(:nth-child(3n)) .stats-group:after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  width: 2rpx;
  height: 40rpx;
  background-color: #e7e7e7;
  transform: translateY(-50%);
}

.stats-item {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.stats-num {
  font-size: 40rpx;
  font-weight: 500;
  color: #1d2129;
}

.stats-unit {
  font-size: 24rpx;
  color: #4e5969;
  margin-left: 4rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #4e5969;
  margin-top: 8rpx;
  text-align: center;
}

.stats-divider {
  width: 1rpx;
  height: 50rpx;
  background-color: #e7e7e7;
}

// 雷达图
.radar-chart {
  height: 400rpx;
  padding: 20rpx;
}

// AI分析内容
.ai-analysis {
  padding: 30rpx;
  background: linear-gradient(
    107deg,
    rgba(230, 213, 255, 0.6) -18.84%,
    rgba(213, 240, 255, 0.6) 113.54%
  );
  margin: 20rpx 0rpx;
  border-radius: 12rpx;
  position: relative;
}

.ai-content {
  font-size: 28rpx;
  line-height: 1.5;
  color: #1d2129;
  margin-bottom: 20rpx;
}

// 按钮样式
.recommend-btn {
  text-align: right;
  margin-top: 20rpx;

  .btn {
    display: inline-block;
    font-size: 28rpx;
    border-radius: 100px;
    padding: 0rpx 35rpx;
    height: 68rpx;
    line-height: 68rpx;
  }

  .btn-outline {
    background-color: #ffffff;
    color: #7d4dff;
    border: 1px solid #7d4dff;
  }
}
</style>
