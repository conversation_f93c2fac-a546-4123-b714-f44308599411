<template>
  <view class="score-self">
    <!-- 主要内容 -->
    <view class="score-content">
      <!-- 原件预览 -->
      <view class="original-section">
        <text class="section-title">提交练习原件</text>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <text>加载中...</text>
        </view>

        <!-- 有图片时显示 -->
        <!-- <scroll-view
          v-else-if="resultData.selfHomeworkFiles.length > 0"
          class="images-scroll"
          scroll-x
        >
          <view class="images-container">
            <view
              v-for="(item, index) in resultData.selfHomeworkFiles"
              :key="index"
              class="image-item"
              @tap="previewImage(index)"
            >
              <image class="preview-image" :src="item.fileUrl" mode="aspectFill" />
            </view>
          </view>
        </scroll-view> -->
        <template v-else-if="resultData.selfHomeworkFiles.length > 0">
          <view class="images-container">
            <view
              v-for="(item, index) in resultData.selfHomeworkFiles"
              :key="index"
              class="image-item"
              @tap="previewImage(index)"
            >
              <image class="preview-image" :src="item.fileUrl" mode="aspectFill" />
            </view>
          </view>
        </template>

        <!-- 无图片时显示 -->
        <view v-else class="empty-state">
          <text>暂无提交的练习原件</text>
        </view>
      </view>

      <!-- 分隔线 -->
      <view class="divider"></view>

      <!-- AI批改结果 -->
      <MySectionCard title="AI批改分析">
        <!-- 总体分析标题 -->
        <view class="section-header">
          <view class="section-indicator"></view>
          <text class="section-title">总体分析</text>
        </view>

        <!-- 总体分析内容 -->
        <view class="overall-analysis">
          <mp-html v-if="resultData.overallAnalysis" :content="resultData.overallAnalysis" />
          <view v-else class="empty-analysis">
            <text>暂无总体分析内容</text>
          </view>
        </view>

        <!-- 详细解析标题 -->
        <view class="section-header">
          <view class="section-indicator"></view>
          <text class="section-title">详细解析</text>
        </view>

        <!-- 详细解析内容 -->
        <view class="detail-analysis">
          <!-- <mp-html :content="resultData.detailedExplanation" />
              -->
          <template
            v-if="detailedExplanationArray.length > 0"
            v-for="(item, index) in detailedExplanationArray"
          >
            <view class="detail-analysis-item">
              <mp-html :content="`${index + 1}、${item.evaluation}`" />
              <mp-html :content="item.analysis" />
            </view>
          </template>
          <view v-else class="empty-analysis">
            <text>暂无详细解析内容</text>
          </view>
        </view>
      </MySectionCard>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import MySectionCard from '../MySectionCard/index.vue';
import { getSelfHomeworkDetail } from '@/api/students/homework';
import type { SelfHomeworkDetail } from '@/types/students/homework';

// 接收作业ID作为参数
const props = defineProps({
  homeworkId: {
    type: String,
    required: true,
  },
});

// 响应式数据
const resultData = ref<SelfHomeworkDetail>({
  selfHomeworkFiles: [],
  overallAnalysis: '',
  detailedExplanation: '',
});
const detailedExplanationArray = ref<any[]>([]);
const loading = ref<boolean>(false);

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    urls: resultData.value.selfHomeworkFiles.map(item => item.fileUrl || '') || [],
    current: resultData.value.selfHomeworkFiles[index]?.fileUrl || '',
  });
};

// 获取作业详情数据
const fetchHomeworkData = async () => {
  if (!props.homeworkId) {
    console.error('作业ID不能为空');
    return;
  }

  try {
    loading.value = true;
    uni.showLoading({ title: '加载中...' });

    console.log('获取作业ID:', props.homeworkId);
    const result = await getSelfHomeworkDetail({ id: props.homeworkId });

    if (result) {
      resultData.value = result;
      try {
        const arr = JSON.parse(result.detailedExplanation);
        detailedExplanationArray.value = arr;
      } catch (error) {
        console.log('解析详细解析失败:', error);
      }
    }
  } catch (error) {
    console.error('获取作业详情失败:', error);
    uni.showToast({ title: '获取作业详情失败', icon: 'none' });
  } finally {
    loading.value = false;
    uni.hideLoading();
  }
};

onMounted(() => {
  fetchHomeworkData();
});
</script>

<style lang="scss" scoped>
.score-self {
  background-color: transparent;
  padding-bottom: 40rpx;
  overflow-y: auto;
}

.score-content {
  padding: 0 32rpx;
}

// 原件预览区域
.original-section {
  padding-top: 20rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.9);
    // margin-bottom: 20rpx;
  }

  .images-scroll {
    width: 100%;
    white-space: nowrap;
  }

  .images-container {
    display: flex;
    flex-wrap: wrap;
    padding: 10rpx 0;
    gap: 10rpx;
    margin-top: 20rpx;
  }

  .image-item {
    width: 160rpx;
    height: 204rpx;
    margin-right: 14rpx;
    border-radius: 12rpx;
    overflow: hidden;
  }

  .preview-image {
    width: 100%;
    height: 100%;
  }
}

// 分隔线
.divider {
  height: 2rpx;
  background-color: #e7e7e7;
  margin: 40rpx 0;
}

// 区域标题
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  .section-indicator {
    width: 12rpx;
    height: 28rpx;
    background-color: #7d4dff;
    border-radius: 999rpx;
    margin-right: 20rpx;
  }

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.9);
  }
}

// 总体分析
.overall-analysis {
  background: linear-gradient(to bottom right, #e6d5ff, #d5f0ff);
  opacity: 0.8;
  border-radius: 12rpx;
  padding: 24rpx;
  min-height: 320rpx;
  margin-bottom: 40rpx;
  line-height: 50rpx;
}

// 详细解析
.detail-analysis {
  background-color: #f2f3f5;
  opacity: 0.8;
  border-radius: 12rpx;
  padding: 24rpx;
  min-height: 320rpx;
  line-height: 50rpx;

  // 排除最后一个元素的margin-bottom
  .detail-analysis-item {
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 加载状态
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: #86909c;
  font-size: 28rpx;
}

// 空状态
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: #1d2129;
  font-size: 28rpx;
  background-color: #f2f3f5;
  border-radius: 12rpx;
  margin-top: 20rpx;
}

// 空分析内容
.empty-analysis {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 24rpx;
  color: #1d2129;
  font-size: 28rpx;
}
</style>
