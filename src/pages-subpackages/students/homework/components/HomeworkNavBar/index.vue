<template>
  <view class="homework-navbar">
    <view class="detail-header" :class="{ extended: extended }" :style="headerStyle">
      <!--  顶部导航栏 -->
      <u-navbar
        class="navbar"
        :autoBack="autoBack"
        bgColor="#7d4dff"
        color="#fff"
        placeholder
        height="88rpx"
        @leftClick="onBackClick"
      >
        <template #left>
          <view class="navbar-left">
            <LkSvg src="/static/homework/back.svg" width="34rpx" height="34rpx" color="#FFFFFF" />
          </view>
        </template>
        <template #center>
          <view class="navbar-center">
            <view class="navbar-type">{{ subjectName }}</view>
            <text class="navbar-title">{{ homeworkTitle }}</text>
          </view>
        </template>
      </u-navbar>

      <!-- 扩展内容区域，可以在这里添加额外的内容 -->
      <view v-if="extended" class="extended-content">
        <slot name="extended"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import LkSvg from '@/components/svg/index.vue';

// 定义组件属性
const props = defineProps({
  // 科目名称
  subjectName: {
    type: String,
    default: '未知',
  },
  // 作业标题
  homeworkTitle: {
    type: String,
    default: '作业',
  },
  // 是否扩展背景
  extended: {
    type: Boolean,
    default: false,
  },
  // 扩展背景高度
  extendHeight: {
    type: Number,
    default: 150,
  },
  // 背景颜色
  backgroundColor: {
    type: String,
    default: '#7D4DFF',
  },
  autoBack: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['leftClick']);

// 计算头部样式
const headerStyle = computed(() => {
  return {
    backgroundColor: props.backgroundColor,
    height: props.extended
      ? `calc(${props.extendHeight}rpx + var(--status-bar-height) + 88rpx)`
      : 'auto',
  };
});

const onBackClick = () => {
  if (props.autoBack) {
    uni.navigateBack();
  } else {
    emits('leftClick');
  }
};
</script>

<style lang="scss" scoped>
.homework-navbar {
  width: 100%;
  position: relative;
}

.detail-header {
  position: relative;
  z-index: 1; // 降低z-index，让内容可以覆盖
  background-color: #7d4dff;
  transition: height 0.3s ease;
}

.detail-header.extended {
  padding-bottom: 20rpx;
}

.extended-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0 32rpx 20rpx;
}

.navbar {
  :deep(.u-navbar__content) {
    padding: 0;
    background-color: transparent !important;
  }

  :deep(.u-navbar__content__title) {
    padding: 0;
    width: 100%;
  }

  :deep(.u-navbar__content__left__text) {
    margin-left: 0;
  }

  :deep(.u-navbar__content__right__text) {
    color: #ffffff !important;
  }
}

.navbar-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.navbar-type {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 10rpx;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.12);
  padding: 4rpx 10rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  // 超出最大宽度显示省略号
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.navbar-left {
  :deep(.lk-svg) {
    filter: brightness(0) invert(1); // 强制将图标变成白色
  }
}
</style>
