<template>
  <MyPopup ref="popupRef" position="bottom" :showClose="true" title="选择学科" @confirm="confirm">
    <view class="filter-popup" :style="popupStyle">
      <!-- 可滚动内容区域 -->
      <scroll-view scroll-y class="filter-container">
        <!-- 学科筛选 -->
        <view class="filter-section">
          <uni-row :gutter="20">
            <uni-col :span="8" v-for="(item, index) in subjectOptions" :key="index">
              <view
                class="filter-option"
                :class="{
                  active: config.multiSelect
                    ? isSubjectSelected(item.value)
                    : subjectValue === item.value,
                }"
                @click="selectSubject(item.value)"
              >
                <text class="option-text">{{ item.label }}</text>
              </view>
            </uni-col>
          </uni-row>
        </view>
      </scroll-view>

      <!-- 固定在底部的按钮 -->
      <view class="filter-actions">
        <view class="reset-btn" @tap="resetFilters">重置</view>
        <view class="confirm-btn" @tap="confirmFilter">确定</view>
      </view>
    </view>
  </MyPopup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import MyPopup from '@/components/MyPopup';
import { getHomeworkSubjects } from '@/api/students/homework';

export type SelectedOptionItem = string | number;
export interface OptionItem {
  label: string;
  value: string | number;
}

// 配置
const config = reactive<{
  multiSelect: boolean;
  showAll: boolean;
}>({
  multiSelect: false, // 默认单选
  showAll: true, // 默认显示"全部"选项
});

// 弹窗样式
const popupStyle = computed(() => {
  return {
    height: 'auto',
    maxHeight: '80vh',
  };
});

// 学科选项，从接口获取 - 根据配置初始化
const subjectOptions = ref<OptionItem[]>(
  config.showAll !== false ? [{ label: '全部学科', value: '' }] : []
);

// 定义事件
const emit = defineEmits(['confirm']);

// 弹窗引用
const popupRef = ref();

// 筛选值 - 根据配置决定是单选还是多选
const subjectValue = ref<SelectedOptionItem>(''); // 单选值
const subjectMultiValue = ref<SelectedOptionItem[]>([]); // 多选值

// 加载状态
const loading = ref(false);

// 获取学科数据
const fetchSubjects = async () => {
  if (loading.value) return;

  try {
    loading.value = true;
    const data = await getHomeworkSubjects();
    // 将接口数据转换为选项格式
    const options = data.map(item => ({
      label: item.name,
      value: item.id,
    }));

    // 根据配置决定是否添加"全部"选项
    subjectOptions.value =
      config.showAll !== false ? [{ label: '全部学科', value: '' }, ...options] : [...options];
  } catch (error) {
    console.error('获取学科数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  // fetchSubjects();
});

// 检查学科是否被选中
const isSubjectSelected = (value: string | number): boolean => {
  if (config.multiSelect) {
    // 多选模式
    // 如果选中了"全部学科"，其他都不选中
    if (subjectMultiValue.value.includes('')) {
      return value == '';
    }
    // 使用some方法和==比较来检查是否包含值，解决类型不匹配问题
    return subjectMultiValue.value.some(item => item == value);
  } else {
    // 单选模式
    // 使用==比较，自动处理类型转换
    const isSelected = subjectValue.value == value;

    console.log(
      `检查选中状态: 当前值=${value}(${typeof value}), 选中值=${subjectValue.value}(${typeof subjectValue.value}), ==比较结果=${isSelected}`
    );

    return isSelected;
  }
};

// 选择学科
const selectSubject = (value: string | number) => {
  console.log('选择学科:', value, '类型:', typeof value);

  if (config.multiSelect) {
    // 多选模式
    // 如果选择的是"全部学科"且显示"全部"选项
    if (value === '' && config.showAll !== false) {
      // 如果当前不是"全部"，则设为"全部"
      if (!subjectMultiValue.value.includes('')) {
        subjectMultiValue.value = [''];
      } else {
        // 如果已经是"全部"，则不做任何变化
        return;
      }
    } else {
      // 如果当前已选中"全部"选项，需要先移除它
      if (subjectMultiValue.value.includes('')) {
        subjectMultiValue.value = [];
      }

      // 判断该选项是否已经选中
      // 使用==比较自动处理类型转换
      const index = subjectMultiValue.value.findIndex(item => item == value);

      if (index > -1) {
        // 已选中，则移除
        subjectMultiValue.value.splice(index, 1);
        // 如果移除后没有选中任何项且显示"全部"选项，默认选中"全部"
        if (subjectMultiValue.value.length === 0 && config.showAll !== false) {
          subjectMultiValue.value = [''];
        }
      } else {
        // 未选中，则添加
        subjectMultiValue.value.push(value);
      }
    }
  } else {
    // 单选模式
    console.log('单选模式设置值:', value);
    subjectValue.value = value;
  }
};

// 获取当前值（始终返回数组）
const getCurrentSubjectValue = (): SelectedOptionItem[] => {
  if (config.multiSelect) {
    // 如果是多选模式且只选了"全部"（即值为[""]），则返回空数组
    if (subjectMultiValue.value.length === 1 && subjectMultiValue.value[0] === '') {
      return [];
    }
    return [...subjectMultiValue.value];
  }
  // 单选模式也返回数组
  return subjectValue.value !== '' ? [subjectValue.value] : [];
};

// 重置筛选
const resetFilters = () => {
  if (config.multiSelect) {
    // 如果显示"全部"选项，则设置为['']，否则设置为[]
    subjectMultiValue.value = config.showAll !== false ? [''] : [];
  } else {
    subjectValue.value =
      config.showAll !== false
        ? ''
        : subjectOptions.value.length > 0
          ? subjectOptions.value[0].value
          : '';
  }
};

// 确认筛选
const confirmFilter = () => {
  // 获取当前值（始终是数组）
  const result = getCurrentSubjectValue();

  // 确保不传递代理对象
  emit('confirm', [...result]);
  popupRef.value.close();
};

// 确认按钮
const confirm = () => {
  confirmFilter();
};

// 打开弹窗方法，可选参数设置初始值和显示模式
const open = (options?: {
  /**
   * 学科
   */
  subject?: SelectedOptionItem | SelectedOptionItem[];
  /**
   * 是否多选
   */
  multiSelect?: boolean;
  /**
   * 是否显示全部
   */
  showAll?: boolean;
  /**
   * 默认值
   */
  defaultValues?: SelectedOptionItem[]; // 新增参数，用于初始化选中的学科，必须是数组
}) => {
  // 更新配置
  if (options?.multiSelect !== undefined) {
    config.multiSelect = options.multiSelect;
  }

  if (options?.showAll !== undefined) {
    config.showAll = options.showAll;

    // 重新初始化学科选项列表
    if (
      !config.showAll &&
      subjectOptions.value.length > 0 &&
      subjectOptions.value[0].value === ''
    ) {
      subjectOptions.value = subjectOptions.value.slice(1);
    } else if (
      config.showAll &&
      (subjectOptions.value.length === 0 || subjectOptions.value[0].value !== '')
    ) {
      subjectOptions.value = [{ label: '全部学科', value: '' }, ...subjectOptions.value];
    }
  }

  // 设置初始值 - 优先使用defaultValues
  if (options?.defaultValues && Array.isArray(options.defaultValues)) {
    console.log('PopupSubject接收到defaultValues:', options.defaultValues);

    // 使用defaultValues初始化选中值
    if (config.multiSelect) {
      // 多选模式 - 直接使用defaultValues
      subjectMultiValue.value =
        options.defaultValues.length > 0
          ? [...options.defaultValues]
          : config.showAll !== false
            ? ['']
            : [];
      console.log('多选模式设置值:', subjectMultiValue.value);
    } else {
      // 单选模式 - 取第一个值
      const firstValue =
        options.defaultValues.length > 0
          ? options.defaultValues[0]
          : config.showAll !== false
            ? ''
            : subjectOptions.value.length > 0
              ? subjectOptions.value[0].value
              : '';

      console.log('单选模式设置值:', firstValue, '类型:', typeof firstValue);
      subjectValue.value = firstValue;
    }
  } else if (options?.subject !== undefined) {
    // 兼容旧的subject参数
    if (config.multiSelect) {
      // 多选模式
      // 如果传入值为空且开启了全部选项，则默认选中全部
      const valueArray = Array.isArray(options.subject) ? [...options.subject] : [options.subject];
      subjectMultiValue.value =
        valueArray.length === 0 && config.showAll !== false ? [''] : valueArray;
    } else {
      // 单选模式
      // 如果传入值为空且开启了全部选项，则默认选中全部
      const value = Array.isArray(options.subject)
        ? options.subject.length > 0
          ? options.subject[0]
          : ''
        : options.subject;
      subjectValue.value = value === '' && config.showAll !== false ? '' : value;
    }
  } else {
    // 设置默认值
    if (config.multiSelect) {
      // 如果开启了全部选项，默认选中全部，否则不选择
      subjectMultiValue.value = config.showAll !== false ? [''] : [];
    } else {
      // 如果开启了全部选项，默认选中全部，否则选择第一项
      subjectValue.value =
        config.showAll !== false
          ? ''
          : subjectOptions.value.length > 0
            ? subjectOptions.value[0].value
            : '';
    }
  }

  // 每次打开弹窗时检查并更新数据
  if (subjectOptions.value.length <= 1) {
    fetchSubjects();
  }

  popupRef.value.open();
};

// 获取指定值对应的标签文本
const getLabelByValue = (value: SelectedOptionItem | SelectedOptionItem[]): string => {
  // 如果是多选模式且值为数组
  if (config.multiSelect && Array.isArray(value)) {
    // 如果值为空或只有一个空字符串（表示"全部"）
    if (value.length === 0 || (value.length === 1 && value[0] == '')) {
      return '全部学科';
    }

    // 如果选中的项不超过2个，显示具体名称
    if (value.length <= 2) {
      return value
        .map(v => {
          const option = subjectOptions.value.find(item => item.value == v);
          return option ? option.label : '';
        })
        .filter(Boolean)
        .join('、');
    } else {
      // 超过2个则显示选中数量
      return `已选${value.length}个学科`;
    }
  } else {
    // 单选模式或传入的值不是数组
    const defaultLabel = '全部学科';

    // 如果是数组但我们处于单选模式，取第一个元素
    const actualValue = Array.isArray(value) ? (value.length > 0 ? value[0] : '') : value;
    const option = subjectOptions.value.find(item => item.value == actualValue);
    return option ? option.label : defaultLabel;
  }
};

// 暴露方法
defineExpose({
  open,
  getLabelByValue,
});
</script>

<style lang="scss" scoped>
.filter-popup {
  position: relative;
  display: flex;
  flex-direction: column;
  height: auto;
  max-height: 80vh;
  min-height: 40vh;
  margin: 0 -30rpx;
}

.filter-container {
  flex: 1;
  padding: 0rpx 30rpx 0rpx;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 30rpx;
  padding: 10rpx 0;
}

:deep(.uni-row) {
  margin-bottom: 16rpx;
}

:deep(.uni-col) {
  padding-bottom: 16rpx;
}

.filter-option {
  height: 80rpx;
  width: 100%;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  background-color: #f5f5f5;
  border: 2rpx solid transparent;
  transition: all 0.2s;

  .option-text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 16rpx;
  }

  &.active {
    border: 2rpx solid #7d4dff;
    color: #7d4dff;
    background-color: #f2f3ff;
  }
}

.filter-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  gap: 24rpx;
  background-color: #fff;
  // box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-top: 1px solid #e7e7e7;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

.reset-btn {
  background-color: #fff;
  color: #4e5969;
  border: 1px solid #dcdcdc;
}

.confirm-btn {
  background-color: #7d4dff;
  color: #fff;
}
</style>
