<template>
  <view class="correction-tabs" v-if="tabs.length > 1">
    <scroll-view
      scroll-x
      :scroll-with-animation="true"
      show-scrollbar="false"
      :scroll-left="scrollLeft"
      class="correction-tabs-scroll"
    >
      <view
        v-for="(batch, index) in tabs"
        :key="batch.id"
        :id="`tab-${index}`"
        class="correction-tab"
        :class="{ active: currentIndex === index }"
        @tap="handleSwitch(index)"
      >
        <view class="correction-tab-text">{{ batch.name }}</view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch, nextTick, getCurrentInstance } from 'vue';

const props = defineProps({
  tabs: {
    type: Array as PropType<
      {
        id: string;
        name: string;
      }[]
    >,
    required: true,
    default: () => [],
  },
  currentIndex: {
    type: Number,
    required: true,
    default: 0,
  },
});

const scrollLeft = ref(0);
const instance = getCurrentInstance();

const emits = defineEmits<{
  (e: 'update:currentIndex', index: number): void;
  (e: 'change', index: number, tab: any): void;
}>();

function handleSwitch(index: number) {
  emits('update:currentIndex', index);
  emits('change', index, props.tabs[index]);
}

// 滚动吸附逻辑（基于真实scrollOffset，居中目标tab）
watch(
  () => props.currentIndex,
  async newIndex => {
    await nextTick();
    const idx = Number(newIndex);
    const query = uni.createSelectorQuery().in(instance?.proxy);
    query.select('.correction-tabs-scroll').boundingClientRect();
    // 读取当前scrollLeft，避免使用内部state导致误差
    (query as any).select('.correction-tabs-scroll').scrollOffset();
    query.select(`#tab-${idx}`).boundingClientRect();
    query.exec((res: any[]) => {
      const scrollViewRect = res?.[0];
      const scrollOffset = res?.[1];
      const tabRect = res?.[2];
      if (!scrollViewRect || !tabRect) return;
      const current =
        scrollOffset && typeof scrollOffset.scrollLeft === 'number' ? scrollOffset.scrollLeft : 0;
      const scrollViewWidth = scrollViewRect.width;
      const tabWidth = tabRect.width;
      const tabLeftInView = tabRect.left - scrollViewRect.left; // 目标tab相对scroll-view可视区域的左偏移
      const target = current + tabLeftInView - (scrollViewWidth - tabWidth) / 2;
      scrollLeft.value = Math.max(0, target);
    });
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.correction-tabs {
  display: flex;
  align-items: center;
  height: 86rpx;
  flex-shrink: 0;
  flex: 1;
  width: 100%;
  background: none;
  overflow-x: auto;
  white-space: nowrap;

  .correction-tab {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 32rpx 32rpx;
    font-size: 28rpx;
    height: 44rpx;
    line-height: 44rpx;
    border-radius: 999px;
    margin-right: 8rpx;
    cursor: pointer;
    color: #1d2129;
    background-color: #f3f3f3;
    font-weight: 400;
    min-width: 120rpx;
    box-sizing: border-box;

    &.active {
      background-color: #ffffff;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      background: linear-gradient(114deg, rgba(77, 163, 255, 0.1) 0%, rgba(125, 77, 255, 0.1) 100%);

      .correction-tab-text {
        background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 500;
      }
    }
  }
}
</style>
