<template>
  <MyPopup ref="popupRef" position="bottom" :showClose="true" title="选择学期" @confirm="confirm">
    <view class="filter-popup" :style="popupStyle">
      <!-- 可滚动内容区域 -->
      <scroll-view scroll-y class="filter-container">
        <!-- 学期筛选 - Picker-View方式 -->
        <view class="filter-section filter-section-semester">
          <view class="semester-picker-wrapper">
            <view class="semester-picker-indicator"></view>
            <picker-view
              class="semester-picker"
              :value="[semesterPickerIndex]"
              @change="handleSemesterPickerChange"
              :indicator-style="indicatorStyle"
              :mask-style="maskStyle"
            >
              <picker-view-column>
                <view
                  class="semester-picker-item"
                  v-for="(item, index) in semesterOptions"
                  :key="index"
                >
                  <text>{{ item.label }}</text>
                </view>
              </picker-view-column>
            </picker-view>
          </view>
        </view>
      </scroll-view>

      <!-- 固定在底部的按钮 -->
      <view class="filter-actions">
        <view class="reset-btn" @tap="resetFilters">重置</view>
        <view class="confirm-btn" @tap="confirmFilter">确定</view>
      </view>
    </view>
  </MyPopup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import MyPopup from '@/components/MyPopup';
import { getHomeworkSemesters } from '@/api/students/homework';
import { numberToChinese } from '../../utils';

export type SelectedOptionItem = string | number;
export interface OptionItem {
  label: string;
  value: string | number;
}

interface MultiSelectConfig {
  multiSelect: boolean;
  showAll?: boolean; // 是否显示"全部"选项
}

// 多选配置，学期固定为单选，默认显示"全部"选项
const config = reactive<{
  multiSelect: boolean;
  showAll: boolean;
}>({
  multiSelect: false, // 学期固定为单选
  showAll: true,
});

// 弹窗样式
const popupStyle = computed(() => {
  return {
    height: 'auto',
    maxHeight: '80vh',
  };
});

// 学期选项，从接口获取 - 根据配置初始化
const semesterOptions = ref<OptionItem[]>(
  config.showAll !== false ? [{ label: '全部学期', value: '' }] : []
);

// 默认初始值
const defaultSemester = '';

// 定义事件
const emit = defineEmits(['confirm']);

// 弹窗引用
const popupRef = ref();

// 筛选值 - 单选
const semesterValue = ref<SelectedOptionItem>(''); // 默认单选

// picker-view相关变量
const semesterPickerIndex = ref(0);
const indicatorStyle = 'height: 80rpx; line-height: 80rpx;';
const maskStyle =
  'background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4)), linear-gradient(0deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4));';

// 加载状态
const loading = ref(false);

// 获取学期数据
const fetchSemesters = async () => {
  if (loading.value) return Promise.resolve();

  try {
    loading.value = true;
    const data = await getHomeworkSemesters();

    // 将接口数据转换为选项格式
    const options = data.map(item => ({
      label: `${item.year} 第${numberToChinese(item.type!)}学期`,
      value: item.id,
    }));

    // 根据配置决定是否添加"全部"选项
    semesterOptions.value =
      config.showAll !== false ? [{ label: '全部学期', value: '' }, ...options] : [...options];

    return Promise.resolve();
  } catch (error) {
    console.error('获取学期数据失败:', error);
    uni.showToast({
      title: '获取学期数据失败',
      icon: 'none',
    });
    return Promise.resolve();
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchSemesters().then(() => {
    // 获取学期数据后更新picker索引
    updateSemesterPickerIndex(semesterValue.value);
  });
});

// 处理学期Picker-View变化事件
const handleSemesterPickerChange = (e: any) => {
  const index = e.detail.value[0];
  if (index >= 0 && index < semesterOptions.value.length) {
    // 更新选中索引
    semesterPickerIndex.value = index;
    // 获取对应的值并设置
    const selectedValue = semesterOptions.value[index].value;
    semesterValue.value = selectedValue;
    console.log('学期Picker选择:', {
      index,
      value: selectedValue,
      label: semesterOptions.value[index].label,
    });
  }
};

// 根据选中的值更新picker-view的索引
const updateSemesterPickerIndex = (value: SelectedOptionItem) => {
  // 查找值对应的索引，使用==比较自动处理类型转换
  const index = semesterOptions.value.findIndex(item => item.value == value);
  if (index !== -1) {
    semesterPickerIndex.value = index;
  } else if (config.showAll !== false) {
    // 如果找不到对应值且开启了"全部"选项，默认选择"全部学期"
    const allSemesterIndex = semesterOptions.value.findIndex(item => item.value == '');
    if (allSemesterIndex !== -1) {
      semesterPickerIndex.value = allSemesterIndex;
    } else {
      semesterPickerIndex.value = 0;
    }
  } else {
    // 如果找不到对应值且没开启"全部"选项，默认选择第一个
    semesterPickerIndex.value = 0;
  }
};

// 重置筛选
const resetFilters = () => {
  // 如果开启了全部选项，则设置为''，否则设置为第一项
  semesterValue.value =
    config.showAll !== false
      ? ''
      : semesterOptions.value.length > 0
        ? semesterOptions.value[0].value
        : '';

  // 重置学期picker到"全部学期"位置
  if (config.showAll !== false) {
    // 查找"全部学期"的索引（通常是0）
    const allSemesterIndex = semesterOptions.value.findIndex(item => item.value === '');
    if (allSemesterIndex !== -1) {
      semesterPickerIndex.value = allSemesterIndex;
    } else {
      semesterPickerIndex.value = 0;
    }
  }
};

// 确认筛选
const confirmFilter = () => {
  // 获取当前值，始终返回数组
  const result = semesterValue.value !== '' ? [semesterValue.value] : [];

  emit('confirm', result);
  popupRef.value.close();
};

// 确认按钮
const confirm = () => {
  confirmFilter();
};

// 打开弹窗方法，可选参数设置初始值和显示模式
const open = (options?: { semester?: SelectedOptionItem; showAll?: boolean }) => {
  // 更新配置
  if (options?.showAll !== undefined) {
    config.showAll = options.showAll;

    // 重新初始化学期选项列表
    if (
      !config.showAll &&
      semesterOptions.value.length > 0 &&
      semesterOptions.value[0].value === ''
    ) {
      semesterOptions.value = semesterOptions.value.slice(1);
    } else if (
      config.showAll &&
      (semesterOptions.value.length === 0 || semesterOptions.value[0].value !== '')
    ) {
      semesterOptions.value = [{ label: '全部学期', value: '' }, ...semesterOptions.value];
    }
  }

  // 设置初始值
  if (options?.semester !== undefined) {
    semesterValue.value = options.semester;
    // 根据选中的值设置picker-view的初始索引
    updateSemesterPickerIndex(semesterValue.value);
  } else {
    // 设置默认值
    // 如果开启了全部选项，默认选中全部，否则选第一项
    semesterValue.value =
      config.showAll !== false
        ? ''
        : semesterOptions.value.length > 0
          ? semesterOptions.value[0].value
          : '';

    // 根据默认值设置picker-view的初始索引
    updateSemesterPickerIndex(semesterValue.value);
  }

  // 每次打开弹窗时检查并更新数据
  if (semesterOptions.value.length <= 1) {
    fetchSemesters();
  }

  popupRef.value.open();
};

// 获取指定值对应的标签文本
const getLabelByValue = (value: SelectedOptionItem): string => {
  const defaultLabel = '全部学期';
  const option = semesterOptions.value.find(item => item.value === value);
  return option ? option.label : defaultLabel;
};

// 暴露方法
defineExpose({
  open,
  getLabelByValue,
});
</script>

<style lang="scss" scoped>
.filter-popup {
  position: relative;
  display: flex;
  flex-direction: column;
  height: auto;
  max-height: 80vh;
  min-height: 40vh;
  margin: 0 -30rpx;
}

.filter-container {
  flex: 1;
  padding: 0rpx 30rpx 0rpx;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 30rpx;
  padding: 10rpx 0;
}

// picker-view样式
.semester-picker-wrapper {
  position: relative;
  height: 300rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 12rpx;
}

.semester-picker {
  width: 100%;
  height: 100%;
}

.semester-picker-indicator {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 80rpx;
  width: 100%;
  background-color: rgba(174, 174, 174, 0.1);
  z-index: 1;
  pointer-events: none;
}

.semester-picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.filter-section-semester {
  margin-bottom: 40rpx;
}

:deep(.uni-picker-view-indicator) {
  font-weight: bold;

  &::after {
    border: none;
  }

  &::before {
    border: none;
  }
}

.filter-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  gap: 24rpx;
  background-color: #fff;
  // box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-top: 1px solid #e7e7e7;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

.reset-btn {
  background-color: #fff;
  color: #4e5969;
  border: 1px solid #dcdcdc;
}

.confirm-btn {
  background-color: #7d4dff;
  color: #fff;
}
</style>
