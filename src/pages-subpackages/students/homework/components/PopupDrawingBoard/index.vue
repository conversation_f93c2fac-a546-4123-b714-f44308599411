<template>
  <uni-popup
    ref="popupRef"
    type="bottom"
    :mask-click="false"
    background-color="#fff"
    @change="handleChange"
  >
    <view class="popup-container">
      <!-- 添加一个顶部安全区域占位 -->
      <view class="safe-area-top" :style="{ height: safeAreaHeight + 'px' }"></view>
      <!-- 内容区域 -->
      <view class="popup-drawing-board">
        <MyDrawingBoard
          :debug="false"
          custom-width="100%"
          :custom-height="drawingBoardHeight"
          v-bind="computedDrawingBoardProps"
          @save="handleSave"
          @close="handleClose"
        >
          <template #area1>
            <!-- 作业题目区域 -->
            <view class="question-container" :style="{ height: `${currentQuestionHeight}rpx` }">
              <!-- 题目内容区域 -->
              <scroll-view class="popup-question-area" scroll-y>
                <!-- 只使用标准的QuestionTitle组件展示题目标题(富文本已包含子标题) -->
                <view class="question-title-container" v-if="currentQuestion">
                  <QuestionTitle
                    :show-index="false"
                    :question-id="props.questionId"
                    :question="currentQuestion"
                  />
                </view>
                <view class="no-question-info" v-if="!currentQuestion">
                  <text>题目区域</text>
                </view>
              </scroll-view>

              <!-- 拖动手柄 - 直接拖动 -->
              <view
                class="drag-handle"
                @touchstart.stop.prevent="handleTouchStart"
                @touchmove.stop.prevent="handleTouchMove"
                @touchend.stop.prevent="handleTouchEnd"
              >
                <view class="drag-indicator">
                  <view class="drag-bar"></view>
                </view>
              </view>
            </view>
          </template>
        </MyDrawingBoard>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import {
  ref,
  defineExpose,
  defineProps,
  defineEmits,
  onMounted,
  computed,
  type PropType,
} from 'vue';
import MyDrawingBoard from '@/components/MyDrawingBoard';
import { useHomeworkStore } from '@/store/homeworkStore';
import { QuestionTitle } from '@/pages-subpackages/students/homework/components/Question/components';
import { getBaseUrl } from '@/common/ai/url';
import { isEmptyValue } from '../../utils';
import { getTopSafeAreaHeight } from '@/utils/common';

const props = defineProps({
  modelValue: Boolean,
  // 透传给MyDrawingBoard的props
  drawingBoardProps: {
    type: Object,
    default: () => ({}),
  },
  // 当前题目ID
  questionId: {
    type: String,
    default: '',
  },
  // 子题目ID（如果有）
  subQuestionId: {
    type: String,
    default: '',
  },
  controller: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:modelValue', 'close', 'save', 'cancel']);
const popupRef = ref();
const defaultStore = useHomeworkStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;
// 添加安全区域高度管理
const safeAreaHeight = ref();

// 获取当前题目信息
const currentQuestion = computed(() => {
  // 如果有questionId，则查找对应题目
  if (props.questionId) {
    // 先在主题目中查找
    const mainQuestion = controller.questions.find((q: any) => q.id === props.questionId);
    if (mainQuestion) return mainQuestion;

    // 如果没找到，可能是子题目
    for (const question of controller.questions) {
      if (question.subQuestions) {
        const subQuestion = question.subQuestions.find((sq: any) => sq.id === props.questionId);
        if (subQuestion) {
          // 将子题目格式化为符合QuestionTitle组件要求的格式
          return {
            ...subQuestion,
            type: question.type, // 继承父题目的类型
            index: subQuestion.index || '',
            title: subQuestion.title || subQuestion.content || '',
          };
        }
      }
    }
  }
  return null;
});

const questionHeight = ref(350); // 题目区域默认高度
const currentQuestionHeight = ref(questionHeight.value); // 当前实际高度
const minQuestionHeight = 300; // 最小高度改为300rpx
const maxQuestionHeight = 600; // 最大高度
const touchStartY = ref(0);
const startHeight = ref(0);

// 动态计算画板高度，保持不变
const drawingBoardHeight = ref(`calc(100%)`);
// const drawingBoardHeight = ref(`calc(${uni.getSystemInfoSync().windowHeight} - 100rpx)`);

// 获取初始绘图数据（如果有）
const initialDrawingData = computed(() => {
  const targetId = props.subQuestionId || props.questionId;
  if (!targetId) return null;
  const answer = controller.getUserAnswer(targetId);
  if (!answer?.answer || typeof answer.answer !== 'object') return null;
  if (answer.answer.drawing && answer.answer.drawing.data) {
    return answer.answer.drawing.data;
  }
  return null;
});

// 合并绘图属性，包括初始数据
const computedDrawingBoardProps = computed(() => {
  return {
    ...props.drawingBoardProps,
    data: initialDrawingData.value,
  };
});

// 获取子题目标题
const getSubQuestionTitle = () => {
  if (!props.questionId || !props.subQuestionId) return '';

  const parentQuestion = controller.questions.find((q: any) => q.id === props.questionId);
  if (!parentQuestion || !parentQuestion.subQuestions) return '';

  const subQuestion = parentQuestion.subQuestions.find((sq: any) => sq.id === props.subQuestionId);
  if (!subQuestion) return '';

  return subQuestion.title || subQuestion.content || '';
};

// 处理触摸开始
const handleTouchStart = (e: any) => {
  touchStartY.value = e.changedTouches[0].clientY;
  startHeight.value = currentQuestionHeight.value;
};

// 处理拖动过程
const handleTouchMove = (e: any) => {
  const currentY = e.changedTouches[0].clientY;
  // 修改计算方式：currentY - touchStartY 表示手指移动的方向
  // 正值表示向下移动（扩大），负值表示向上移动（缩小）
  const deltaY = currentY - touchStartY.value;

  // 计算新高度
  let newHeight = startHeight.value + deltaY;

  // 限制高度范围
  if (newHeight < minQuestionHeight) newHeight = minQuestionHeight;
  if (newHeight > maxQuestionHeight) newHeight = maxQuestionHeight;

  currentQuestionHeight.value = newHeight;
};

// 处理拖动结束
const handleTouchEnd = () => {
  // 拖动结束后可以添加其他逻辑
};

onMounted(() => {
  safeAreaHeight.value = getTopSafeAreaHeight();
});

// 处理popup状态变化
const handleChange = (e: { show: boolean }) => {
  if (!e.show) {
    emit('update:modelValue', false);
    emit('close');
  }
};

// 打开弹窗
const open = () => {
  popupRef.value?.open('bottom');
};

// 关闭弹窗
const close = () => {
  popupRef.value?.close();
  emit('update:modelValue', false);
  emit('close');
};

const handleClose = () => {
  close();
};

const handleSave = (data: any) => {
  // 只用 questionId（优先 subQuestionId，其次 questionId）
  const targetId = props.subQuestionId || props.questionId;
  if (!targetId) return;

  // 获取当前答案
  const existingAnswer = controller.getUserAnswer(targetId)?.answer || {};

  // 判断 drawingdata 的数据是否一致，如果一致不上传
  if (JSON.stringify(existingAnswer?.drawing?.data) === JSON.stringify(data.data)) {
    close();
  } else {
    // 合并数据
    const mergedData = {
      ...existingAnswer,
      files: Array.isArray(existingAnswer.files) ? existingAnswer.files : [],
    };

    if (!mergedData.drawing) {
      mergedData.drawing = {
        imageUrl: '',
        data: null,
      };
    }

    // 如果图片为空，则清空数据
    if (!data.imageUrl) {
      mergedData.drawing.imageUrl = '';
      mergedData.drawing.data = null;
      mergedData.drawing.imageFile = null;
      // 递归更新
      controller.setUserAnswer(targetId, mergedData);
      emit('save', mergedData);
      close();
      return;
    }

    // 上传图片到服务器
    const baseUrl = getBaseUrl();
    const fileName = `drawing_${Date.now()}.png`;
    debugger;
    uni.uploadFile({
      url: `${baseUrl}/huayun-ai/system/file/upload?filename=${fileName}`,
      filePath: data.imageUrl,
      name: 'file',
      header: {
        Authorization: uni.getStorageSync('token'),
      },
      formData: {},
      success: (uploadRes: any) => {
        const uploadData =
          typeof uploadRes.data === 'string' ? JSON.parse(uploadRes.data) : uploadRes.data;
        const fileData = uploadData.data;

        mergedData.drawing.imageFile = fileData;
        mergedData.drawing.imageUrl = fileData.fileUrl;
        mergedData.drawing.data = data.data;

        // 递归更新
        controller.setUserAnswer(targetId, mergedData);

        emit('save', mergedData);
        close();
      },
      fail: (err: any) => {
        console.error('上传失败:', err);
        uni.showToast({
          title: '上传失败',
          icon: 'none',
        });
      },
    });
  }
};

defineExpose({ open, close });

// 支持v-model
if (props.modelValue) {
  open();
}
</script>

<style lang="scss" scoped>
:deep(.uni-popup) {
}

.popup-container {
  width: 100vw;
  height: calc(100vh);
  background-color: #f7f8fc;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.popup-drawing-board {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.question-container {
  position: absolute;
  // position: relative;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background: #f7f8fc;
  display: flex;
  flex-direction: column;
}

.popup-question-area {
  width: 100%;
  flex: 1;
  background: #f7f8fc;
  padding: 20rpx;
  box-sizing: border-box;
  height: 100%;
  box-shadow: 0 8px 30px -10px rgba(0, 0, 0, 0.5);

  .question-title-container {
    width: 100%;
  }

  .no-question-info {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 32rpx;
  }
}

.drag-handle {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%) translateY(100%);
  width: 160rpx;
  height: 50rpx;

  display: inline-flex;
  justify-content: center;
  align-items: center;
  // background-color: #fff;
  // border-bottom: 1px solid #f5f5f5;

  .drag-indicator {
    // position: absolute;
    // top: 0;
    // left: 50%;
    // transform: translateX(-50%);
    width: 160rpx;
    height: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    // 绘制图形
    background-color: #f7f8fc;
    border-bottom-left-radius: 24rpx;
    border-bottom-right-radius: 24rpx;

    box-shadow: 0 12px 15px -7px rgba(0, 0, 0, 0.5);
  }

  .drag-bar {
    width: 70rpx;
    height: 8rpx;
    background-color: #ddd;
    border-radius: 3rpx;
  }
}
</style>
