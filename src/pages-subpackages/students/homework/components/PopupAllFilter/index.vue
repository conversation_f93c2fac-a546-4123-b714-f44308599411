<template>
  <MyPopup
    ref="popupRef"
    position="bottom"
    :showClose="true"
    :title="currentTitle"
    @confirm="confirm"
  >
    <view class="filter-popup" :style="popupStyle">
      <!-- 可滚动内容区域 -->
      <scroll-view scroll-y class="filter-container">
        <!-- 学期筛选 - Picker-View方式 -->
        <view class="filter-section filter-section-semester" v-if="showSemester">
          <!-- <view class="filter-title">
            学期
          </view> -->
          <view class="semester-picker-wrapper">
            <view class="semester-picker-indicator"></view>
            <picker-view
              class="semester-picker"
              :value="[semesterPickerIndex]"
              @change="handleSemesterPickerChange"
              :indicator-style="indicatorStyle"
              :mask-style="maskStyle"
            >
              <picker-view-column>
                <view
                  class="semester-picker-item"
                  v-for="(item, index) in semesterOptions"
                  :key="index"
                >
                  <text>{{ item.label }}</text>
                </view>
              </picker-view-column>
            </picker-view>
          </view>
        </view>

        <!-- 学科筛选 -->
        <view class="filter-section filter-section-subject" v-if="showSubject">
          <!-- <view class="filter-title">
            学科
            <text v-if="config.subject?.multiSelect" class="multi-select-text">(可多选)</text>
          </view> -->
          <uni-row :gutter="20">
            <uni-col :span="8" v-for="(item, index) in subjectOptions" :key="index">
              <view
                class="filter-option"
                :class="{
                  active: config.subject?.multiSelect
                    ? isSubjectSelected(item.value)
                    : subjectValue === item.value,
                }"
                @click="selectSubject(item.value)"
              >
                <text class="option-text">{{ item.label }}</text>
              </view>
            </uni-col>
          </uni-row>
        </view>

        <!-- 类型筛选 -->
        <view class="filter-section filter-section-type" v-if="showType">
          <!-- <view class="filter-title">
              类型
              <text v-if="config.type?.multiSelect" class="multi-select-text">(可多选)</text>
            </view> -->
          <uni-row :gutter="20">
            <uni-col :span="8" v-for="(item, index) in typeOptions" :key="index">
              <view
                class="filter-option"
                :class="{
                  active: config.type?.multiSelect
                    ? isTypeSelected(item.value)
                    : typeValue === item.value,
                }"
                @click="selectType(item.value)"
              >
                <text class="option-text">{{ item.label }}</text>
              </view>
            </uni-col>
          </uni-row>
        </view>
      </scroll-view>

      <!-- 固定在底部的按钮 -->
      <view class="filter-actions">
        <view class="reset-btn" @tap="resetFilters">重置</view>
        <view class="confirm-btn" @tap="confirmFilter">确定</view>
      </view>
    </view>
  </MyPopup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import MyPopup from '@/components/MyPopup';
import {
  getHomeworkSemesters,
  getHomeworkSubjects,
  getHomeworkTypeList,
} from '@/api/students/homework';
import type {
  HomeworkSemesterItem,
  HomeworkSubjectItem,
  HomeworkTypeItem,
} from '@/types/students/homework';
import { numberToChinese } from '../../utils';

export type SelectedOptionItem = string | number;
export interface OptionItem {
  label: string;
  value: string | number;
}

interface MultiSelectConfig {
  multiSelect: boolean;
  showAll?: boolean; // 是否显示"全部"选项
}

interface FilterConfig {
  semester?: MultiSelectConfig;
  subject?: MultiSelectConfig;
  type?: MultiSelectConfig;
}

// 显示控制
const showSemester = ref(true);
const showSubject = ref(true);
const showType = ref(true);

// 当前弹窗模式
const currentMode = ref('all');

// 多选配置，学期固定为单选，其他多选，默认都显示"全部"选项
const config = reactive<FilterConfig>({
  semester: { multiSelect: false, showAll: true }, // 学期固定为单选
  subject: { multiSelect: true, showAll: true },
  type: { multiSelect: true, showAll: true },
});

// 弹窗标题
const currentTitle = computed(() => {
  if (currentMode.value === 'subject') return '选择学科';
  if (currentMode.value === 'semester') return '选择学期';
  if (currentMode.value === 'type') return '选择类型';
  return '筛选';
});

// 简化的弹窗样式
const popupStyle = computed(() => {
  return {
    height: 'auto',
    maxHeight: '80vh',
  };
});

// 学期选项，从接口获取 - 根据配置初始化
const semesterOptions = ref<OptionItem[]>(
  config.semester?.showAll !== false ? [{ label: '全部学期', value: '' }] : []
);

// 学科选项，从接口获取 - 根据配置初始化
const subjectOptions = ref<OptionItem[]>(
  config.subject?.showAll !== false ? [{ label: '全部学科', value: '' }] : []
);

// 类型选项 - 根据配置初始化
const typeOptions = ref<OptionItem[]>(
  config.type?.showAll !== false ? [{ label: '全部类型', value: '' }] : []
);

// 默认初始值
const defaultSemester = '';
const defaultSemesterMulti: SelectedOptionItem[] = [];
const defaultSubject: SelectedOptionItem[] = [];
const defaultType: SelectedOptionItem[] = [];

// 定义事件
const emit = defineEmits(['confirm']);

// 弹窗引用
const popupRef = ref();

// 筛选值 - 根据配置决定是单选还是多选
const semesterValue = ref<SelectedOptionItem>(''); // 默认单选
const semesterMultiValue = ref<SelectedOptionItem[]>([]); // 多选值
const subjectValue = ref<SelectedOptionItem>(''); // 单选值
const subjectMultiValue = ref<SelectedOptionItem[]>([]); // 默认多选
const typeValue = ref<SelectedOptionItem>(''); // 单选值
const typeMultiValue = ref<SelectedOptionItem[]>([]); // 默认多选

// picker-view相关变量
const semesterPickerIndex = ref(0);
const indicatorStyle = 'height: 80rpx; line-height: 80rpx;';
const maskStyle =
  'background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4)), linear-gradient(0deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4));';

// 加载状态
const loading = ref({
  semester: false,
  subject: false,
  type: false,
});

// 获取学期数据
const fetchSemesters = async () => {
  if (loading.value.semester) return Promise.resolve();

  try {
    loading.value.semester = true;
    const data = await getHomeworkSemesters();

    // 将接口数据转换为选项格式
    const options = data.map(item => ({
      label: `${item.year} 第${numberToChinese(item.type!)}学期`,
      value: item.id,
    }));

    // 根据配置决定是否添加"全部"选项
    semesterOptions.value =
      config.semester?.showAll !== false
        ? [{ label: '全部学期', value: '' }, ...options]
        : [...options];

    return Promise.resolve();
  } catch (error) {
    console.error('获取学期数据失败:', error);
    uni.showToast({
      title: '获取学期数据失败',
      icon: 'none',
    });
    return Promise.resolve();
  } finally {
    loading.value.semester = false;
  }
};

// 获取学科数据
const fetchSubjects = async () => {
  if (loading.value.subject) return;

  try {
    loading.value.subject = true;
    const data = await getHomeworkSubjects();
    // 将接口数据转换为选项格式
    const options = data.map(item => ({
      label: item.name,
      value: item.id,
    }));

    // 根据配置决定是否添加"全部"选项
    subjectOptions.value =
      config.subject?.showAll !== false
        ? [{ label: '全部学科', value: '' }, ...options]
        : [...options];
  } catch (error) {
    console.error('获取学科数据失败:', error);
  } finally {
    loading.value.subject = false;
  }
};

// 获取类型数据
const fetchTypes = async () => {
  if (loading.value.type) return;

  try {
    loading.value.type = true;
    const data = await getHomeworkTypeList();

    // 将接口数据转换为选项格式
    const options = data.map(item => ({
      label: item.alias,
      value: item.id,
    }));

    // 根据配置决定是否添加"全部"选项
    typeOptions.value =
      config.type?.showAll !== false
        ? [{ label: '全部类型', value: '' }, ...options]
        : [...options];
  } catch (error) {
    console.error('获取类型数据失败:', error);
  } finally {
    loading.value.type = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchSemesters().then(() => {
    // 获取学期数据后更新picker索引
    updateSemesterPickerIndex(semesterValue.value);
  });
  fetchSubjects();
  fetchTypes();
});

// 检查学期是否被选中（多选模式）
const isSemesterSelected = (value: string | number): boolean => {
  // 如果选中了"全部学期"，其他都不选中
  if (semesterMultiValue.value.includes('')) {
    return value === '';
  }
  return semesterMultiValue.value.includes(value);
};

// 处理学期Picker-View变化事件
const handleSemesterPickerChange = (e: any) => {
  const index = e.detail.value[0];
  if (index >= 0 && index < semesterOptions.value.length) {
    // 更新选中索引
    semesterPickerIndex.value = index;
    // 获取对应的值并设置
    const selectedValue = semesterOptions.value[index].value;
    semesterValue.value = selectedValue;
    console.log('学期Picker选择:', {
      index,
      value: selectedValue,
      label: semesterOptions.value[index].label,
    });
  }
};

// 根据选中的值更新picker-view的索引
const updateSemesterPickerIndex = (value: SelectedOptionItem) => {
  // 查找值对应的索引
  const index = semesterOptions.value.findIndex(item => item.value === value);
  if (index !== -1) {
    semesterPickerIndex.value = index;
  } else if (config.semester?.showAll !== false) {
    // 如果找不到对应值且开启了"全部"选项，默认选择"全部学期"
    const allSemesterIndex = semesterOptions.value.findIndex(item => item.value === '');
    if (allSemesterIndex !== -1) {
      semesterPickerIndex.value = allSemesterIndex;
    } else {
      semesterPickerIndex.value = 0;
    }
  } else {
    // 如果找不到对应值且没开启"全部"选项，默认选择第一个
    semesterPickerIndex.value = 0;
  }
};

// 选择学期
const selectSemester = (value: string | number) => {
  // 根据配置决定是单选还是多选
  if (config.semester?.multiSelect) {
    // 多选模式
    // 如果选择的是"全部学期"且显示"全部"选项
    if (value === '' && config.semester?.showAll !== false) {
      // 如果当前不是"全部"，则设为"全部"
      if (!semesterMultiValue.value.includes('')) {
        semesterMultiValue.value = [''];
      } else {
        // 如果已经是"全部"，则不做任何变化
        return;
      }
    } else {
      // 如果当前已选中"全部"选项，需要先移除它
      if (semesterMultiValue.value.includes('')) {
        semesterMultiValue.value = [];
      }

      // 判断该选项是否已经选中
      const index = semesterMultiValue.value.indexOf(value);
      if (index > -1) {
        // 已选中，则移除
        semesterMultiValue.value.splice(index, 1);
        // 如果移除后没有选中任何项且显示"全部"选项，默认选中"全部"
        if (semesterMultiValue.value.length === 0 && config.semester?.showAll !== false) {
          semesterMultiValue.value = [''];
        }
      } else {
        // 未选中，则添加
        semesterMultiValue.value.push(value);
      }
    }
  } else {
    // 单选模式
    semesterValue.value = value;
  }
};

// 检查学科是否被选中
const isSubjectSelected = (value: string | number): boolean => {
  if (config.subject?.multiSelect) {
    // 多选模式
    // 如果选中了"全部学科"，其他都不选中
    if (subjectMultiValue.value.includes('')) {
      return value === '';
    }
    return subjectMultiValue.value.includes(value);
  } else {
    // 单选模式
    return subjectValue.value === value;
  }
};

// 选择学科
const selectSubject = (value: string | number) => {
  if (config.subject?.multiSelect) {
    // 多选模式
    // 如果选择的是"全部学科"且显示"全部"选项
    if (value === '' && config.subject?.showAll !== false) {
      // 如果当前不是"全部"，则设为"全部"
      if (!subjectMultiValue.value.includes('')) {
        subjectMultiValue.value = [''];
      } else {
        // 如果已经是"全部"，则不做任何变化
        return;
      }
    } else {
      // 如果当前已选中"全部"选项，需要先移除它
      if (subjectMultiValue.value.includes('')) {
        subjectMultiValue.value = [];
      }

      // 判断该选项是否已经选中
      const index = subjectMultiValue.value.indexOf(value);
      if (index > -1) {
        // 已选中，则移除
        subjectMultiValue.value.splice(index, 1);
        // 如果移除后没有选中任何项且显示"全部"选项，默认选中"全部"
        if (subjectMultiValue.value.length === 0 && config.subject?.showAll !== false) {
          subjectMultiValue.value = [''];
        }
      } else {
        // 未选中，则添加
        subjectMultiValue.value.push(value);
      }
    }
  } else {
    // 单选模式
    subjectValue.value = value;
  }
};

// 检查类型是否被选中
const isTypeSelected = (value: string | number): boolean => {
  if (config.type?.multiSelect) {
    // 多选模式
    // 如果选中了"全部类型"，其他都不选中
    if (typeMultiValue.value.includes('')) {
      return value === '';
    }
    return typeMultiValue.value.includes(value);
  } else {
    // 单选模式
    return typeValue.value === value;
  }
};

// 选择类型
const selectType = (value: string | number) => {
  if (config.type?.multiSelect) {
    // 多选模式
    // 如果选择的是"全部类型"且显示"全部"选项
    if (value === '' && config.type?.showAll !== false) {
      // 如果当前不是"全部"，则设为"全部"
      if (!typeMultiValue.value.includes('')) {
        typeMultiValue.value = [''];
      } else {
        // 如果已经是"全部"，则不做任何变化
        return;
      }
    } else {
      // 如果当前已选中"全部"选项，需要先移除它
      if (typeMultiValue.value.includes('')) {
        typeMultiValue.value = [];
      }

      // 判断该选项是否已经选中
      const index = typeMultiValue.value.indexOf(value);
      if (index > -1) {
        // 已选中，则移除
        typeMultiValue.value.splice(index, 1);
        // 如果移除后没有选中任何项且显示"全部"选项，默认选中"全部"
        if (typeMultiValue.value.length === 0 && config.type?.showAll !== false) {
          typeMultiValue.value = [''];
        }
      } else {
        // 未选中，则添加
        typeMultiValue.value.push(value);
      }
    }
  } else {
    // 单选模式
    typeValue.value = value;
  }
};

// 获取当前值（根据是否多选返回不同类型的值）
const getCurrentSemesterValue = (): SelectedOptionItem | SelectedOptionItem[] => {
  if (config.semester?.multiSelect) {
    // 如果是多选模式且只选了"全部"（即值为[""]），则返回空数组
    if (semesterMultiValue.value.length === 1 && semesterMultiValue.value[0] === '') {
      return [];
    }
    return semesterMultiValue.value;
  }
  return semesterValue.value;
};

const getCurrentSubjectValue = (): SelectedOptionItem | SelectedOptionItem[] => {
  if (config.subject?.multiSelect) {
    // 如果是多选模式且只选了"全部"（即值为[""]），则返回空数组
    if (subjectMultiValue.value.length === 1 && subjectMultiValue.value[0] === '') {
      return [];
    }
    return subjectMultiValue.value;
  }
  return subjectValue.value;
};

const getCurrentTypeValue = (): SelectedOptionItem | SelectedOptionItem[] => {
  if (config.type?.multiSelect) {
    // 如果是多选模式且只选了"全部"（即值为[""]），则返回空数组
    if (typeMultiValue.value.length === 1 && typeMultiValue.value[0] === '') {
      return [];
    }
    return typeMultiValue.value;
  }
  return typeValue.value;
};

// 重置筛选
const resetFilters = () => {
  // 根据配置重置值
  if (config.semester?.multiSelect) {
    // 如果显示"全部"选项，则设置为['']，否则设置为[]
    semesterMultiValue.value = config.semester?.showAll !== false ? [''] : [];
  } else {
    semesterValue.value =
      config.semester?.showAll !== false
        ? ''
        : semesterOptions.value.length > 0
          ? semesterOptions.value[0].value
          : '';

    // 重置学期picker到"全部学期"位置
    if (config.semester?.showAll !== false) {
      // 查找"全部学期"的索引（通常是0）
      const allSemesterIndex = semesterOptions.value.findIndex(item => item.value === '');
      if (allSemesterIndex !== -1) {
        semesterPickerIndex.value = allSemesterIndex;
      } else {
        semesterPickerIndex.value = 0;
      }
    }
  }

  if (config.subject?.multiSelect) {
    // 如果显示"全部"选项，则设置为['']，否则设置为[]
    subjectMultiValue.value = config.subject?.showAll !== false ? [''] : [];
  } else {
    subjectValue.value =
      config.subject?.showAll !== false
        ? ''
        : subjectOptions.value.length > 0
          ? subjectOptions.value[0].value
          : '';
  }

  if (config.type?.multiSelect) {
    // 如果显示"全部"选项，则设置为['']，否则设置为[]
    typeMultiValue.value = config.type?.showAll !== false ? [''] : [];
  } else {
    typeValue.value =
      config.type?.showAll !== false
        ? ''
        : typeOptions.value.length > 0
          ? typeOptions.value[0].value
          : '';
  }

  // 不关闭弹窗，让用户可以继续选择或点击确定
};

// 确认筛选
const confirmFilter = () => {
  // 获取当前值
  const semesterVal = getCurrentSemesterValue();
  const subjectVal = getCurrentSubjectValue();
  const typeVal = getCurrentTypeValue();

  // 确保不传递代理对象，而是传递原始值
  const result = {
    semester: semesterVal,
    // 如果是数组，创建一个新数组，确保是原始值
    subject: Array.isArray(subjectVal) ? [...subjectVal] : subjectVal,
    // 如果是数组，创建一个新数组，确保是原始值
    type: Array.isArray(typeVal) ? [...typeVal] : typeVal,
  };

  emit('confirm', result);
  popupRef.value.close();
};

// 确认按钮
const confirm = () => {
  confirmFilter();
};

// 打开弹窗方法，可选参数设置初始值和显示模式
const open = (options?: {
  semester?: SelectedOptionItem | SelectedOptionItem[];
  subject?: SelectedOptionItem | SelectedOptionItem[];
  type?: SelectedOptionItem | SelectedOptionItem[];
  mode?: 'all' | 'semester' | 'subject' | 'type';
  config?: FilterConfig;
}) => {
  // 更新配置
  if (options?.config) {
    // 通过深拷贝方式更新配置，避免引用问题
    if (options.config.semester !== undefined) {
      config.semester = { ...options.config.semester };
      // 重新初始化学期选项列表
      if (
        config.semester.showAll === false &&
        semesterOptions.value.length > 0 &&
        semesterOptions.value[0].value === ''
      ) {
        semesterOptions.value = semesterOptions.value.slice(1);
      }
    }

    if (options.config.subject !== undefined) {
      config.subject = { ...options.config.subject };
      // 重新初始化学科选项列表
      if (
        config.subject.showAll === false &&
        subjectOptions.value.length > 0 &&
        subjectOptions.value[0].value === ''
      ) {
        subjectOptions.value = subjectOptions.value.slice(1);
      }
    }

    if (options.config.type !== undefined) {
      config.type = { ...options.config.type };
      // 重新初始化类型选项列表
      if (
        config.type.showAll === false &&
        typeOptions.value.length > 0 &&
        typeOptions.value[0].value === ''
      ) {
        typeOptions.value = typeOptions.value.slice(1);
      }
    }

    console.log('更新后的配置:', config);
  }

  // 设置初始值
  if (options?.semester !== undefined) {
    // 单选模式 (学期现在只有单选)
    const value = Array.isArray(options.semester)
      ? options.semester.length > 0
        ? options.semester[0]
        : ''
      : options.semester;
    semesterValue.value = value === '' && config.semester?.showAll !== false ? '' : value;

    // 根据选中的值设置picker-view的初始索引
    updateSemesterPickerIndex(semesterValue.value);
  } else {
    // 设置默认值
    // 如果开启了全部选项，默认选中全部，否则选第一项
    semesterValue.value =
      config.semester?.showAll !== false
        ? ''
        : semesterOptions.value.length > 0
          ? semesterOptions.value[0].value
          : '';

    // 根据默认值设置picker-view的初始索引
    updateSemesterPickerIndex(semesterValue.value);
  }

  if (options?.subject !== undefined) {
    if (config.subject?.multiSelect) {
      // 多选模式
      // 如果传入值为空且开启了全部选项，则默认选中全部
      const valueArray = Array.isArray(options.subject) ? [...options.subject] : [options.subject];
      subjectMultiValue.value =
        valueArray.length === 0 && config.subject?.showAll !== false ? [''] : valueArray;
    } else {
      // 单选模式
      // 如果传入值为空且开启了全部选项，则默认选中全部
      const value = Array.isArray(options.subject)
        ? options.subject.length > 0
          ? options.subject[0]
          : ''
        : options.subject;
      subjectValue.value = value === '' && config.subject?.showAll !== false ? '' : value;
    }
  } else {
    // 设置默认值
    if (config.subject?.multiSelect) {
      // 如果开启了全部选项，默认选中全部，否则不选择
      subjectMultiValue.value = config.subject?.showAll !== false ? [''] : [];
    } else {
      // 如果开启了全部选项，默认选中全部，否则不选择
      subjectValue.value =
        config.subject?.showAll !== false
          ? ''
          : subjectOptions.value.length > 0
            ? subjectOptions.value[0].value
            : '';
    }
  }

  if (options?.type !== undefined) {
    if (config.type?.multiSelect) {
      // 多选模式
      // 如果传入值为空且开启了全部选项，则默认选中全部
      const valueArray = Array.isArray(options.type) ? [...options.type] : [options.type];
      typeMultiValue.value =
        valueArray.length === 0 && config.type?.showAll !== false ? [''] : valueArray;
    } else {
      // 单选模式
      // 如果传入值为空且开启了全部选项，则默认选中全部
      const value = Array.isArray(options.type)
        ? options.type.length > 0
          ? options.type[0]
          : ''
        : options.type;
      typeValue.value = value === '' && config.type?.showAll !== false ? '' : value;
    }
  } else {
    // 设置默认值
    if (config.type?.multiSelect) {
      // 如果开启了全部选项，默认选中全部，否则不选择
      typeMultiValue.value = config.type?.showAll !== false ? [''] : [];
    } else {
      // 如果开启了全部选项，默认选中全部，否则不选择
      typeValue.value =
        config.type?.showAll !== false
          ? ''
          : typeOptions.value.length > 0
            ? typeOptions.value[0].value
            : '';
    }
  }

  // 设置显示模式
  currentMode.value = options?.mode || 'all';

  // 根据模式控制显示内容
  if (options?.mode === 'semester') {
    showSemester.value = true;
    showSubject.value = false;
    showType.value = false;
  } else if (options?.mode === 'subject') {
    showSemester.value = false;
    showSubject.value = true;
    showType.value = false;
  } else if (options?.mode === 'type') {
    showSemester.value = false;
    showSubject.value = false;
    showType.value = true;
  } else {
    // 默认全部显示
    showSemester.value = true;
    showSubject.value = true;
    showType.value = true;
  }

  // 每次打开弹窗时检查并更新数据
  if (showSemester.value && semesterOptions.value.length <= 1) {
    fetchSemesters();
  }
  if (showSubject.value && subjectOptions.value.length <= 1) {
    fetchSubjects();
  }
  if (showType.value && typeOptions.value.length <= 1) {
    fetchTypes();
  }

  popupRef.value.open();
};

// 获取指定值对应的标签文本
const getLabelByValue = (
  type: 'semester' | 'subject' | 'type',
  value: SelectedOptionItem | SelectedOptionItem[]
): string => {
  // 如果是多选模式且值为数组
  const isMultiSelect =
    (type === 'semester' && config.semester?.multiSelect) ||
    (type === 'subject' && config.subject?.multiSelect) ||
    (type === 'type' && config.type?.multiSelect);

  if (isMultiSelect && Array.isArray(value)) {
    // 如果值为空或只有一个空字符串（表示"全部"）
    if (value.length === 0 || (value.length === 1 && value[0] === '')) {
      if (type === 'semester') return '全部学期';
      if (type === 'subject') return '全部学科';
      return '全部类型';
    }

    // 如果选中的项不超过2个，显示具体名称
    if (value.length <= 2) {
      let options: OptionItem[] = [];
      switch (type) {
        case 'semester':
          options = semesterOptions.value;
          break;
        case 'subject':
          options = subjectOptions.value;
          break;
        case 'type':
          options = typeOptions.value;
          break;
      }

      return value
        .map(v => {
          const option = options.find(item => item.value === v);
          return option ? option.label : '';
        })
        .filter(Boolean)
        .join('、');
    } else {
      // 超过2个则显示选中数量
      const typeName = type === 'semester' ? '学期' : type === 'subject' ? '学科' : '类型';
      return `已选${value.length}个${typeName}`;
    }
  } else {
    // 单选模式或传入的值不是数组
    let options: OptionItem[] = [];
    const defaultLabel =
      type === 'semester' ? '全部学期' : type === 'subject' ? '全部学科' : '全部类型';

    switch (type) {
      case 'semester':
        options = semesterOptions.value;
        break;
      case 'subject':
        options = subjectOptions.value;
        break;
      case 'type':
        options = typeOptions.value;
        break;
    }

    // 如果是数组但我们处于单选模式，取第一个元素
    const actualValue = Array.isArray(value) ? (value.length > 0 ? value[0] : '') : value;
    const option = options.find(item => item.value === actualValue);
    return option ? option.label : defaultLabel;
  }
};

// 获取当前选项数据
const getOptions = () => {
  return {
    semesterOptions: semesterOptions.value,
    subjectOptions: subjectOptions.value,
    typeOptions: typeOptions.value,
  };
};

// 暴露方法
defineExpose({
  open,
  getLabelByValue,
  getOptions,
});
</script>

<style lang="scss" scoped>
.filter-popup {
  position: relative;
  display: flex;
  flex-direction: column;
  height: auto;
  max-height: 80vh;
  min-height: 40vh;
  /* 设置一个合理的最小高度 */
  margin: 0 -30rpx;
}

.filter-container {
  flex: 1;
  padding: 0rpx 30rpx 0rpx;
  /* 增加底部内边距，防止内容被按钮遮挡 */
  overflow-y: auto;
  // max-height: calc(80vh - 180rpx);
  /* 设置最大高度，超出时滚动 */
}

.filter-section {
  margin-bottom: 30rpx;
  padding: 10rpx 0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.multi-select-text {
  font-size: 24rpx;
  font-weight: normal;
  color: #84888f;
  margin-left: 10rpx;
}

// picker-view样式
.semester-picker-wrapper {
  position: relative;
  height: 300rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 12rpx;
}

.semester-picker {
  width: 100%;
  height: 100%;
}

.semester-picker-indicator {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 80rpx;
  width: 100%;
  background-color: rgba(174, 174, 174, 0.1);
  // border-top: 1rpx solid rgba(125, 77, 255, 0.2);
  // border-bottom: 1rpx solid rgba(125, 77, 255, 0.2);
  z-index: 1;
  pointer-events: none;
}

.semester-picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.filter-section-semester {
  margin-bottom: 40rpx;
}

:deep(.uni-row) {
  margin-bottom: 16rpx;
}

:deep(.uni-col) {
  padding-bottom: 16rpx;
}

:deep(.uni-picker-view-indicator) {
  font-weight: bold;

  &::after {
    border: none;
  }

  &::before {
    border: none;
  }
}

.filter-option {
  height: 80rpx;
  width: 100%;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  background-color: #f5f5f5;
  border: 2rpx solid transparent;
  transition: all 0.2s;

  .option-text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 16rpx;
  }

  &.active {
    border: 2rpx solid #7d4dff;
    color: #7d4dff;
    background-color: #f2f3ff;
  }
}

.filter-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  gap: 24rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

.reset-btn {
  background-color: #f2f2f2;
  color: #333;
}

.confirm-btn {
  background-color: #7d4dff;
  color: #fff;
}
</style>
