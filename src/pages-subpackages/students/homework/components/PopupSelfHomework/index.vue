<template>
  <MyPopup ref="popupRef" title="自主作业提交AI批改" :maskClosable="false">
    <uni-forms ref="formRef" :modelValue="formData" :rules="rules" label-position="top">
      <!-- 学科选择 -->
      <uni-forms-item
        label="所属学科"
        name="subject"
        required
        class="form-item"
        :error-message="false"
      >
        <FilterItem
          :text="formData.subject"
          defaultText="请选择学科"
          @click="openSubjectSelector"
          :customClassList="['subject-filter']"
        />
      </uni-forms-item>

      <!-- 作业名称 -->
      <uni-forms-item
        label="作业名称"
        name="name"
        required
        class="form-item"
        :error-message="false"
      >
        <input
          class="input-field"
          v-model="formData.name"
          placeholder="请输入作业名称"
          maxlength="20"
        />
      </uni-forms-item>

      <!-- 上传图片区域 -->
      <uni-forms-item label="上传图片" name="images" required :error-message="false">
        <view class="image-list">
          <MyImageItem
            v-for="(image, index) in formData.images"
            :key="index"
            :image="image"
            :index="index"
            :warning="image.error === true"
            :loading="image.isOcrLoading === true"
            width="160rpx"
            height="160rpx"
            @click="previewImage"
            @delete="deleteImage"
          />

          <view
            v-if="formData.images.length < maxImages"
            class="add-image-btn"
            @click="handleCameraUpload"
          >
            <LkSvg src="/static/homework/camera.svg" width="48rpx" height="48rpx" color="#86909C" />
            <text class="add-text">作业拍照</text>
          </view>
        </view>
        <text class="tip-text">最多可上传{{ maxImages }}张图片</text>
      </uni-forms-item>

      <!-- 备注区域 -->
      <!-- <uni-forms-item label="备注" name="remark">
                <textarea class="textarea-field" v-model="formData.remark" placeholder="请输入作业备注（选填）"
                    maxlength="200" />
                <text class="word-count">{{ formData.remark.length }}/200</text>
            </uni-forms-item> -->
    </uni-forms>

    <template #footer>
      <LkButton
        class="footer-btn"
        block
        type="info"
        @click="handleSelectPhoto"
        customStyle=" border: 1rpx solid #E5E5E5;color: #86909C; height: 88rpx;"
        >相册选择</LkButton
      >
      <LkButton class="footer-btn" block type="primary" @click="submit">确认提交</LkButton>
    </template>

    <!-- 引入 MyBasicUpload 组件 -->
    <MyBasicUpload
      ref="myBasicUploadRef"
      @upload-success="handleUploadSuccess"
      @upload-fail="handleUploadFail"
      @capture-success="handleCaptureSuccess"
      @capture-fail="handleCaptureFail"
    />

    <!-- 学科选择弹窗 -->
    <PopupSubject ref="subjectFilterRef" @confirm="handleSubjectConfirm" />
  </MyPopup>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import MyPopup from '@/components/MyPopup';
import MyBasicUpload, { type UploadFileData } from '@/components/MyBasicUpload';
import { LkSvg } from '@/components';
import FilterItem from '../FilterItem/index.vue';
import PopupSubject from '../PopupSubject/index.vue';
import MyImageItem from '../MyImageItem/index.vue';
import {
  createSelfHomework,
  getCurrentSemester,
  getImageOcr,
  getSelfHomeworkDetail,
  updateSelfHomework,
} from '@/api/students/homework';

// 定义props
const props = defineProps({
  id: { type: [String, Number], default: '' },
  editMode: { type: Boolean, default: false },
});

// 定义emits
const emit = defineEmits(['success']);

// 组件引用
const popupRef = ref();
const formRef = ref();
const myBasicUploadRef = ref<InstanceType<typeof MyBasicUpload> | null>(null);
const subjectFilterRef = ref();

// 存储当前编辑的作业ID和编辑模式
const currentId = ref<string | number>('');
const isEditMode = ref(false);

// 不再需要学科选项，由PopupAllFilter组件内部维护

// 当前学期ID
const currentSemesterId = ref('');

// 最大图片数量
const maxImages = 9;

// 获取当前学期
const fetchCurrentSemester = async () => {
  try {
    const result = await getCurrentSemester();
    if (result && result.id) {
      currentSemesterId.value = result.id;
      console.log('获取当前学期成功:', result);
    }
  } catch (error) {
    console.error('获取当前学期失败:', error);
  }
};

// 表单数据
const formData = reactive({
  subject: '', // 学科名称显示
  subjectId: '' as string | number, // 学科ID值，可能是字符串或数字
  name: '',
  images: [] as any[],
  remark: '',
});

// getImageUrl 方法已移至 MyImageItem 组件

// 表单校验规则
const rules = {
  subject: {
    rules: [
      {
        required: true,
        errorMessage: '请选择学科',
      },
    ],
  },
  homeworkName: {
    rules: [
      {
        required: true,
        errorMessage: '请输入作业名称',
      },
    ],
  },
  images: {
    rules: [
      {
        required: true,
        errorMessage: '请上传至少一张图片',
        validateFunction: (rule: any, value: any[], data: any, callback: Function) => {
          if (value.length === 0) {
            callback('请上传至少一张图片');
          }
          return true;
        },
      },
    ],
  },
};

// 处理学科选择确认
const handleSubjectConfirm = (selectedValues: (string | number)[]) => {
  // 单选模式，取第一个值，如果数组为空则设置为空字符串
  const value = selectedValues.length > 0 ? selectedValues[0] : '';

  // 从PopupSubject组件获取选项的标签
  const label = subjectFilterRef.value.getLabelByValue([value]);
  formData.subject = label; // 保存显示用的标签
  formData.subjectId = value; // 保存ID值用于提交
  console.log('选中的学科:', { label: formData.subject, value: formData.subjectId });
};

// 获取作业详情
const fetchSelfHomeworkDetail = async (id: string | number) => {
  try {
    // uni.showLoading({ title: '加载中...' });
    const result = await getSelfHomeworkDetail({ id });
    if (result) {
      console.log('获取自主作业详情成功:', result);
      // 回显表单数据
      formData.name = result.name || '';
      formData.subjectId = result.subjectId || '';
      formData.subject = result.subjectName;
      // 处理文件列表
      if (result.selfHomeworkFiles && Array.isArray(result.selfHomeworkFiles)) {
        formData.images = result.selfHomeworkFiles.map((file: any) => ({
          fileKey: file.fileKey || file.key,
          fileUrl: file.fileUrl || file.url,
          fileName: file.fileName || file.name,
          error: false,
          isOcrLoading: false,
        }));
      }
    }
  } catch (error) {
    console.error('获取自主作业详情失败:', error);
    uni.showToast({
      title: '获取作业详情失败',
      icon: 'none',
    });
  } finally {
    uni.hideLoading();
  }
};

// 打开弹窗
const open = async (options?: { id?: string | number; editMode?: boolean }) => {
  // 重置表单数据
  resetFormData();

  // 打开弹窗前获取最新的当前学期
  await fetchCurrentSemester();

  // 优先使用传入的参数，其次使用props
  const id = options?.id !== undefined ? options.id : props.id;
  const isEdit = options?.editMode !== undefined ? options.editMode : props.editMode;

  // 如果是编辑模式且有ID，获取详情并回显
  if (isEdit && id) {
    currentId.value = id;
    isEditMode.value = true;
    await fetchSelfHomeworkDetail(id);
  } else {
    currentId.value = '';
    isEditMode.value = false;
  }

  // 打开弹窗
  popupRef.value.open();
};

// 重置表单数据
const resetFormData = () => {
  formData.subject = '';
  formData.subjectId = '';
  formData.name = '';
  formData.images = [];
  formData.remark = '';

  // 重置编辑状态
  currentId.value = '';
  isEditMode.value = false;
};

// 关闭弹窗
const close = () => {
  popupRef.value.close();
  // 关闭弹窗后重置数据
  resetFormData();
};

// 处理相册选择
const handleSelectPhoto = () => {
  if (formData.images.length >= maxImages) {
    uni.showToast({
      title: `最多只能上传${maxImages}张图片`,
      icon: 'none',
    });
    return;
  }
  myBasicUploadRef.value?.openAlbum({
    count: maxImages - formData.images.length,
  });
};

// 处理拍照上传
const handleCameraUpload = () => {
  if (formData.images.length >= maxImages) {
    uni.showToast({
      title: `最多只能上传${maxImages}张图片`,
      icon: 'none',
    });
    return;
  }
  myBasicUploadRef.value?.openCamera();
};

// 处理上传成功
const handleUploadSuccess = async (fileData: UploadFileData) => {
  console.log('上传成功:', fileData);
  // 确保fileData有fileUrl字段
  if (fileData && fileData.fileUrl) {
    // 添加临时loading状态
    const newImage = {
      ...fileData,
      isOcrLoading: true, // 添加OCR识别中的标记
      error: false, // 默认没有错误
    };
    formData.images.push(newImage);

    // 获取当前添加的图片索引
    const currentIndex = formData.images.length - 1;

    // 调用OCR识别
    try {
      const ocrResult = await getImageOcr({
        url: fileData.fileUrl,
        // urls: 'https://huayun-ai-obs-public.huayuntiantu.com/e996d3782c3922a93451e7aa9628c9af.png'
      });
      console.log('OCR识别结果:', ocrResult);

      // 更新图片的OCR识别状态
      if (formData.images[currentIndex]) {
        formData.images[currentIndex].isOcrLoading = false;
        // 判断识别结果是否为数组且长度大于0
        const isSuccessful =
          Array.isArray(ocrResult?.textBlocks) && ocrResult?.textBlocks?.length > 0;
        formData.images[currentIndex].error = !isSuccessful;
      }
    } catch (error) {
      console.error('OCR识别失败:', error);
      // 识别失败也标记为错误
      if (formData.images[currentIndex]) {
        formData.images[currentIndex].isOcrLoading = false;
        formData.images[currentIndex].error = true;
      }
    }
  }
};

// 处理上传失败
const handleUploadFail = (error: any) => {
  console.error('上传失败:', error);
  uni.showToast({
    title: '上传失败，请重试',
    icon: 'none',
  });
};

// 处理拍照/相册选择成功
const handleCaptureSuccess = (result: any) => {
  console.log(`${result.type}选择成功:`, result.files);
};

// 处理拍照/相册选择失败
const handleCaptureFail = (error: any) => {
  console.error('拍照/相册选择失败:', error);
  // uni.showToast({
  //   title: '选择失败，请重试',
  //   icon: 'none',
  // });
};

// 删除图片
const deleteImage = (index: number) => {
  formData.images.splice(index, 1);
};

// 预览图片
const previewImage = (index: number) => {
  // 只有非警告状态的图片才能预览
  if (formData.images[index] && formData.images[index].error) {
    return;
  }

  // 收集所有可显示的图片URL
  const urls = formData.images
    .filter(img => !img.error) // 过滤掉有错误的图片
    .map(img => {
      if (!img) return '';
      if (typeof img === 'string') return img;
      return img.fileUrl || img.url || '';
    })
    .filter(url => !!url);

  if (urls.length === 0) return;

  uni.previewImage({
    urls,
    current: urls[index],
    success: () => {
      console.log('图片预览成功');
    },
    fail: err => {
      console.error('图片预览失败:', err);
    },
  });
};

const checkForm = () => {
  let errorMessage = '';
  const nameLength = formData.name.trim().length;

  // 手动检查必填项，使用toast显示错误
  if (!formData.subjectId) {
    errorMessage = '请选择学科';
  } else if (!formData.name || formData.name.trim() === '') {
    errorMessage = '请输入作业名称';
  } else if (nameLength < 2) {
    errorMessage = '作业名称不能少于2个字符';
  } else if (nameLength > 20) {
    errorMessage = '作业名称不能超过20个字符';
  } else if (formData.images.length === 0) {
    errorMessage = '请上传至少一张图片';
  } else {
    // 检查是否有未识别成功的图片
    const hasErrorImage = formData.images.some(img => img.error === true);
    if (hasErrorImage) {
      errorMessage = '存在未识别的图片，请重新上传';
    }
  }

  if (errorMessage) {
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    });
    return false;
  }
  return true;
};

// 提交自主作业
const submit = async () => {
  try {
    // 检查表单是否有效
    if (!checkForm()) {
      return;
    }

    // 验证通过，构建提交数据
    const submitData = {
      name: formData.name,
      subjectId: formData.subjectId,
      semesterId: currentSemesterId.value, // 固定传入当前学期ID
      fileList: formData.images.map(item => {
        return {
          fileKey: item.fileKey,
        };
      }),
    };
    console.log('准备提交的数据:', submitData);

    // 显示加载中
    uni.showLoading();

    let res;
    // 根据是否编辑模式调用不同接口
    if (isEditMode.value && currentId.value) {
      // 更新作业
      res = await updateSelfHomework({
        id: currentId.value,
        ...submitData,
      });
      console.log('更新结果:', res);

      // 显示成功提示
      uni.showToast({
        title: '作业更新成功',
        icon: 'success',
      });
    } else {
      // 创建作业
      res = await createSelfHomework(submitData);
      console.log('提交结果:', res);

      // 显示成功提示
      uni.showToast({
        title: '作业提交成功',
        icon: 'success',
      });
    }

    // 提交成功后关闭弹窗
    close(); // 这里会调用close方法，其中已包含重置数据的逻辑

    // 触发成功事件，通知父组件刷新数据
    emit('success');
  } catch (error: any) {
    console.error(error.message);
    uni.showToast({
      title: isEditMode.value ? '更新失败' : '提交失败',
      icon: 'none',
    });
  }
};

// 打开学科选择器
const openSubjectSelector = () => {
  console.log(
    '打开学科选择器，当前subjectId:',
    formData.subjectId,
    '类型:',
    typeof formData.subjectId
  );

  // 构建defaultValues数组
  const defaultValues = formData.subjectId ? [String(formData.subjectId)] : [];
  console.log('传递给PopupSubject的defaultValues:', defaultValues);

  // 使用PopupSubject组件打开学科选择
  subjectFilterRef.value.open({
    multiSelect: false, // 单选模式
    showAll: false,
    defaultValues, // 传递当前选中的学科ID
  });
};

// 组件挂载时获取当前学期
onMounted(() => {
  fetchCurrentSemester();
});

// 向父组件暴露的方法
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
// 以下样式已合并到统一样式中

// 统一学科选择和作业名称的样式
:deep(.uni-forms-item) {
  .uni-forms-item__inner {
    padding-bottom: 24rpx; // 统一底部间距
  }

  .uni-forms-item__label {
    padding-bottom: 10rpx; // 统一标签与输入框之间的距离
  }
}

// 确保学科选择和作业名称表单项完全一致
:deep(.form-item) {
  margin-bottom: 20rpx; // 统一下边距

  .uni-forms-item__label {
    font-size: 28rpx;
    color: #1d2129;
    height: 40rpx;
    line-height: 40rpx;
    padding-bottom: 10rpx;
  }

  .uni-forms-item__content {
    height: 80rpx; // 确保内容区域高度一致
  }
}

// 确保学科选择和输入框样式完全一致的公共样式
@mixin input-common-style {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  color: #4e5969;
}

:deep(.subject-filter) {
  width: 100%;
  box-sizing: border-box;

  .picker-trigger {
    @include input-common-style;
    display: flex;
    align-items: center;
  }

  .picker-text {
    color: #4e5969;
    line-height: 80rpx;
  }
}

.input-field {
  @include input-common-style;

  &::placeholder {
    color: #4e5969;
  }
}

.textarea-field {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

// 移除了 image-item 相关样式，已移至 MyImageItem 组件

.add-image-btn {
  display: flex;
  width: 160rpx;
  height: 160rpx;
  padding: 25rpx 24rpx 21rpx 24rpx;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
  box-sizing: border-box;
}

.add-text {
  margin-top: 10rpx;
  color: #86909c;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 22rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.word-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.footer-btn {
  height: 88rpx;
  border-radius: 45rpx;
}
</style>
