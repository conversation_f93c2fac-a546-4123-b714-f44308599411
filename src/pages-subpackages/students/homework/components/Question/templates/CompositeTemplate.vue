<template>
  <view class="composite-template">
    <!-- 子题列表 -->
    <view class="sub-questions">
      <template v-for="(subQuestion, index) in question?.subQuestions" :key="subQuestion.id">
        <!-- 子题标题与序号 -->
        <view class="sub-question-header">
          <!-- <text class="sub-question-index">{{ index + 1 }}. </text> -->
          <!-- 填空题不展示标题 -->
          <text class="sub-question-title" v-if="subQuestion.type !== QuestionType.FILL_BLANK"
            ><mp-html :content="subQuestion.title"
          /></text>
        </view>

        <view class="sub-question-content">
          <!-- 选择题类型（单选、多选、判断题） -->
          <SelectionTemplate
            v-if="isSelectionType(subQuestion.type)"
            :questionId="subQuestion.id"
          />

          <!-- 填空题类型 -->
          <BlankTemplate
            v-else-if="
              subQuestion.type === QuestionType.FILL_BLANK ||
              subQuestion.type === QuestionType.CLOZE
            "
            :questionId="subQuestion.id"
          />

          <!-- 简答题类型 -->
          <TextareaTemplate
            v-else-if="subQuestion.type === QuestionType.SHORT_ANSWER"
            :questionId="subQuestion.id"
          />

          <!-- 不支持的类型 -->
          <view v-else class="unsupported-type"> 不支持的题目类型：{{ subQuestion.type }} </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { QuestionType } from '@/constants/students/homework';
import SelectionTemplate from './SelectionTemplate.vue';
import BlankTemplate from './BlankTemplate.vue';
import TextareaTemplate from './TextareaTemplate.vue';
import { QuestionTitle } from '../components';

// 定义属性 - 只接收questionId
const props = defineProps<{
  questionId: string;
}>();

// 使用store
const homeworkStore = useHomeworkStore();

// 从store获取数据
const question = computed(() => homeworkStore.getQuestion(props.questionId));

// 判断是否为选择题类型（单选、多选、判断题）
const isSelectionType = (type: string | undefined): boolean => {
  if (!type) return false;
  return [QuestionType.SINGLE, QuestionType.MULTIPLE, QuestionType.TRUE_FALSE].includes(
    type as QuestionType
  );
};

// 在挂载时初始化子题的用户答案结构
onMounted(() => {
  const currentQuestion = question.value;
  if (!currentQuestion?.subQuestions?.length) return;

  // 获取当前用户答案
  const userAnswer = homeworkStore.getUserAnswer(props.questionId);

  // 如果已经有答案结构则不需要初始化
  if (userAnswer?.answer && typeof userAnswer.answer === 'object') return;

  // 如果没有答案结构，创建一个空对象作为初始答案
  homeworkStore.setUserAnswer(props.questionId, {});
});
</script>

<style lang="scss" scoped>
.composite-template {
  width: 100%;
  padding: 16rpx;

  .sub-questions {
    margin-top: 30rpx;

    .sub-question-header {
      display: flex;
      margin-bottom: 16rpx;
      align-items: flex-start;

      .sub-question-index {
        font-weight: bold;
        margin-right: 8rpx;
      }
    }

    .sub-question-content {
      margin-bottom: 10rpx;
      padding-left: 20rpx;
    }

    .unsupported-type {
      padding: 20rpx;
      color: #999;
      background-color: #f5f5f5;
      border-radius: 8rpx;
      text-align: center;
    }
  }
}
</style>
