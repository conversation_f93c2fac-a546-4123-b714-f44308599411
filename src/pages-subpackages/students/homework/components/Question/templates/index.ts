import SelectionTemplate from './SelectionTemplate.vue';
import BlankTemplate from './BlankTemplate.vue';
import TextareaTemplate from './TextareaTemplate.vue';
import SortTemplate from './SortTemplate.vue';
import MatchTemplate from './MatchTemplate.vue';
import CompositeTemplate from './CompositeTemplate.vue';

import { QuestionType } from '@/constants/students/homework';

// 模板类型映射
export const templateMap = {
  selection: SelectionTemplate,
  blank: BlankTemplate,
  textarea: TextareaTemplate,
  sort: SortTemplate,
  match: MatchTemplate,
  composite: CompositeTemplate,
};

// 根据题目类型获取对应的模板类型
export const getTemplateType = (questionType: QuestionType): string => {
  // 选项题模板
  if (
    [QuestionType.SINGLE, QuestionType.MULTIPLE, QuestionType.TRUE_FALSE].includes(questionType)
  ) {
    return 'selection';
  }

  // 填空题模板
  if (
    [QuestionType.FILL_BLANK, QuestionType.CLOZE, QuestionType.GRAPHIC_FILL].includes(questionType)
  ) {
    return 'blank';
  }

  // 排序题模板
  if (questionType === QuestionType.SORTING) {
    return 'sort';
  }

  // 匹配题模板
  if (questionType === QuestionType.MATCHING) {
    return 'match';
  }

  // 复合题模板
  if ([QuestionType.READING, QuestionType.MATERIAL_ANALYSIS].includes(questionType)) {
    return 'composite';
  }

  // 自定义题或其他类型默认使用文本域
  return 'textarea';
};

// 根据题目类型获取对应模板组件
export const getTemplateComponent = (questionType: QuestionType) => {
  const templateType = getTemplateType(questionType);
  return templateMap[templateType as keyof typeof templateMap] || TextareaTemplate;
};

export {
  SelectionTemplate,
  BlankTemplate,
  TextareaTemplate,
  SortTemplate,
  MatchTemplate,
  CompositeTemplate,
};
