<template>
  <MyExtraBlock title="评分说明">
    <template #default>
      <view class="note-content">
        <mp-html :content="reason" />
        <MyEmpty v-if="!reason" :showImage="false" />
      </view>
    </template>
  </MyExtraBlock>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { useEventBus } from '@/hooks/useEventBus';
import { HomeworkEventNames } from '@/constants/students/homework';
import MyExtraBlock from '../../MyExtraBlock/index.vue';
import MyEmpty from '@/components/MyEmpty/index.vue';

const props = defineProps<{
  questionId: string;
}>();

// 使用store
const homeworkStore = useHomeworkStore();

// 从store获取数据
const question = computed(() => homeworkStore.getQuestion(props.questionId));
const reason = computed(() => question.value?.backendData?.question?.submitQuestion?.reason);
</script>

<style lang="scss" scoped>
.note-content {
  padding: 16rpx 0;

  font-size: 32rpx;
  line-height: 1.5;
  color: #000000;

  .note-text {
    font-size: 32rpx;
    line-height: 1.5;
    color: #000000;
    white-space: pre-wrap;
    word-break: break-all;
    margin-bottom: 20rpx;
  }
}

.add-note-btn {
  background-color: #ffffff;
  color: #7d4dff;
  font-size: 26rpx;
  font-weight: 500;
  border: 1px solid #7d4dff;
  border-radius: 100px;
  padding: 8rpx 20rpx;
  line-height: 1.6;
  margin-left: 12rpx;
  height: auto;
}
</style>
