<template>
  <MyExtraBlock title="试题详解" @header-click="toggleAnalysis">
    <template #header-right>
      <button
        v-if="homeworkStore.answerType === HomeworkTaskType.ONLINE"
        class="feedback-btn"
        @click.stop="onFeedbackClick"
      >
        试题纠错
      </button>
    </template>
    <template #default>
      <view class="analysis-content" v-if="showContent">
        <view v-if="question?.analysis || hasAnswer">
          <!-- <view class="answer-section" v-if="hasAnswer">
            <text class="section-title">[答案] {{ formatAnswer }}</text>
          </view> -->
          <view class="analysis-section" v-if="question?.analysis">
            <mp-html :content="question?.analysis" />
          </view>
        </view>
        <!-- <view v-else class="empty-analysis">
          <text>暂无解析</text>
        </view> -->
        <MyEmpty :showImage="false" v-else />
      </view>
    </template>
  </MyExtraBlock>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { QuestionType } from '@/constants/students/homework';
import { useEventBus } from '@/hooks/useEventBus';
import { HomeworkEventNames } from '@/constants/students/homework';
import MyExtraBlock from '../../MyExtraBlock/index.vue';
import MyEmpty from '@/components/MyEmpty/index.vue';
import { HomeworkTaskType } from '@/constants/students/homework';

const props = defineProps<{
  questionId: string;
  showAnalysis?: boolean;
}>();

// 使用store
const homeworkStore = useHomeworkStore();

// 从store获取数据
const question = computed(() => homeworkStore.getQuestion(props.questionId));
const showAnalysis = computed(() => props.showAnalysis || homeworkStore.showAnalysis);

// 控制解析内容显示状态
const showContent = ref(showAnalysis.value);

// 切换解析显示状态
function toggleAnalysis() {
  showContent.value = !showContent.value;
}

// 获取事件总线
const eventBus = useEventBus();

// 解析按钮点击
function onFeedbackClick(event: Event) {
  event.stopPropagation();
  eventBus.emit(HomeworkEventNames.OPEN_FEEDBACK_POPUP, props.questionId);
}

// 判断是否有答案
const hasAnswer = computed(() => {
  if (!question.value) return false;
  switch (question.value.type) {
    case QuestionType.SINGLE:
      return !!question.value.answer;
    case QuestionType.MULTIPLE:
      return !!(question.value.answers && question.value.answers.length > 0);
    case QuestionType.SHORT_ANSWER:
      return !!question.value.answer;
    default:
      return false;
  }
});

// 根据题目类型格式化答案
const formatAnswer = computed(() => {
  if (!question.value) return '';
  switch (question.value.type) {
    case QuestionType.SINGLE:
      return question.value.answer || '';
    case QuestionType.MULTIPLE:
      return question.value.answers?.join('、') || '';
    case QuestionType.SHORT_ANSWER:
      return question.value.answer || '';
    default:
      return '';
  }
});
</script>

<style lang="scss" scoped>
.analysis-content {
  padding: 16rpx 0;
  font-size: 32rpx;
  line-height: 1.5;
  color: #000000;

  .answer-section {
    margin-bottom: 16rpx;

    .section-title {
      font-weight: 500;
      margin-bottom: 8rpx;
      display: block;
    }
  }

  .analysis-section {
    white-space: pre-wrap;
    word-break: break-all;
  }

  .empty-analysis {
    text-align: center;
    color: #999;
    padding: 30rpx 0;
  }

  :deep(.analysis-tag) {
    font-weight: 500;
    margin-top: 16rpx;
    margin-bottom: 8rpx;
    display: block;
  }
}

.feedback-btn {
  background-color: #ffffff;
  color: #4e5969;
  font-size: 26rpx;
  font-weight: 500;
  border: 1px solid #dcdcdc;
  border-radius: 100px;
  padding: 8rpx 20rpx;
  line-height: 1.6;
  margin-left: 12rpx;
  height: auto;
}
</style>
