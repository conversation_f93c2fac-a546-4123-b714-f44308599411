<template>
  <view class="question-fill-line" @click="handleClick" :style="{ width: inputWidth }">
    <view
      class="fill-line-input-wrapper"
      :class="{ error: isError, correct: isCorrect, readonly: readonly }"
    >
      <input
        class="fill-line-input"
        :value="modelValueWithDefault"
        :focus="isFocused"
        :adjust-position="true"
        @input="handleInput"
        @blur="handleBlur"
        @confirm="handleConfirm"
        :disabled="readonly"
        placeholder=""
      />
      <view class="fill-line-underline"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { inject, ref, computed } from 'vue';

// 组件属性定义
const props = defineProps<{
  modelValue?: string; // 改为可选属性
  readonly?: boolean;
  placeholder?: string;
  isError?: boolean;
  isCorrect?: boolean;
  blankId?: string | number; // 填空标识，用于识别当前填空
  blankIndex?: number; // 填空序号
}>();

// 组件事件定义
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

// 状态管理
const isFocused = ref(false);

// 自动计算宽度
const inputWidth = computed(() => {
  const baseWidth = 100; // 基础宽度(rpx)
  const charWidthEN = 20; // 英文字符的大致宽度(rpx)
  const charWidthCN = 32; // 中文字符的大致宽度(rpx)

  if (!modelValueWithDefault.value) {
    return `${baseWidth}rpx`;
  }

  // 计算文本宽度，区分中英文字符
  let textWidth = 0;
  for (let i = 0; i < modelValueWithDefault.value.length; i++) {
    const char = modelValueWithDefault.value.charAt(i);
    // 使用Unicode范围判断是否为中文字符
    if (/[\u4e00-\u9fa5]/.test(char)) {
      textWidth += charWidthCN;
    } else {
      textWidth += charWidthEN;
    }
  }

  // 计算宽度，最小60rpx，加上基础宽度和内容宽度
  const calculatedWidth = Math.max(baseWidth, textWidth + 40); // 40rpx作为padding

  // 设置一个上限，防止过宽
  const maxWidth = 500;
  const finalWidth = Math.min(calculatedWidth, maxWidth);

  return `${finalWidth}rpx`;
});

// 获取父组件提供的方法
const fillLineGroup = inject('fillLineGroup', null) as {
  readonly?: boolean;
  handleUpdate: (blankId: string | number, blankIndex: number, value: string) => void;
} | null;

// 处理点击事件
const handleClick = () => {
  if (props.readonly) return;
  isFocused.value = true;
};

// 处理输入事件
const handleInput = (e: any) => {
  if (props.readonly) return;
  const value = e.detail?.value || '';
  emit('update:modelValue', value);

  // 通知父组件更新
  if (fillLineGroup && props.blankId !== undefined && props.blankIndex !== undefined) {
    fillLineGroup.handleUpdate(props.blankId, props.blankIndex, value);
  }
};

// 处理失焦事件
const handleBlur = () => {
  isFocused.value = false;
};

// 处理确认事件（回车）
const handleConfirm = () => {
  isFocused.value = false;
};

// 设置默认值
const modelValueWithDefault = computed(() => props.modelValue || '');
</script>

<style lang="scss" scoped>
.question-fill-line {
  display: inline-flex;
  align-items: center;
  min-width: 60rpx;

  /* 改为自适应 */
  /* 默认宽度 */
  // max-width: 400rpx;
  max-width: auto;
  /* 增加最大宽度 */
  margin: 0 4rpx;
  vertical-align: middle;
  position: relative;
  transition: width 0.2s ease-in-out;
  /* 添加宽度变化的过渡动画 */

  .fill-line-input-wrapper {
    width: 100%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin: 0 4rpx;
    height: 60rpx;
    padding: 0 8rpx;
    position: relative;
    border-radius: 8rpx;
  }

  .fill-line-input {
    width: 100%;
    height: 60rpx;
    font-size: 32rpx;
    text-align: center;
    color: #7d4dff;
    background-color: transparent;
    border: none;
    padding: 0 4rpx;
    white-space: nowrap;
    /* 防止文字换行 */
    overflow: visible;
    /* 确保内容可见 */
    text-overflow: clip;
    /* 不使用省略号 */
    box-sizing: border-box;
    /* 确保padding不会增加宽度 */
  }

  .fill-line-underline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background-color: #b694ff;
  }

  // 只读状态
  .fill-line-input-wrapper.readonly {
    .fill-line-input {
      color: #7d4dff;
      pointer-events: none;
      opacity: 0.9;
    }
  }

  // 错误状态
  .fill-line-input-wrapper.error {
    .fill-line-input {
      color: #e85555;
    }

    .fill-line-underline {
      background-color: #e85555;
    }
  }

  // 正确状态
  .fill-line-input-wrapper.correct {
    .fill-line-input {
      color: #2cd990;
    }

    .fill-line-underline {
      background-color: #2cd990;
    }
  }
}
</style>
