<template>
  <view class="question-title">
    <!-- 题型、分数和序号信息 -->
    <view class="title-header">
      <view class="title-left">
        <text class="question-type" v-if="question?.type">{{ typeText }}</text>
        <text class="question-score" v-if="question?.score">{{ question?.score }}分</text>
      </view>
      <!-- 当前题目序号和题目总数 -->
      <view class="progress-count" v-if="showCurrentNumber">
        {{ currentQuestionNumber }}/{{ questionsLength }}
      </view>
    </view>

    <!-- 标题内容（使用富文本渲染） -->
    <view class="title-content" v-if="question?.type !== QuestionType.FILL_BLANK">
      <text class="question-index" v-if="showIndex">{{ question?.index }}、</text>
      <mp-html :content="titleHtml" :domain="domain" />
    </view>

    <!-- 显示材料内容 -->
    <view class="question-material" v-if="showMaterial">
      <mp-html :content="material" :domain="domain" />
    </view>

    <!-- 如果有子题渲染子题标题(暂时不启用) -->
    <!-- <view
      class="sub-questions-wrap"
      v-if="question?.subQuestions && question.subQuestions.length > 0"
    >
      <view
        class="sub-question-item"
        v-for="subQuestion in question.subQuestions"
        :key="subQuestion.id"
      >
        <text class="sub-question-index">{{ subQuestion.index }}、</text>
        <mp-html class="sub-question-content" :content="subQuestion.title" :domain="domain" />
      </view>
    </view> -->
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { QuestionType } from '@/constants/students/homework';
import { getQuestionTypeLabel } from '@/pages-subpackages/students/homework/utils';

const props = withDefaults(
  defineProps<{
    /** 题目ID */
    questionId: string;
    /** 是否显示题目序号 */
    showIndex?: boolean;
    /** 是否显示当前题目序号和题目总数 */
    showCurrentNumber?: boolean;
  }>(),
  {
    showIndex: true,
    showCurrentNumber: true,
  }
);

// 使用store
const homeworkStore = useHomeworkStore();

// 从store获取数据
const question = computed(() => homeworkStore.getQuestion(props.questionId));

// 设置mp-html的domain
const domain = ref('');

// 根据题型枚举获取对应的中文文本
const typeText = computed(() => {
  if (!question.value) return '';
  const typeName =
    question.value.backendData?.questionTypeName ||
    question.value.backendData?.question?.questionTypeName;
  return typeName;
  // return getQuestionTypeLabel(question.value.type); //临时注释暂时不要删除
});

// 获取题目总数和当前索引
const totalQuestions = computed(() => homeworkStore.questions.length);
const currentQuestionNumber = computed(() => homeworkStore.currentQuestionIndex + 1);

// 获取已作答题目数量和总题目数
const answeredCount = computed(() => homeworkStore.answeredCount);
const questionsLength = computed(() => homeworkStore.currentBatchQuestions.length);

// 获取材料内容
const material = computed(() => {
  if (!question.value) return '';
  // @ts-ignore - 类型定义可能没有包含material属性
  return question.value.material || '';
});

// 处理标题，确保它可以作为富文本显示
const titleHtml = computed(() => {
  if (!question.value) return '';

  // 如果标题已经是HTML格式则直接返回，否则包装一下
  let title = question.value.title || '';
  if (title.includes('<') && title.includes('>')) {
    return title; // 可能已经是HTML
  }
  return `<div>${title}</div>`;
});

// 是否显示材料内容
const showMaterial = computed(() => {
  if (!question.value || !material.value) return false;
  return (
    question.value.type === QuestionType.READING ||
    question.value.type === QuestionType.MATERIAL_ANALYSIS
  );
});
</script>

<style lang="scss" scoped>
.question-title {
  margin-bottom: 24rpx;

  .title-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
  }

  .title-left {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .question-type,
  .question-score {
    background: #fdbb02;
    color: #fff;
    border-radius: 12rpx;
    padding: 3rpx 13rpx;
    vertical-align: middle;
    font-size: 24rpx;
    font-weight: 500;
    margin-right: 10rpx;
  }

  .question-index {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  .title-content {
    margin: 8rpx 0 16rpx;
    font-size: 32rpx;
    color: #000;
    line-height: 1.6;
    display: flex;
  }

  .question-material {
    margin: 20rpx 0;
    padding: 20rpx;
    background-color: #f9f9f9;
    border-radius: 8rpx;
    font-size: 28rpx;
    line-height: 1.6;
  }

  .sub-questions-wrap {
    padding-left: 80rpx;
    margin-top: 16rpx;
    display: flex;
    flex-direction: column;
  }

  .sub-question-item {
    display: flex;
    margin-bottom: 16rpx;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .sub-question-index {
    font-size: 28rpx;
    color: #666;
    margin-right: 12rpx;
    min-width: 30rpx;
    font-weight: 500;
  }

  .sub-question-content {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    line-height: 1.5;
  }

  .progress-count {
    font-size: 28rpx;
    font-weight: 600;
    color: #1d2129;
  }
}
</style>
