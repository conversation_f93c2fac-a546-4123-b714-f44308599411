<template>
  <view class="question-renderer">
    <!-- 添加统一的题目标题 -->
    <QuestionTitle :questionId="questionId" />
    <!-- 复合题模板 -->
    <CompositeTemplate
      v-if="question.subQuestions && question.subQuestions.length > 0"
      :questionId="questionId"
    />
    <!-- 选项题模板 -->
    <SelectionTemplate
      v-else-if="
        question.type === QuestionType.SINGLE ||
        question.type === QuestionType.MULTIPLE ||
        question.type === QuestionType.TRUE_FALSE
      "
      :questionId="questionId"
    />

    <!-- 填空题模板 -->
    <BlankTemplate
      v-else-if="
        question.type === QuestionType.FILL_BLANK ||
        question.type === QuestionType.CLOZE ||
        question.type === QuestionType.GRAPHIC_FILL
      "
      :questionId="questionId"
    />

    <!-- 文本区模板 -->
    <TextareaTemplate
      v-else-if="question.type === QuestionType.SHORT_ANSWER"
      :questionId="questionId"
    />

    <!-- 排序题模板 -->

    <!-- 匹配题模板 -->

    <view class="question-not-found" v-else>
      <text>未找到对应题型的渲染组件</text>
    </view>
    <!-- 题目解析 -->
    <QuestionAnalysis v-if="homeworkStore.showAnswer" :questionId="questionId" />
    <!-- 评分说明 -->
    <QuestionScoreExplanation
      v-if="
        homeworkStore.showAnswer && question.value?.backendData?.question?.submitQuestion?.reason
      "
      :questionId="questionId"
    />
    <!-- 题目笔记 -->
    <QuestionNote v-if="homeworkStore.showAnswer" :questionId="questionId" />
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import QuestionTitle from './QuestionTitle.vue';
import QuestionAnalysis from './QuestionAnalysis.vue';
import QuestionNote from './QuestionNote.vue';
import QuestionScoreExplanation from './QuestionScoreExplanation.vue';
import { getTemplateType } from '../templates';
import { QuestionType } from '@/constants/students/homework';

// 导入所有模板组件
import SelectionTemplate from '../templates/SelectionTemplate.vue';
import BlankTemplate from '../templates/BlankTemplate.vue';
import TextareaTemplate from '../templates/TextareaTemplate.vue';
import SortTemplate from '../templates/SortTemplate.vue';
import MatchTemplate from '../templates/MatchTemplate.vue';
import CompositeTemplate from '../templates/CompositeTemplate.vue';

// 定义组件属性
const props = defineProps<{
  questionId: string;
}>();

// 使用store
const homeworkStore = useHomeworkStore();

// 从store获取数据
const question = computed(() => homeworkStore.getQuestion(props.questionId));
const userAnswer = computed(() => homeworkStore.getUserAnswer(props.questionId));
const readonly = computed(() => homeworkStore.readonly);
const showAnalysis = computed(() => homeworkStore.showAnalysis);
const showNote = computed(() => homeworkStore.showNote);
const showScoreExplanation = computed(
  () => homeworkStore.showAnswer && question.value?.backendData?.question?.submitQuestion?.reason
);

// 获取题型对应的模板类型
const templateType = computed(() => {
  if (!question.value) return '';
  return getTemplateType(question.value.type);
});

// 更新答案（统一处理各种类型）
const onUpdateAnswer = (answer: any) => {
  if (readonly.value) return;
  // console.log(`[QuestionRenderer] 更新答案:`, props.questionId, answer);
  homeworkStore.setUserAnswer(props.questionId, answer);
};

// 组件挂载时记录初始值
onMounted(() => {
  if (question.value) {
    // console.log(`[QuestionRenderer] 组件挂载, 题目ID:${props.questionId}, 类型:${question.value.type}`);
    // console.log(`[QuestionRenderer] 用户答案:`, userAnswer.value);
  }
});
</script>

<style lang="scss" scoped>
.question-renderer {
  margin-bottom: 30rpx;
}

.question-not-found {
  padding: 20rpx;
  color: #ff4d4f;
  text-align: center;
  background-color: #fff1f0;
  border: 1rpx solid #ffccc7;
  border-radius: 4rpx;
}
</style>
