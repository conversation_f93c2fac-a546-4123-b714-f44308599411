<template>
  <view class="question-item">
    <!-- 选项卡 - 仅在非只读模式下显示 -->
    <view class="tab-container" v-if="!readonly">
      <view
        class="tab-item"
        :class="{ active: currentActiveTab === 'online' }"
        @click="switchTab('online')"
      >
        在线录入
      </view>
      <view
        class="tab-item"
        :class="{ active: currentActiveTab === 'upload' }"
        @click="switchTab('upload')"
      >
        上传文件
      </view>
    </view>

    <!-- 在线录入内容 - 只读模式下始终显示 -->
    <template v-if="currentActiveTab === 'online' || readonly">
      <view class="textarea-container">
        <QuestionAnswerArea
          @fullscreen="handleFullscreen"
          :image-url="drawingImageUrl"
          :readonly="readonly"
          :status="answerStatus"
        />
      </view>
    </template>

    <!-- 上传文件内容 - 在只读模式下，如果有附件就显示 -->
    <template v-if="currentActiveTab === 'upload' || (readonly && hasFiles)">
      <view class="upload-area">
        <!-- 上传按钮，只在非只读模式下显示 -->
        <view v-if="!readonly" class="upload-file-container" @click="openUploadPopup">
          <view class="upload-icon">
            <image src="/static/homework/add_icon.svg" width="100%" height="100%" />
          </view>
          <text class="upload-btn-text">上传文件</text>
        </view>

        <!-- 已上传文件展示 -->
        <view v-if="answerContent.files && answerContent.files.length > 0" class="file-list">
          <uni-row :gutter="20">
            <uni-col :span="12" v-for="(file, index) in filteredFiles" :key="file.id">
              <view class="file-item" @click="$emit('preview-file', file)">
                <!-- 文件缩略图/图标 -->
                <view class="file-icon">
                  <!-- 图片文件-->
                  <image
                    v-if="checkFileType(file.fileType, 'image')"
                    :src="file.fileUrl"
                    mode="cover"
                    class="file-thumbnail"
                  >
                  </image>
                  <!-- DOC文件 -->
                  <image
                    v-else-if="checkFileType(file.fileType, 'document')"
                    src="/static/fileTypeIcon/doc.svg"
                    mode="aspectFit"
                    class="file-icon-img"
                  >
                  </image>
                  <!-- 音频文件 -->
                  <image
                    v-else-if="checkFileType(file.fileType, 'audio')"
                    src="/static/fileTypeIcon/amr.svg"
                    mode="aspectFit"
                    class="file-icon-img"
                  >
                  </image>
                  <!-- 其他文件图标 -->
                  <LkSvg
                    v-else
                    :src="`/static/fileTypeIcon/${file.fileType.replace('.', '')}.svg`"
                    width="100%"
                    height="100%"
                  />
                </view>

                <!-- 文件名 -->
                <text class="file-name">{{ file.fileName }}</text>

                <!-- 删除按钮 -->
                <view v-if="!readonly" class="file-delete" @click.stop="handleDeleteFile(file)">
                  <text class="icon-close">×</text>
                </view>
              </view>
            </uni-col>
          </uni-row>
        </view>

        <!-- 没有附件时显示提示 -->
        <view v-if="shouldShowEmptyAttachmentMessage" class="no-attachment">
          <MyEmpty />
        </view>
      </view>
    </template>

    <!-- 解析区域 -->
    <view class="analysis" v-if="showAnalysis && subQuestion.analysis">
      <view class="analysis-title">解析：</view>
      <view class="analysis-content">{{ subQuestion.analysis }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { LkSvg } from '@/components';
import type { UploadFileData } from '@/components/MyUploadPopup';
import MyEmpty from '@/components/MyEmpty';
import QuestionAnswerArea from './QuestionAnswerArea.vue';
import { useEventBus } from '@/hooks/useEventBus';
import { HomeworkEventNames } from '@/constants/students/homework';
import { useHomeworkStore } from '@/store/homeworkStore';
import type { FrontComplexAnswerContent, FrontSubQuestion } from '@/types/students/homework';
import { getFileType, checkFileType } from '@/pages-subpackages/students/homework/utils';
import { HomeworkStatus } from '@/constants/students/homework';

interface AnswerContent {
  text?: string;
  files: UploadFileData[];
  drawing?: {
    imageUrl: string;
    data: any;
  };
  correct?: boolean;
}

const homeworkStore = useHomeworkStore();
const eventBus = useEventBus();

const props = withDefaults(
  defineProps<{
    parentQuestionId: string;
    subQuestion: FrontSubQuestion;
    index: number;
    answer?: AnswerContent;
    readonly?: boolean;
    showAnalysis?: boolean;
    isShortAnswer?: boolean;
    activeTab?: 'online' | 'upload';
    answerStatus?: string;
  }>(),
  {
    readonly: false,
    showAnalysis: false,
    isShortAnswer: false,
  }
);
const maxFileCount = 9;

const emit = defineEmits<{
  (e: 'update:answer', subQuestionId: string, answer: AnswerContent): void;
  (e: 'preview-file', file: UploadFileData): void;
  (e: 'delete-file', file: UploadFileData): void;
  (e: 'upload'): void;
}>();

// 从store中获取当前题目的答案内容
const answerContent = computed(() => {
  // 如果有传入的 answer prop，优先使用
  if (props.answer) {
    return {
      text: props.answer.text || '',
      files: props.answer.files || [],
      drawing: props.answer.drawing,
    };
  }

  // 非子题情况，直接获取答案
  const parentQuestionId = props.parentQuestionId;
  if (!parentQuestionId) return { text: '', files: [] };

  // 获取当前题目的答案
  const answer = homeworkStore.getUserAnswer(parentQuestionId);
  if (!answer || !answer.answer) return { text: '', files: [] };

  // 处理答案数据
  if (typeof answer.answer === 'string') {
    return {
      text: answer.answer,
      files: [],
    };
  } else if (typeof answer.answer === 'object' && !Array.isArray(answer.answer)) {
    const answerObj = answer.answer as any;

    // 检查是否有画板数据
    const hasDrawing = answerObj.drawing && answerObj.drawing.imageUrl;

    if (answerObj.text !== undefined || answerObj.files || hasDrawing) {
      return answerObj as FrontComplexAnswerContent;
    }
  }

  return { text: '', files: [] };
});

// 获取当前应该激活的选项卡
const getActiveTab = (): 'online' | 'upload' => {
  if (props.activeTab) {
    return props.activeTab;
  }

  // 如果是简答题，始终默认显示在线录入选项卡
  if (props.isShortAnswer) {
    return 'online';
  }

  // 如果有附件，默认显示上传选项卡
  if (answerContent.value.files && answerContent.value.files.length > 0) {
    return 'upload';
  }

  // 否则显示在线录入选项卡
  return 'online';
};

// 选项卡状态
const currentActiveTab = ref(props.activeTab || getActiveTab());

// 从store获取只读状态
const readonly = computed(() => props.readonly || homeworkStore.readonly);

// 从store中获取当前题目的画板图片URL
const drawingImageUrl = computed(() => {
  // 获取题目ID
  const questionId = props.parentQuestionId;
  if (!questionId) return '';

  // 获取题目答案
  const answer = homeworkStore.getUserAnswer(questionId);
  if (!answer || !answer.answer) return '';

  // 检查是否是画板数据
  const answerData = answer.answer;
  if (typeof answerData === 'object' && answerData !== null) {
    // 使用类型断言避免类型错误
    const answerDataObj = answerData as any;

    // 先检查新版画板数据格式 - ComplexAnswerContent.drawing
    if (answerDataObj.drawing && answerDataObj.drawing.imageUrl) {
      return answerDataObj.drawing.imageUrl;
    }

    // 兼容旧格式
    if (answerDataObj.type === 'drawing' && answerDataObj.imageUrl) {
      return answerDataObj.imageUrl;
    }
  }

  return '';
});

// 是否有画板图片
const hasDrawingImage = computed(() => {
  return !!drawingImageUrl.value;
});

// 是否有附件
const hasFiles = computed(() => {
  return answerContent.value.files && answerContent.value.files.length > 0;
});

// 过滤重复的附件
const filteredFiles = computed(() => {
  if (!answerContent.value.files || answerContent.value.files.length === 0) {
    return [];
  }

  const seen = new Map();
  return answerContent.value.files.filter((file: UploadFileData) => {
    // 使用多种方式生成唯一标识
    const key =
      file.id ||
      `${file.fileName}_${file.fileSize || 0}_${file.fileType || ''}` ||
      `${file.fileName}_${file.fileUrl || ''}`;

    if (seen.has(key)) {
      console.log('发现重复文件:', file.fileName, '已过滤');
      return false; // 重复文件，过滤掉
    }

    seen.set(key, true);
    return true;
  });
});

// 获取答案状态
const answerStatus = computed(() => {
  // 如果状态是已经完成 或 showAnswer 为 true 判断是否显示答案
  if (homeworkStore.showAnswer || homeworkStore.status == HomeworkStatus.COMPLETED) {
    // 检查答案中的 correct 字段
    if (props.answer && 'correct' in props.answer) {
      return props.answer.correct === true ? 'correct' : 'incorrect';
    }
  }

  return props.answerStatus || '';
});

// 切换选项卡
const switchTab = (tab: 'online' | 'upload') => {
  currentActiveTab.value = tab;
};

// 文本框占位符
const textareaPlaceholder = computed(() => {
  return props.isShortAnswer ? '请输入简要回答...' : '请在此处作答...';
});

// 是否显示"暂未上传"消息
const shouldShowEmptyAttachmentMessage = computed(() => {
  // 只在只读模式下显示，且附件为空时显示
  return readonly.value && (!answerContent.value.files || answerContent.value.files.length === 0);
});

// 处理文本输入
const handleTextInput = (value: string) => {
  if (readonly.value) return;

  emit('update:answer', props.subQuestion.id, {
    text: value,
    files: answerContent.value.files || [],
    drawing: answerContent.value.drawing,
  });
};

// 处理删除文件 - 改为传递文件对象而不是索引
const handleDeleteFile = (file: UploadFileData) => {
  if (readonly.value) return;
  emit('delete-file', file);
};

// 打开上传弹窗
const openUploadPopup = () => {
  // 如果只读模式，则不显示上传弹窗
  if (readonly.value) return;

  // 检查文件数量限制
  const currentFileCount = answerContent.value.files ? answerContent.value.files.length : 0;

  if (currentFileCount >= maxFileCount) {
    uni.showToast({
      title: '最多可上传9个文件',
      icon: 'none',
      duration: 2000,
    });
    return;
  }
  // 触发上传事件
  emit('upload');
};

const handleFullscreen = () => {
  // 只读模式下不触发全屏事件
  if (readonly.value) return;

  const parentQuestionId = props.parentQuestionId;
  if (!parentQuestionId) return;

  eventBus.emit(HomeworkEventNames.OPEN_DRAWING_BOARD_POPUP, {
    parentQuestionId: parentQuestionId,
    subQuestionId: null,
  });
};

// 在组件挂载后检查画板数据
onMounted(() => {
  // 延迟一下，确保数据已经加载
  setTimeout(() => {
    checkDrawingData();
  }, 100);
});

// 检查画板数据是否存在并打印相关信息
const checkDrawingData = () => {
  // 可以在这里添加调试逻辑
};
</script>

<style lang="scss" scoped>
.question-item {
  margin-bottom: 30rpx;

  .tab-container {
    display: flex;
    margin-bottom: 20rpx;
    background-color: #f5f5f5;
    border-radius: 16rpx;
    padding: 6rpx;

    .tab-item {
      flex: 1;
      text-align: center;
      padding: 16rpx 0;
      font-size: 28rpx;
      color: #999;
      position: relative;
      border-radius: 16rpx;
      transition: all 0.3s;

      &.active {
        color: #6951ff;
        font-weight: 500;
        background-color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }
    }
  }

  .textarea-container {
    margin-bottom: 20rpx;
  }

  .upload-area {
    margin-bottom: 20rpx;
  }

  .upload-file-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 200rpx;
    background-color: #f9f9f9;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .upload-icon {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
    }

    .upload-btn-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .file-list {
    margin-bottom: 10rpx;
  }

  /* 调整uni-row的间距 */
  :deep(.uni-row) {
    margin-left: -5rpx !important;
    margin-right: -5rpx !important;
  }

  /* 调整uni-col的间距 */
  :deep(.uni-col) {
    padding-left: 5rpx !important;
    padding-right: 5rpx !important;
  }

  .file-item {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 12rpx;
    margin-bottom: 10rpx;
    box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.05);
    min-height: 70rpx;
    border-radius: 8px;
    border: 1px solid #dcdcdc;
    background: #fff;

    .file-icon {
      width: 80rpx;
      height: 80rpx;
      margin-right: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      border-radius: 4rpx;
      overflow: hidden;

      .file-thumbnail,
      .file-icon-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .file-name {
      flex: 1;
      font-size: 24rpx;
      color: #333;
      line-height: 1.2;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 24rpx;
    }

    .file-delete {
      position: absolute;
      top: 0rpx;
      right: 0rpx;
      width: 30rpx;
      height: 30rpx;
      background-color: #999;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0px 8px 0px 5.766px;
      background: rgba(0, 0, 0, 0.4);

      .icon-close {
        font-size: 16rpx;
        color: #fff;
        line-height: 1;
        font-weight: bold;
      }
    }
  }

  .no-attachment {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
  }

  .analysis {
    margin-top: 16rpx;
    padding: 16rpx;
    background-color: rgba(255, 236, 217, 0.3);
    border-left: 4rpx solid #ff9800;
    border-radius: 6rpx;

    .analysis-title {
      font-weight: bold;
      color: #ff9800;
      font-size: 26rpx;
      margin-bottom: 8rpx;
    }

    .analysis-content {
      font-size: 26rpx;
      color: #666;
    }
  }
}
</style>
