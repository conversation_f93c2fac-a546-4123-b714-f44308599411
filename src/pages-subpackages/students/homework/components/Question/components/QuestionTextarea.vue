<template>
  <view class="question-textarea">
    <textarea
      v-if="!isReadonly"
      class="answer-textarea"
      :value="modelValue"
      @input="handleInput"
      :placeholder="placeholder"
      :maxlength="maxlength"
    ></textarea>
    <view v-else class="readonly-textarea">
      <text>{{ modelValue || emptyText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';

const homeworkStore = useHomeworkStore();

const props = defineProps({
  // v-model绑定值
  modelValue: {
    type: String,
    default: '',
  },
  // 占位文本
  placeholder: {
    type: String,
    default: '请在此处作答...',
  },
  // 最大字符数
  maxlength: {
    type: Number,
    default: -1,
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false,
  },
  // 空值显示文本
  emptyText: {
    type: String,
    default: '未作答',
  },
});

// 计算是否只读，考虑 homeworkStore 的 showAnswer 状态
const isReadonly = computed(() => props.readonly || homeworkStore.showAnswer);

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'input', value: string): void;
}>();

// 处理输入事件
const handleInput = (e: any) => {
  const value = e.detail?.value || '';
  emit('update:modelValue', value);
  emit('input', value);
};
</script>

<style lang="scss" scoped>
.question-textarea {
  width: 100%;

  .answer-textarea {
    width: 100%;
    min-height: 200rpx;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    background-color: #fff;
    box-sizing: border-box;
  }

  .readonly-textarea {
    width: 100%;
    min-height: 200rpx;
    border: 1rpx solid #e0e0e0;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    background-color: #f8f8f8;
    color: #666;
    box-sizing: border-box;
    white-space: pre-wrap;
  }
}
</style>
