<template>
  <view class="question-input">
    <input
      v-if="!readonly"
      class="answer-input"
      type="text"
      :value="modelValue"
      @input="handleInput"
      :placeholder="placeholder"
      :maxlength="maxlength"
      :disabled="disabled"
    />
    <view v-else class="readonly-input">
      <text>{{ modelValue || emptyText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  // v-model绑定值
  modelValue: {
    type: String,
    default: '',
  },
  // 占位文本
  placeholder: {
    type: String,
    default: '请填写...',
  },
  // 最大字符数
  maxlength: {
    type: Number,
    default: 100,
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 空值显示文本
  emptyText: {
    type: String,
    default: '未填写',
  },
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'input', value: string): void;
}>();

// 处理输入事件
const handleInput = (e: any) => {
  const value = e.detail?.value || '';
  emit('update:modelValue', value);
  emit('input', value);
};
</script>

<style lang="scss" scoped>
.question-input {
  display: inline-block;
  vertical-align: middle;

  .answer-input {
    min-width: 120rpx;
    height: 60rpx;
    border-bottom: 2rpx solid #ddd;
    padding: 0 10rpx;
    font-size: 28rpx;
    background-color: #fff;
    box-sizing: border-box;
    text-align: center;

    &:focus {
      border-bottom-color: #3370ff;
    }
  }

  .readonly-input {
    min-width: 120rpx;
    height: 60rpx;
    border-bottom: 2rpx solid #e0e0e0;
    padding: 0 10rpx;
    font-size: 28rpx;
    background-color: #f8f8f8;
    color: #666;
    box-sizing: border-box;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
