/**
 * 组件注册表
 * 用于动态注册题目组件，支持自定义题目组件覆盖默认模板
 */

import { QuestionType } from '@/constants/students/homework';
import { getTemplateType } from './templates';

// 自定义组件注册表
const customComponents: Record<string, any> = {};

/**
 * 注册自定义题目组件
 * @param type 题目类型
 * @param component 自定义组件
 */
export function registerQuestionComponent(type: QuestionType, component: any) {
  customComponents[type] = component;
}

/**
 * 获取题目组件类型，优先返回自定义类型，否则返回默认模板类型
 * @param type 题目类型
 */
export function getComponentType(type: QuestionType): string {
  // 优先使用已注册的自定义组件类型
  if (hasCustomComponent(type)) {
    return type;
  }

  // 如果没有自定义组件，使用默认模板类型
  return getTemplateType(type);
}

/**
 * 检查是否有针对该题型的自定义组件
 * @param type 题目类型
 */
export function hasCustomComponent(type: QuestionType): boolean {
  return !!customComponents[type];
}

export { customComponents };
