<template>
  <view class="my-extra-block-container">
    <view class="block-header" @click="onHeaderClick">
      <view class="block-title-wrapper">
        <view class="divider-line"></view>
        <text class="block-title">{{ title }}</text>
        <view class="divider-line"></view>
      </view>
      <view class="header-right">
        <slot name="header-right"></slot>
      </view>
    </view>
    <view class="block-content">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['header-click']);

function onHeaderClick(e: Event) {
  emit('header-click', e);
}
</script>

<style lang="scss" scoped>
.my-extra-block-container {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.block-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  position: relative;
  cursor: pointer;

  .block-title-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
  }

  .divider-line {
    width: 64rpx;
    height: 1px;
    background-color: #e7e7e7;
    margin: 0 12rpx;
  }

  .block-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #000000;
    width: 145rpx;
    text-align: center;
  }

  .header-right {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
  }
}

.block-content {
  padding: 16rpx 0;
}
</style>
