<template>
  <view class="answer-question-container">
    <!-- 头部内容 -->
    <view class="header-box" v-if="homeworkStore.answerType !== HomeworkTaskType.WRITING">
      <view class="left-group">
        <!-- 订正选项卡 -->
        <!-- <view class="correction-tabs" v-if="homeworkStore.batches.length > 1">
          <view v-for="(batch, index) in homeworkStore.batches" :key="batch.id" class="correction-tab"
            :class="{ active: currentBatchIndex === index }" @click="switchBatch(index)">
            <view class="correction-tab-text">
              {{ batch.name }}
            </view>
          </view>
        </view> -->

        <CorrectionTabs
          v-model:currentIndex="currentCorrectionIndex"
          :tabs="homeworkStore.batches"
          @change="switchBatch"
        />
      </view>
      <!-- 计时器 -->
      <view class="clock-box" v-show="homeworkStore.shouldShowTimer">
        <LkSvg
          src="/static/homework/clock.svg"
          width="40rpx"
          height="40rpx"
          color="#333333"
          class="clock-icon"
        />
        <text class="clock-text">{{ formattedTime }}</text>
        <!-- <text class="clock-text">{{ timer.formattedTime }}</text> -->
      </view>
    </view>

    <!-- 调试打印数据 -->
    <!-- <view>
            {{ homeworkStore.batches }}
        </view> -->
    <!-- 进度栏，仅答题tab显示 -->
    <view class="progress-box" v-if="homeworkStore.answerType !== HomeworkTaskType.WRITING">
      <!-- 做题计时已移动到detail.vue -->
    </view>
    <!-- 作业内容 -->
    <view class="homework-box">
      <!-- 由于小程序/uni-app等跨端环境不支持 <component :is="..."> 动态组件语法，
                 这里改为条件渲染，保证兼容性和稳定性 -->
      <HomeworkOnline v-if="homeworkStore.answerType === HomeworkTaskType.ONLINE" />
      <HomeworkPaper v-else-if="homeworkStore.answerType === HomeworkTaskType.PAPER" />
      <HomeworkWriting v-else-if="homeworkStore.answerType === HomeworkTaskType.WRITING" />
    </view>
    <!-- 工具栏，仅答题tab显示 -->
    <view class="tools-box">
      <view class="left-group" v-if="isShowElement('answerCard')">
        <view class="answer-card-btn" @click="showAnswerCard">
          <LkSvg width="44rpx" height="44rpx" src="/static/homework/answer_card.svg" />
          <text>答题卡</text>
        </view>
      </view>
      <view class="right-group">
        <!-- <LkButton class="homework-btn default-btn" block type="secondary" @click="showRecommendExercises">推荐练习
        </LkButton> -->
        <!-- 已完成 -->
        <template v-if="isShowElement('completed')">
          <view class="right-group-item">
            <LkButton class="homework-btn default-btn" block type="secondary" @click="toErrorBook"
              >错题本</LkButton
            >
          </view>
          <view class="right-group-item">
            <LkButton class="homework-btn" block @click="toScore">查看成绩 </LkButton>
          </view>
        </template>
        <!-- 待批改 截止时间内-->
        <view class="right-group-item" v-else-if="isShowElement('toBeCorrected')">
          <LkButton class="homework-btn" block type="primary" @tap="onToBeCorrected"
            >修改作业
          </LkButton>
        </view>
        <!-- 待批改 截止时间外 -->
        <view class="right-group-item" v-else-if="isShowElement('toBeCorrectedOvertime')">
          <LkButton class="homework-btn" block type="primary" disabled>待批改 </LkButton>
        </view>
        <!-- 批改中 -->
        <view class="right-group-item" v-else-if="isShowElement('correcting')">
          <LkButton class="homework-btn" block type="primary" disabled>批改中 </LkButton>
        </view>
        <!-- 未提交 -->
        <!-- <view class="right-group-item" v-else-if="isShowElement('notSubmitted')">
          <LkButton class="homework-btn" block type="primary" disabled>未提交 </LkButton>
        </view> -->
        <!-- 待提交 或者 待批改 或者 待订正 或者 未提交-->
        <template v-else-if="isShowElement('toBeSubmitted')">
          <view class="right-group-item">
            <LkButton
              :disabled="homeworkStore.isSubmitDisabled"
              class="homework-btn default-btn"
              block
              @click="clearAnswers"
            >
              {{ homeworkStore.answerType === HomeworkTaskType.ONLINE ? '清空答题记录' : '清空' }}
            </LkButton>
          </view>
          <view class="right-group-item">
            <LkButton
              class="homework-btn"
              :disabled="homeworkStore.isSubmitDisabled"
              block
              type="primary"
              @click="onSubmit"
            >
              提交作业</LkButton
            >
          </view>
        </template>
        <!-- <LkButton class="homework-btn" block type="primary" @click="onUpdate">
                    修改作业</LkButton> -->
      </view>
    </view>

    <!-- 答题卡弹出层 -->
    <PopupAnswerCard
      ref="answerCardPopupRef"
      v-model:open="isAnswerCardOpen"
      @clearAnswers="clearAnswers"
      @submit="onSubmit"
    />

    <!-- 解析纠错弹出层 -->
    <PopupFeedback ref="feedbackPopupRef" :questionId="currentQuestionId" />

    <!-- 笔记弹出层 -->
    <PopupNote ref="notePopupRef" :questionId="currentQuestionId" />

    <!-- 上传弹出层 -->
    <MyUploadPopup
      ref="uploadPopupRef"
      @upload-success="handleUploadSuccess"
      @upload-fail="handleUploadFail"
    />

    <!-- 绘图弹出层 -->
    <PopupDrawingBoard
      ref="drawingBoardPopupRef"
      :questionId="currentQuestionId"
      :subQuestionId="currentSubQuestionId"
    />
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import {
  HomeworkOnline,
  HomeworkPaper,
  HomeworkWriting,
  PopupAnswerCard,
  PopupFeedback,
  PopupNote,
} from '..';
import PopupDrawingBoard from '../PopupDrawingBoard/index.vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { useEventBus } from '@/hooks/useEventBus';
import LkSvg from '@/components/svg/index.vue';
import {
  HomeworkStatus,
  HomeworkEventNames,
  HomeworkTaskType,
} from '@/constants/students/homework';
import { LkButton } from '@/components';
import { showDialogInfo } from '@/components/MyDialog';
import MyUploadPopup, { type UploadFileData } from '@/components/MyUploadPopup';
import { showDialogDanger, showDialogConfirm } from '@/components/MyDialog';
import { useTimer } from '@/hooks/useTimer';
import { getHomeworkStatus } from '@/api/students/homework';
import { isEmptyValue } from '../../utils';
import CorrectionTabs from '../CorrectionTabs/index.vue';

const homeworkStore = useHomeworkStore();
const eventBus = useEventBus();
const isFirstLoaded = ref(true);
const maxSeconds = computed(() => {
  const deadlineTime = homeworkStore.backendData.deadlineTime;
  // const deadlineTime = "2025-07-31 18:50:50";
  const deadlineTimestamp = new Date(deadlineTime).getTime();
  const now = Date.now();
  const maxSeconds = Math.floor((deadlineTimestamp - now) / 1000);
  return maxSeconds;
});
// 添加计时器相关逻辑
// 初始化计时器，使用store中的已计时时间
const timer = useTimer({
  initialSeconds: homeworkStore.elapsedTime,
  interval: 1000,
  autoStart: false,
  // maxSeconds: maxSeconds.value,
  onTick: seconds => {
    // 实时同步时间到store
    homeworkStore.updateElapsedTime(seconds);
  },
  onFinished() {
    console.log('已经达到最大作答时间');
    // showDialogInfo({
    //   content: '已经达到最大作答时间',
    // });
    // homeworkStore.setIsReachedDeadlineTime(true);
    // homeworkStore.status = HomeworkStatus.TO_BE_SUBMITTED;
  },
});
// 格式化的时间
const formattedTime = computed(() => {
  return timer.getFormattedTime();
});
const answerCardPopupRef = ref(null);
const feedbackPopupRef = ref(null);
const notePopupRef = ref(null);
const homeworkComponentRef = ref(null);
const isAnswerCardOpen = ref(false);
const currentQuestionId = ref('');
const uploadPopupRef = ref<InstanceType<typeof MyUploadPopup> | null>(null);
const drawingBoardPopupRef = ref<InstanceType<typeof PopupDrawingBoard> | null>(null);
const currentCorrectionIndex = ref(homeworkStore.batches.length - 1);
const questions = computed(() => homeworkStore.questions);
const userAnswers = computed(() => homeworkStore.userAnswers);
// 移除答题进度计算，已移动到QuestionTitle组件
const currentBatchIndex = computed(() => homeworkStore.currentBatchIndex);
const allAnswered = computed(
  () => homeworkStore.answeredCount === questions.value.length && questions.value.length > 0
);
const status = computed(() => homeworkStore.status);

// 监听状态切换变化，控制计时器
const timerWatch = watch(
  () => homeworkStore.shouldShowTimer,
  shouldShow => {
    // if (isFirstLoaded.value) {
    //   return;
    // }
    // // 只有在线作业才启动计时器
    // if (shouldShow) {
    //   //  重置计时器，重新获取已计时时间
    //   timer.setConfig({
    //     initialSeconds: homeworkStore.elapsedTime || 0,
    //   });
    //   timer.resume();
    // } else {
    //   timer.pause();
    // }
  }
);
// 监听后端数据中的已计时时间
const submitListWatch = watch(
  () => homeworkStore.backendData.submitList,
  newVal => {
    if (!homeworkStore.shouldShowTimer) return;
    if (newVal && newVal.length > 0) {
      timer.setConfig({
        initialSeconds: newVal[0].timeSecond || 0,
      });
      isFirstLoaded.value = false;
    }
  }
);
// 监听状态变化，控制计时器
watch(
  () => [homeworkStore.isFinishGestureGuide, homeworkStore.toBeCorrectedEditable],
  newVal => {
    // 如果未完成手势引导，则不控制计时器
    if (!newVal[0]) return;

    // 待批改状态，控制计时器
    if (homeworkStore.status === HomeworkStatus.TO_BE_CORRECTED) {
      if (newVal[1]) {
        timer.resume();
      }
      return;
    }
    if (homeworkStore.status !== HomeworkStatus.COMPLETED) {
      timer.resume();
    }
  },
  {
    immediate: true,
  }
);

// 如果当前作业状态已经完成且当前订正改变更新计时器时间为订正的最后时间
watch(
  () => [homeworkStore.status, currentCorrectionIndex.value],
  newVal => {
    if (
      homeworkStore.status === HomeworkStatus.COMPLETED &&
      homeworkStore.currentBatchIndex === currentCorrectionIndex.value
    ) {
      timer.setSeconds(
        homeworkStore.backendData.submitList[currentCorrectionIndex.value].timeSecond || 0
      );
    }
  },
  {
    immediate: true,
  }
);

const initEvents = () => {
  // 监听纠错弹框事件
  eventBus.on(HomeworkEventNames.OPEN_FEEDBACK_POPUP, handleFeedbackEvent);

  // 监听笔记弹框事件
  eventBus.on(HomeworkEventNames.OPEN_NOTE_POPUP, handleNoteEvent);

  // 监听上传文件弹框事件
  eventBus.on(HomeworkEventNames.OPEN_UPLOAD_POPUP, handleUploadPopupEvent);

  // 监听绘图弹框事件
  eventBus.on(HomeworkEventNames.OPEN_DRAWING_BOARD_POPUP, openDrawingBoardPopup);
};
initEvents();
// 在组件挂载时注册事件监听
onMounted(() => {
  // 如果作业状态为待提交，则默认启动计时器
  if (
    (homeworkStore.status === HomeworkStatus.TO_BE_SUBMITTED ||
      homeworkStore.status === HomeworkStatus.TO_BE_REVISED) &&
    !isEmptyValue(homeworkStore.backendData) &&
    homeworkStore.isFinishGestureGuide
  ) {
    timer.resume();
  }
});

// 在组件卸载时移除事件监听，避免内存泄漏
onUnmounted(() => {
  eventBus.off(HomeworkEventNames.OPEN_FEEDBACK_POPUP);
  eventBus.off(HomeworkEventNames.OPEN_NOTE_POPUP);
  eventBus.off(HomeworkEventNames.OPEN_UPLOAD_POPUP);
  eventBus.off(HomeworkEventNames.OPEN_DRAWING_BOARD_POPUP);

  // 彻底清理计时器资源
  timer.destroy();

  // 清理 watch 监听器
  timerWatch();
  submitListWatch();
});

function switchBatch(index: number) {
  homeworkStore.setBatchIndex(index);
}

const onSubmit = async () => {
  if (
    homeworkStore.answerType === HomeworkTaskType.PAPER ||
    homeworkStore.answerType === HomeworkTaskType.WRITING
  ) {
    // 纸质作业
    // 检查附件是否存在且是否有错误
    let files: any[] = [];
    debugger;
    if (homeworkStore.answerType === HomeworkTaskType.WRITING) {
      const answers = homeworkStore.userAnswers;
      if (!isEmptyValue(answers)) {
        const answer = Object.values(answers)[0]?.answer;
        if (answer && typeof answer === 'object' && 'files' in answer) {
          files = answer?.files || [];
        }
      }
    } else if (homeworkStore.answerType === HomeworkTaskType.PAPER) {
      files = homeworkStore.currentBatch?.files || [];
    }

    // 检查附件是否存在
    if (files && files.length === 0) {
      showDialogInfo({
        content: '请上传图片',
      });
      return;
    }
    // 检查附件是否存在错误
    const isAllRight = files.every(files => !files.error);
    console.log('isAllRight', files);
    if (!isAllRight) {
      showDialogInfo({
        content: '存在未识别的内容',
      });
      return;
    }
  } else {
    // 在线作业
    // 验证是否完成所有题目
    // if (!allAnswered.value) {
    //   showDialogInfo({
    //     content: '请完成所有题目',
    //   });
    //   return;
    // }
  }

  let confirmContent = `确定要提交作业吗？`;
  // 获取未答题的数量
  const unAnsweredCount = questions.value.length - homeworkStore.answeredCount;
  if (homeworkStore.answerType == HomeworkTaskType.ONLINE && unAnsweredCount > 0) {
    confirmContent = `还有${unAnsweredCount}道题未完成，确定要提交作业吗？`;
  }

  await showDialogConfirm({
    title: '提示',
    content: confirmContent,
    async onConfirm() {
      try {
        // uni.showLoading({
        //   title: '提交中...',
        // });

        // 使用store中的公共保存逻辑
        const result = await homeworkStore.saveHomework({ status: 1 });

        if (result.success) {
          setTimeout(() => {
            showDialogInfo({
              content: '提交成功',
              onConfirm() {
                homeworkStore.setAutoSaveAnswer(false);
                // 刷新页面
                // uni.redirectTo({
                //   url: '/pages-subpackages/students/homework/index',
                // });
                uni.redirectTo({
                  url: `/pages-subpackages/students/homework/submit-success?id=${homeworkStore.backendData.homeworkId}&subTitle=${homeworkStore.backendData.subjectName}&title=${homeworkStore.backendData.name}`,
                });
              },
            });
          }, 500);
        }
      } catch (error) {
        console.error('提交作业失败:', error);
      } finally {
        // uni.hideLoading();
      }
    },
  });
};

function onUpdate() {
  showDialogInfo({
    title: '提示',
    content: '老师正在批改你的作业,当前无法修改',
  });
}
function onCancel() {
  uni.navigateBack();
}

function showAnswerCard() {
  isAnswerCardOpen.value = true;
}

// 处理解析纠错事件
function handleFeedbackEvent(questionId: string) {
  currentQuestionId.value = questionId;
  if (feedbackPopupRef.value) {
    // @ts-ignore - 忽略类型检查错误
    feedbackPopupRef.value.open();
  }
}

// 处理笔记事件
function handleNoteEvent(questionId: string, note: any) {
  currentQuestionId.value = questionId;

  if (notePopupRef.value) {
    // @ts-ignore - 忽略类型检查错误
    notePopupRef.value.open({
      ...note,
    });
  }
}

// 跳转到错题本
function toErrorBook() {
  uni.navigateTo({
    url: `/pages-subpackages/students/error-book/detail?homeworkId=${homeworkStore.backendData.homeworkId}&status=1`,
  });
}

// 跳转到成绩
function toScore() {
  const query = {
    id: homeworkStore.backendData.homeworkId,
    stuHomeworkId: homeworkStore.backendData.id,
    subjectName: homeworkStore.backendData.subjectName,
    title: homeworkStore.backendData.name,
    type: 'single',
    submitId: undefined,
  };
  if (homeworkStore.answerType === HomeworkTaskType.WRITING) {
    query.type = 'writing';
    query.submitId = homeworkStore.backendData.submitList[0].id;
  } else {
    query.type = 'single';
  }
  // 将query转成字符串
  const queryString = Object.entries(query)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  uni.navigateTo({
    url: `/pages-subpackages/students/homework/score?${queryString}`,
  });
}

// 清空答题记录
async function clearAnswers() {
  try {
    const confirmed = await showDialogDanger({
      title: '提示',
      content: '清空答题记录将不可恢复,确定清空全部答题记录吗?',
    });

    if (confirmed) {
      // 调用工具函数清空答题记录
      await homeworkStore.clearUserAnswers();
      // 清空计时
      timer.reset();
      // 开始计时
      timer.start();

      setTimeout(() => {
        showDialogInfo({
          content:
            homeworkStore.answerType === HomeworkTaskType.PAPER ||
            homeworkStore.answerType === HomeworkTaskType.WRITING
              ? '上传作业删除成功'
              : '清空答题记录成功',
        });
      }, 300);
    }
  } catch (error) {
    console.error('清空答题记录出错:', error);
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    });
  }
}

function isShowElement(name: string) {
  let isShow = false;
  switch (name) {
    // 答案卡片
    case 'answerCard':
      if (homeworkStore.answerType === HomeworkTaskType.ONLINE) {
        isShow = true;
      } else if (homeworkStore.answerType === HomeworkTaskType.PAPER) {
        if (homeworkStore.status == HomeworkStatus.COMPLETED) {
          isShow = true;
        } else {
          isShow = homeworkStore.readonly && !homeworkStore.isLastBatch;
        }
      }
      break;
    // 已完成
    case 'completed':
      isShow = status.value === HomeworkStatus.COMPLETED;
      break;
    // 待批改（截止时间外）
    case 'toBeCorrectedOvertime':
      // 底栏按钮变化为【待批改】，不可点击
      isShow =
        status.value === HomeworkStatus.TO_BE_CORRECTED && homeworkStore.isReachedDeadlineTime;
      break;
    // 待批改 截止时间内
    case 'toBeCorrected':
      if (
        status.value === HomeworkStatus.TO_BE_CORRECTED &&
        !homeworkStore.toBeCorrectedEditable &&
        !homeworkStore.isReachedDeadlineTime
      ) {
        isShow = true;
      }
      break;
    // 待提交
    case 'toBeSubmitted':
      if (
        status.value === HomeworkStatus.TO_BE_SUBMITTED ||
        status.value === HomeworkStatus.NOT_SUBMITTED
      ) {
        isShow = true;
      } else if (status.value === HomeworkStatus.TO_BE_CORRECTED) {
        isShow = homeworkStore.toBeCorrectedEditable;
      } else if (status.value === HomeworkStatus.TO_BE_REVISED) {
        isShow = true;
      }
      break;
    // 批改中
    case 'correcting':
      isShow = status.value === HomeworkStatus.CORRECTING;
      break;
    // 待订正
    case 'toBeRevised':
      isShow = status.value === HomeworkStatus.TO_BE_REVISED;
      break;
    // 未提交
    case 'notSubmitted':
      isShow = status.value === HomeworkStatus.NOT_SUBMITTED;
      break;
    default:
      break;
  }
  return isShow;
}

// 当前上传的子题ID
const currentSubQuestionId = ref<string>();

// 处理上传弹窗事件
function handleUploadPopupEvent(data: { questionId: string; subQuestionId: string | null }) {
  // 存储当前上传的题目信息
  currentQuestionId.value = data.questionId;
  currentSubQuestionId.value = data.subQuestionId || '';
  // 打开上传弹窗
  uploadPopupRef.value?.open({
    extension: ['.doc', '.docx', '.pdf', '.jpg', '.jpeg', '.png', '.mp4'],
    count: 1,
  });
}

function handleUploadSuccess(fileData: UploadFileData) {
  console.log('handleUploadSuccess', fileData);

  // 触发上传成功事件，通知对应的题目组件
  // eventBus.emit(HomeworkEventNames.UPLOAD_SUCCESS, {
  //   fileData,
  //   questionId: currentQuestionId.value,
  //   subQuestionId: currentSubQuestionId.value,
  // });

  const currentAnswer = homeworkStore.getUserAnswer(currentQuestionId.value)?.answer || {};

  const newAnswer = {
    ...currentAnswer,
    files: [...(currentAnswer.files || []), fileData],
  };
  homeworkStore.setUserAnswer(currentQuestionId.value, newAnswer);
}

function handleUploadFail(error: any) {
  console.log('handleUploadFail', error);
  let msg = error.errorMsg || '上传失败';
  if (error.errorType === 'fileType') {
    msg = '支持文件类型:Word、PDF、MP4、图片';
  }
  uni.showToast({
    title: msg,
    icon: 'none',
    duration: 1500,
  });
}

// 处理绘图弹框事件
function openDrawingBoardPopup(data: { parentQuestionId: string; subQuestionId: string | null }) {
  currentQuestionId.value = data.parentQuestionId;
  // 处理subQuestionId可能为null的情况
  currentSubQuestionId.value = data.subQuestionId || '';
  drawingBoardPopupRef.value?.open();
}

// 修改作业
async function onToBeCorrected() {
  if (!homeworkStore.isLastBatch) {
    return;
  }

  let result = await getHomeworkStatus({ id: homeworkStore.backendData?.id });
  if (result == HomeworkStatus.CORRECTING) {
    showDialogInfo({
      content: '教师正在批改作业，不可修改',
    });
    homeworkStore.setToBeCorrectedEditable(false);
    homeworkStore.setStatus(HomeworkStatus.CORRECTING);
  } else {
    homeworkStore.setToBeCorrectedEditable(true);
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/index.scss';

page {
  height: 100%;
  overflow: hidden;
}

.answer-question-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  margin-bottom: 20rpx;

  .left-group {
    width: calc(100% - 120rpx);
    overflow-x: auto;
    margin-right: 10rpx;
  }
}

.correction-tabs {
  display: flex;
  align-items: center;
  height: 86rpx;
  overflow-x: auto;
  flex-shrink: 0;
  white-space: nowrap;
  flex: 1;

  .correction-tab {
    padding: 32rpx 32rpx;
    font-size: 28rpx;
    height: 44rpx;
    line-height: 44rpx;
    border-radius: 999px;
    margin-right: 8rpx;
    cursor: pointer;
    color: #1d2129;
    background-color: #f3f3f3;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      background-color: #ffffff;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      background: linear-gradient(114deg, rgba(77, 163, 255, 0.1) 0%, rgba(125, 77, 255, 0.1) 100%);

      .correction-tab-text {
        background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 500;
      }
    }
  }
}

.progress-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d2129;
}

/* 添加计时器样式 */
.clock-box {
  // width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.clock-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  width: 70rpx;
  text-align: right;
}

/* 添加 CSS 滤镜来确保图标显示为黑色 */
.clock-icon {
  filter: brightness(0);
}

.homework-box {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  // padding: 0 32rpx;
}

.tools-box {
  height: 120rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  flex-shrink: 0;
  border-top: 1rpx solid #e7e7e7;
  gap: 10rpx;

  .left-group,
  .right-group {
    display: flex;
    align-items: center;
  }

  .right-group {
    flex-grow: 1;
    justify-content: space-between;
    overflow-x: auto;
    white-space: nowrap;
    gap: 30rpx;
    padding: 0rpx 20rpx;

    .right-group-item {
      flex: 1;
    }
  }

  .answer-card-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #4e5969;
  }

  .tools-btn {
    border-radius: 100rpx;
    font-weight: initial;

    &.default-btn {
      border: 1px solid #dcdcdc;
      background-color: #ffffff;
      color: #4e5969;
    }

    &.submit-btn {
      background-color: #7d4dff;
      color: #fff;
    }
  }

  // button {
  //     flex: 1;
  //     background-color: #f5f5f5;
  //     color: #333;
  //     border-radius: 8rpx;
  //     height: 80rpx;
  //     line-height: 80rpx;
  //     font-size: 26rpx;
  //     padding: 0 20rpx;
  //     min-width: 90rpx;
  //     margin: 0 6rpx;
  //     flex-shrink: 0;
  //     text-align: center;

  //     &:first-child {
  //         margin-left: 0;
  //     }

  //     &:last-child {
  //         margin-right: 0;
  //     }

  //     &.clear-btn {
  //         color: #4E5969;
  //         background-color: #FFFFFF;
  //         border: 1px solid #DCDCDC;
  //     }

  //     &.submit-btn {
  //         background-color: #7D4DFF;
  //         color: #fff;
  //         min-width: 120rpx;
  //     }

  //     &:disabled {
  //         background-color: #cccccc;
  //         color: #fff;
  //     }
  // }
}
</style>
