<template>
  <view class="resource-list">
    <template v-if="resources.length > 0">
      <view
        v-for="(item, idx) in resources"
        :key="idx"
        class="resource-item"
        @click="goDetail(item)"
      >
        <view class="icon-wrap">
          <image class="icon" :src="getImageIcon(item.type)" mode="aspectFit"> </image>
        </view>
        <view class="info">
          <view class="title">{{ item.title }}</view>
          <MyEllipsisBox :lines="1" class="tags">
            <template v-for="(tag, i) in item.tags" :key="i">
              <text class="tag" v-if="tag">{{ tag }}</text>
            </template>
          </MyEllipsisBox>
          <view class="size">{{ getFileSizeText(item.size) }}</view>
        </view>
      </view>
    </template>
    <view v-else-if="resources.length === 0 && !isLoading" class="empty">
      <MyEmpty />
    </view>
    <MyLoading v-if="isLoading" type="spinner" overlay />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getHomeworkDetail } from '@/api/students/homework';
import { useHomeworkStore } from '@/store/homeworkStore';
import MyEmpty from '@/components/MyEmpty';
import MyLoading from '@/components/MyLoading';
import MyEllipsisBox from '@/components/MyEllipsisBox';
import { getFileSizeText } from '@/utils/common';

const homeworkStore = useHomeworkStore();

const isLoading = ref(false);
const resources = ref<any[]>([
  // {
  //   type: 'word',
  //   title: '三角形的内角和(拓展)(学案)-2023-2024',
  //   size: '386KB',
  //   tags: ['北师大版(2012)', '小学语文', '学案'],
  // },
  // {
  //   type: 'pdf',
  //   title: '三角形的内角和(拓展)(学案)-2023-2024',
  //   size: '386KB',
  //   tags: ['北师大版(2012)', '小学语文', '学案'],
  // },
  // {
  //   type: 'image',
  //   title: '三角形的内角和(拓展)(学案)-2023-2024',
  //   size: '386KB',
  //   tags: ['北师大版(2012)', '小学语文', '学案'],
  // },
]);

onMounted(() => {
  fetchResourceList();
});

// 获取资源列表（后端现在耦合在 detail接口返回的materialFileList字段当中，后续需要优化）
const fetchResourceList = async () => {
  try {
    isLoading.value = true;
    const res = await getHomeworkDetail({
      id: homeworkStore.homeworkId,
    });
    console.log(res);
    resources.value = res.materialFileList.map((item: any) => {
      return {
        item,
        type: item?.resourcesVO?.fileFormatName?.replace('.', ''),
        title: item.file?.fileName,
        size: item.file?.fileSize,
        // 资源类型+学科+教材版本+地区+年份+知识点
        tags: [
          item.resourcesVO?.resourceTypeName,
          item.resourcesVO?.subjectName,
          item.resourcesVO?.textbookVersionName,
          item.resourcesVO?.areaName,
          item.resourcesVO?.vintages,
          ...(item.resourcesVO?.knowledgePointNames || []),
        ],
      };
    });
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

function getImageIcon(type: string) {
  if (!type) return '';
  type = type.replace('.', '');
  switch (type) {
    case 'word':
      return '/static/fileTypeIcon/doc.svg';
    case 'pdf':
      return '/static/fileTypeIcon/pdf.svg';
    case 'image':
      return '/static/fileTypeIcon/image.svg';
    default:
      return `/static/fileTypeIcon/${type}.svg`;
  }
}

function goDetail(item: any) {
  uni.navigateTo({
    url: `/pages-subpackages/students/learning-resource/resource-details?id=${item.item.resourceId}`,
  });
}
</script>

<style lang="scss" scoped>
.resource-list {
  padding: 16rpx;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  .resource-item {
    display: flex;
    background: #fff;
    border-radius: 20rpx 28rpx 20rpx 20rpx;
    margin-bottom: 28rpx;
    padding: 28rpx;
    box-shadow: 0 0 30rpx 0 rgba(237, 237, 237, 0.62);
    border: 1px solid #f1f1f1;
  }

  .icon-wrap {
    margin-right: 20rpx;

    .icon {
      width: 84rpx;
      height: 84rpx;
      border-radius: 18rpx;
    }
  }

  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12rpx;
    /* 确保info容器有明确的宽度限制 */
    min-width: 0;
    width: 100%;

    .title {
      font-size: 32rpx;
      color: #1d2129;
      font-weight: 400;
      line-height: 1.5;
      letter-spacing: 0.5%;
      /* 单行显示，超出省略号 */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      /* 确保title有明确的宽度限制 */
      width: 100%;
      max-width: 100%;
    }

    .tags {
      .tag {
        display: inline-block;
        font-size: 24rpx;
        color: #4e5969;
        background: #f2f3f5;
        border-radius: 4rpx;
        padding: 3rpx 8rpx;
        margin-right: 16rpx;
        white-space: nowrap;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .size {
      font-size: 24rpx;
      color: #86909c;
      line-height: 1.8;
    }
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 120rpx;

    .empty-icon {
      font-size: 80rpx;
      color: #eee;
      margin-bottom: 20rpx;
    }

    .empty-text {
      font-size: 28rpx;
      color: #aaa;
    }
  }
}
</style>
