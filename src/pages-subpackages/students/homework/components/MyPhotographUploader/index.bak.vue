<template>
  <view class="my-photograph-uploader">
    <!-- 拍照上传部分 -->
    <view class="photo-upload" v-if="!readonly">
      <view class="upload-camera" @click="handleCameraUpload">
        <view class="upload-icon">
          <image src="/static/homework/camera.svg" mode="aspectFit"></image>
        </view>
        <view class="upload-text">作业拍照提交</view>
      </view>
      <LkButton class="upload-button" type="secondary" @click="handleSelectPhoto"
        >相册选择</LkButton
      >
    </view>

    <view class="section-header">
      <slot name="section-header"> </slot>
    </view>

    <!-- 已上传图片展示部分 -->
    <view class="uploaded-images-section" v-if="localFiles.length > 0">
      <!-- 展示容器 -->
      <view class="photo-box">
        <l-drag
          v-if="!readonly"
          :list="localFiles"
          @change="handleDragChange"
          ref="dragRef"
          :column="column"
          :gridHeight="'190rpx'"
          :longpress="!readonly"
          style="width: 100%"
        >
          <template #grid="{ active, content, index, oindex }">
            <view class="photo-item">
              <MyImageItem
                :image="content"
                :index="oindex"
                :warning="content.error"
                :loading="content.isOcrLoading"
                :active="active"
                :showDelete="!readonly"
                width="100%"
                height="100%"
                @click="handlePreviewImage"
                @delete="handleDeleteImage"
              />
            </view>
          </template>
        </l-drag>
        <!-- 只读模式：使用弹性布局 -->
        <view v-else class="readonly-flex-container">
          <view class="readonly-photo-item" v-for="(img, idx) in localFiles" :key="img.id || idx">
            <MyImageItem
              :image="img.fileUrl!"
              :index="idx"
              :showDelete="false"
              width="100%"
              height="100%"
              @click="handlePreviewImage"
            />
          </view>
        </view>
      </view>
    </view>
    <MyBasicUpload
      ref="myBasicUploadRef"
      @upload-success="handleUploadSuccess"
      @upload-fail="handleUploadFail"
      @capture-success="handleCaptureSuccess"
      @capture-fail="handleCaptureFail"
      @before-upload="handleBeforeUpload"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import MyImageItem from '../MyImageItem/index.vue';
import MyBasicUpload, {
  UploadType,
  type UploadFileData,
  type UploadFiles,
} from '@/components/MyBasicUpload';
import { getImageOcr } from '@/api/students/homework';
import { LkButton } from '@/components';
import type { HomeworkFileInfo } from '@/types/students/homework';

const props = defineProps<{
  files?: HomeworkFileInfo[];
  readonly?: boolean;
  ocr?: boolean;
  draggable?: boolean;
  max?: number;
  /** 是否删除上传返回的 id 字段，默认不删除 */
  deleteAddId?: boolean;
}>();
const emit = defineEmits(['update:files', 'change', 'before-upload']);

const maxImageCount = props.max ?? 2;
const enableOcr = props.ocr !== false;
const enableDrag = props.draggable !== false;

const myBasicUploadRef = ref<InstanceType<typeof MyBasicUpload> | null>(null);
const dragRef = ref(null);

// 屏幕宽度和动态列数计算
const screenWidth = ref(375); // 默认宽度
const ITEM_WIDTH = 180; // 每个item的宽度 (rpx)
const CONTAINER_PADDING = 40; // 容器左右内边距 (rpx)

// 计算每行能放多少张图片
const column = computed(() => {
  const availableWidth = screenWidth.value - CONTAINER_PADDING;
  const itemWidth = uni.upx2px(180); // 转换 rpx 到 px
  const gap = uni.upx2px(10); // 间距转换为 px，与样式保持一致

  // 计算能放下的图片数量：(可用宽度 + 间距) / (图片宽度 + 间距)
  const maxColumns = Math.floor((availableWidth + gap) / (itemWidth + gap));
  return Math.max(2, maxColumns); // 至少2列
});

// 获取屏幕宽度
const getScreenWidth = () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    screenWidth.value = systemInfo.windowWidth || 375;
  } catch (error) {
    console.warn('获取屏幕宽度失败，使用默认值', error);
    screenWidth.value = 375;
  }
};

onMounted(() => {
  getScreenWidth();
});

export interface ImageItem extends HomeworkFileInfo {
  error?: boolean;
  isOcrLoading?: boolean;
}

const getInitialFiles = () => {
  if (props.files) return props.files.map(f => ({ ...f }));
  return [];
};
const localFiles = ref<ImageItem[]>(getInitialFiles());

// 监听外部 props.files 变化
watch(
  () => props.files,
  val => {
    localFiles.value = getInitialFiles();
  },
  { deep: true, immediate: true }
);

const handleSelectPhoto = () => {
  if (localFiles.value.length >= maxImageCount) {
    uni.showToast({ title: `最多只能上传${maxImageCount}张图片`, icon: 'none' });
    return;
  }
  myBasicUploadRef.value?.openAlbum({
    count: maxImageCount - localFiles.value.length,
  });
};

const handleCameraUpload = () => {
  if (localFiles.value.length >= maxImageCount) {
    uni.showToast({ title: `最多只能上传${maxImageCount}张图片`, icon: 'none' });
    return;
  }
  myBasicUploadRef.value?.openCamera();
};

const handleUploadSuccess = async (fileData: UploadFileData) => {
  if (fileData && fileData.fileUrl) {
    const newImage: ImageItem = { ...fileData, isOcrLoading: false, error: false };

    // 后端不需要默认上传的 id 字段，统一处理了。
    if (props.deleteAddId) {
      delete newImage.id;
    }

    if (enableOcr) {
      newImage.isOcrLoading = true;
      localFiles.value.push(newImage);
      const currentIndex = localFiles.value.length - 1;
      try {
        const ocrResult = await getImageOcr({ url: fileData.fileUrl });
        console.log('ocrResult', ocrResult);
        if (localFiles.value[currentIndex]) {
          localFiles.value[currentIndex].isOcrLoading = false;
          const isSuccessful =
            Array.isArray(ocrResult?.textBlocks) && ocrResult?.textBlocks?.length > 0;
          localFiles.value[currentIndex].error = !isSuccessful;
        }
      } catch (error) {
        console.log('ocr error', error);
        if (localFiles.value[currentIndex]) {
          localFiles.value[currentIndex].isOcrLoading = false;
          localFiles.value[currentIndex].error = true;
        }
      }
      // 强制触发更新
      const arr = JSON.parse(JSON.stringify(localFiles.value));
      localFiles.value = arr;
    } else {
      localFiles.value.push(newImage);
    }
  }
  emit('update:files', localFiles.value);
  emit('change', localFiles.value);
};

const handleUploadFail = (error: any) => {
  uni.showToast({ title: '上传失败，请重试', icon: 'none' });
};
const handleCaptureSuccess = (result: any) => {};
const handleCaptureFail = (error: any) => {};

const handleDeleteImage = (index: number) => {
  localFiles.value.splice(index, 1);
  emit('update:files', localFiles.value);
  emit('change', localFiles.value);
};

const handleDragChange = (newList: any[]) => {
  const updatedImages = newList.map(item => {
    const imageData = item.content || item;
    return { ...imageData };
  });
  localFiles.value = updatedImages;

  emit('update:files', localFiles.value);
  emit('change', localFiles.value);
};

const handlePreviewImage = (index: number) => {
  if (localFiles.value[index] && localFiles.value[index].error) return;
  const currentImage = localFiles.value[index];
  const imageUrl = currentImage.fileUrl;
  if (!imageUrl) return;
  const urls: string[] = localFiles.value
    .filter(img => !img.error)
    .map(img => img.fileUrl)
    .filter((url): url is string => typeof url === 'string');
  if (urls.length === 0) return;
  uni.previewImage({ current: imageUrl, urls, indicator: 'number', loop: true });
};

const handleBeforeUpload = (files: UploadFiles, type: UploadType) => {
  console.log('handleBeforeUpload', files, type);
  emit('before-upload', files, type);
};
</script>

<style lang="scss" scoped>
.my-photograph-uploader {
  // padding: 20rpx;
  display: flex;
  flex-direction: column;
  // min-height: 400rpx;
}

.photo-box {
  display: flex;
  gap: 10rpx; // 减小间距
  flex-wrap: wrap;

  .photo-item {
    width: 180rpx;
    height: 180rpx;
  }
}

// 只读模式的弹性布局
.readonly-flex-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx; // 增加上下左右间距
  width: 100%;

  .readonly-photo-item {
    width: 180rpx;
    height: 180rpx;
    flex: 0 0 auto; // 不伸缩，保持固定尺寸
  }
}

.photo-upload {
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 24rpx;
  padding: 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  height: 292rpx;

  .upload-camera {
    display: flex;
    flex-direction: column;
    align-items: center;
    // margin-bottom: 20rpx;
  }

  .upload-icon {
    width: 72rpx;
    height: 72rpx;
    // background-color: #ffffff;
    // border-radius: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;
    // box-shadow: 0px 4rpx 12rpx rgba(0, 0, 0, 0.06);

    image {
      width: 100%;
      height: 100%;
    }
  }

  .upload-text {
    font-size: 28rpx;
    color: #86909c;
    margin-bottom: 20rpx;
    // font-weight: 500;
  }

  .upload-button {
    border-radius: 40rpx;
    width: 160rpx;
    height: 56rpx;
    padding: 6rpx 16rpx;
  }

  // &--small {
  //   width: 218rpx;
  //   height: 204rpx;
  //   padding: 10rpx;
  //   box-sizing: border-box;

  //   .upload-icon {
  //     width: 44rpx;
  //     height: 44rpx;
  //   }

  //   .upload-text {
  //     font-size: 24rpx;
  //     margin-bottom: 0;
  //   }

  //   .upload-button {
  //     width: 130rpx;
  //     height: 40rpx;
  //     padding: 6rpx 16rpx;
  //     font-size: 24rpx;
  //   }
  // }
}

.uploaded-images-section {
  width: 100%;
  margin-top: 20rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

// 拖拽模式的样式优化
:deep(.l-drag) {
  width: 100%;

  .l-drag__inner {
    padding: 0 !important;
    margin: 0 !important;
  }

  .l-drag__view {
    // 只移除间距，不改变布局方式，保持拖拽功能
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }
}
</style>
