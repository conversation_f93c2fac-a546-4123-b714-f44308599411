<template>
  <view class="homework-card" @click="onClick">
    <view class="homework-card-main">
      <!-- 右上角标签 -->
      <view class="homework-badge">
        <LkSvg class="badge-svg" src="/static/homework/card-badge.svg" />
        <text class="badge-text">{{ item.typeAlias }}</text>
      </view>

      <view class="card-content">
        <!-- 标题和学科在同一行 -->
        <view class="title-row">
          <!-- 学科标签 -->
          <view class="subject-tag">{{ item.subjectName || '未知' }}</view>

          <!-- 标题 -->
          <view class="homework-title text-ellipsis">{{ item.name }}</view>
        </view>
        <!-- 自主作业展示图片 -->
        <view
          v-if="item.selfHomeworkFiles && item.selfHomeworkFiles.length"
          class="homework-images"
          @click.stop
        >
          <view class="images-container">
            <view
              v-for="(imageItem, imageIndex) in item.selfHomeworkFiles.slice(0, 3)"
              :key="imageIndex"
              class="image-item"
              @click.stop="
                previewImage(
                  item.selfHomeworkFiles.map((item: any) => item.fileUrl),
                  imageIndex
                )
              "
            >
              <image :src="imageItem.fileUrl" mode="aspectFill" />
              <view
                v-if="imageIndex === 2 && item.selfHomeworkFiles.length > 3"
                class="image-count"
              >
                +{{ item.selfHomeworkFiles.length - 3 }}
              </view>
            </view>
          </view>
        </view>

        <!-- 其他类型作业展示描述 -->
        <view v-else class="homework-desc text-ellipsis-2">{{ item.description }}</view>
      </view>
    </view>
    <view class="homework-footer">
      <!-- 左侧：包含截止时间、倒计时、状态标签和问号图标 -->
      <view class="footer-left">
        <!-- 截止时间 -->
        <text class="deadline" v-if="item.deadlineTime">
          截止时间: {{ dayjs(item.deadlineTime).format('MM-DD HH:mm') }}</text
        >
        <!-- 提交时间 -->
        <text class="deadline" v-if="item.uploadTime">
          上传时间: {{ dayjs(item.uploadTime).format('MM-DD HH:mm') }}</text
        >

        <!-- 状态标签 -->
        <view class="status-tag-wrapper">
          <uni-tag
            :class="getStatusClass(item.status)"
            size="small"
            :inverted="false"
            :text="getStatusLabel(item.status)"
          />
        </view>

        <!-- 问号按钮，仅在特定状态下显示 -->
        <view v-if="isShowQuestionIcon()" @click.stop="handleQuestionClick" class="question-icon">
          <LkSvg width="40rpx" height="40rpx" color="none" src="/static/homework/question.svg" />
        </view>

        <!-- 预计时间，仅在"批改中"状态下显示 -->
        <!-- <view v-if="item.status === HomeworkStatus.CORRECTING" class="countdown">
          预计<text class="countdown-value">{{ 10 }}s...</text>
        </view> -->
      </view>

      <!-- 右侧：只有箭头 -->
      <view class="footer-right">
        <!-- 箭头 -->
        <image class="arrow-icon" src="/static/homework/arrow_right.svg" mode="aspectFit" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getHomeworkTypeLabel, getStatusLabel } from '@/pages-subpackages/students/homework/utils';
import { HomeworkStatus, HomeworkType } from '@/constants/students/homework';
import LkSvg from '@/components/svg/index.vue';
import { showDialog } from '@/components/MyDialog';
import type { HomeworkPageItem } from '@/types/students/homework';
import dayjs from 'dayjs';

// 组件属性
interface Props {
  item: HomeworkPageItem;
}

const emit = defineEmits<{
  click: [HomeworkPageItem];
}>();
const props = defineProps<Props>();

const isShowQuestionIcon = () => {
  const item = props.item;
  if (
    item.status === HomeworkStatus.TO_BE_REVISED ||
    item.status === HomeworkStatus.CORRECTION_FAILED
  ) {
    if (item.correctionReason || item.failureReason) {
      return true;
    }
  }
  return false;
};

// 获取标签类型
const getStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    [HomeworkStatus.TO_BE_SUBMITTED]: 'status-warning',
    [HomeworkStatus.TO_BE_CORRECTED]: 'status-warning',
    [HomeworkStatus.CORRECTING]: 'status-success',
    [HomeworkStatus.COMPLETED]: 'status-default',
    [HomeworkStatus.TO_BE_REVISED]: 'status-error',
    [HomeworkStatus.NOT_SUBMITTED]: 'status-default',
    [HomeworkStatus.CORRECTION_FAILED]: 'status-error',
    [HomeworkStatus.TO_BE_CONFIRMED]: 'status-primary',
  };
  return classMap[status] || 'status-default';
};
// 预览图片
const previewImage = (images: string[], current: number = 0) => {
  uni.previewImage({
    urls: images, // 需要预览的图片链接列表
    current: current, // 当前显示图片的索引
    indicator: 'number', // 显示页码指示器
    loop: true, // 是否开启循环预览
    longPressActions: {
      itemList: ['发送给朋友', '保存图片', '收藏'],
      success: function (data) {
        console.log('选中了第' + (data.tapIndex + 1) + '个按钮');
      },
      fail: function (err) {
        console.log(err.errMsg);
      },
    },
  });
};

// 处理问号点击事件
const handleQuestionClick = (e: any) => {
  console.log('handleQuestionClick', e);
  // uni.showToast({
  //   title: '111',
  //   icon: 'none',
  // });
  const title = props.item.correctionReason ? '订正说明' : '失败说明';
  const content = props.item.correctionReason || props.item.failureReason;
  showDialog({
    title: title,
    type: 'info',
    content,
    confirmText: '知道了',
    onConfirm() {
      console.log('onConfirm');
    },
  });
};

const onClick = () => {
  emit('click', props.item);
};
</script>

<style lang="scss" scoped>
.homework-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx 28rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: relative;
  margin-bottom: 28rpx;
  // gap: 20rpx;
  min-height: 300rpx;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
}

/* 作业类型标签 - 右上角彩色标签 */
.homework-badge {
  position: absolute;
  top: -10rpx;
  right: 0;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 160rpx;
  height: 74rpx;
  overflow: visible;
}

.badge-svg {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
}

.badge-text {
  position: relative;
  z-index: 2;
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
  margin-top: -15rpx;
  margin-right: 10rpx;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2;
  padding-left: 10rpx;
}

/* 卡片内容区 */
.card-content {
  display: flex;
  flex-direction: column;
}

/* 标题行 */
.title-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 14rpx;
}

/* 学科标签 */
.subject-tag {
  font-size: 28rpx;
  font-weight: 500;
  color: #84888f;
  background-color: #eff1f4;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

/* 作业标题 */
.homework-title {
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1.5;
  flex: 1;
  min-width: 0;
  color: #000000;
  max-width: calc(100% - 220rpx);
  padding-left: 10rpx;
}

/* 作业描述 */
.homework-desc {
  font-size: 28rpx;
  color: #4e5969;
  line-height: 1.4;
  margin-bottom: 10rpx;
}

/* 自主作业图片样式 */
.homework-images {
  margin-bottom: 20rpx;
}

.images-container {
  display: flex;
  gap: 10rpx;
  overflow: hidden;
  border-radius: 8rpx;
}

.image-item {
  // flex: 1;
  width: 200rpx;
  height: 150rpx;
  position: relative;
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
  border-radius: 8rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-count {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
}

/* 文字省略 */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 卡片底部操作区 */
.homework-footer {
  border-top: 1px solid #e7e7e7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
}

/* 分割线 */
.divider {
  width: 100%;
  height: 1rpx;
  background-color: #e7e7e7;
  margin: 10rpx 0;
}

/* 左侧：包含截止时间、倒计时、状态标签和问号图标 */
.footer-left {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 20rpx;
}

/* 截止时间 */
.deadline {
  font-size: 24rpx;
  color: #4e5969;
  line-height: 1.67;
}

/* 倒计时 */
.countdown {
  font-size: 24rpx;
  color: #00b42a;
  line-height: 1.67;
  display: inline-flex;
  align-items: center;
}

.countdown-value {
  margin-left: 2rpx;
  font-weight: 400;
}

/* 右侧：只有箭头 */
.footer-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 20rpx;
}

/* 状态文字 */
.status-text {
  font-size: 24rpx;
  font-weight: 500;
}

:deep(.uni-tag) {
  border: none;
}

.status-success {
  color: #00b42a;
  background-color: #e8ffea;
}

.status-primary {
  color: #2196f3;
  background-color: #e3f2fd;
}

.status-warning {
  color: #ff9800;
  background-color: #fff3e0;
}

.status-error {
  color: #f53f3f;
  background-color: #ffece8;
}

.status-default {
  color: #4e5969;
  background-color: #f2f3f5;
}

/* 问号图标 */
.question-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 箭头图标 */
.arrow-icon {
  font-size: 48rpx;
  color: #09244b;
  font-weight: blod;
  line-height: 1;
  width: 48rpx;
  height: 48rpx;
}

/* 状态标签包装器 */
.status-tag-wrapper {
  display: flex;
  align-items: center;
  margin-left: 5rpx;
}
</style>
