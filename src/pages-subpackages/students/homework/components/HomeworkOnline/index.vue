<template>
  <!-- 在线作业 -->
  <view class="online-container">
    <swiper
      class="questions-swiper"
      :current="swiperCurrent"
      :duration="finalyDuration"
      :circular="finalyCircular"
      :autoplay="false"
      :indicator-dots="false"
      :acceleration="false"
      easing-function="easeInOutCubic"
      @change="onQuestionSwiperChange"
      style="flex: 1; height: 100%; min-height: 0"
      v-if="questions.length > 0"
    >
      <swiper-item
        v-for="(item, index) in currentSwipers"
        :key="item?._id ?? index"
        class="swiper-item"
      >
        <scroll-view v-if="item" scroll-y class="question-scroll-view" style="height: 100%">
          <view class="question-container">
            <QuestionRenderer :questionId="item.id" />
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
    <!-- 如果题目为空，显示空状态 -->
    <MyEmpty v-if="questions.length === 0" text="" />
  </view>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import { QuestionRenderer } from '../Question';
import { useHomeworkStore } from '@/store/homeworkStore';
import useVirtualSwiper from '@/hooks/useVirtualSwiper';
import type { FrontQuestion as HomeworkQuestion } from '@/types/students/homework';
import type { SwiperItem } from '@/hooks/useVirtualSwiper';
import MyEmpty from '@/components/MyEmpty/index.vue';

const homeworkStore = useHomeworkStore();
const questions = computed(() => homeworkStore.currentBatchQuestions);

// 初始化虚拟滑动器
const swiperProps = reactive({
  defaultCurrent: homeworkStore.currentQuestionIndex,
  data: questions.value,
  circular: false, // 是否循环
  keyField: 'id',
  duration: 200, // 缩短动画时长，避免频繁切换时的动画堆积
  ignoreChangeByManual: false, // 改为false，避免状态不一致
  triggerWhenMounted: true, // 是否在组件挂载时触发
  virtualCount: 10, // 减少虚拟DOM数量，提高性能
});

const {
  swiperCurrent,
  dataCurrent,
  finalyDuration,
  finalyCircular,
  currentSwipers,
  onSwiperChange,
  scrollIntoSwiper,
} = useVirtualSwiper(ref(swiperProps));

// 只通过 scrollIntoSwiper 跳题，保证同步
const prevQuestion = () => {
  if (dataCurrent.value > 0) {
    scrollIntoSwiper(dataCurrent.value - 1);
  }
};

const nextQuestion = () => {
  if (dataCurrent.value < questions.value.length - 1) {
    scrollIntoSwiper(dataCurrent.value + 1);
  }
};

const jumpToQuestion = (index: number) => {
  if (index >= 0 && index < questions.value.length) {
    scrollIntoSwiper(index);
  }
};

const onQuestionSwiperChange = (e: any) => {
  onSwiperChange(e);
};

defineExpose({ jumpToQuestion });

// 保证 store.currentIndex 和 dataCurrent 完全同步
watch(dataCurrent, newValue => {
  if (homeworkStore.currentQuestionIndex !== newValue) {
    homeworkStore.setCurrentQuestionIndex(newValue);
  }
});

watch(
  () => homeworkStore.currentQuestionIndex,
  newValue => {
    if (newValue !== dataCurrent.value) {
      scrollIntoSwiper(newValue);
    }
  }
);

// 监听 questions 变化，更新 swiperProps.data
watch(
  questions,
  newQuestions => {
    swiperProps.data = newQuestions;
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.online-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
}

.header {
  margin-bottom: 20rpx;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 10rpx;
  }

  .desc {
    font-size: 28rpx;
    color: #666;
  }
}

.questions-swiper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 0;
}

.swiper-item {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.question-scroll-view {
  height: 100%;
  width: 100%;
}

.question-container {
  padding: 20rpx;
  box-sizing: border-box;
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 20rpx;

  .nav-button {
    width: 45%;
    height: 80rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
  }

  .prev-button {
    background-color: #f5f5f5;
    color: #333;
  }

  .next-button {
    background-color: #2979ff;
    color: #fff;
  }
}
</style>
