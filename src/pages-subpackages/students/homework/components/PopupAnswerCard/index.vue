<template>
  <my-popup
    ref="popup"
    type="bottom"
    title="答题卡"
    :show="props.open"
    :mask-closable="false"
    @close="handleClose"
  >
    <view class="answer-card">
      <!-- 状态图例 -->
      <view class="legend-row">
        <!-- 已完成状态：显示未作答、答对、答错 -->
        <template v-if="isCompletedStatus">
          <view class="legend-item">
            <view class="legend-icon unanswered"></view>
            <text class="legend-text">未作答</text>
          </view>
          <view class="legend-item">
            <view class="legend-icon correct"></view>
            <text class="legend-text">答对</text>
          </view>
          <view class="legend-item">
            <view class="legend-icon wrong"></view>
            <text class="legend-text">答错</text>
          </view>
        </template>
        <!-- 其他状态：显示未作答、已作答 -->
        <template v-else>
          <view class="legend-item">
            <view class="legend-icon unanswered"></view>
            <text class="legend-text">未作答</text>
          </view>
          <view class="legend-item">
            <view class="legend-icon answered"></view>
            <text class="legend-text">已作答</text>
          </view>
        </template>
      </view>

      <!-- 可滚动的内容区域 -->
      <scroll-view class="scroll-area" scroll-y>
        <!-- 无题目数据提示 -->
        <view class="empty-tip" v-if="!questions.length">
          <text>暂无题目数据</text>
        </view>

        <!-- 动态题型区域 -->
        <template v-for="(group, gIndex) in questionGroups" :key="'group-' + gIndex">
          <view
            class="question-section"
            v-if="group && group.questions && group.questions.length > 0"
          >
            <text class="section-title">{{ group.title || '未知题型' }}</text>
            <view class="question-grid">
              <template v-for="(question, qIndex) in group.questions" :key="'question-' + qIndex">
                <view
                  class="question-item"
                  :class="[
                    getQuestionStatusClass(question.id),
                    getCurrentQuestion(question) ? 'current' : '',
                  ]"
                  @click="jumpToQuestion(getQuestionIndex(question.id))"
                >
                  <!-- 暂时注释，先不要删除 -->
                  <!-- <text>{{ getQuestionIndex(question.id) + 1 }}</text> -->
                  <text>{{ question.index }}</text>
                </view>
              </template>
            </view>
          </view>
        </template>
      </scroll-view>

      <!-- 底部按钮区域 -->
      <view class="footer-buttons" v-if="!homeworkStore.readonly">
        <LkButton
          class="homework-btn default-btn"
          :disabled="homeworkStore.isSubmitDisabled"
          block
          @click="clearAnswers"
        >
          清空答题记录</LkButton
        >
        <LkButton
          class="homework-btn submit-btn"
          :disabled="homeworkStore.isSubmitDisabled"
          block
          @click="submitAnswers"
          >提交作业
        </LkButton>
      </view>
    </view>
  </my-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { HomeworkStatus, QuestionType } from '@/constants/students/homework';
import { getQuestionTypeLabel, hasPartsProperty, isQuestionAnswered } from '../../utils';
import MyPopup from '@/components/MyPopup';
import { LkButton } from '@/components';
import { showDialogDanger } from '@/components/MyDialog';

const props = defineProps<{ open: boolean }>();
const emit = defineEmits(['update:open', 'clearAnswers', 'submit']);

const homeworkStore = useHomeworkStore();
const popup = ref<any>(null);

// 监听 props.open 变化，控制弹窗显示隐藏
watch(
  () => props.open,
  val => {
    if (val && popup.value) {
      popup.value.open();
    } else if (!val && popup.value) {
      popup.value.close();
    }
  },
  { immediate: true }
);

// 修改之前的 questions 定义
const questions = computed(() => homeworkStore.currentBatchQuestions || []);
const userAnswers = computed(() => homeworkStore.userAnswers || {});
const currentQuestionId = computed(() => {
  const idx = homeworkStore.currentQuestionIndex;
  return homeworkStore.questions[idx]?.id || '';
});

// 判断是否为已完成状态
const isCompletedStatus = computed(() => {
  return homeworkStore.backendData?.status === HomeworkStatus.COMPLETED;
});

// 动态分组题目
const questionGroups = computed(() => {
  if (!questions.value || questions.value.length === 0) {
    return [];
  }

  const groups: Array<{
    type: string;
    title: string;
    questions: any[];
    needsProgress: boolean;
  }> = [];

  let currentType = '';
  let currentQuestions: any[] = [];

  try {
    // 遍历所有题目，按类型分组
    questions.value.forEach((question: any) => {
      if (!question) return;

      const questionType = question.type || '';
      let groupType = questionType;
      let groupTitle = '';

      // 如果是万能题，根据 backendData?.question?.questionTypeCode 来判断具体题型
      if (questionType === QuestionType.ANY_TYPE) {
        const backendTypeCode = question.backendData?.question?.questionTypeCode;
        if (backendTypeCode) {
          groupType = backendTypeCode;
          groupTitle = getQuestionTypeLabel(backendTypeCode);
        } else {
          groupTitle = '组合题';
        }
      } else {
        groupTitle = getQuestionTypeLabel(questionType);
      }

      // 如果题型变了，创建新分组
      if (groupType !== currentType) {
        // 保存上一个分组（如果有的话）
        if (currentQuestions.length > 0) {
          groups.push({
            type: currentType,
            title:
              currentType === QuestionType.ANY_TYPE
                ? groupTitle
                : getQuestionTypeLabel(currentType),
            questions: currentQuestions,
            needsProgress: false,
          });
        }

        // 开始新分组
        currentType = groupType;
        currentQuestions = [question];
      } else {
        // 同类型题目，添加到当前分组
        currentQuestions.push(question);
      }
    });

    // 添加最后一个分组
    if (currentQuestions.length > 0) {
      groups.push({
        type: currentType,
        title: currentType === QuestionType.ANY_TYPE ? '组合题' : getQuestionTypeLabel(currentType),
        questions: currentQuestions,
        needsProgress: false,
      });
    }

    return groups;
  } catch (error) {
    return [];
  }
});

function getQuestionIndex(questionId: string) {
  if (!questionId) return 0;
  return homeworkStore.questions.findIndex((q: any) => q && q.id === questionId);
}

function getCurrentQuestion(question: any) {
  if (!question || !question.id) return false;
  return question.id === currentQuestionId.value;
}

function getQuestionStatusClass(questionId: string) {
  if (!questionId) return 'unanswered';

  const answer = userAnswers.value[questionId];
  if (!answer) return 'unanswered';

  const question = questions.value.find((q: any) => q && q.id === questionId);
  if (!question) return 'unanswered';

  // 已完成状态：根据答题结果显示答对或答错
  if (isCompletedStatus.value) {
    // 从后端数据中获取答题结果
    // correctResult 批改结果 0-错误；1-正确；2-部分正确
    const backendQuestion = question.backendData;
    const submitQuestion = backendQuestion?.question?.submitQuestion;
    if (submitQuestion) {
      const correctResult = submitQuestion.correctResult;
      if (correctResult == 1) {
        return 'correct';
      } else {
        return 'wrong';
      }
    }
    // 如果没有答题记录，显示未作答
    return 'unanswered';
  }

  // 其他状态：只显示是否已作答
  const allAnswers = Object.values(userAnswers.value);
  const result = isQuestionAnswered(question, answer, allAnswers) ? 'answered' : 'unanswered';

  return result;
}

const close = () => {
  popup.value.close();
  emit('update:open', false);
};

const show = () => {
  popup.value.open();
  emit('update:open', true);
};

const handleClose = () => {
  emit('update:open', false);
};

const jumpToQuestion = (index: number) => {
  homeworkStore.setCurrentQuestionIndex(index);
  close();
};

const clearAnswers = () => {
  // showDialogDanger({
  //   title: '提示',
  //   content: '清空答题记录将不可恢复,确定清空全部答题记录吗?',
  //   onConfirm: () => {
  //     homeworkStore.clearUserAnswers();
  //     close();
  //   },
  // });
  emit('clearAnswers');
};

const submitAnswers = () => {
  // 实际项目应提交到后端
  // uni.showToast({ title: '提交成功', icon: 'success' });
  emit('submit');
  close();
};

defineExpose({
  open: show,
  close,
});
</script>

<style lang="scss" scoped>
@import '../../styles/index.scss';

.answer-card {
  position: relative;
  height: 65vh;
  display: flex;
  flex-direction: column;

  // 图例行
  .legend-row {
    padding: 15rpx 0rpx;
    display: flex;
    // justify-content: center;
    flex-shrink: 0;

    .legend-item {
      display: flex;
      align-items: center;
      // margin: 0 15rpx;
      margin-right: 15rpx;

      .legend-icon {
        width: 30rpx;
        height: 30rpx;
        border-radius: 3px;
        margin-right: 10rpx;
        background: #eee;

        &.unanswered {
          background: #eee;
        }

        &.answered {
          border-color: #7d4dff;
          background: #7d4dff;
        }

        &.correct {
          background-color: #4caf50;
        }

        &.wrong {
          background: #ff463c;
        }
      }

      .legend-text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  // 可滚动区域
  .scroll-area {
    margin-top: 15rpx;
    margin-bottom: 15rpx;
    flex: 1;
    padding: 0 0rpx;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6rpx;
    }

    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      border-radius: 3rpx;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #c1c1c1;
      border-radius: 3rpx;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #a8a8a8;
    }

    // 空数据提示
    .empty-tip {
      text-align: center;
      padding: 40rpx 0;
      color: #999;
      font-size: 28rpx;
    }

    // 题目分类区域
    .question-section {
      margin-bottom: 30rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
      }

      .question-grid {
        display: flex;
        flex-wrap: wrap;

        .question-item {
          width: 80rpx;
          height: 80rpx;
          margin: 10rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 40rpx;
          border: 2rpx solid #ddd;
          font-size: 28rpx;
          color: #333;
          background-color: #fff;

          &.unanswered {
            background-color: #fff;
            color: #333;
            border-color: #ddd;
          }

          &.answered {
            background-color: #7d4dff;
            color: #fff;
            border-color: #7d4dff;
          }

          &.correct {
            border-color: #2cd990;
            background: #e3f9e9;
            color: #2cd990;
          }

          &.wrong {
            border-color: #ff463c;
            background: #fff0ed;
            color: #ff463c;
          }

          &.current {
            // border: 2rpx solid #7D4DFF;
            // background: #F3ECFF;
            position: relative;
            z-index: 1;

            &::after {
              content: '当前';
              position: absolute;
              bottom: -15rpx;
              left: 50%;
              transform: translateX(-50%);
              font-size: 22rpx;
              color: #7d4dff;
              background-color: #f3ecff;
              border-radius: 6rpx;
              padding: 2rpx 15rpx;
              white-space: nowrap;
              line-height: 1.2;
            }
          }
        }
      }
    }
  }

  // 底部按钮区域
  .footer-buttons {
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    flex-shrink: 0;
    border-top: 1rpx solid #eee;
    background-color: #fff;
    padding-bottom: env(safe-area-inset-bottom);
    gap: 10rpx;
    margin-bottom: 20rpx;

    .homework-btn {
      width: 45%;
      border-radius: 100rpx;

      &.default-btn {
        border: 1px solid #dcdcdc;
        background-color: #ffffff;
        color: #4e5969;
      }

      &.submit-btn {
        background-color: #7d4dff;
        color: #ffffff;
      }
    }
  }
}
</style>
