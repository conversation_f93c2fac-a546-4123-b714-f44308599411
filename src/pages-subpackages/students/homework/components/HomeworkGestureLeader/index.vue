<template>
  <view v-if="visible" class="homework-gesture-leader" @click="handleMaskClick">
    <!-- 半透明蒙版背景 -->
    <view class="mask"></view>

    <!-- 手势提示内容 -->
    <view class="gesture-content">
      <!-- 手势图标 -->
      <view class="gesture-icon">
        <image src="/static/homework/gesture_leader.svg" mode="aspectFit" class="gesture-svg" />
      </view>

      <!-- 提示文字 -->
      <text class="gesture-text">{{ text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  /** 提示文字 */
  text?: string;
  /** 是否允许点击蒙版关闭 */
  maskClosable?: boolean;
}

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  text: '左右滑动切换上下题',
  maskClosable: true,
});

const emit = defineEmits<Emits>();

// 控制组件显示状态
const visible = ref(true);

// 处理蒙版点击
const handleMaskClick = () => {
  if (props.maskClosable) {
    visible.value = false;
    emit('close');
  }
};
</script>

<style lang="scss" scoped>
.homework-gesture-leader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.gesture-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  padding: 40rpx;
  max-width: 526rpx;
}

.gesture-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 308rpx;
  height: 183.62rpx;
}

.gesture-svg {
  width: 100%;
  height: 100%;
}

.gesture-text {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 32rpx;
  line-height: 1.5;
  color: #ffffff;
  text-align: center;
}
</style>
