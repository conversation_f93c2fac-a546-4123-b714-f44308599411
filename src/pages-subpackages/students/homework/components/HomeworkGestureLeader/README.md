# HomeworkGestureLeader 手势提示组件

一个用于作业模块的手势提示组件，在特定场景下覆盖一层蒙版信息给用户提示操作。

## 功能特性

- 半透明蒙版背景
- 手势图标展示
- 自定义提示文字
- 支持点击任意区域关闭（组件会自动隐藏）

## 使用方法

### 基础用法

```vue
<template>
  <view>
    <button @click="showGesture = true">显示手势提示</button>

    <HomeworkGestureLeader v-if="showGesture" text="左右滑动切换上下题" @close="handleClose" />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { HomeworkGestureLeader } from '@/pages-subpackages/students/homework/components';

const showGesture = ref(false);

const handleClose = () => {
  showGesture.value = false;
  console.log('手势提示已关闭');
};
</script>
```

### 禁用点击关闭

```vue
<HomeworkGestureLeader v-if="showGesture" :mask-closable="false" text="请按照提示操作" />
```

## Props

| 参数         | 类型    | 默认值               | 说明                     |
| ------------ | ------- | -------------------- | ------------------------ |
| text         | string  | '左右滑动切换上下题' | 提示文字                 |
| maskClosable | boolean | true                 | 是否允许点击任意区域关闭 |

## Events

| 事件名 | 说明                                 | 回调参数 |
| ------ | ------------------------------------ | -------- |
| close  | 关闭手势提示时触发（组件会自动隐藏） | -        |

## 样式定制

组件使用了以下CSS变量，可以通过覆盖这些变量来自定义样式：

- 蒙版背景色：`rgba(0, 0, 0, 0.6)`
- 文字颜色：`#ffffff`
- 文字字体：`PingFang SC`
- 文字大小：`32rpx`

## 注意事项

1. 组件使用了固定定位，会覆盖整个屏幕
2. 手势图标路径为 `/static/homework/gesture_leader.svg`，请确保该文件存在
3. 组件层级为 `z-index: 9999`，确保在其他元素之上显示
4. 点击任意区域时组件会自动隐藏，无需外部控制
