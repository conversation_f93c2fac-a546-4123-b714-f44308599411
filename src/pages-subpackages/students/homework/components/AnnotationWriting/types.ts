// 批注相关类型定义

// 添加批注类型枚举
export enum AnnotationType {
  GREAT = 'great', // 佳句赏析(波浪线) - 使用great.svg
  INSERT = 'insert', // 插入内容(V形) - 使用insert.svg
  DELETE = 'delete', // 删除内容(特殊符号) - 使用delete.svg
  REPLACE = 'replace', // 替换内容(半圆形) - 使用replace.svg
  RECOMMEND = 'recommend', // 优化建议(直线) - 使用recommend.svg
}

export interface AnnotationLine {
  x: number;
  y: number;
  width?: number;
  height?: number;
}

// 图片信息类型
export interface ImageItem {
  id: string; // 图片唯一ID
  url: string; // 图片URL
  width?: number; // 图片宽度(可选)
  height?: number; // 图片高度(可选)
}

// 批注数据项，用于组件props
export interface AnnotationDataItem {
  imageUrl: string; // 图片URL（必需）
  annotations: Annotation[]; // 该图片的批注数组
  [key: string]: any; // 其他自定义属性
}

// 画布信息类型
export interface CanvasInfo {
  id: string; // 对应图片ID
  width: number; // 画布宽度
  height: number; // 画布高度
  originalWidth: number; // 原始图片宽度
  originalHeight: number; // 原始图片高度
  scaleX: number; // X轴缩放比例
  scaleY: number; // Y轴缩放比例
}

export interface Annotation {
  id: string;
  type: AnnotationType | string; // 使用枚举类型，但也保留string兼容性
  x: number;
  y: number;
  width?: number;
  height?: number;
  number?: number;
  content?: string;
  lines?: AnnotationLine[];
  imageId?: string; // 关联的图片ID（可选，保持兼容性）
}

export interface TransparentArea {
  x: number;
  y: number;
  width: number;
  height: number;
  annotationId: string;
  isActive: boolean;
}

export interface ScaledArea extends TransparentArea {
  right: number;
  bottom: number;
}

export interface CircleArea {
  x: number;
  y: number;
  radius: number;
  annotation: Annotation;
  imageId?: string; // 关联的图片ID（可选，保持兼容性）
}

export interface SvgOriginalSize {
  originalWidth: number;
  originalHeight: number;
}
