<template>
  <!-- 添加scroll-view包裹整个内容，设置scroll-y属性使其可以垂直滚动 -->
  <scroll-view
    scroll-y
    class="annotation-writing-scroll"
    :scroll-with-animation="true"
    :scroll-top="scrollTop"
  >
    <view class="annotation-writing-container">
      <!-- 图片画布容器，使用v-for循环创建多个画布容器 -->
      <view
        v-for="(img, imgIndex) in data"
        :key="imgIndex"
        :id="`canvas-container-${imgIndex}`"
        class="canvas-container"
        :style="{
          marginBottom: imgIndex < data.length - 1 ? '20px' : '0',
          height: canvasHeights[imgIndex] + 'px',
          width: canvasWidths[imgIndex] + 'px',
        }"
        @tap="($event: any) => handleContainerTap($event, imgIndex)"
      >
        <!-- 底层画布 - 显示图片和批注 -->
        <canvas
          :canvas-id="`annotateCanvas_${imgIndex}`"
          class="base-canvas"
          :style="{ width: canvasWidths[imgIndex] + 'px', height: canvasHeights[imgIndex] + 'px' }"
          :width="canvasWidths[imgIndex]"
          :height="canvasHeights[imgIndex]"
          data-will-read-frequently="true"
        ></canvas>

        <!-- 顶层蒙版画布 - 控制区域透明度 -->
        <canvas
          :canvas-id="`maskCanvas_${imgIndex}`"
          class="mask-canvas"
          :style="{
            width: canvasWidths[imgIndex] + 'px',
            height: canvasHeights[imgIndex] + 'px',
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 10,
          }"
          :width="canvasWidths[imgIndex]"
          :height="canvasHeights[imgIndex]"
          data-will-read-frequently="true"
        ></canvas>
      </view>
    </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import type {
  Annotation,
  AnnotationLine,
  TransparentArea,
  ScaledArea,
  CircleArea,
  SvgOriginalSize,
  AnnotationDataItem,
} from './types';
import { AnnotationType } from './types';

declare const uni: any;

// 修改props，只接受一个data参数
const props = defineProps<{
  data: Array<AnnotationDataItem>;
  debug?: boolean;
  containerWidth?: number;
  containerHeight?: number;
}>();

// 添加日志控制函数，只在调试模式下打印日志
const log = (message: string, ...args: any[]) => {
  if (props.debug) {
    console.log(message, ...args);
  }
};

// 添加错误日志函数，错误日志始终打印
const logError = (message: string, ...args: any[]) => {
  console.error(message, ...args);
};

// 添加警告日志函数，警告日志始终打印
const logWarn = (message: string, ...args: any[]) => {
  console.warn(message, ...args);
};

// 简化后的事件，只保留必要的
const emit = defineEmits<{
  // 激活批注区域时触发，增加imageIndex参数
  (e: 'annotationActivate', annotation: Annotation, imageIndex: number): void;
}>();

// 响应式状态，改为数组以支持多个画布
const canvasWidths = ref<number[]>([]);
const canvasHeights = ref<number[]>([]);
const imgOriginWidths = ref<number[]>([]);
const imgOriginHeights = ref<number[]>([]);
const scaleXs = ref<number[]>([]);
const scaleYs = ref<number[]>([]);
const deviceScale = ref(1);
// 全局唯一的激活区域ID和所属图片索引
const activeAreaId = ref<string | null>(null);
const activeImageIndex = ref<number | null>(null);
// 滚动位置
const scrollTop = ref(0);
// scroll-view的高度
const scrollViewHeight = ref(500); // 默认值500px

// 内部配置，不再通过props传入
const yOffset = 90; // 批注区域Y轴偏移量，从70增加到90，增加单行批注的透明区域高度
const maskOpacity = 0.1; // 蒙版透明度
// 支持透明区域的批注类型（使用types.ts中定义的枚举）
const supportedTypes = [AnnotationType.GREAT, AnnotationType.RECOMMEND];
const baseWidth = 375; // 基准设计尺寸（以iPhone 6/7/8为基准）

// 内部管理SVG资源路径，根据新的文件名更新
const svgPaths = {
  [AnnotationType.GREAT]: '/static/homework/annotation_writing/great.svg', // 佳句赏析 - 波浪线
  [AnnotationType.INSERT]: '/static/homework/annotation_writing/insert.svg', // 插入内容 - V形
  [AnnotationType.DELETE]: '/static/homework/annotation_writing/delete.svg', // 删除内容 - 特殊符号
  [AnnotationType.REPLACE]: '/static/homework/annotation_writing/replace.svg', // 替换内容 - 半圆形
  [AnnotationType.RECOMMEND]: '/static/homework/annotation_writing/recommend.svg', // 优化建议 - 直线
};

// 缓存数据，改为二维数组，每个图片一组
const circleAreas = reactive<CircleArea[][]>([]);
const svgOriginalSizes = reactive<Record<string, SvgOriginalSize>>({});
const svgImages = reactive<Record<string, string>>({});
const transparentAreas = reactive<TransparentArea[][]>([]);

// 初始化
const init = async () => {
  // 获取设备宽高
  const res = uni.getSystemInfoSync();
  deviceScale.value = res.windowWidth / baseWidth;

  // 获取scroll-view的高度
  updateScrollViewHeight();

  // 初始化数组
  // 使用自定义宽度或设备宽度
  const defaultWidth = props.containerWidth || res.windowWidth;
  canvasWidths.value = new Array(props.data.length).fill(defaultWidth);
  canvasHeights.value = new Array(props.data.length).fill(0); // 高度将在加载图片后计算
  imgOriginWidths.value = new Array(props.data.length).fill(0);
  imgOriginHeights.value = new Array(props.data.length).fill(0);
  scaleXs.value = new Array(props.data.length).fill(1);
  scaleYs.value = new Array(props.data.length).fill(1);

  // 初始化每个图片的圆形区域和透明区域数组
  for (let i = 0; i < props.data.length; i++) {
    circleAreas[i] = [];
    transparentAreas[i] = [];

    // 为所有批注设置默认高度和宽度值（如果未指定）
    if (props.data[i] && props.data[i].annotations) {
      props.data[i].annotations.forEach(annotation => {
        // 为主批注设置默认高度和宽度
        if (annotation.height === undefined) {
          annotation.height = 10; // 默认高度为10
        }
        if (annotation.width === undefined) {
          annotation.width = 100; // 默认宽度为100
        }

        // 为多行批注设置默认高度和宽度
        if (annotation.lines && annotation.lines.length > 0) {
          annotation.lines.forEach(line => {
            if (line.height === undefined) {
              line.height = 10; // 默认高度为10
            }
            if (line.width === undefined) {
              line.width = 100; // 默认宽度为100
            }
          });
        }
      });
    }
  }

  // 预加载所有SVG图像
  await preloadSvgImages();

  // 处理每张图片
  const imagePromises = props.data.map((img, index) => {
    return processImage(img.imageUrl, index);
  });

  await Promise.all(imagePromises);

  // 在所有图像处理完成后，设置一个短暂的延迟，确保UI已经更新
  setTimeout(() => {
    // 重新绘制所有画布
    for (let i = 0; i < props.data.length; i++) {
      drawAll(i);
      drawPermanentMask(i);
    }
  }, 50);
};

// 处理单张图片
const processImage = async (url: string, index: number) => {
  // 获取设备宽度或使用自定义宽度
  const deviceWidth = props.containerWidth || uni.getSystemInfoSync().windowWidth;
  canvasWidths.value[index] = deviceWidth;

  // 获取图片原始宽高
  await getImageInfo(url, index);

  // 根据图片宽高比和容器宽度计算适合的画布高度
  if (props.containerHeight) {
    // 如果提供了自定义高度，则使用自定义高度
    canvasHeights.value[index] = props.containerHeight;
  } else {
    // 否则根据图片宽高比计算高度
    const aspectRatio = imgOriginHeights.value[index] / imgOriginWidths.value[index];
    canvasHeights.value[index] = deviceWidth * aspectRatio;
  }

  // 计算缩放比例
  scaleXs.value[index] = deviceWidth / imgOriginWidths.value[index];
  scaleYs.value[index] = canvasHeights.value[index] / imgOriginHeights.value[index];

  // 绘制底层画布
  drawAll(index);

  // 绘制蒙版
  drawPermanentMask(index);
};

// 重写获取图片信息函数，确保图片尺寸计算正确
const getImageInfo = (url: string, index: number): Promise<void> => {
  return new Promise(resolve => {
    uni.getImageInfo({
      src: url,
      success: (info: any) => {
        log(`图片信息获取成功 - 索引:${index}, 宽度:${info.width}, 高度:${info.height}`);
        imgOriginWidths.value[index] = info.width;
        imgOriginHeights.value[index] = info.height;

        // 根据图片原始宽高比计算画布高度
        const aspectRatio = info.height / info.width;
        log(`图片宽高比 - ${aspectRatio}`);

        // 如果提供了自定义高度，则使用自定义高度
        if (props.containerHeight) {
          canvasHeights.value[index] = props.containerHeight;
          log(`使用自定义容器高度 - ${props.containerHeight}`);
        } else {
          // 否则使用设备宽度和原始宽高比计算适合的画布高度
          canvasHeights.value[index] = canvasWidths.value[index] * aspectRatio;
          log(`根据比例计算画布高度 - ${canvasHeights.value[index]}`);
        }

        log(`设置画布大小 - 宽:${canvasWidths.value[index]}, 高:${canvasHeights.value[index]}`);

        resolve();
      },
      fail: (err: any) => {
        logError(`获取图片信息失败 - 索引:${index}`, err);
        resolve();
      },
    });
  });
};

// 预加载SVG图像
const preloadSvgImages = async (): Promise<void> => {
  const svgTypes = Object.keys(svgPaths);
  const promises = svgTypes.map(type => {
    return new Promise<void>(resolve => {
      uni.getImageInfo({
        src: svgPaths[type as AnnotationType],
        success: (res: any) => {
          svgImages[type] = res.path;
          // 存储SVG原始尺寸信息
          svgOriginalSizes[type] = {
            originalWidth: res.width,
            originalHeight: res.height,
          };
          resolve();
        },
        fail: () => {
          logError(`加载SVG失败: ${type}`);
          resolve();
        },
      });
    });
  });

  return Promise.all(promises).then(() => {});
};

// 获取SVG原始尺寸
const getSvgOriginalSize = (type: string): SvgOriginalSize | undefined => {
  return svgOriginalSizes[type];
};

// 绘制所有内容
const drawAll = (imageIndex: number) => {
  // 创建Canvas上下文时添加willReadFrequently属性
  const ctx = uni.createCanvasContext(`annotateCanvas_${imageIndex}`, null, {
    willReadFrequently: true,
  });

  // 清空画布，确保每次重绘都是干净的
  ctx.clearRect(0, 0, canvasWidths.value[imageIndex], canvasHeights.value[imageIndex]);

  // 1. 绘制图片
  ctx.drawImage(
    props.data[imageIndex].imageUrl,
    0,
    0,
    imgOriginWidths.value[imageIndex],
    imgOriginHeights.value[imageIndex],
    0,
    0,
    canvasWidths.value[imageIndex],
    canvasHeights.value[imageIndex]
  );

  // 清空序号点击区域缓存
  circleAreas[imageIndex] = [];

  // 2. 绘制批注
  props.data[imageIndex].annotations.forEach(item => {
    drawAnnotation(ctx, item, imageIndex);
  });

  // 确保绘制操作被立即执行
  ctx.draw(false);
};

// 绘制永久性蒙版 - 确保正确计算和处理透明区域
const drawPermanentMask = (imageIndex: number) => {
  // 创建Canvas上下文时添加willReadFrequently属性
  const ctx = uni.createCanvasContext(`maskCanvas_${imageIndex}`, null, {
    willReadFrequently: true,
  });

  // 确保清空整个画布
  ctx.clearRect(0, 0, canvasWidths.value[imageIndex], canvasHeights.value[imageIndex]);

  // 2. 先绘制整个蒙版
  ctx.setFillStyle(`rgba(0, 0, 0, ${maskOpacity})`);
  ctx.fillRect(0, 0, canvasWidths.value[imageIndex], canvasHeights.value[imageIndex]);

  // 3. 动态生成透明区域数据（如果未初始化则跳过）
  if (!transparentAreas[imageIndex]) {
    transparentAreas[imageIndex] = [];
  } else {
    transparentAreas[imageIndex].length = 0;
  }

  // 检查批注数据是否存在
  if (!props.data[imageIndex] || props.data[imageIndex].annotations.length === 0) {
    log(`图片${imageIndex}没有批注数据`);
    ctx.draw(false);
    return;
  } else {
    log(
      `正在计算透明区域 - 图片:${imageIndex}, 批注数量:${props.data[imageIndex].annotations.length}`
    );
  }

  // 遍历所有批注
  props.data[imageIndex].annotations.forEach(annotation => {
    // 只处理支持的批注类型
    if (supportedTypes.includes(annotation.type as AnnotationType)) {
      // 判断当前批注是否应该处于激活状态
      const isActive =
        annotation.id === activeAreaId.value && imageIndex === activeImageIndex.value;

      // 如果有多行，为每行创建一个透明区域
      if (annotation.lines && annotation.lines.length > 0) {
        annotation.lines.forEach(line => {
          // 保持各行透明区域的独立性，确保多行之间有不透明的间隔
          // 增加高度以匹配图片中绿色区域的高度
          transparentAreas[imageIndex].push({
            x: line.x,
            y: line.y - 60, // 向上扩展40个像素
            width: line.width!,
            height: line.height! + 50, // 增加高度到原高度的3倍左右，匹配绿色区域
            annotationId: annotation.id,
            // 使用直接计算的激活状态
            isActive: isActive,
          });
        });
      } else {
        // 单行批注
        transparentAreas[imageIndex].push({
          x: annotation.x,
          y: annotation.y - yOffset, // 应用Y轴偏移
          width: annotation.width!,
          height: annotation.height! + yOffset, // 增加高度以覆盖批注
          annotationId: annotation.id,
          // 使用直接计算的激活状态
          isActive: isActive,
        });
      }
    }
  });

  // 调试输出透明区域信息
  log(`透明区域计算完成 - 图片:${imageIndex}, 区域数量:${transparentAreas[imageIndex].length}`);
  if (props.debug) {
    transparentAreas[imageIndex].forEach((area, i) => {
      log(
        `区域${i}: ID=${area.annotationId}, 原始位置=(${area.x},${area.y}), 大小=${area.width}x${area.height}`
      );
    });
  }

  // 4. 创建一个临时数组来存储所有透明区域的坐标信息（应用缩放比例）
  const scaledAreas: ScaledArea[] = transparentAreas[imageIndex].map(area => ({
    x: area.x * scaleXs.value[imageIndex],
    y: area.y * scaleYs.value[imageIndex],
    width: area.width * scaleXs.value[imageIndex],
    height: area.height * scaleYs.value[imageIndex],
    right: (area.x + area.width) * scaleXs.value[imageIndex],
    bottom: (area.y + area.height) * scaleYs.value[imageIndex],
    annotationId: area.annotationId,
    isActive: area.isActive,
  }));

  // 有激活区域时打印日志
  const activeAreas = scaledAreas.filter(area => area.isActive);
  if (activeAreas.length > 0) {
    log(`图片${imageIndex}有${activeAreas.length}个激活区域，ID: ${activeAreas[0].annotationId}`);
  }

  // 5. 确保清除蒙版上的透明区域
  ctx.clearRect(0, 0, canvasWidths.value[imageIndex], canvasHeights.value[imageIndex]);

  // 6. 逐行绘制蒙版，只跳过激活的透明区域
  for (let i = 0; i < canvasHeights.value[imageIndex]; i++) {
    // 检查当前行是否在任何激活的透明区域内
    const activeOverlappingAreas = scaledAreas.filter(
      area => area.isActive && i >= area.y && i < area.bottom
    );

    if (activeOverlappingAreas.length === 0) {
      // 如果不在任何激活的透明区域内，绘制整行
      ctx.setFillStyle(`rgba(0, 0, 0, ${maskOpacity})`);
      ctx.fillRect(0, i, canvasWidths.value[imageIndex], 1);
    } else {
      // 如果在一个或多个激活的透明区域内，需要绘制该行中不属于激活透明区域的部分
      // 首先按x坐标排序区域
      activeOverlappingAreas.sort((a, b) => a.x - b.x);

      let x = 0;
      for (const area of activeOverlappingAreas) {
        // 绘制当前透明区域左侧的蒙版
        if (x < area.x) {
          ctx.setFillStyle(`rgba(0, 0, 0, ${maskOpacity})`);
          ctx.fillRect(x, i, area.x - x, 1);
        }
        // 更新x为当前透明区域的右边界
        x = Math.max(x, area.right);
      }

      // 绘制最后一个透明区域右侧到画布右边缘的蒙版
      if (x < canvasWidths.value[imageIndex]) {
        ctx.setFillStyle(`rgba(0, 0, 0, ${maskOpacity})`);
        ctx.fillRect(x, i, canvasWidths.value[imageIndex] - x, 1);
      }
    }
  }

  // 7. 在调试模式下绘制区域边界
  if (props.debug) {
    // 仅在调试模式下绘制所有区域的边界，帮助开发人员查看区域位置
    scaledAreas.forEach(area => {
      if (area.isActive) {
        // 激活区域使用黄色边框（仅调试时显示）
        ctx.setStrokeStyle('rgba(255, 255, 0, 0.5)');
        ctx.setLineWidth(2);
      } else {
        // 非激活区域使用灰色边框（仅调试时显示）
        ctx.setStrokeStyle('rgba(200, 200, 200, 0.3)');
        ctx.setLineWidth(1);
      }
      ctx.strokeRect(area.x, area.y, area.width, area.height);
    });
  }

  // 立即绘制，不合并渲染命令
  ctx.draw(false);
};

// 绘制批注
const drawAnnotation = (ctx: any, item: Annotation, imageIndex: number) => {
  // 检查是否是多行批注
  if (item.lines && item.lines.length > 0) {
    // 多行批注，每行单独绘制
    item.lines.forEach((line, index) => {
      const x = line.x * scaleXs.value[imageIndex];
      const y = line.y * scaleYs.value[imageIndex];
      const width = line.width! * scaleXs.value[imageIndex];
      const height = line.height! * scaleYs.value[imageIndex];

      // 只对第一行绘制序号
      const shouldDrawNumber = index === 0;
      drawSingleLine(ctx, item, x, y, width, height, shouldDrawNumber, imageIndex);
    });
  } else {
    // 单行批注
    const x = item.x * scaleXs.value[imageIndex];
    const y = item.y * scaleYs.value[imageIndex];
    // 使用非空断言，因为我们已经在初始化时设置了默认值
    const width = item.width! * scaleXs.value[imageIndex]; // 已经在初始化时设置了默认值
    const height = item.height! * scaleYs.value[imageIndex]; // 已经在初始化时设置了默认值
    drawSingleLine(ctx, item, x, y, width, height, true, imageIndex);
  }
};

// 绘制单行批注
const drawSingleLine = (
  ctx: any,
  item: Annotation,
  x: number,
  y: number,
  width: number,
  height: number,
  shouldDrawNumber: boolean,
  imageIndex: number
) => {
  // 检查SVG是否已加载
  if (svgImages[item.type]) {
    ctx.save();

    // 根据图形类型决定是平铺还是拉伸
    if (item.type === AnnotationType.GREAT || item.type === AnnotationType.RECOMMEND) {
      // 佳句赏析(波浪线)和优化建议(直线)需要平铺绘制
      // 获取原始SVG尺寸用于平铺计算
      const svgInfo = getSvgOriginalSize(item.type);
      if (svgInfo) {
        const { originalWidth } = svgInfo;

        // 使用不同的方法处理波浪线，避免断层
        if (item.type === AnnotationType.GREAT) {
          // 波浪线
          // 创建一个波浪线的渐变模式
          // 使用Canvas API绘制波浪线，而不是直接使用SVG图片
          const waveHeight = height;
          const waveLength = 30; // 一个波浪的长度
          const waveCount = Math.ceil(width / waveLength);

          // 设置波浪线样式
          ctx.setStrokeStyle('#00B8AC'); // 使用SVG中波浪线的颜色
          ctx.setLineWidth(2); // 调整波浪线粗细为2
          ctx.setLineCap('round');
          ctx.beginPath();

          // 绘制波浪线路径
          ctx.moveTo(x, y + waveHeight / 2);
          for (let i = 0; i < waveCount; i++) {
            // 每个波浪由两个贝塞尔曲线组成，形成一个完整的波
            const startX = x + i * waveLength;
            const middleX = startX + waveLength / 2;
            const endX = startX + waveLength;

            // 确保不超出指定宽度
            if (startX > x + width) break;

            // 上半波
            if (endX <= x + width) {
              ctx.quadraticCurveTo(startX + waveLength / 4, y, middleX, y + waveHeight / 2);
            } else {
              // 处理最后一个不完整的波
              const remainingWidth = x + width - startX;
              const ratio = remainingWidth / waveLength;
              const controlX = startX + (waveLength / 4) * ratio;
              const endPointX = startX + waveLength * ratio;
              ctx.quadraticCurveTo(controlX, y, endPointX, y + waveHeight / 2);
              break;
            }

            // 下半波
            if (endX <= x + width) {
              ctx.quadraticCurveTo(
                middleX + waveLength / 4,
                y + waveHeight,
                endX,
                y + waveHeight / 2
              );
            } else {
              // 处理最后一个不完整的波
              break;
            }
          }

          // 描边波浪线
          ctx.stroke();
        } else if (item.type === AnnotationType.RECOMMEND) {
          // 直线
          // 直接使用Canvas API绘制直线，避免断层
          // 设置直线样式
          ctx.setStrokeStyle('#FFB700'); // 使用SVG中直线的颜色
          ctx.setLineWidth(2); // 调整直线粗细为2
          ctx.setLineCap('round'); // 圆角线帽

          // 绘制直线
          ctx.beginPath();
          ctx.moveTo(x, y + height / 2); // 起点，垂直居中
          ctx.lineTo(x + width, y + height / 2); // 终点
          ctx.stroke();
        }
      } else {
        // 如果无法获取原始尺寸，退回到普通绘制
        ctx.drawImage(svgImages[item.type], x, y, width, height);
      }
    } else {
      // 其他类型的批注（如插入、删除等）使用普通绘制
      ctx.drawImage(svgImages[item.type], x, y, width, height);
    }

    // 只有佳句赏析和优化建议才有序号，且只在shouldDrawNumber为true时绘制
    if (
      shouldDrawNumber &&
      item.number !== undefined &&
      (item.type === AnnotationType.GREAT || item.type === AnnotationType.RECOMMEND)
    ) {
      // 调整序号大小和位置
      let circleSize, circleX, circleY;

      // 序号大小根据设备屏幕尺寸自适应缩放
      const baseCircleSize = 15; // 基准圆圈大小
      const baseOffset = 7; // 基准偏移量

      // 应用设备缩放比例
      const adaptiveCircleSize = baseCircleSize * deviceScale.value;
      const adaptiveOffset = baseOffset * deviceScale.value;

      // 统一序号位置
      circleSize = adaptiveCircleSize;
      circleX = x - adaptiveOffset;
      circleY = y - adaptiveOffset;

      // 绘制黑色圆圈
      ctx.setFillStyle('#000000');
      ctx.beginPath();
      ctx.arc(circleX, circleY, circleSize / 2, 0, 2 * Math.PI);
      ctx.fill();

      // 绘制序号
      const fontSize = circleSize * 0.7; // 字体大小为圆圈大小的70%
      ctx.setFillStyle('#ffffff');
      ctx.setTextAlign('center');
      ctx.setTextBaseline('middle');
      ctx.setFontSize(fontSize);
      ctx.fillText(String(item.number), circleX, circleY);

      // 保存序号点击区域信息，用于点击检测
      if (!circleAreas[imageIndex]) {
        circleAreas[imageIndex] = [];
      }

      // 为了提高点击体验，适当扩大检测区域（增加半径）
      const hitRadius = (circleSize / 2) * 1.5; // 扩大1.5倍，提高点击容错率

      if (props.debug) {
        log(`保存序号区域 - ID:${item.id}, 位置:(${circleX},${circleY}), 检测半径:${hitRadius}`);
      }

      circleAreas[imageIndex].push({
        x: circleX,
        y: circleY,
        radius: hitRadius, // 使用扩大的半径
        annotation: item,
      });
    }

    ctx.restore();
  }
};

// 处理容器点击事件 - 修复坐标偏移问题
const handleContainerTap = (e: TouchEvent, imageIndex: number) => {
  log(`容器点击 - 图片索引:${imageIndex}`);

  // 获取点击位置相对于页面的坐标
  const { detail } = e as any;
  let x = detail.x;
  let y = detail.y;

  // 获取容器的位置信息，用于调整坐标偏移
  const query = uni.createSelectorQuery();
  query
    .select(`.canvas-container:nth-child(${imageIndex + 1})`)
    .boundingClientRect((data: any) => {
      if (data) {
        if (props.debug) {
          log(`容器信息 - 位置:(${data.left},${data.top}), 大小:${data.width}x${data.height}`);
        }

        // 调整坐标，使其相对于容器
        x = x - data.left;
        y = y - data.top;

        if (props.debug) {
          log(`调整后的点击坐标: (${x}, ${y})`);

          // 检查画布尺寸和缩放比例
          log(
            `画布尺寸 - 宽:${canvasWidths.value[imageIndex]}, 高:${canvasHeights.value[imageIndex]}`
          );
          log(`缩放比例 - X:${scaleXs.value[imageIndex]}, Y:${scaleYs.value[imageIndex]}`);

          // 在点击处绘制一个临时标记，帮助调试
          markClickPosition(x, y, imageIndex);

          // 输出序号区域信息，方便调试
          if (circleAreas[imageIndex] && circleAreas[imageIndex].length > 0) {
            log(`序号区域信息 - 图片:${imageIndex}, 区域数量:${circleAreas[imageIndex].length}`);
            circleAreas[imageIndex].forEach((circle, i) => {
              log(
                `  序号区域${i}: ID=${circle.annotation.id}, 位置:(${circle.x},${circle.y}), 半径:${circle.radius}`
              );
            });
          }
        }

        // 执行点击检测
        performHitTest(x, y, imageIndex);
      } else {
        logError('无法获取容器位置信息');
      }
    })
    .exec();
};

// 执行点击检测
const performHitTest = (x: number, y: number, imageIndex: number) => {
  // 激活状态变更标记
  let isActivated = false;

  // 检查批注数据
  if (!props.data[imageIndex] || props.data[imageIndex].annotations.length === 0) {
    logWarn(`图片${imageIndex}没有批注数据`);
    return;
  } else if (props.debug) {
    log(`图片${imageIndex}有${props.data[imageIndex].annotations.length}个批注`);

    // 输出批注数据以便调试
    props.data[imageIndex].annotations.forEach((anno, i) => {
      log(
        `批注${i}: ID=${anno.id}, 类型=${anno.type}, 位置=(${anno.x},${anno.y}), 大小=${anno.width}x${anno.height}`
      );
      if (anno.lines) {
        log(`  多行数据: ${anno.lines.length}行`);
        anno.lines.forEach((line, j) => {
          // 计算缩放后的坐标和尺寸
          const scaledX = line.x * scaleXs.value[imageIndex];
          const scaledY = line.y * scaleYs.value[imageIndex];
          const scaledWidth = line.width! * scaleXs.value[imageIndex];
          const scaledHeight = line.height! * scaleYs.value[imageIndex];

          log(
            `  行${j}: 原始=(${line.x},${line.y},${line.width}x${line.height}), 缩放后=(${scaledX},${scaledY},${scaledWidth}x${scaledHeight})`
          );
        });
      }
    });
  }

  // 首先检查是否点击了序号圆圈区域
  if (circleAreas[imageIndex] && circleAreas[imageIndex].length > 0) {
    if (props.debug) {
      log(`检查序号区域点击 - 图片:${imageIndex}, 序号区域数量:${circleAreas[imageIndex].length}`);
    }

    // 遍历所有序号圆圈区域
    for (const circle of circleAreas[imageIndex]) {
      // 计算点击位置到圆心的距离
      const dx = x - circle.x;
      const dy = y - circle.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // 如果距离小于圆半径，表示点击了序号区域
      if (distance <= circle.radius) {
        if (props.debug) {
          log(
            `点击序号区域 - 批注ID:${circle.annotation.id}, 圆心:(${circle.x},${circle.y}), 半径:${circle.radius}`
          );
        }

        // 激活相关联的批注
        setActiveAnnotation(circle.annotation.id, imageIndex);
        emit('annotationActivate', circle.annotation, imageIndex);
        isActivated = true;
        break; // 找到第一个匹配的就终止循环
      }
    }
  }

  // 先直接检查所有透明区域，不依赖之前计算的transparentAreas
  // 这是为了排除transparentAreas计算有误的可能性
  if (!isActivated) {
    if (props.debug) {
      log(`直接检查所有批注区域 - 图片:${imageIndex}`);
    }

    for (const annotation of props.data[imageIndex].annotations) {
      if (supportedTypes.includes(annotation.type as AnnotationType)) {
        // 如果有多行，检查每一行
        if (annotation.lines && annotation.lines.length > 0) {
          for (const line of annotation.lines) {
            // 计算缩放后的坐标和尺寸
            const scaledX = line.x * scaleXs.value[imageIndex];
            const scaledY = (line.y - 40) * scaleYs.value[imageIndex]; // 应用-40的Y偏移
            const scaledWidth = line.width! * scaleXs.value[imageIndex];
            const scaledHeight = (line.height! + 30) * scaleYs.value[imageIndex]; // 应用+30的高度增加

            // 计算区域边界
            const right = scaledX + scaledWidth;
            const bottom = scaledY + scaledHeight;

            if (props.debug) {
              log(
                `检查行区域 - ID:${annotation.id}, 区域:(${scaledX},${scaledY})-(${right},${bottom})`
              );
            }

            // 检查点击是否在区域内
            if (x >= scaledX && x <= right && y >= scaledY && y <= bottom) {
              if (props.debug) {
                log(`命中批注区域(直接检查) - ID:${annotation.id}`);
              }
              setActiveAnnotation(annotation.id, imageIndex);
              emit('annotationActivate', annotation, imageIndex);
              isActivated = true;
              break;
            }
          }
        } else {
          // 单行批注
          const scaledX = annotation.x * scaleXs.value[imageIndex];
          const scaledY = (annotation.y - yOffset) * scaleYs.value[imageIndex];
          const scaledWidth = annotation.width! * scaleXs.value[imageIndex];
          const scaledHeight = (annotation.height! + yOffset) * scaleYs.value[imageIndex];

          // 计算区域边界
          const right = scaledX + scaledWidth;
          const bottom = scaledY + scaledHeight;

          if (props.debug) {
            log(
              `检查单行区域 - ID:${annotation.id}, 区域:(${scaledX},${scaledY})-(${right},${bottom})`
            );
          }

          // 检查点击是否在区域内
          if (x >= scaledX && x <= right && y >= scaledY && y <= bottom) {
            if (props.debug) {
              log(`命中批注区域(直接检查) - ID:${annotation.id}`);
            }
            setActiveAnnotation(annotation.id, imageIndex);
            emit('annotationActivate', annotation, imageIndex);
            isActivated = true;
            break;
          }
        }
      }

      // 如果已经激活了，就不再继续检查
      if (isActivated) break;
    }
  }

  // 使用透明区域数组进行二次检查（保留原有逻辑）
  if (!isActivated && transparentAreas[imageIndex]) {
    if (props.debug) {
      log(`使用透明区域检查 - 图片:${imageIndex}, 区域数量:${transparentAreas[imageIndex].length}`);

      // 调试：输出所有透明区域
      transparentAreas[imageIndex].forEach((area, i) => {
        const scaledX = area.x * scaleXs.value[imageIndex];
        const scaledY = area.y * scaleYs.value[imageIndex];
        const scaledWidth = area.width * scaleXs.value[imageIndex];
        const scaledHeight = area.height * scaleYs.value[imageIndex];
        const right = scaledX + scaledWidth;
        const bottom = scaledY + scaledHeight;

        log(
          `透明区域${i}: ID=${area.annotationId}, 位置=(${scaledX},${scaledY})-(${right},${bottom})`
        );
      });
    }

    // 检查是否点击了任何透明区域
    const clickedArea = transparentAreas[imageIndex].find(area => {
      // 应用缩放比例
      const scaledX = area.x * scaleXs.value[imageIndex];
      const scaledY = area.y * scaleYs.value[imageIndex];
      const scaledWidth = area.width * scaleXs.value[imageIndex];
      const scaledHeight = area.height * scaleYs.value[imageIndex];

      // 计算区域边界
      const right = scaledX + scaledWidth;
      const bottom = scaledY + scaledHeight;

      // 检查点击是否在区域内
      const isInArea = x >= scaledX && x <= right && y >= scaledY && y <= bottom;

      if (isInArea && props.debug) {
        log(`命中透明区域 - ID:${area.annotationId}`);
      }

      return isInArea;
    });

    if (clickedArea) {
      // 找到对应的批注
      const annotation = props.data[imageIndex].annotations.find(
        anno => anno.id === clickedArea.annotationId
      );

      if (annotation) {
        if (props.debug) {
          log(`激活批注 - 图片:${imageIndex}, 批注ID:${annotation.id}`);
        }
        // 激活点击的区域，并记录当前激活的图片索引
        setActiveAnnotation(annotation.id, imageIndex);
        // 发出激活事件，包含图片索引
        emit('annotationActivate', annotation, imageIndex);
        isActivated = true;
      }
    }
  }

  // 如果没有激活任何区域，记录点击但未命中区域
  if (!isActivated && props.debug) {
    log(`点击未命中任何批注区域 - 图片:${imageIndex}, 坐标:(${x},${y})`);
  }
};

// 在点击位置绘制临时标记（用于调试）
const markClickPosition = (x: number, y: number, imageIndex: number) => {
  // 只在debug模式下绘制标记
  if (!props.debug) return;

  const ctx = uni.createCanvasContext(`annotateCanvas_${imageIndex}`, null, {
    willReadFrequently: true,
  });

  // 首先重绘原始内容
  drawAll(imageIndex);

  // 绘制点击标记
  ctx.save();

  // 1. 绘制点击位置的十字线
  ctx.beginPath();
  ctx.arc(x, y, 10, 0, 2 * Math.PI);
  ctx.setFillStyle('rgba(255, 0, 0, 0.5)');
  ctx.fill();
  ctx.beginPath();
  ctx.moveTo(x - 15, y);
  ctx.lineTo(x + 15, y);
  ctx.moveTo(x, y - 15);
  ctx.lineTo(x, y + 15);
  ctx.setStrokeStyle('red');
  ctx.setLineWidth(2);
  ctx.stroke();

  // 不再显示序号区域的边界，即使在调试模式下
  // 如果需要在控制台输出序号区域信息
  if (props.debug && circleAreas[imageIndex] && circleAreas[imageIndex].length > 0) {
    log(`序号区域信息 - 图片:${imageIndex}, 区域数量:${circleAreas[imageIndex].length}`);
    circleAreas[imageIndex].forEach((circle, i) => {
      log(
        `  序号区域${i}: ID=${circle.annotation.id}, 位置:(${circle.x},${circle.y}), 半径:${circle.radius}`
      );
    });
  }

  ctx.restore();

  // 立即绘制
  ctx.draw(true); // 使用true以保留原始内容

  // 5秒后移除标记，延长显示时间以便更好观察
  setTimeout(() => {
    drawAll(imageIndex);
  }, 5000);
};

// 以下是原来的handleCanvasTap函数，可以移除或保留为备份
const handleCanvasTap = (e: any, imageIndex: number) => {
  // 不再使用，所有点击事件由handleContainerTap处理
  log('Canvas点击事件被触发，但不再使用');
};

// 设置活动批注区域，并更新所有图片的蒙版
const setActiveAnnotation = (id: string | null, imageIndex: number | null) => {
  log(`设置激活状态 - 批注ID:${id}, 图片索引:${imageIndex}`);

  // 更新活动区域ID和图片索引
  activeAreaId.value = id;
  activeImageIndex.value = imageIndex;

  // 重新计算所有透明区域的激活状态
  for (let i = 0; i < props.data.length; i++) {
    if (transparentAreas[i]) {
      transparentAreas[i].forEach(area => {
        // 只有匹配的批注ID和图片索引才设置为激活
        area.isActive = area.annotationId === id && i === imageIndex;
      });
    }
  }

  // 重绘所有图片的蒙版
  for (let i = 0; i < props.data.length; i++) {
    drawPermanentMask(i);
  }
};

// 从defineExpose中提取出来的方法
// 刷新所有蒙版
const refreshAllMasks = () => {
  for (let i = 0; i < props.data.length; i++) {
    drawPermanentMask(i);
  }
};

// 刷新所有画布和蒙版
const refreshAll = () => {
  for (let i = 0; i < props.data.length; i++) {
    drawAll(i);
    drawPermanentMask(i);
  }
};

// 设置活动区域并触发事件
const setActiveArea = (id: string | null, imageIndex: number | null) => {
  // 使用 setActiveAnnotation 完成所有激活逻辑
  setActiveAnnotation(id, imageIndex);

  // 如果有效的批注ID和图片索引，触发事件
  if (id && imageIndex !== null) {
    // 查找对应的批注对象
    const annotation = props.data[imageIndex].annotations.find(anno => anno.id === id);
    if (annotation) {
      // 触发 annotationActivate 事件
      emit('annotationActivate', annotation, imageIndex);
      return true;
    }
  }
  return false;
};

// 记录上次激活的批注ID和时间
const lastActivatedAnnotation = reactive({
  id: '',
  timestamp: 0,
});

// 通过ID激活批注的方法，自动在所有图片中查找
const activateAnnotationById = (annotationId: string) => {
  log(`尝试激活批注: ${annotationId}`);

  // 防抖处理：如果短时间内重复激活同一个批注，则忽略
  const now = Date.now();
  const debounceTime = 500; // 防抖时间，单位毫秒
  if (
    lastActivatedAnnotation.id === annotationId &&
    now - lastActivatedAnnotation.timestamp < debounceTime
  ) {
    log(
      `防抖处理：忽略重复激活批注 ${annotationId}，间隔时间: ${now - lastActivatedAnnotation.timestamp}ms`
    );
    return true; // 返回true表示激活成功，但实际上是忽略了这次激活
  }

  // 更新上次激活的批注信息
  lastActivatedAnnotation.id = annotationId;
  lastActivatedAnnotation.timestamp = now;

  // 在所有图片批注中查找指定ID的批注
  for (let imgIndex = 0; imgIndex < props.data.length; imgIndex++) {
    // 在当前图片的批注数组中查找
    const annotation = props.data[imgIndex].annotations.find(anno => anno.id === annotationId);
    if (annotation) {
      log(`找到批注: ${annotationId}, 图片索引: ${imgIndex}, 批注编号: ${annotation.number}`);

      // 找到匹配的批注，调用setActiveArea方法
      const result = setActiveArea(annotationId, imgIndex);

      // 如果成功激活，滚动到批注位置
      if (result) {
        // 对于第二张图片上的批注，增加更长的延时
        const delay = imgIndex > 0 ? 400 : 300;

        // 使用setTimeout确保DOM已更新
        setTimeout(() => {
          log(`准备滚动到批注位置: ${annotationId}, 延时: ${delay}ms`);
          scrollToAnnotation(imgIndex, annotation);
        }, delay);
      }

      return result;
    }
  }

  logError(`未找到annotationId为${annotationId}的批注`);
  return false;
};

// 滚动到批注位置
const scrollToAnnotation = (imageIndex: number, annotation: Annotation) => {
  log(
    `开始滚动到批注位置: 图片索引=${imageIndex}, 批注ID=${annotation.id}, 批注编号=${annotation.number}`
  );

  // 计算批注在画布中的位置
  let targetY = 0;

  // 根据批注类型确定滚动位置
  if (annotation.lines && annotation.lines.length > 0) {
    // 多行批注，使用第一行的位置
    targetY = annotation.lines[0].y * scaleYs.value[imageIndex];
  } else {
    // 单行批注，使用批注的y坐标
    targetY = annotation.y * scaleYs.value[imageIndex];
  }

  // 对于第二张及以后的图片，需要累加前面所有图片的高度
  let totalOffset = 0;
  for (let i = 0; i < imageIndex; i++) {
    totalOffset += canvasHeights.value[i] + 20; // 加上图片间距20px
  }

  // 将目标位置加上前面图片的累计高度
  const finalTargetY = totalOffset + targetY;

  // 设置滚动位置，减去一定的偏移量，使批注位置更靠近视口顶部
  const offsetY = 150; // 增加偏移量，确保批注在视口中更靠上的位置
  const newScrollTop = Math.max(0, finalTargetY - offsetY);

  // 检查批注是否已经在可视窗口内
  const isInViewport = checkIfAnnotationInViewport(finalTargetY);

  if (isInViewport) {
    log(`批注已在可视窗口内，不进行滚动`);
    return; // 如果批注已在可视窗口内，不进行滚动
  }

  // 使用平滑滚动效果
  smoothScrollTo(newScrollTop);

  log(
    `滚动到位置: ${newScrollTop}px, 最终目标Y: ${finalTargetY}px, 原始目标Y: ${targetY}px, 累计偏移: ${totalOffset}px`
  );
};

// 平滑滚动到指定位置
const smoothScrollTo = (targetScrollTop: number) => {
  // 获取当前滚动位置
  const startScrollTop = scrollTop.value;
  // 计算滚动距离
  const distance = targetScrollTop - startScrollTop;

  // 如果距离很小，直接设置滚动位置
  if (Math.abs(distance) < 50) {
    scrollTop.value = targetScrollTop;
    return;
  }

  // 滚动动画参数
  const duration = 300; // 动画持续时间，单位毫秒
  const startTime = Date.now();

  // 动画函数
  const animateScroll = () => {
    const currentTime = Date.now();
    const elapsed = currentTime - startTime;

    // 使用缓动函数计算当前滚动位置
    const progress = Math.min(elapsed / duration, 1);
    const easedProgress = easeInOutCubic(progress);
    const currentScrollTop = startScrollTop + distance * easedProgress;

    // 更新滚动位置
    scrollTop.value = currentScrollTop;

    // 如果动画未完成，继续请求动画帧
    if (elapsed < duration) {
      requestAnimationFrame(animateScroll);
    } else {
      // 动画完成，确保滚动到目标位置
      scrollTop.value = targetScrollTop;
    }
  };

  // 开始动画
  requestAnimationFrame(animateScroll);
};

// 缓动函数 - 三次方缓动
const easeInOutCubic = (t: number): number => {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};

// 更新scroll-view高度
const updateScrollViewHeight = () => {
  const query = uni.createSelectorQuery();
  query
    .select('.annotation-writing-scroll')
    .boundingClientRect((data: any) => {
      if (data && data.height) {
        scrollViewHeight.value = data.height;
        log(`获取到scroll-view高度: ${scrollViewHeight.value}px`);
      }
    })
    .exec();
};

// 检查批注是否在可视窗口内
const checkIfAnnotationInViewport = (annotationY: number): boolean => {
  // 获取当前滚动位置
  const currentScrollTop = scrollTop.value;

  // 计算可视窗口的上下边界
  // 添加缓冲区，扩大判断范围，减少边界情况下的抖动
  const bufferZone = 100; // 缓冲区大小，单位像素
  const viewportTop = currentScrollTop - bufferZone;
  const viewportBottom = currentScrollTop + scrollViewHeight.value + bufferZone;

  // 检查批注是否在可视窗口内
  // 我们给批注位置添加一个小的偏移量，确保它在视口中的位置合适
  const annotationPosition = annotationY - 100; // 减去100px，使批注位置更靠近视口顶部

  // 如果批注位置在可视窗口内，则返回true
  const isInViewport = annotationPosition >= viewportTop && annotationPosition <= viewportBottom;

  log(
    `检查批注是否在可视窗口内: 批注位置=${annotationPosition}px, 视口上边界(含缓冲)=${viewportTop}px, 视口下边界(含缓冲)=${viewportBottom}px, 视口高度=${scrollViewHeight.value}px, 结果=${isInViewport}`
  );

  return isInViewport;
};

// 监听props变化
watch(
  () => props.data,
  () => {
    // 当批注数据变化时，重新绘制所有图片
    for (let i = 0; i < props.data.length; i++) {
      drawAll(i);
      drawPermanentMask(i);
    }
  },
  { deep: true }
);

watch(
  () => props.data,
  () => {
    // 当图片URL数组变化时，重新初始化
    init();
  },
  { deep: true }
);

// 在onMounted中初始化
onMounted(() => {
  // 初始化
  init();

  // 监听窗口大小变化，更新scroll-view高度
  uni.onWindowResize(() => {
    updateScrollViewHeight();
  });
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  // 移除窗口大小变化监听
  uni.offWindowResize(() => {
    log('移除窗口大小变化监听');
  });
});

// 清除活动区域
const clearActiveArea = () => {
  // 调用setActiveAnnotation方法，传入null参数清除当前激活的批注
  setActiveAnnotation(null, null);
  return true;
};

// 暴露方法和响应式数据给模板
defineExpose({
  canvasWidths,
  canvasHeights,
  handleCanvasTap,
  refreshAllMasks,
  refreshAll,
  setActiveArea,
  activateAnnotationById,
  clearActiveArea,
  activeImageIndex,
  activeAreaId,
});
</script>

<style scoped>
/* 容器样式 */
.annotation-writing-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding-bottom: 20px;
  /* 添加底部间距，确保最后一个批注可以完全滚动到视图中 */
}

/* 滚动视图样式 */
.annotation-writing-scroll {
  height: 100%;
  /* 确保scroll-view填充父容器 */
  width: 100%;
  position: relative;
}

/* 画布容器样式 */
.canvas-container {
  position: relative;
  margin: 0 auto;
  margin-bottom: 20px;
  /* 确保图片之间有足够的间距 */
}

/* 画布样式 */
.base-canvas,
.mask-canvas {
  position: absolute;
  top: 0;
  left: 0;
}
</style>
