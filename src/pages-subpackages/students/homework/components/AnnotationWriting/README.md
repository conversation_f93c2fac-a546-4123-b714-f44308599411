# AnnotationWriting 批注组件

一个用于在图片上展示和交互批注的Vue组件。

## 功能特点

- 支持多种批注类型：佳句赏析(波浪线)、插入内容、删除内容、替换内容、优化建议(直线)
- 支持多图片批注展示
- 支持批注区域点击交互
- 支持多行批注
- 批注区域自适应图片缩放

## 使用方法

```vue
<template>
  <AnnotationWriting :data="annotationData" @annotationActivate="handleAnnotationActivate" />
</template>

<script setup>
import AnnotationWriting from '@/components/AnnotationWriting';
import { ref } from 'vue';
import { AnnotationType } from '@/components/AnnotationWriting';

// 准备批注数据
const annotationData = ref([
  {
    imageUrl: '/static/images/example.jpg',
    annotations: [
      {
        id: '1',
        type: AnnotationType.GREAT,
        x: 100,
        y: 200,
        width: 150,
        height: 10,
        number: 1,
        content: '这是一个佳句赏析批注',
      },
      {
        id: '2',
        type: AnnotationType.RECOMMEND,
        x: 100,
        y: 300,
        width: 200,
        height: 10,
        number: 2,
        content: '这是一个优化建议批注',
      },
    ],
  },
]);

// 处理批注激活事件
const handleAnnotationActivate = (annotation, imageIndex) => {
  console.log('批注被激活:', annotation, '图片索引:', imageIndex);
};
</script>
```

## 属性

| 属性名          | 类型                      | 默认值           | 说明                                     |
| --------------- | ------------------------- | ---------------- | ---------------------------------------- |
| data            | Array<AnnotationDataItem> | []               | 批注数据，包含图片URL和批注信息          |
| debug           | Boolean                   | false            | 是否开启调试模式，显示调试信息和区域边界 |
| containerWidth  | Number                    | 设备宽度         | 容器宽度，默认使用设备宽度               |
| containerHeight | Number                    | 根据图片比例计算 | 容器高度，默认根据图片宽高比计算         |

## 事件

| 事件名             | 参数                                         | 说明                       |
| ------------------ | -------------------------------------------- | -------------------------- |
| annotationActivate | (annotation: Annotation, imageIndex: number) | 当批注区域被点击激活时触发 |

## 方法

通过 ref 获取组件实例后可以调用以下方法：

| 方法名                 | 参数                                             | 返回值  | 说明                                 |
| ---------------------- | ------------------------------------------------ | ------- | ------------------------------------ |
| refreshAll             | 无                                               | void    | 刷新所有画布和蒙版                   |
| refreshAllMasks        | 无                                               | void    | 仅刷新所有蒙版                       |
| setActiveArea          | (id: string \| null, imageIndex: number \| null) | boolean | 设置激活区域并触发事件               |
| activateAnnotationById | (id: string)                                     | boolean | 通过ID激活批注，自动在所有图片中查找 |

## 数据结构

### AnnotationDataItem

```typescript
interface AnnotationDataItem {
  imageUrl: string; // 图片URL
  annotations: Annotation[]; // 该图片的批注数组
}
```

### Annotation

```typescript
interface Annotation {
  id: string;
  type: AnnotationType | string;
  x: number;
  y: number;
  width?: number; // 默认值: 100
  height?: number; // 默认值: 10
  number?: number; // 序号，仅用于佳句赏析和优化建议类型
  content?: string;
  lines?: AnnotationLine[]; // 多行批注
}
```

### AnnotationType

```typescript
enum AnnotationType {
  GREAT = 'great', // 佳句赏析(波浪线)
  INSERT = 'insert', // 插入内容(V形)
  DELETE = 'delete', // 删除内容(特殊符号)
  REPLACE = 'replace', // 替换内容(半圆形)
  RECOMMEND = 'recommend', // 优化建议(直线)
}
```

### AnnotationLine

```typescript
interface AnnotationLine {
  x: number;
  y: number;
  width: number; // 默认值: 100
  height: number; // 默认值: 10
}
```

## 注意事项

1. 批注的默认高度为10px，默认宽度为100px
2. 波浪线和直线的粗细为2px
3. 组件会自动适应图片尺寸和容器尺寸
4. 只有佳句赏析(GREAT)和优化建议(RECOMMEND)类型的批注支持透明区域交互
