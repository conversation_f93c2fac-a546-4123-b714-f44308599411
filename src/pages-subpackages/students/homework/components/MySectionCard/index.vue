<template>
  <view class="section-card">
    <view class="section-header">
      <view class="section-title-bg">
        <text class="section-title">{{ title }}</text>
      </view>
    </view>
    <view class="section-content">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  title: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});
</script>

<style lang="scss" scoped>
.section-card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 0 0 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 0 15px rgba(237, 237, 237, 0.62);
  border: 1px solid #f1f1f1;
}

.section-header {
  height: 80rpx;
  position: relative;
  margin-bottom: 20rpx;
}

.section-title-bg {
  position: absolute;
  top: -12rpx;
  left: 20rpx;
  width: 250rpx;
  height: 78rpx;
  background: linear-gradient(to right, #7d4dff, #a25aff);
  border-radius: 0 0 14rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;

  background-image: url('/static/homework/clip.svg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.section-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #ffffff;
  margin-left: -20rpx;
}

.section-content {
  padding: 0 30rpx;
}
</style>
