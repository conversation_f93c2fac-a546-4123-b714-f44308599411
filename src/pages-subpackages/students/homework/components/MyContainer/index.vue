<template>
  <view
    class="my-container"
    :class="[
      `bg-${bgColor}`,
      {
        'safe-top': safeTop,
        'safe-bottom': safeBottom,
        'has-padding': hasPadding,
        'full-height': fullHeight,
        'flex-column': flexColumn,
      },
    ]"
    :style="customStyle"
  >
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>

    <!-- 内容区域 -->
    <template v-else>
      <slot></slot>
    </template>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { CSSProperties } from 'vue';

// 定义组件属性
interface Props {
  // 背景颜色预设
  bgColor?: string;
  // 是否适配顶部安全区域
  safeTop?: boolean;
  // 是否适配底部安全区域
  safeBottom?: boolean;
  // 是否添加内边距
  hasPadding?: boolean;
  // 是否占满全屏高度
  fullHeight?: boolean;
  // 是否使用flex纵向布局
  flexColumn?: boolean;
  // 自定义样式
  customStyle?: CSSProperties;
  // 是否显示加载状态
  loading?: boolean;
  // 加载提示文字
  loadingText?: string;
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  bgColor: 'white',
  safeTop: false,
  safeBottom: false,
  hasPadding: false,
  fullHeight: true,
  flexColumn: false,
  customStyle: () => ({}),
  loading: false,
  loadingText: '加载中...',
});
</script>

<style lang="scss" scoped>
.my-container {
  width: 100%;
  box-sizing: border-box;

  // 背景颜色预设
  &.bg-white {
    background-color: #ffffff;
  }

  &.bg-gray {
    background-color: #f5f6fa;
  }

  &.bg-primary {
    background-color: #7d4dff;
  }

  &.bg-transparent {
    background-color: transparent;
  }

  // 安全区域适配
  &.safe-top {
    padding-top: env(safe-area-inset-top);
  }

  &.safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  // 内边距设置
  &.has-padding {
    padding-left: 30rpx;
    padding-right: 30rpx;
  }

  // 高度设置
  &.full-height {
    height: 100vh;
  }

  // flex布局
  &.flex-column {
    display: flex;
    flex-direction: column;
  }

  // 加载状态样式
  .loading-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 6rpx solid rgba(125, 77, 255, 0.2);
      border-top: 6rpx solid #7d4dff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: #86909c;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
