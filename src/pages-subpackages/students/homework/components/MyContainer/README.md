# MyContainer 容器组件

一个用于处理页面布局的容器组件，提供头部底部安全距离和背景颜色预设功能。

## 特性

- 预设背景颜色（白色、灰色、主题色、透明）
- 顶部/底部安全区域适配
- 内边距设置
- 全屏高度设置
- Flex纵向布局
- 自定义样式支持

## 使用方法

### 基础用法

```vue
<MyContainer>
  <text>基础容器，默认白色背景</text>
</MyContainer>
```

### 背景颜色

```vue
<!-- 灰色背景 -->
<MyContainer bgColor="gray">
  <text>灰色背景容器</text>
</MyContainer>

<!-- 主题色背景 -->
<MyContainer bgColor="primary">
  <text class="text-white">主题色背景容器</text>
</MyContainer>

<!-- 透明背景 -->
<MyContainer bgColor="transparent">
  <text>透明背景容器</text>
</MyContainer>
```

### 安全区域适配

```vue
<!-- 顶部安全区域 -->
<MyContainer safeTop>
  <text>适配顶部安全区域</text>
</MyContainer>

<!-- 底部安全区域 -->
<MyContainer safeBottom>
  <text>适配底部安全区域</text>
</MyContainer>

<!-- 同时适配顶部和底部 -->
<MyContainer safeTop safeBottom>
  <text>适配顶部和底部安全区域</text>
</MyContainer>
```

### 内边距设置

```vue
<MyContainer hasPadding>
  <text>带内边距的容器</text>
</MyContainer>
```

### 全屏高度

```vue
<MyContainer fullHeight>
  <text>全屏高度容器</text>
</MyContainer>
```

### Flex纵向布局

```vue
<MyContainer flexColumn>
  <text>第一行</text>
  <text>第二行</text>
  <text>第三行</text>
</MyContainer>
```

### 自定义样式

```vue
<MyContainer :customStyle="{ borderRadius: '16rpx', margin: '20rpx' }">
  <text>带自定义样式的容器</text>
</MyContainer>
```

### 组合使用

```vue
<MyContainer bgColor="gray" safeBottom hasPadding flexColumn :customStyle="{ gap: '20rpx' }">
  <text>组合使用示例</text>
  <view class="demo-box">内容区域</view>
</MyContainer>
```

## 属性

| 属性名      | 类型          | 默认值  | 说明                                                        |
| ----------- | ------------- | ------- | ----------------------------------------------------------- |
| bgColor     | string        | 'white' | 背景颜色，可选值：'white'、'gray'、'primary'、'transparent' |
| safeTop     | boolean       | false   | 是否适配顶部安全区域                                        |
| safeBottom  | boolean       | false   | 是否适配底部安全区域                                        |
| hasPadding  | boolean       | false   | 是否添加内边距（左右30rpx）                                 |
| fullHeight  | boolean       | false   | 是否占满全屏高度                                            |
| flexColumn  | boolean       | false   | 是否使用flex纵向布局                                        |
| customStyle | CSSProperties | {}      | 自定义样式                                                  |

## 插槽

| 名称    | 说明               |
| ------- | ------------------ |
| default | 默认插槽，容器内容 |
