<template>
  <view class="image-item" :class="{ active: active }" :style="customStyle" @click="handleClick">
    <!-- 加载中状态 -->
    <template v-if="loading">
      <view class="loading-container">
        <view class="loading-icon"></view>
        <!-- <view class="loading-text">识别中...</view> -->
      </view>
      <view class="delete-icon" @click.stop="handleDelete" v-if="showDelete">×</view>
    </template>

    <!-- 正常模式 -->
    <template v-else-if="!warning">
      <image :src="imageUrl" mode="aspectFill" />
      <view class="delete-icon" @click.stop="handleDelete" v-if="showDelete">×</view>
    </template>

    <!-- 警告模式 -->
    <template v-else>
      <view class="warning-container">
        <view class="warning-icon">
          <image src="/static/common/warning.svg" width="32rpx" height="32rpx" />
        </view>
        <view class="warning-text">图片未识别到</view>
        <view class="warning-text">请重新上传</view>
      </view>
      <view class="delete-icon" @click.stop="handleDelete" v-if="showDelete">×</view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { LkSvg } from '@/components';

// 定义组件属性
const props = defineProps({
  // 图片对象或URL
  image: {
    type: [Object, String],
    required: true,
  },
  // 图片索引
  index: {
    type: Number,
    default: 0,
  },
  // 警告模式
  warning: {
    type: Boolean,
    default: false,
  },
  // 加载中状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 拖拽激活状态
  active: {
    type: Boolean,
    default: false,
  },
  // 是否显示删除按钮
  showDelete: {
    type: Boolean,
    default: true,
  },
  // 自定义宽度
  width: {
    type: [String, Number],
    default: '160rpx',
  },
  // 自定义高度
  height: {
    type: [String, Number],
    default: '160rpx',
  },
});

// 定义事件
const emit = defineEmits(['click', 'delete']);

// 计算图片URL
const imageUrl = computed(() => {
  if (!props.image) return '';
  if (typeof props.image === 'string') return props.image;
  return props.image.fileUrl || props.image.url || '';
});

// 计算自定义样式
const customStyle = computed(() => {
  return {
    width: typeof props.width === 'number' ? `${props.width}rpx` : props.width,
    height: typeof props.height === 'number' ? `${props.height}rpx` : props.height,
  };
});

// 处理点击事件
const handleClick = () => {
  if (!props.warning) {
    emit('click', props.index);
  }
};

// 处理删除事件
const handleDelete = () => {
  emit('delete', props.index);
};
</script>

<style lang="scss" scoped>
.image-item {
  // 默认宽高由props控制
  border-radius: 10rpx;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  transition: all 0.2s;

  &.active {
    transform: scale(1.05);
    box-shadow: 0px 4rpx 16rpx rgba(0, 0, 0, 0.2);
    z-index: 10;
    opacity: 0.8;
  }
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 0 0 0 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.warning-container {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  box-sizing: border-box;
}

.warning-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;

  image {
    width: 32rpx;
    height: 32rpx;
  }
}

.warning-text {
  color: #ffffff;
  font-size: 20rpx;
  line-height: 28rpx;
  text-align: center;
}

.loading-container {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  box-sizing: border-box;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  margin-bottom: 10rpx;
}

.loading-text {
  color: #ffffff;
  font-size: 20rpx;
  line-height: 28rpx;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
