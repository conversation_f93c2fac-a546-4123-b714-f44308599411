<template>
  <view class="filter-item" :class="props.customClassList" @click="onClick">
    <view class="picker-trigger">
      <text class="picker-text">{{ displayText }}</text>
      <!-- <text class="picker-arrow"></text> -->
      <image src="/static/homework/arrow_down_fill.svg" class="picker-arrow" mode="aspectFit" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  /** 自定义class列表 */
  customClassList?: string[];
  /** 显示的文本 */
  text: string;
  /** 默认文本，当没有选择值时显示 */
  defaultText?: string;
}>();

const emit = defineEmits<{
  /** 点击事件 */
  (e: 'click'): void;
}>();

// 计算显示的文本
const displayText = computed(() => {
  return props.text || props.defaultText || '全部';
});

// 点击事件处理
const onClick = () => {
  emit('click');
};
</script>

<style lang="scss" scoped>
.filter-item {
  flex: 1;
  // padding: 0 10rpx;
  min-width: 0;
  /* 防止内容溢出 */
}

/* 自定义picker触发器 */
.picker-trigger {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f8f8;
  padding: 0rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  width: 100%;
  height: 64rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
}

.picker-text {
  color: #4e5969;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  font-style: normal;
  font-weight: 400;
  font-size: 28rpx;
}

.picker-arrow {
  font-size: 20rpx;
  color: #4e5969;
  margin-left: 8rpx;
  width: 42rpx;
  height: 32rpx;
}
</style>
