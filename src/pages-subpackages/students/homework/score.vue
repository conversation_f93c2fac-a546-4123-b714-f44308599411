<template>
  <view class="score-page">
    <!-- 顶部导航栏 -->
    <HomeworkNavBar
      :subjectName="subjectName"
      :homeworkTitle="homeworkTitle"
      :extended="scoreType !== 'writing'"
      :autoBack="false"
      @leftClick="onBackClick"
    >
    </HomeworkNavBar>

    <ScoreWriting v-if="scoreType === 'writing'" :submitId="submitId" />
    <!-- 内容区域 -->
    <view class="score-content" v-else>
      <!-- 动态加载不同类型的成绩单组件 -->
      <ScoreSingle
        v-if="scoreType === 'single'"
        :homeworkId="homeworkId"
        :stuHomeworkId="stuHomeworkId"
      />
      <ScoreSelf v-else-if="scoreType === 'self'" :homeworkId="stuHomeworkId" />
    </view>
    <!-- 底部工具栏 -->
    <view
      class="score-toolbar"
      :style="{ paddingBottom: `${getBottomSafeAreaHeight() + 10}px` }"
      v-if="scoreType !== 'self'"
    >
      <view class="toolbar-container">
        <button v-if="showErrorBookBtn" class="toolbar-btn" @click="goToQuestionPaper">
          去错题本
        </button>
        <button class="toolbar-btn" @click="viewHomeworkDetail">查看作业详情</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import HomeworkNavBar from './components/HomeworkNavBar/index.vue';
import ScoreSingle from './components/Score/ScoreSingle.vue';
import ScoreWriting from './components/Score/ScoreWriting.vue';
import ScoreSelf from './components/Score/ScoreSelf.vue';
import MyContainer from './components/MyContainer/index.vue';
import { getBottomSafeAreaHeight } from '@/utils/common';
// 页面参数
const scoreType = ref('single'); // 默认为单次成绩单
const homeworkId = ref('');
const subjectName = ref('');
const homeworkTitle = ref('');
const stuHomeworkId = ref('');
const submitId = ref('');

const showErrorBookBtn = computed(() => {
  return scoreType.value === 'single';
});

onLoad((options: any) => {
  // 获取作业ID
  homeworkId.value = options.id || '';
  // 获取学生作业ID
  stuHomeworkId.value = options.stuHomeworkId || '';
  // 获取成绩单类型
  scoreType.value = options.type || 'single';
  // 获取科目名称
  subjectName.value = options.subTitle || '';
  // 获取作业标题
  homeworkTitle.value = options.title || '';
  // 获取提交ID
  submitId.value = options.submitId || '';
});

onMounted(() => {});

// 去错题本
const goToQuestionPaper = () => {
  uni.navigateTo({
    url: `/pages-subpackages/students/error-book/detail?homeworkId=${homeworkId.value}&status=1`,
  });
};

// 查看作业详情
const viewHomeworkDetail = () => {
  uni.navigateTo({
    url: `/pages-subpackages/students/homework/detail?id=${homeworkId.value}`,
  });
};

const onBackClick = () => {
  uni.navigateTo({
    url: `/pages-subpackages/students/homework/index`,
  });
};
</script>

<style lang="scss" scoped>
.score-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.score-content {
  flex: 1;
  overflow-y: auto;
  border-radius: 28rpx 28rpx 0 0;
  background: #fefefe;
  margin: 0;
  position: relative;
  z-index: 2;
  /* 确保内容区域在导航栏之上 */
  padding: 0;
  margin: -120rpx 0rpx 10rpx 0rpx;
}

.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #909399;
  padding: 40rpx 0;
}

.score-toolbar {
  padding: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.toolbar-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #7d4dff;
  background-color: #ffffff;
  border: 2rpx solid #7d4dff;
  text-align: center;
  margin: 0;
  text-align: center;

  &::after {
    border: none;
  }

  &:active {
    opacity: 0.8;
  }
}
</style>
