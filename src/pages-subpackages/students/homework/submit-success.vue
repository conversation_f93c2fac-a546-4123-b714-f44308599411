<template>
  <view class="submit-success-page">
    <!-- 头部导航栏 -->
    <HomeworkNavBar
      :subjectName="subTitle"
      :homeworkTitle="title"
      :extended="false"
      backgroundColor="#7D4DFF"
    />

    <!-- 内容主体 -->
    <view class="success-content">
      <image class="success-image" src="/static/homework/paper_edit.svg" mode="aspectFit" />
      <view class="success-title">作业提交成功，待教师批改</view>
      <!-- <view class="success-desc">您的作业已成功提交，感谢您的作答</view> -->
      <button class="success-btn" @click="goList">返回我的作业</button>
      <button class="success-btn" @click="goDetail">查看作业详情</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import HomeworkNavBar from '@/pages-subpackages/students/homework/components/HomeworkNavBar/index.vue';

const homeworkId = ref<string>('');
const subTitle = ref<string>('作业');
const title = ref<string>('提交成功');

onLoad((options: any) => {
  homeworkId.value = options.id;
  subTitle.value = options.subTitle;
  title.value = options.title;
});

function goList() {
  uni.redirectTo({
    url: '/pages-subpackages/students/homework/index',
  });
}

function goDetail() {
  uni.redirectTo({
    url: `/pages-subpackages/students/homework/detail?id=${homeworkId.value}&backHome=true`,
  });
}
</script>

<style lang="scss" scoped>
.submit-success-page {
  min-height: 100vh;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
}

.success-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: center;
  padding: 0 40rpx;
  margin-top: 300rpx;
}

.success-image {
  width: 240rpx;
  height: 160rpx;
  margin-bottom: 48rpx;
  margin-top: 40rpx;
}

.success-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #222;
  margin-bottom: 48rpx;
  text-align: center;
}

.success-desc {
  font-size: 28rpx;
  color: #86909c;
  margin-bottom: 60rpx;
  text-align: center;
}

.success-btn {
  width: 290rpx;
  height: 96rpx;
  background: #f4eeff;
  color: #7d4dff;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  border: none;
  outline: none;
  box-shadow: none;
  letter-spacing: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  background: #f3ecff;

  &::after {
    border: none;
  }
}
</style>
