import { type FrontQuestion } from '@/types/students/homework';
import { QuestionType } from '@/constants/students/homework';

// 在线作业题目模拟数据
export const onlineQuestions: FrontQuestion[] = [
  // 单选题 - 选项模板
  {
    id: '1',
    index: 1,
    type: QuestionType.SINGLE,
    title: 'Vue3 中的 ref() 和 reactive() 的主要区别是什么？',
    score: 10,
    options: [
      { id: '1-1', content: 'ref 只能用于基本类型，reactive 只能用于对象' },
      { id: '1-2', content: 'ref 需要.value访问，reactive 直接访问' },
      { id: '1-3', content: 'ref 不支持深层响应，reactive 支持' },
      { id: '1-4', content: 'ref 性能更好，reactive 性能较差' },
    ],
    answer: '1-2',
    analysis:
      'ref() 和 reactive() 最主要的区别在于使用方式：ref 包装的数据需要通过 .value 访问，而 reactive 可以直接访问。两者都支持深层响应式，都可以用于任何类型的数据。',
    /* 后端数据缓存，实际情况是后端返回的题目数据 */
    backendData: {
      id: '40',
      question: {
        questionTypeName: '单选题',
      },
      // ...
    },
  },

  // 新增第二道单选题
  {
    id: '2',
    index: 2,
    type: QuestionType.SINGLE,
    title: 'Vue3 中 setup() 函数的返回值会被用作什么？',
    score: 10,
    options: [
      { id: '2-1', content: '组件的data选项' },
      { id: '2-2', content: '组件的methods选项' },
      { id: '2-3', content: '组件的模板渲染上下文' },
      { id: '2-4', content: '组件的生命周期钩子' },
    ],
    answer: '2-3',
    analysis:
      'setup() 函数的返回值会被用作组件的模板渲染上下文，即模板中可以直接使用setup返回对象中的属性和方法。这是Vue3组合式API的核心特性之一。',
  },

  // 多选题 - 选项模板（注意index已更新为3）
  {
    id: '3',
    index: 3,
    type: QuestionType.MULTIPLE,
    title: '以下哪些是 Vue3 组合式 API 的优势？（多选）',
    score: 15,
    options: [
      { id: '3-1', content: '更好的代码组织和复用' },
      { id: '3-2', content: '更好的类型推导' },
      { id: '3-3', content: '更小的打包体积' },
      { id: '3-4', content: '更好的运行时性能' },
      { id: '3-5', content: '更容易调试' },
    ],
    answer: ['3-1', '3-2'],
    analysis:
      'Vue3 组合式 API 的主要优势包括：1. 相关功能的代码可以更好地组织在一起；2. 更好的 TypeScript 类型推导支持；3. 通过 tree-shaking 和响应式系统的优化，提供了更好的性能。调试体验和选项式 API 相比各有优劣。',
  },

  // 填空题模板（index更新为4）
  {
    id: '4',
    index: 4,
    type: QuestionType.FILL_BLANK,
    title: '请填写下面空格',
    score: 10,
    blanks: [
      {
        id: '4-1',
        parts: [
          { type: 'text', value: 'Vue3中，创建响应式对象的两个主要API是' },
          { type: 'blank', index: 0 },
          { type: 'text', value: '和' },
          { type: 'blank', index: 1 },
          { type: 'text', value: '。' },
        ],
      },
      {
        id: '4-2',
        parts: [
          { type: 'text', value: 'Vue3中，创建响应式对象的两个主要API是' },
          { type: 'blank', index: 0 },
          { type: 'text', value: '和' },
          { type: 'blank', index: 1 },
          { type: 'text', value: '。' },
        ],
      },
      {
        id: '4-3',
        parts: [
          { type: 'text', value: 'Vue3中，创建响应式对象的两个主要API是' },
          { type: 'blank', index: 0 },
          { type: 'text', value: '和' },
          { type: 'blank', index: 1 },
          { type: 'text', value: '。' },
        ],
      },
    ],
    // 题目答案
    answer: {
      '4-1': ['ref', 'reactive'],
      '4-2': ['ref', 'reactive'],
      '4-3': ['ref', 'reactive'],
    },
    analysis:
      'Vue3中创建响应式数据的两个主要API是ref和reactive。ref通常用于基本类型数据，reactive通常用于对象类型数据，但两者都可以用于任何类型。',
  },

  // 新增结构化填空题示例
  {
    id: '5',
    index: 5,
    type: QuestionType.FILL_BLANK,
    title: '根据课文内容填写正确的词语',
    score: 15,
    blanks: [
      {
        id: '5-1',
        parts: [
          { type: 'text', value: '他们的草屋，稀稀疏疏的，在雨里' },
          { type: 'blank', index: 0 },
          { type: 'text', value: '着。' },
        ],
      },
      {
        id: '5-2',
        parts: [
          { type: 'text', value: '呼吸变得畅快，在' },
          { type: 'blank', index: 0 },
          { type: 'text', value: '着鼻子和' },
          { type: 'blank', index: 1 },
          { type: 'text', value: '唇。' },
        ],
      },
    ],
    answer: {
      '5-1': ['矗立', '舒展', '嘴'],
      '5-2': ['矗立', '舒展', '嘴'],
    },
    analysis: "根据课文内容，第一空应填'矗立'，第二空应填'舒展'，第三空应填'嘴'。",
  },

  // 简答题 - 文本域模板
  {
    id: '6',
    index: 6,
    type: QuestionType.SHORT_ANSWER,
    title: '请详细说明Vue3中setup函数的作用和使用方式。',
    score: 20,
  },

  // 判断题 - 选项模板（index更新为6）
  {
    id: '7',
    index: 7,
    type: QuestionType.TRUE_FALSE,
    title: 'Vue3中的setup函数是可选的，可以不使用。',
    score: 5,
    options: [
      { id: '7-1', content: '正确' },
      { id: '7-2', content: '错误' },
    ],
    answer: '7-1',
    analysis:
      '这个说法是正确的。在Vue3中，你可以选择使用选项式API（Options API）或组合式API（Composition API）。如果使用选项式API，则不需要setup函数。',
  },
  // 阅读理解题（复合题模板）（index更新为8）
  {
    id: '8',
    index: 8,
    type: QuestionType.READING,
    score: 25,
    title: `<div class="reading-material">
      <h3>Vue3的新特性简介</h3>
      <p>Vue3是Vue.js的最新主要版本，发布于2020年9月18日。它带来了许多重要的改进和新特性，使Vue更加强大和灵活。</p>
    </div>`,
    subQuestions: [
      {
        id: '8-1',
        index: 1,
        type: QuestionType.SINGLE,
        title: '根据文章内容，Vue3的响应式系统是基于哪种技术实现的？',
        score: 5,
        options: [
          { id: '8-1-1', content: 'Object.defineProperty' },
          { id: '8-1-2', content: 'Proxy' },
          { id: '8-1-3', content: 'Observer' },
          { id: '8-1-4', content: 'Watcher' },
        ],
        answer: '8-1-2',
        analysis:
          '文章第二段明确提到，Vue3采用了全新的响应式系统，基于ES6的Proxy特性实现，不再使用Object.defineProperty。',
      },
      {
        id: '8-2',
        index: 2,
        type: QuestionType.MULTIPLE,
        title: '根据文章内容，Vue3引入了哪些新特性？（多选）',
        score: 5,
        options: [
          { id: '8-2-1', content: 'Composition API' },
          { id: '8-2-2', content: 'Fragment' },
          { id: '8-2-3', content: 'Virtual DOM' },
          { id: '8-2-4', content: 'Teleport' },
          { id: '8-2-5', content: 'Suspense' },
        ],
        answer: ['8-2-1', '8-2-2', '8-2-4', '8-2-5'],
        analysis:
          '文章提到了Composition API（第三段）以及Fragment、Teleport和Suspense（最后一段）。Virtual DOM在Vue2中就已经存在，不是Vue3新引入的特性。',
      },
      // {
      //   id: "8-3",
      //   index: 3,
      //   type: QuestionType.FILL_BLANK,
      //   title: "Vue3的Composition API是一种全新的________和________方式。",
      //   score: 5,
      //   answer: ["逻辑复用", "代码组织"],
      //   analysis: "文章第三段提到，Vue3引入了Composition API，这是一种全新的逻辑复用和代码组织方式。",
      //   blanks: [
      //     {
      //       id: "8-3-1",
      //       index: 0,
      //       type: "text",
      //       value: "逻辑复用"
      //     }
      //   ]
      // },
      {
        id: '8-4',
        index: 4,
        type: QuestionType.SHORT_ANSWER,
        title: '请根据文章内容，简述Vue3相比Vue2的主要改进。',
        score: 10,
        analysis:
          '这个问题需要总结文章中提到的Vue3的主要改进，包括响应式系统、API设计、性能优化和新特性等方面。',
      },
    ],
    analysis:
      '这篇短文介绍了Vue3的主要新特性和改进，包括基于Proxy的响应式系统、Composition API、性能优化以及Fragment等新特性。理解这些概念对于掌握Vue3的核心优势非常重要。',
  },
];

// 根据作业类型获取题目列表
export function getQuestionsByType(homeworkType: string): FrontQuestion[] {
  switch (homeworkType) {
    case 'online':
      return onlineQuestions as FrontQuestion[];
    // 其他类型可以在这里扩展
    default:
      return onlineQuestions as FrontQuestion[];
  }
}
