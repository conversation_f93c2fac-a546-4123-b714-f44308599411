import type { BatchInfo } from '@/store/homeworkStore';
import { drawingDataDemo } from './drawing';

// 创建模拟订正数据
export const batchesDemo: BatchInfo[] = [
  {
    id: 'first',
    name: '首次',
    userAnswers: {
      '1': {
        questionId: '1',
        answer: '1-1',
      },
    },
    // readonly: true, // 历史订正只读
  },
  {
    id: 'review1',
    name: '订正一',
    questions: [{ questionId: '1' }, { questionId: '2' }, { questionId: '4' }],
    userAnswers: {
      '1': {
        questionId: '1',
        answer: '1-2',
      },
      '2': {
        questionId: '2',
        answer: '2-1',
      },
      '4': {
        questionId: '4',
        answer: {
          '4-1': ['ref', '错误答案'], // 第一个正确，第二个错误
          '4-2': ['ref', 'reactive'], // 全部正确
          '4-3': ['错误答案', 'reactive'], // 第一个错误，第二个正确
        },
      },
    },
    // readonly: true, // 历史订正只读
  },
  {
    id: 'review2',
    name: '订正二',
    userAnswers: {
      '1': {
        questionId: '1',
        answer: '1-1', // 订正二选择了C
      },
      '2': {
        questionId: '2',
        answer: '2-2',
      },
      '3': {
        questionId: '3',
        answer: ['3-1', '3-2'],
      },
      '4': {
        questionId: '4',
        answer: {
          '4-1': ['ref', 'reactive'],
          '4-2': ['ref', 'reactive'],
          '4-3': ['ref', 'reactive'],
        },
      },
      '5': {
        questionId: '5',
        answer: {
          '5-1': ['好'],
          '5-2': ['好', '不好'],
        },
      },
      '6': {
        questionId: '6',
        answer: {
          correct: true,
          drawing: {
            imageUrl: drawingDataDemo.imageUrl,
            data: drawingDataDemo.drawingData,
          },
          files: [
            {
              id: 'file_001_6_1', // 修改为子题标识的唯一ID
              fileUrl: 'https://i.mij.rip/2025/07/14/a69c2384b41417bfaf6591e85dc176a7.jpeg',
              fileName: 'a69c2384b41417bfaf6591e85dc176a7.jpeg',
              fileType: 'jpeg',
              fileSize: 100,
              fileExtension: 'jpeg',
            },
          ],
        },
      },
    },
    // readonly: false, // 最新订正可编辑
  },
];
