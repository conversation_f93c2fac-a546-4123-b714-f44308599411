import { type AnnotationDataItem, AnnotationType } from '../components/AnnotationWriting';

export const annotationDataMock: AnnotationDataItem[] = [
  // 第一张图片及其批注
  {
    imageUrl: 'https://i.mij.rip/2025/07/14/6514dd3a6642c82961000322170b3a89.jpeg',
    annotations: [
      // 示例数据，实际请用后端返回的数据替换
      {
        id: 'anno_001',
        type: AnnotationType.GREAT,
        x: 200,
        y: 240,
        number: 1,
        // 多行批注示例
        lines: [
          { x: 200, y: 240, width: 1100 },
          { x: 120, y: 320, width: 1100 },
        ],
      }, // 佳句赏析 - 波浪线
      {
        id: 'anno_002',
        type: AnnotationType.INSERT,
        x: 280,
        y: 310,
        width: 70,
        height: 70,
      }, // 插入内容 - V形
      {
        id: 'anno_003',
        type: AnnotationType.DELETE,
        x: 240,
        y: 410,
        width: 80,
        height: 80,
      }, // 删除内容 - 特殊符号
      {
        id: 'anno_004',
        type: AnnotationType.REPLACE,
        x: 470,
        y: 450,
        width: 60,
        height: 60,
      }, // 替换内容 - 半圆形
      {
        id: 'anno_005',
        type: AnnotationType.RECOMMEND,
        number: 2,
        x: 260,
        y: 580,
        // 多行批注示例
        lines: [{ x: 260, y: 580, width: 570 }],
      }, // 优化建议 - 直线
    ],
  },
  // 第二张图片及其批注
  {
    imageUrl: 'https://i.mij.rip/2025/07/14/6514dd3a6642c82961000322170b3a89.jpeg', // 示例用第二张相同的图片
    annotations: [
      {
        id: 'anno_101',
        type: AnnotationType.GREAT,
        number: 3,
        x: 150,
        y: 230,
        lines: [{ x: 150, y: 230, width: 900, height: 20 }],
      },
      {
        id: 'anno_102',
        type: AnnotationType.RECOMMEND,
        number: 4,
        x: 300,
        y: 400,
        lines: [{ x: 300, y: 400, width: 600, height: 10 }],
      },
    ],
  },
];
