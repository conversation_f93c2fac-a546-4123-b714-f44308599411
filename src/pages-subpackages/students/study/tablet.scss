/* 平板端样式 - 学习中心页面 */

.device-tablet {
  .student-home {
    width: 100%;
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
  }

  .navbar {
    padding-top: var(--status-bar-height, 44rpx);

    .navbar-content {
      height: 120rpx; // 平板端增加导航栏高度
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 60rpx; // 平板端增加左右边距

      .navbar-left {
        .tenant-name {
          font-size: 28px;
          max-width: 500px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 700;
        }
      }

      .notice-icon {
        width: 32px; // 平板端适中放大图片 (24px * 1.3)
        height: 32px; // 平板端适中放大图片 (24px * 1.3)
      }
    }
  }

  .home_title {
    width: 100%;
    box-sizing: border-box;
    position: relative;
    font-size: 48rpx; // 平板端标题字体，参考图片中"学习中心"的大小
    font-weight: bold;
    padding-left: 60rpx;
    padding-right: 60rpx;

    .title-text {
      position: relative;
      z-index: 1;
    }

    // 标题装饰线按比例放大
    .home-title-decoration {
      transform: scale(1.3);
      transform-origin: left;
      position: absolute;
      bottom: -10rpx;
      height: 32rpx;
      width: 150rpx;
      z-index: 0;
    }
  }

  .home_empty {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-top: 80rpx; // 平板端增加顶部间距
  }

  .cards-box {
    padding-left: 60rpx;
    padding-right: 60rpx;
    margin-top: 60rpx;
    margin-bottom: 30rpx;

    .custom-grid {
      display: flex;
      height: 280px; // 平板端增加卡片高度
      margin-bottom: 60rpx; // 平板端增加底部间距

      .left-section {
        flex: 1;
        height: 100%;
      }

      .right-section {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        margin-left: 20px; // 平板端增加间距

        .right-top-box,
        .right-bottom-box {
          flex: 1;
        }
        .right-bottom-box {
          margin-top: 20px; // 平板端增加间距
        }
      }
    }

    .left-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 24px; // 平板端增加圆角
      background: linear-gradient(303deg, #6f3aff 0%, #a685ff 100%);
      box-shadow: 0 5px 39.3px 0 #d8beff;
      overflow: hidden;
      padding-top: 10px;

      .title-text {
        font-size: 28px;
        font-weight: bold;
      }

      .subtitle-text {
        font-size: 20px;
        font-weight: bold;
        margin-top: 10px;
      }

      .homework-img-box {
        width: 166px;
        height: 144px;
        position: relative;

        .homework-img {
          width: 166px;
          height: 144px;
          position: absolute;
          bottom: -0.8rem;
          right: 0;
        }
      }
    }

    .right-top-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      border-radius: 24px; // 平板端增加圆角
      background: linear-gradient(303deg, #06e4b1 0%, #1ac8a4 74.81%, #5af787 100%);
      box-shadow: 0px 4px 40px 0px rgba(151, 248, 219, 0.62); // 平板端增强阴影
      position: relative;
      overflow: hidden;
      padding: 40rpx 50rpx;

      .right-top-img {
        width: 93px;
        height: 68px;
        position: absolute;
        bottom: 2px;
        right: 20px;
        z-index: -1;
      }

      .title-text {
        font-size: 28px;
        font-weight: bold;
      }

      .subtitle-text {
        font-size: 20px;
        font-weight: bold;
        margin-top: 10px;
      }
    }

    .right-bottom-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      border-radius: 24px; // 平板端增加圆角
      background: linear-gradient(303deg, #38afff 0%, #38b8ff 82.23%, #48d8fd 100%);
      box-shadow: 0px 4px 40px 0px #81e0ff; // 平板端增强阴影
      position: relative;
      overflow: hidden;
      padding: 40rpx 50rpx;

      .right-bottom-img {
        width: 91px;
        height: 86px;
        position: absolute;
        bottom: -2px;
        right: 20px;
        z-index: -1;
      }

      .title-text {
        font-size: 28px;
        font-weight: bold;
      }

      .subtitle-text {
        font-size: 20px;
        font-weight: bold;
        margin-top: 10px;
      }
    }

    .ghost-box-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); // 平板端增加最小宽度
      gap: 40rpx; // 平板端增加间距
      margin: 60rpx 0 40rpx 0; // 平板端调整间距

      .ghost-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 20px; // 平板端增加圆角
        border: 1px solid #cb7dff;
        background: linear-gradient(102deg, #f0e5ff 7.83%, #eee9ff 19.7%, #d9ddff 109.31%);
        box-shadow: 0px 6px 16px 0px rgba(114, 90, 220, 0.12); // 平板端增强阴影
        overflow: hidden;
        padding: 30rpx 50rpx; // 平板端增加内边距
        position: relative;

        .ghost-text-box {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          flex: 1;
        }
        .ghost-img-box {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .ghost-img {
          width: 50px;
          height: 43px;
        }

        .title-text {
          font-size: 20px;
        }

        .subtitle-text {
          font-size: 16px;
          margin-top: 10px;
        }
      }
    }
  }

  /* 简洁流畅动画效果 - 平板端优化 */
  .animated-card {
    animation: smoothAppearTablet 0.6s cubic-bezier(0.4, 0, 0.2, 1) both; // 平板端稍微延长动画时间
    /* 硬件加速优化 */
    will-change: opacity, transform;
    /* 动画完成后移除will-change */
    animation-fill-mode: both;
  }

  /* 动画完成后移除性能优化属性 */
  .animated-card.animation-complete {
    will-change: auto;
  }

  @keyframes smoothAppearTablet {
    0% {
      opacity: 0;
      transform: translateY(30rpx) scale(0.92); // 平板端稍微调整动画参数
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}

/* ===== 不同尺寸平板的字体和图标适配 ===== */
@media screen and (min-width: 568px) and (max-width: 767px) {
  .device-tablet {
    .navbar .navbar-content {
      .tenant-name {
        font-size: 24px !important;
      }
      .notice-icon {
        width: 28px !important;
        height: 28px !important;
      }
    }

    .home_title {
      font-size: 42rpx !important;
    }

    .left-box {
      .title-text {
        font-size: 24px !important;
      }
      .subtitle-text {
        font-size: 18px !important;
      }
      .homework-img {
        width: 140px !important;
        height: 120px !important;
        bottom: 0rem !important;
      }
    }

    .right-top-box,
    .right-bottom-box {
      .title-text {
        font-size: 20px !important;
      }
      .subtitle-text {
        font-size: 16px !important;
      }
    }

    .right-top-img {
      width: 72px !important;
      height: 50px !important;
      bottom: 4px !important;
      right: 20px !important;
    }

    .right-bottom-img {
      width: 70px !important;
      height: 64px !important;
      bottom: 0px !important;
      right: 20px !important;
    }

    .ghost-box {
      .title-text {
        font-size: 18px !important;
      }
      .subtitle-text {
        font-size: 14px !important;
      }
      .ghost-img {
        width: 42px !important;
        height: 36px !important;
      }
    }
  }
}

/* 小型平板 (768px - 900px) */
@media screen and (min-width: 768px) and (max-width: 900px) {
  .device-tablet {
    .navbar .navbar-content {
      .tenant-name {
        font-size: 24px !important;
      }
      .notice-icon {
        width: 28px !important;
        height: 28px !important;
      }
    }

    .home_title {
      font-size: 42rpx !important;
    }

    .left-box {
      .title-text {
        font-size: 24px !important;
      }
      .subtitle-text {
        font-size: 18px !important;
      }
      .homework-img {
        width: 140px !important;
        height: 120px !important;
      }
    }

    .right-top-box,
    .right-bottom-box {
      .title-text {
        font-size: 24px !important;
      }
      .subtitle-text {
        font-size: 18px !important;
      }
    }

    .right-top-img {
      width: 80px !important;
      height: 58px !important;
    }

    .right-bottom-img {
      width: 78px !important;
      height: 74px !important;
    }

    .ghost-box {
      .title-text {
        font-size: 18px !important;
      }
      .subtitle-text {
        font-size: 14px !important;
      }
      .ghost-img {
        width: 40px !important;
        height: 34px !important;
      }
    }
  }
}

/* 中型平板 (901px - 1024px) */
@media screen and (min-width: 901px) and (max-width: 1024px) {
  .device-tablet {
    .navbar .navbar-content {
      .tenant-name {
        font-size: 26px !important;
      }
      .notice-icon {
        width: 30px !important;
        height: 30px !important;
      }
    }

    .home_title {
      font-size: 46rpx !important;
    }

    .left-box {
      .title-text {
        font-size: 26px !important;
      }
      .subtitle-text {
        font-size: 19px !important;
      }
      .homework-img {
        width: 155px !important;
        height: 135px !important;
      }
    }

    .right-top-box,
    .right-bottom-box {
      .title-text {
        font-size: 26px !important;
      }
      .subtitle-text {
        font-size: 19px !important;
      }
    }

    .right-top-img {
      width: 87px !important;
      height: 63px !important;
    }

    .right-bottom-img {
      width: 85px !important;
      height: 80px !important;
    }

    .ghost-box {
      .title-text {
        font-size: 19px !important;
      }
      .subtitle-text {
        font-size: 15px !important;
      }
      .ghost-img {
        width: 46px !important;
        height: 40px !important;
      }
    }
  }
}

/* 大型平板 (1025px - 1366px) - 默认样式 */
@media screen and (min-width: 1025px) and (max-width: 1366px) {
  .device-tablet {
    .navbar .navbar-content {
      .tenant-name {
        font-size: 26px !important;
      }
      .notice-icon {
        width: 30px !important;
        height: 30px !important;
      }
    }

    .home_title {
      font-size: 46rpx !important;
    }

    .left-box {
      .title-text {
        font-size: 26px !important;
      }
      .subtitle-text {
        font-size: 19px !important;
      }
      .homework-img {
        width: 155px !important;
        height: 135px !important;
      }
    }

    .right-top-box,
    .right-bottom-box {
      .title-text {
        font-size: 26px !important;
      }
      .subtitle-text {
        font-size: 19px !important;
      }
    }

    .right-top-img {
      width: 87px !important;
      height: 63px !important;
    }

    .right-bottom-img {
      width: 85px !important;
      height: 80px !important;
    }

    .ghost-box {
      .title-text {
        font-size: 19px !important;
      }
      .subtitle-text {
        font-size: 15px !important;
      }
      .ghost-img {
        width: 43px !important;
        height: 37px !important;
      }
    }
  }
}

/* 超大平板 (1367px+) */
@media screen and (min-width: 1367px) {
  .device-tablet {
    .navbar .navbar-content {
      .tenant-name {
        font-size: 32px !important;
      }
      .notice-icon {
        width: 36px !important;
        height: 36px !important;
      }
    }

    .home_title {
      font-size: 52rpx !important;
    }

    .left-box {
      .title-text {
        font-size: 32px !important;
      }
      .subtitle-text {
        font-size: 22px !important;
      }
      .homework-img {
        width: 190px !important;
        height: 165px !important;
      }
    }

    .right-top-box,
    .right-bottom-box {
      .title-text {
        font-size: 32px !important;
      }
      .subtitle-text {
        font-size: 22px !important;
      }
    }

    .right-top-img {
      width: 105px !important;
      height: 77px !important;
    }

    .right-bottom-img {
      width: 103px !important;
      height: 97px !important;
    }

    .ghost-box {
      .title-text {
        font-size: 22px !important;
      }
      .subtitle-text {
        font-size: 18px !important;
      }
      .ghost-img {
        width: 52px !important;
        height: 44px !important;
      }
    }
  }
}
