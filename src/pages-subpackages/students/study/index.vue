<template>
  <view class="student-home" :class="deviceClass">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-content">
        <view class="navbar-left">
          <LkText :lines="1" class="tenant-name" ellipsis bold size="large">{{
            currentUserInfo?.tenantName || '请先登录'
          }}</LkText>
        </view>
        <view @tap="handleNoticeClick">
          <template v-if="unreadNotificationCount > 0">
            <uni-badge
              :text="unreadNotificationCount > 99 ? '99+' : unreadNotificationCount.toString()"
              :inverted="false"
              type="error"
              size="small"
              :absolute="true"
              :offset="[2, 2]"
            >
              <LkSvg
                width="24px"
                height="24px"
                customClass="notice-icon"
                src="/static/study/notice.svg"
              />
            </uni-badge>
          </template>
          <template v-else>
            <LkSvg
              width="24px"
              height="24px"
              customClass="notice-icon"
              src="/static/study/notice.svg"
            />
          </template>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <view class="home_title">
        <view class="title-text">学习中心</view>
        <image
          class="home-title-decoration"
          src="https://huayun-ai-obs-public.huayuntiantu.com/33fc7ff8-3dc3-48fc-a832-dc844acc701b.png"
        />
      </view>
      <!-- 学习功能卡片区域 - 数据驱动遍历 -->
      <view class="cards-box">
        <view class="custom-grid">
          <!-- 左侧主要功能卡片 - 索引0 -->
          <view class="left-section" v-if="studyCards[0]">
            <view
              class="left-box animated-card"
              :style="{
                animationDelay: `${getCardStyle(0).animationDelay}s`,
                background: getCardStyle(0).gradient,
                boxShadow: getCardStyle(0).shadow,
              }"
              @tap="handleCardClick(studyCards[0])"
            >
              <LkText
                type="primary"
                bold
                size="large"
                class="title-text up-line-1"
                :customStyle="`color: ${getCardStyle(0).textColor};`"
              >
                {{ studyCards[0].title }}
              </LkText>
              <LkText
                type="primary"
                class="subtitle-text up-line-1"
                :customStyle="`color: ${getCardStyle(0).textColor};font-weight: 200;`"
              >
                {{ studyCards[0].subtitle }}
              </LkText>
              <view class="homework-img-box">
                <image :src="studyCards[0].icon" class="homework-img" />
              </view>
            </view>
          </view>

          <!-- 右侧功能卡片区域 -->
          <view class="right-section">
            <!-- 右上卡片 - 索引1 -->
            <view
              v-if="studyCards[1]"
              class="right-top-box animated-card"
              :style="{
                animationDelay: `${getCardStyle(1).animationDelay}s`,
                background: getCardStyle(1).gradient,
                boxShadow: getCardStyle(1).shadow,
              }"
              @tap="handleCardClick(studyCards[1])"
            >
              <LkText
                type="primary"
                bold
                size="large"
                class="title-text up-line-1"
                :customStyle="`color: ${getCardStyle(1).textColor};`"
              >
                {{ studyCards[1].title }}
              </LkText>
              <LkText
                type="primary"
                class="subtitle-text up-line-1"
                size="small"
                :customStyle="`color: ${getCardStyle(1).textColor};font-weight: 200;`"
              >
                {{ studyCards[1].subtitle }}
              </LkText>
              <image :src="studyCards[1].icon" class="right-top-img"></image>
            </view>

            <!-- 右下卡片 - 索引2 -->
            <view
              v-if="studyCards[2]"
              class="right-bottom-box animated-card"
              :style="{
                animationDelay: `${getCardStyle(2).animationDelay}s`,
                background: getCardStyle(2).gradient,
                boxShadow: getCardStyle(2).shadow,
              }"
              @tap="handleCardClick(studyCards[2])"
            >
              <LkText
                type="primary"
                bold
                size="large"
                class="title-text up-line-1"
                :customStyle="`color: ${getCardStyle(2).textColor};`"
              >
                {{ studyCards[2].title }}
              </LkText>
              <LkText
                type="primary"
                class="subtitle-text up-line-1"
                size="small"
                :customStyle="`color: ${getCardStyle(2).textColor};font-weight: 200;`"
              >
                {{ studyCards[2].subtitle }}
              </LkText>
              <image :src="studyCards[2].icon" class="right-bottom-img"></image>
            </view>
          </view>
        </view>

        <!-- 底部功能卡片容器 - 索引3和4 -->
        <view class="ghost-box-container">
          <view
            v-for="(card, index) in studyCards.slice(3)"
            :key="index"
            class="ghost-box animated-card"
            :style="{
              animationDelay: `${getCardStyle(index + 3).animationDelay}s`,
              background: getCardStyle(index + 3).gradient,
              boxShadow: getCardStyle(index + 3).shadow,
              border: getCardStyle(index + 3).border,
            }"
            @tap="handleCardClick(card)"
          >
            <view class="ghost-text-box">
              <LkText
                type="primary"
                bold
                size="large"
                class="title-text up-line-1"
                :customStyle="`color: ${getCardStyle(index + 3).textColor};`"
              >
                {{ card.title }}
              </LkText>
              <LkText
                type="primary"
                class="subtitle-text up-line-1"
                size="small"
                :customStyle="`color: ${getCardStyle(index + 3).textColor};font-weight: 200;`"
              >
                {{ card.subtitle }}
              </LkText>
            </view>
            <view class="ghost-img-box">
              <image :src="card.icon" class="ghost-img"></image>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 全局提示组件 -->
    <LkToast ref="toastRef" />

    <!-- 滑块解锁弹窗组件 -->
    <UnlockSliderDialog ref="unlockDialogRef" />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { onHide, onShow } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/userStore';
import useNetworkRefresh from '@/hooks/useNetworkRefresh';
import useDeviceDetection from '@/hooks/useDeviceDetection';
import UnlockSliderDialog from './UnlockSliderDialog.vue';
import { getUnreadNotificationCount } from '@/api/notification';

const userStore = useUserStore();

// 设备检测
const { deviceType, isMobile, isTablet } = useDeviceDetection();

// 设备样式类名
const deviceClass = computed(() => `device-${deviceType.value}`);
// const deviceClass = computed(() => `device-tablet`);

const currentUserInfo = computed(() => userStore.userInfo);

// 组件引用
const toastRef = ref();
const unlockDialogRef = ref();

// 未读通知数量
const unreadNotificationCount = ref(0);

// 定时器
let notificationTimer: number | null = null;

// 学习功能卡片数据 - 简洁版本，样式通过索引确定
const studyCards = ref([
  {
    title: '我的作业',
    subtitle: '全科作业，清晰分类',
    icon: '/static/study/homework.png',
    action: 'homework',
  },
  {
    title: '学习资源',
    subtitle: '精品资料，助力学习',
    icon: '/static/study/learning_resource.png',
    action: 'resources',
  },
  {
    title: '学情分析',
    subtitle: '精准分析，高效提升',
    icon: '/static/study/learn_condition.png',
    action: 'analysis',
  },
  {
    title: '错题本',
    subtitle: '变式训练,巩固提升',
    icon: '/static/study/error_book.png',
    action: 'error-book',
  },
  {
    title: 'AI智能答疑',
    subtitle: '智能辅导,随学随问',
    icon: '/static/study/answer_questions.png',
    action: 'ai-chat',
  },
  // {
  //   title: '同步练习',
  //   subtitle: '同步练习,巩固提升',
  //   icon: '/static/study/answer_questions.png',
  //   action: 'sync-practice',
  // },
  // {
  //   title: 'AI自习室',
  //   subtitle: '千人千面,因材施教',
  //   icon: '/static/study/answer_questions.png',
  //   action: 'ai-study-room',
  // },
]);

// 前三个卡片的固定样式配置
const fixedCardStyles = [
  // 索引0: 左侧主卡片
  {
    gradient: 'linear-gradient(303deg, #6f3aff 0%, #a685ff 100%)',
    shadow: '0px 2px 19.4px 0px #d8beff',
    textColor: '#fff',
    animationDelay: 0,
    border: undefined,
  },
  // 索引1: 右上卡片
  {
    gradient: 'linear-gradient(303deg, #06e4b1 0%, #1ac8a4 74.81%, #5af787 100%)',
    shadow: '0px 3px 35.1px 0px rgba(151, 248, 219, 0.62)',
    textColor: '#fff',
    animationDelay: 0.08,
    border: undefined,
  },
  // 索引2: 右下卡片
  {
    gradient: 'linear-gradient(303deg, #38afff 0%, #38b8ff 82.23%, #48d8fd 100%)',
    shadow: '0px 3px 35.1px 0px #81e0ff',
    textColor: '#fff',
    animationDelay: 0.16,
    border: undefined,
  },
];

// 索引3及以后的默认样式模板
const defaultCardStyle = {
  gradient: 'linear-gradient(102deg, #f0e5ff 7.83%, #eee9ff 19.7%, #d9ddff 109.31%)',
  shadow: '0px 4px 11.3px 0px rgba(114, 90, 220, 0.07)',
  border: '1px solid #cb7dff',
  textColor: '#2E1674',
};

// 获取卡片样式的辅助函数
const getCardStyle = (index: number) => {
  // 前三个卡片使用固定样式
  if (index < 3) {
    return fixedCardStyles[index];
  }

  // 索引3及以后使用默认样式，animationDelay自增
  return {
    ...defaultCardStyle,
    animationDelay: 0.24 + (index - 3) * 0.08,
  };
};

const handleCardClick = (card: any) => {
  console.log('点击功能卡片:', card);

  if (card.action === 'homework') {
    uni.navigateTo({
      url: '/pages-subpackages/students/homework/index',
    });
    return;
  }

  // 处理错题本跳转
  if (card.action === 'error-book') {
    uni.navigateTo({
      url: '/pages-subpackages/students/error-book/index',
    });
    return;
  }

  if (card.action === 'analysis') {
    uni.navigateTo({
      url: '/pages-subpackages/students/learn-condition/index',
    });
    return;
  }

  if (card.action === 'resources') {
    uni.navigateTo({
      url: '/pages-subpackages/students/learning-resource/index',
    });
    return;
  }

  if (card.action === 'ai-chat') {
    uni.navigateTo({
      url: '/pages-subpackages/students/chat/index?chatType=25',
    });
    return;
  }

  if (card.action === 'sync-practice') {
    uni.navigateTo({
      url: '/pages-subpackages/students/sync-practice/index',
    });
    return;
  }

  if (card.action === 'ai-study-room') {
    // 显示滑块解锁弹窗
    unlockDialogRef.value?.show();
    return;
  }

  // 其他功能暂时显示开发中提示
  toastRef.value?.show({
    message: `${card.title}功能开发中...`,
    type: 'info',
  });
};

const handleNoticeClick = () => {
  uni.navigateTo({
    url: '/pages-subpackages/students/notice/index',
  });
};

// 获取未读通知数量
const fetchUnreadNotificationCount = async () => {
  try {
    const response = await getUnreadNotificationCount();
    unreadNotificationCount.value = response || 0;
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    unreadNotificationCount.value = 0;
  }
};

// 启动定时器
const startNotificationTimer = () => {
  // 先清除可能存在的定时器
  stopNotificationTimer();

  // 设置每5秒执行一次
  notificationTimer = setInterval(() => {
    fetchUnreadNotificationCount();
  }, 5000);
};

// 停止定时器
const stopNotificationTimer = () => {
  if (notificationTimer) {
    clearInterval(notificationTimer);
    notificationTimer = null;
  }
};

// 页面卸载时清理定时器
onUnmounted(() => {
  stopNotificationTimer();
});

// 页面隐藏时停止定时器
onHide(() => {
  stopNotificationTimer();
});

// 页面显示时重新启动定时器
onShow(() => {
  fetchUnreadNotificationCount();
  startNotificationTimer();
});

// 监听网络状态变化，网络恢复时显示提示，断开时显示断网提示
useNetworkRefresh({
  onReconnect: () => {
    // 网络恢复时显示提示
    toastRef.value?.show({
      message: '网络已恢复',
      type: 'success',
    });
  },
  onDisconnect: () => {
    // 网络断开时显示提示
    toastRef.value?.show({
      message: '网络已断开',
      type: 'error',
    });
  },
  throttleDuration: 3000,
});
</script>

<style scoped lang="scss">
// 引入设备特定的样式文件
@import './mobile.scss';
@import './tablet.scss';
</style>
