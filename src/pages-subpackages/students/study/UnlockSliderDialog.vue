<template>
  <view>
    <!-- 滑块解锁弹窗 -->
    <uni-popup ref="unlockPopupRef" type="center" :mask-click="false">
      <view class="unlock-dialog-container">
        <view class="unlock-dialog">
          <!-- 关闭按钮 -->
          <view class="close-btn" @tap="handleClose">
            <text class="close-icon">×</text>
          </view>

          <!-- 标题 -->
          <view class="dialog-title">解锁使用</view>

          <!-- 滑块容器 -->
          <view class="unlock-content">
            <view class="slider-container" :class="{ unlocked: isUnlocked }">
              <view class="slider-track" ref="sliderTrackRef">
                <!-- 滑动进度背景 -->
                <view
                  class="slider-progress"
                  :style="{
                    width: `${sliderPosition + 45}px`,
                  }"
                ></view>

                <view class="slider-text" :class="{ hidden: isUnlocked }">
                  {{ isUnlocked ? '' : '滑动解锁' }}
                </view>
                <view class="slider-success-text" :class="{ visible: isUnlocked }"> 解锁成功 </view>
                <view
                  class="slider-button"
                  :class="{ unlocked: isUnlocked }"
                  :style="{ transform: `translateX(${sliderPosition}px)` }"
                  @touchstart="handleTouchStart"
                  @touchmove="handleTouchMove"
                  @touchend="handleTouchEnd"
                >
                  <view class="slider-icon">
                    <text class="icon-arrow">››</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 成功提示弹窗 -->
    <MyDialog
      v-model:visible="showSuccessDialog"
      type="info"
      title="提示"
      content="请联系学校信息化部管理老师开通使用"
      confirm-text="知道了"
      @confirm="handleSuccessConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, nextTick, getCurrentInstance } from 'vue';
import { MyDialog } from '@/components/MyDialog';

// 获取当前组件实例
const instance = getCurrentInstance();

// 组件引用
const unlockPopupRef = ref();
const sliderTrackRef = ref();

// 弹窗显示状态
const showSuccessDialog = ref(false);

// 滑块状态
const isUnlocked = ref(false);
const sliderPosition = ref(0);
const isDragging = ref(false);
const startX = ref(0);
const sliderWidth = ref(0);
const maxSlideDistance = ref(0);
const isUnlocking = ref(false);

// 显示解锁弹窗
const show = () => {
  unlockPopupRef.value?.open();
  resetSlider();
};

// 重置滑块状态
const resetSlider = () => {
  isUnlocked.value = false;
  sliderPosition.value = 0;
  isDragging.value = false;
  isUnlocking.value = false;

  // 获取滑块容器宽度
  nextTick(() => {
    setTimeout(() => {
      uni
        .createSelectorQuery()
        .in(instance)
        .select('.slider-track')
        .boundingClientRect((rect: any) => {
          if (rect) {
            sliderWidth.value = rect.width;
            // 计算最大滑动距离：轨道宽度 - 按钮宽度 - 左右边距
            // 按钮宽度80rpx，左边距10rpx，右边距10rpx
            // 在H5中，1rpx ≈ 0.5px，所以80rpx ≈ 40px，10rpx ≈ 5px
            // 滑块右边缘贴着轨道右边缘时的最大滑动距离
            // 滑块宽度: 80rpx(40px)，滑块初始left: 10rpx(5px)
            // 滑块右边缘应该到达的位置: 轨道宽度 - 右内边距(5px)
            // 滑块left的最大位置: (轨道宽度 - 5px) - 滑块宽度(40px) = 轨道宽度 - 45px
            // 最大滑动距离: 最大left位置 - 初始left位置(5px) = (轨道宽度 - 45) - 5 = 轨道宽度 - 50
            maxSlideDistance.value = rect.width - 50;
          }
        })
        .exec();
    }, 100); // 延迟100ms确保DOM渲染完成
  });
};

// 触摸开始
const handleTouchStart = (e: any) => {
  if (isUnlocked.value) return;

  isDragging.value = true;
  startX.value = e.touches[0].clientX;

  // 记录当前滑块位置作为起始位置
  const currentPosition = sliderPosition.value;
  startX.value = e.touches[0].clientX - currentPosition;
};

// 触摸移动
const handleTouchMove = (e: any) => {
  if (!isDragging.value || isUnlocked.value) return;

  e.preventDefault(); // 防止页面滚动

  const currentX = e.touches[0].clientX;
  const deltaX = currentX - startX.value;

  // 限制滑动范围，完全跟手
  let newPosition = Math.max(0, Math.min(deltaX, maxSlideDistance.value));
  sliderPosition.value = newPosition;

  // 当滑块滑到最右端时解锁（允许小误差，避免精度问题）
  if (newPosition >= maxSlideDistance.value * 0.99 && !isUnlocking.value) {
    handleUnlock();
  }
};

// 触摸结束
const handleTouchEnd = () => {
  if (!isDragging.value || isUnlocked.value) return;

  isDragging.value = false;

  // 如果没有滑动到底部，回弹到起始位置
  if (!isUnlocked.value && sliderPosition.value < maxSlideDistance.value) {
    // 添加动画效果的回弹
    const startPos = sliderPosition.value;
    const duration = 300; // 300ms动画
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数
      const easeOut = 1 - Math.pow(1 - progress, 3);
      sliderPosition.value = startPos * (1 - easeOut);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }
};

// 解锁成功
const handleUnlock = () => {
  if (isUnlocking.value) return; // 防止重复触发

  isUnlocking.value = true;
  isUnlocked.value = true;
  sliderPosition.value = maxSlideDistance.value;

  // 延迟显示成功提示
  setTimeout(() => {
    unlockPopupRef.value?.close();
    showSuccessDialog.value = true;
  }, 500);
};

// 处理关闭按钮
const handleClose = () => {
  unlockPopupRef.value?.close();
  resetSlider();
};

// 处理成功提示确认
const handleSuccessConfirm = () => {
  showSuccessDialog.value = false;
  resetSlider();
};

// 对外暴露方法
defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.unlock-dialog-container {
  width: 90vw;
  max-width: 600rpx;
}

.unlock-dialog {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;

  .close-icon {
    font-size: 40rpx;
    color: #999;
    line-height: 1;
  }
}

.dialog-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 60rpx;
}

.unlock-content {
  padding: 0;
}

.slider-container {
  width: 100%;
  padding: 0;

  &.unlocked {
    .slider-track {
      background: linear-gradient(90deg, #6f3aff 0%, #a685ff 100%);
      border-color: #6f3aff;
    }
  }
}

.slider-track {
  position: relative;
  width: 100%;
  height: 100rpx;
  background: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
  margin: 40rpx 0;
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.slider-progress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, rgba(111, 58, 255, 0.2) 0%, rgba(166, 133, 255, 0.2) 100%);
  border-radius: 50rpx;
  // 移除transition，让背景完全跟手
  z-index: 1;
}

.slider-text {
  position: absolute;
  font-size: 28rpx;
  color: #999;
  transition: opacity 0.3s ease;
  z-index: 2;

  &.hidden {
    opacity: 0;
  }
}

.slider-success-text {
  position: absolute;
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;

  &.visible {
    opacity: 1;
  }
}

.slider-button {
  position: absolute;
  left: 10rpx;
  width: 80rpx;
  height: 80rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 3;
  cursor: pointer;
  // 移除transition，让滑块完全跟手

  &.unlocked {
    background: #fff;
    box-shadow: 0 4rpx 12rpx rgba(111, 58, 255, 0.3);

    .slider-icon {
      .icon-arrow {
        color: #6f3aff;
      }
    }
  }
}

.slider-icon {
  .icon-arrow {
    font-size: 28rpx;
    color: #999;
    font-weight: bold;
    transition: color 0.3s ease;
  }
}
</style>
