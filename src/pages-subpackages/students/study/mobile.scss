/* 手机端样式 - 学习中心页面 */

.device-mobile {
  .student-home {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .navbar {
    padding-top: var(--status-bar-height, 44rpx);

    .navbar-content {
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32rpx;

      .navbar-left {
        .tenant-name {
          font-size: 40rpx;
          max-width: 272px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 700;
        }
      }
    }
  }

  .home_title {
    width: 100%;
    box-sizing: border-box;
    position: relative;
    font-size: 36rpx;
    font-weight: bold;
    padding-left: 32rpx;
    padding-right: 32rpx;

    .title-text {
      position: relative;
      z-index: 1;
    }

    .home-title-decoration {
      position: absolute;
      bottom: -10rpx;
      height: 32rpx;
      width: 150rpx;
      z-index: 0;
    }
  }

  .home_empty {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-top: 50rpx;
  }

  .cards-box {
    padding-left: 32rpx;
    padding-right: 32rpx;
    margin-top: 40rpx;
    margin-bottom: 15rpx;

    .custom-grid {
      display: flex;
      height: 189px;
      margin-bottom: 40rpx;

      .left-section {
        flex: 1;
        height: 100%;
      }

      .right-section {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        margin-left: 12px;

        .right-top-box,
        .right-bottom-box {
          flex: 1;
        }
        .right-bottom-box {
          margin-top: 12px;
        }
      }
    }

    .left-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 18px;
      background: linear-gradient(303deg, #6f3aff 0%, #a685ff 100%);
      box-shadow: 0 2px 19.4px 0 #d8beff;
      overflow: hidden;

      .title-text {
        margin-top: 17px;
        font-size: 19px;
      }

      .subtitle-text {
        margin-top: 10px;
        font-size: 14px;
      }

      .homework-img-box {
        margin-top: 8px;
        position: relative;
        width: 115px;
        height: 99px;
        .homework-img {
          position: absolute;
          width: 115px;
          height: 99px;
          bottom: -8px;
        }
      }
    }

    .right-top-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      border-radius: 18px;
      background: linear-gradient(303deg, #06e4b1 0%, #1ac8a4 74.81%, #5af787 100%);
      box-shadow: 0px 3px 35.1px 0px rgba(151, 248, 219, 0.62);
      position: relative;
      overflow: hidden;
      padding: 20rpx;

      .title-text {
        font-size: 18px;
      }

      .subtitle-text {
        margin-top: 8px;
        font-size: 12px;
      }

      .right-top-img {
        width: 50px;
        height: 40px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }

    .right-bottom-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      border-radius: 18px;
      background: linear-gradient(303deg, #38afff 0%, #38b8ff 82.23%, #48d8fd 100%);
      box-shadow: 0px 3px 35.1px 0px #81e0ff;
      position: relative;
      overflow: hidden;
      padding: 20rpx;

      .title-text {
        font-size: 18px;
      }

      .subtitle-text {
        margin-top: 8px;
        font-size: 12px;
      }

      .right-bottom-img {
        width: 50px;
        height: 50px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }

    .ghost-box-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20rpx;
      margin: 40rpx 0 20rpx 0;

      .ghost-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 16px;
        border: 1px solid #cb7dff;
        background: linear-gradient(102deg, #f0e5ff 7.83%, #eee9ff 19.7%, #d9ddff 109.31%);
        box-shadow: 0 4px 11.3px 0 rgba(114, 90, 220, 0.07);
        overflow: hidden;
        padding: 30rpx 20rpx 30rpx 30rpx;
        position: relative;

        .ghost-text-box {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          flex: 1;

          .title-text {
            font-size: 17px;
          }

          .subtitle-text {
            font-size: 12px;
            margin-top: 4px;
          }
        }

        .ghost-img-box {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .ghost-img {
          width: 38px;
          height: 37px;
        }
      }
    }
  }

  /* 简洁流畅动画效果 */
  .animated-card {
    animation: smoothAppear 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
    /* 硬件加速优化 */
    will-change: opacity, transform;
    /* 动画完成后移除will-change */
    animation-fill-mode: both;
  }

  /* 动画完成后移除性能优化属性 */
  .animated-card.animation-complete {
    will-change: auto;
  }

  @keyframes smoothAppear {
    0% {
      opacity: 0;
      transform: translateY(20rpx) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}
