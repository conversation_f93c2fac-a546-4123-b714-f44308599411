<template>
  <MyDialogProvider>
    <MyContainer>
      <view class="detail-container">
        <up-navbar bgColor="transparent" placeholder>
          <template #left>
            <up-icon name="arrow-left" size="24px" @tap="handleBack" />
          </template>
        </up-navbar>
        <view class="tab-content">
          <!-- 加载状态 -->
          <MyLoading v-if="isLoading" fullscreen type="spinner" />
          <template v-else>
            <Manual :controller="questionSubManualStore" />
          </template>
        </view>
      </view>
    </MyContainer>
  </MyDialogProvider>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Manual from './components/QuestionContainer/Manual.vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import { useWrongStore } from '@/store/wrongStore';
import { useQuestionSubManualStore } from '@/store/questionSubManualStore';
import { MyContainer } from '@/pages-subpackages/students/homework/components';
import { MyLoading } from '@/components/MyLoading';
import { MyDialogProvider } from '@/components/MyDialog';

const wrongStore = useWrongStore();
const questionSubManualStore = useQuestionSubManualStore();
const queryOptions = ref<any>({});

// 用于存储从上一页面传递过来的答案数据
const injectedAnswerData = ref<any>(null);

const handleBack = () => {
  uni.navigateBack();
};

// 加载状态
const isLoading = ref(false);

// 获取作业详情
const fetchHomeworkDetail = async (options: any) => {
  isLoading.value = true;
  try {
    if (options.state) {
      queryOptions.value.state = options.state;
    }
    if (options.questionId) {
      queryOptions.value.questionId = options.questionId;
    }

    // 优先使用注入的答案数据，如果没有则从 wrongStore 获取
    let questions, userAnswer;

    if (
      injectedAnswerData.value &&
      injectedAnswerData.value.question &&
      injectedAnswerData.value.userAnswer
    ) {
      questions = injectedAnswerData.value.question;
      userAnswer = injectedAnswerData.value.userAnswer;
    }

    questionSubManualStore.initHomework({
      homeworkId: wrongStore.homeworkId,
      answerType: 1,
      questions: [{ ...questions, index: 1 }],
    });

    // 设置用户答案
    if (userAnswer && userAnswer.answer) {
      questionSubManualStore.setUserAnswer(queryOptions.value.questionId, userAnswer.answer);
    }
  } catch (error) {
    console.error('获取作业详情失败:', error);
    uni.showToast({
      title: '获取作业详情失败',
      icon: 'error',
    });
  } finally {
    isLoading.value = false;
  }
};

onLoad(async options => {
  // 接收页面间传递的答案数据
  try {
    const eventChannel = (
      getCurrentPages()[getCurrentPages().length - 1] as any
    ).getOpenerEventChannel?.();
    if (eventChannel) {
      eventChannel.on('receiveAnswerData', async function (data: any) {
        injectedAnswerData.value = data;
        await fetchHomeworkDetail(options);
      });
    }
  } catch (error) {
    console.log('获取 eventChannel 失败:', error);
  }
});

onUnload(async () => {
  // 清空store
  questionSubManualStore.clearAll();
});
</script>
<style lang="scss" scoped>
@import '@/pages-subpackages/students/homework/styles/index.scss';

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
}

.navbar-tab-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 380rpx;
  height: 58rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10rpx;
  padding: 4rpx;
}

.navbar-tab-item {
  flex: 1;
  height: 100%;
  border-radius: 10rpx;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    background: #ffffff;
    color: #7d4dff;
  }
}

.tab-content {
  flex: 1;
  overflow: hidden;
  // border-radius: 20px 20px 0px 0px;
  background: #fff;
  position: relative;
  z-index: 2;
  padding-bottom: 15px;
}

// AI助手
.ai-assistant {
  position: fixed;
  bottom: 409rpx;
  right: 30rpx;

  z-index: 99;

  display: flex;
  align-items: center;
  justify-content: center;

  .ai-assistant-image {
    width: 120rpx;
    height: 120rpx;
  }
}
</style>
