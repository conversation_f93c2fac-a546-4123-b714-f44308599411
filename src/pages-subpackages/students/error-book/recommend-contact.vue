<template>
  <MyDialogProvider>
    <MyContainer>
      <view class="error-detail">
        <up-navbar bgColor="transparent" placeholder>
          <template #left>
            <up-icon name="arrow-left" size="24px" @tap="handleBack" color="#fff" />
          </template>
          <template #center>
            <LkText :lines="1" ellipsis size="large" customStyle="color: #fff;">推荐练习</LkText>
          </template>
        </up-navbar>
        <view class="error-detail-bg" />
        <view class="error-detail-tabs">
          <up-subsection
            :list="tabs"
            :current="currentTab"
            @change="handleTabChange"
            activeColor="#7D4DFF"
            inactiveColor="#fff"
            bgColor="rgba(0, 0, 0, 0.12)"
            style="width: 300rpx"
          />
        </view>
        <view class="error-detail-content">
          <MyLoading v-if="isLoading" fullscreen type="spinner" />
          <template v-else>
            <Recommend v-show="currentTab === 0" :controller="recommendStore" />
            <ResourceList v-show="currentTab === 1" :controller="recommendStore" />
          </template>
        </view>
      </view>
    </MyContainer>
  </MyDialogProvider>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import { MyContainer } from '@/pages-subpackages/students/homework/components';
import { MyLoading } from '@/components/MyLoading';
import { MyDialogProvider, showDialogConfirm } from '@/components/MyDialog';
import { getQuestionRecommend } from '@/api/students/errorBook';

import { useRecommendStore } from '@/store/recommendStore';

import ResourceList from './components/ResourceList/index.vue';
import Recommend from './components/QuestionContainer/Recommend.vue';
import { convertBackendToFrontQuestion } from '@/pages-subpackages/students/homework/utils/convert';

const recommendStore = useRecommendStore();

const isLoading = ref(false);
const handleBack = () => {
  uni.navigateBack();
};

const tabs = [
  {
    name: '答题',
    value: 0,
  },
  {
    name: '辅助资料',
    value: 1,
  },
];

// 当前选中的 tab 索引
const currentTab = ref(0);

const queryOptions = ref<any>({});
// 保存来源页面信息，用于返回时刷新
const sourcePageInfo = ref<any>({});

const handleTabChange = (index: number) => {
  currentTab.value = index;
};

// 获取作业详情
const fetchHomeworkDetail = async (options: any) => {
  isLoading.value = true;
  try {
    if (options.questionId) {
      queryOptions.value.questionId = Number(options.questionId);
    }
    if (options.errorId) {
      queryOptions.value.errorId = Number(options.errorId);
    }
    if (options.homeworkId) {
      queryOptions.value.homeworkId = Number(options.homeworkId);
    }

    const data = await getQuestionRecommend({
      questionId: queryOptions.value.questionId,
      errorId: queryOptions.value.errorId,
      homeworkId: queryOptions.value.homeworkId,
    });

    // 提取所有推荐的题目
    const res = data?.questionRecommendList?.reduce((acc: any[], item: any) => {
      return acc.concat(item.recommendQuestions);
    }, [] as any[]);

    const dataBackup = {
      status: 0, // 已完成
      enableAnswerVisible: 1, // 答案对学生可见
      homeworkId: res?.[0]?.homeworkId || '',
      questionList: res || [],
      materialFileList: data?.homeworkMaterialFiles || [],
    };
    recommendStore.homeworkId = dataBackup.homeworkId;

    // 设置当前作业后端数据
    recommendStore.setBackendData(dataBackup);
    // 获取问题数据
    let questionList = [];

    // 获取问题数据
    questionList = dataBackup?.questionList || [];
    // 将后端问题数据转换为前端问题数据
    const questions = convertBackendToFrontQuestion(questionList);

    recommendStore.initHomework({
      homeworkId: recommendStore.homeworkId,
      answerType: res?.[0]?.taskType || 1,
      questions,
    });
  } catch (error) {
    console.error('获取作业详情失败:', error);
    uni.showToast({
      title: '获取作业详情失败',
      icon: 'error',
    });
  } finally {
    isLoading.value = false;
  }
};

onLoad(async options => {
  // 保存来源页面信息，用于返回时通知刷新
  sourcePageInfo.value = {
    errorId: options?.errorId,
    questionId: options?.questionId,
    homeworkId: options?.homeworkId,
  };

  await fetchHomeworkDetail(options);
});

onUnload(() => {
  recommendStore.clearAll();
});
</script>

<style scoped lang="scss">
.error-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
  :deep(.u-navbar--fixed) {
    z-index: 1;
  }
  .error-detail-bg {
    height: 400rpx;
    background: #7d4dff;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 0;
  }
  .error-detail-tabs {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 20rpx;
  }

  .error-detail-content {
    flex: 1;
    background: #fff;
    border-radius: 14px 14px 0px 0px;
    position: relative;
    z-index: 1;
    padding: 15px 0;
    min-height: 300rpx;
    //height: 500rpx;
  }

  .error-detail-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-top: 1px solid #e7e7e7;
    gap: 10px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background: #fff;
    .error-detail-footer-sheet {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 50px;
    }
    .error-detail-footer-recommend {
      flex: 1;
      border-radius: 100px;
      border: 1px solid #dcdcdc;
      padding: 10px;
      color: #4e5969;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
    .error-detail-footer-complete {
      flex: 1;
      border-radius: 100px;
      border: 1px solid #7d4dff;
      padding: 10px;
      color: #fff;
      background: #7d4dff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
  }
}
</style>
