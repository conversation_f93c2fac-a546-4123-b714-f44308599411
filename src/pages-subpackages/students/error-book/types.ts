// 题目类型枚举
export enum QuestionType {
  SINGLE_CHOICE = 'single_choice',
  MULTIPLE_CHOICE = 'multiple_choice',
  ESSAY = 'essay',
}

// 选择题选项接口
export interface QuestionOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

// 题目详解接口
export interface QuestionAnalysis {
  answer: string; // 答案
  examPoint: string; // 考点
  subject: string; // 专题
  analysis: string; // 分析
  solution: string; // 解答
  comment: string; // 点评
}

// 题目数据接口
export interface Question {
  id: string;
  type: QuestionType;
  title: string;
  isPassed: boolean;
  options?: QuestionOption[]; // 选择题选项（单选题和多选题使用）
  correctAnswer: string[]; // 正确答案（选择题为选项ID数组，解答题为空数组）
  studentAnswer: string[]; // 学生答案（选择题为选项ID数组，解答题为空数组）
  correctAnswerText?: string; // 解答题正确答案文本
  studentAnswerText?: string; // 解答题学生答案文本
  analysis: QuestionAnalysis; // 试题详解
  note: string; // 笔记
}

// 题目类型配置接口
export interface QuestionTypeConfig {
  type: QuestionType;
  label: string;
}

// 题目分组接口
export interface QuestionGroup {
  typeConfig: QuestionTypeConfig;
  questions: Question[];
  startIndex: number; // 该分组题目的起始编号
}
