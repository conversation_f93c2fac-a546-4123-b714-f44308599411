<template>
  <MyDialogProvider>
    <MyContainer>
      <view class="error-detail">
        <up-navbar bgColor="transparent" placeholder>
          <template #left>
            <up-icon name="arrow-left" size="24px" color="white" @tap="handleBack" />
          </template>
          <template #center>
            <!-- <span class="error-detail-title-tag" style="color: white">{{ subjectName }}</span> -->
            <LkText :lines="1" ellipsis size="large" style="color: white">{{ pageTitle }}</LkText>
          </template>
          <template #right>
            <LkDropdown :menu-list="menuOptions" @menuClick="onMenuClick" direction="bottom-left">
              <up-icon name="more-dot-fill" size="24px" color="white" />
              <template #menu-item="{ item }">
                <template v-if="item.text === '移除错题'">
                  <up-icon name="trash" size="20"></up-icon>
                  <text style="margin-left: 10rpx">{{ item.text }}</text>
                </template>
                <template v-else>
                  <text>{{ item.text }}</text>
                </template>
              </template>
            </LkDropdown>
          </template>
        </up-navbar>
        <view class="error-detail-bg" />
        <view class="error-detail-tabs">
          <up-subsection
            :list="tabs"
            :current="currentTab"
            @change="handleTabChange"
            activeColor="#7D4DFF"
            inactiveColor="#fff"
            bgColor="rgba(0, 0, 0, 0.12)"
            style="width: 300rpx"
          />
        </view>
        <view class="error-detail-content">
          <MyLoading v-if="isLoading" fullscreen type="spinner" />
          <template v-else>
            <Wrong
              v-show="currentTab === 0"
              :controller="wrongStore"
              :status="queryOptions.status"
            />
            <ResourceList v-show="currentTab === 1" :controller="wrongStore" />
          </template>
        </view>
      </view>
    </MyContainer>
  </MyDialogProvider>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { onLoad, onUnload, onShow } from '@dcloudio/uni-app';
import { MyContainer } from '@/pages-subpackages/students/homework/components';
import { MyLoading } from '@/components/MyLoading';
import { MyDialogProvider, showDialogConfirm } from '@/components/MyDialog';
import {
  getErrorBookList,
  getErrorBookListByStatus,
  deleteErrorBook,
} from '@/api/students/errorBook';
import type {
  GetErrorBookListByStatusParams,
  GetErrorBookListParams,
} from '@/types/students/errorBook';
import { getHomeworkDetail } from '@/api/students/homework';

import { useWrongStore } from '@/store/wrongStore';

import ResourceList from './components/ResourceList/index.vue';
import Wrong from './components/QuestionContainer/Wrong.vue';
import { convertBackendToFrontQuestion } from '@/pages-subpackages/students/homework/utils/convert';

const wrongStore = useWrongStore();

const isLoading = ref(false);

const handleBack = () => {
  uni.navigateBack();
};

const tabs = [
  {
    name: '答题',
    value: 0,
  },
  {
    name: '辅助资料',
    value: 1,
  },
];

// 当前选中的 tab 索引
const currentTab = ref(0);

const queryOptions = ref<any>({});

// 页面标题和学科名称
const pageTitle = ref('错题详情');

const menuOptions = ref([
  {
    text: '移除错题',
    value: 'remove',
  },
]);
const handleTabChange = (index: number) => {
  currentTab.value = index;
};

const onMenuClick = async (item: any) => {
  if (item.value === 'remove') {
    // 获取当前错题的ID
    const currentQuestion = wrongStore.currentUnderTakingQuestion;
    const errorBookId = currentQuestion?.backendData?.id;

    if (!errorBookId) {
      uni.showToast({
        title: '无法获取错题信息',
        icon: 'error',
      });
      return;
    }

    // 显示移除错题确认弹窗
    await showDialogConfirm({
      title: '提示',
      content: '移除错题将不可恢复，确定要移除吗？',
      async onConfirm() {
        try {
          uni.showLoading({
            title: '删除中...',
            mask: true,
          });

          // 调用删除错题接口
          await deleteErrorBook({ id: errorBookId });

          uni.hideLoading();
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          });

          // 设置刷新标记，返回错题本列表页时会刷新数据
          uni.setStorageSync('needRefreshErrorBookList', true);

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 500);
        } catch (error) {
          uni.hideLoading();
          console.error('删除错题失败:', error);
        }
      },
    });
  } else {
    console.log(item);
  }
};

// 获取作业详情
const fetchHomeworkDetail = async (options: any) => {
  isLoading.value = true;
  try {
    // 处理通用参数
    if (options.semesterId) {
      queryOptions.value.semesterId = Number(options.semesterId);
    }
    if (options.startDate) {
      queryOptions.value.startDate = options.startDate;
    }
    if (options.endDate) {
      queryOptions.value.endDate = options.endDate;
    }
    if (options.subjectId) {
      queryOptions.value.subjectId = Number(options.subjectId);
    }
    if (options.homeworkId) {
      queryOptions.value.homeworkId = Number(options.homeworkId);
    }

    // 设置页面标题
    if (options.status !== undefined) {
      const statusMap = {
        0: '全部错题',
        1: '未过关错题',
        2: '已过关错题',
      };
      pageTitle.value = statusMap[Number(options.status) as keyof typeof statusMap] || '错题详情';
    } else {
      pageTitle.value = '错题详情';
    }

    let res;

    // 根据参数类型调用不同的接口
    if (options.status !== undefined) {
      // 按状态查询错题列表
      queryOptions.value.status = Number(options.status);
      const params: GetErrorBookListByStatusParams = {
        status: queryOptions.value.status,
        semesterId: queryOptions.value.semesterId,
        startDate: queryOptions.value.startDate,
        endDate: queryOptions.value.endDate,
        subjectId: queryOptions.value.subjectId || undefined,
        homeworkId: queryOptions.value.homeworkId || undefined,
      };
      const data = await getErrorBookListByStatus(params);
      // 提取所有错题
      res = data?.reduce((acc: any[], item: any) => {
        return acc.concat(item.questionList);
      }, [] as any[]);
    } else {
      // 按分类查询错题列表
      if (options.type) {
        queryOptions.value.type = Number(options.type);
      }
      if (options.categoryId) {
        queryOptions.value.categoryId = Number(options.categoryId);
      }
      const params: GetErrorBookListParams = {
        categoryId: queryOptions.value.categoryId,
        type: queryOptions.value.type,
        semesterId: queryOptions.value.semesterId,
        startDate: queryOptions.value.startDate,
        endDate: queryOptions.value.endDate,
        subjectId: queryOptions.value.subjectId || undefined,
      };
      res = await getErrorBookList(params);
    }

    const dataBackup = {
      status: 0, // 已完成
      enableAnswerVisible: 1, // 答案对学生可见
      homeworkId: res?.[0]?.homeworkId || '',
      questionList: res || [],
      materialFileList: res?.[0]?.materialFileList || [],
    };
    wrongStore.homeworkId = dataBackup.homeworkId;

    // 设置当前作业后端数据
    wrongStore.setBackendData(dataBackup);
    // 获取问题数据
    let questionList = [];

    // 获取问题数据
    questionList = dataBackup?.questionList || [];
    // 将后端问题数据转换为前端问题数据
    const questions = convertBackendToFrontQuestion(questionList);

    wrongStore.initHomework({
      homeworkId: wrongStore.homeworkId,
      answerType: res?.[0]?.taskType || 1,
      questions,
    });
  } catch (error) {
    console.error('获取作业详情失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 组件挂载时注册事件监听
onMounted(async () => {
  // await fetchHomeworkDetail();
});

onLoad(async options => {
  // 监听滚动事件
  await fetchHomeworkDetail(options);
});

// 页面显示时检查是否需要刷新
onShow(async () => {
  // 检查是否从推荐练习返回，需要刷新数据
  const needRefreshFromStorage = uni.getStorageSync('needRefreshErrorDetail');
  if (needRefreshFromStorage) {
    console.log('从推荐练习返回，刷新错题本详情数据');
    // 清除标记
    uni.removeStorageSync('needRefreshErrorDetail');
    // 重新获取错题详情数据
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1] as any;
    const options = currentPage.options || {};
    await fetchHomeworkDetail(options);
  }
});

// 组件卸载时清理事件监听
onUnload(() => {
  // wrongStore.clearAll();
});
</script>

<style scoped lang="scss">
.error-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
  :deep(.u-navbar--fixed) {
    z-index: 1;
  }
  .error-detail-bg {
    height: 400rpx;
    background: #7d4dff;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 0;
  }
  .error-detail-title-tag {
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.12);
    padding: 3px 6px;
    color: #fff;
    margin-right: 6px;
    transition: all 0.3s;
    &.dark-tag {
      background: rgba(125, 77, 255, 0.1);
      color: #7d4dff;
    }
  }
  .error-detail-tabs {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 20rpx;
  }

  .error-detail-content {
    flex: 1;
    background: #fff;
    border-radius: 14px 14px 0px 0px;
    position: relative;
    z-index: 1;
    padding: 15px 0;
    min-height: 300rpx;
    //height: 500rpx;
  }

  .error-detail-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-top: 1px solid #e7e7e7;
    gap: 10px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background: #fff;
    .error-detail-footer-sheet {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 50px;
    }
    .error-detail-footer-recommend {
      flex: 1;
      border-radius: 100px;
      border: 1px solid #dcdcdc;
      padding: 10px;
      color: #4e5969;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
    .error-detail-footer-complete {
      flex: 1;
      border-radius: 100px;
      border: 1px solid #7d4dff;
      padding: 10px;
      color: #fff;
      background: #7d4dff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
  }
}
</style>
