<template>
  <view class="error-book">
    <up-navbar title="错题本" bgColor="transparent" placeholder>
      <template #left>
        <up-icon name="arrow-left" size="24px" color="#000" @tap="handleBack" />
      </template>
    </up-navbar>

    <!-- 主要内容区域 -->
    <scroll-view scroll-y class="content-container">
      <!-- 时间筛选区 -->
      <view class="filter-row">
        <view class="filter-item" @tap="handleSemesterSelect">
          <view class="filter-value">
            <text class="filter-text">{{ currentSemester.label }}</text>
            <LkSvg width="16px" height="16px" src="/static/common/solid_up.svg" />
          </view>
        </view>
        <view class="filter-item" @tap="handleDateRangeSelect">
          <view class="filter-value">
            <text class="filter-text">{{ dateRangeText }}</text>
            <LkSvg width="16px" height="16px" src="/static/common/solid_up.svg" />
          </view>
        </view>
      </view>

      <!-- 学科Tab区 -->
      <view class="subject-section">
        <scroll-view scroll-x="true" class="subject-scroll">
          <LkTabGroup
            v-model="currentSubject"
            :tabs="computedSubjectTabs"
            :disabled="isChartAnimating"
            @update:modelValue="handleSubjectChange"
          />
        </scroll-view>
      </view>

      <!-- 统计展示区 -->
      <view class="stats-section">
        <view class="stats-card">
          <!-- 错题率区域 -->
          <view class="error-rate-area">
            <ErrorRateChart
              :value="currentStats.errorRate"
              title="错题率"
              :width="280"
              :height="160"
              :stroke-width="23"
              start-color="#5372f7"
              active-color="#6218CF"
              inactive-color="#F0F0F0"
              :duration="1500"
              @animationStart="handleChartAnimationStart"
              @animationEnd="handleChartAnimationEnd"
            />
          </view>

          <view class="stats-divider"></view>

          <!-- 统计数据区域 -->
          <view class="stats-data-area">
            <view class="stats-item">
              <text class="stats-number all-homework">{{
                currentStats.homeworkQuestionCount
              }}</text>
              <span class="stats-label"> 总做题数 </span>
            </view>
            <view class="stats-item" @tap="handleStatsItemClick(0)">
              <text class="stats-number all-errors">{{ currentStats.totalCount }}</text>
              <span class="stats-label">
                全部错题
                <up-icon name="arrow-right" color="#4E5969" size="14px" />
              </span>
            </view>
            <view class="stats-item" @tap="handleStatsItemClick(1)">
              <text class="stats-number unpassed">{{ currentStats.unPassCount }}</text>
              <span class="stats-label">
                未过关
                <up-icon name="arrow-right" color="#4E5969" size="14px" />
              </span>
            </view>
            <view class="stats-item" @tap="handleStatsItemClick(2)">
              <text class="stats-number passed">{{ currentStats.passCount }}</text>
              <span class="stats-label">
                已过关
                <up-icon name="arrow-right" color="#4E5969" size="14px" />
              </span>
            </view>
          </view>
        </view>
      </view>

      <!-- 错题分类区 -->
      <view class="category-section">
        <view class="category-header">
          <text class="category-title">错题分类</text>
        </view>
        <view class="category-tabs">
          <scroll-view scroll-x="true" class="subject-scroll">
            <LkTabGroup
              v-model="currentCategory"
              :tabs="categoryTabs"
              @update:modelValue="handleCategoryChange"
            />
          </scroll-view>
        </view>

        <!-- 分类列表 -->
        <view class="category-list" v-if="currentCategoryData.length > 0">
          <view
            v-for="item in currentCategoryData"
            :key="item.categoryId"
            class="category-item"
            @click="handleCategoryItemClick(item)"
          >
            <text class="category-item-name">{{ item.categoryName }}</text>
            <view class="category-item-right">
              <text class="category-item-count">{{ item.questionCount }}题</text>
              <view class="category-item-arrow">
                <up-icon name="arrow-right" color="#4E5969" />
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <MyEmpty v-else text="暂无错题数据" :image-width="'160rpx'" :image-height="'160rpx'" />
      </view>

      <!-- 底部安全距离 -->
      <view class="safe-bottom"></view>
    </scroll-view>
    <LkSvg
      width="280rpx"
      height="280rpx"
      src="/static/study/error_book.svg"
      customClass="bg-icon"
    />

    <!-- 选择器弹窗 -->
    <LkSelectPopupList
      ref="semesterPopupRef"
      :list="semesterOptions"
      :default-value="currentSemester.value"
      title="选择学期"
      closeOnClickOverlay
      @confirm="handleSemesterConfirm"
    />

    <LkDateRangePicker
      ref="dateRangePickerRef"
      :startDate="currentDateRange.startDate"
      :endDate="currentDateRange.endDate"
      title="选择日期区间"
      @confirm="handleDateRangeConfirm"
    />

    <!-- 错误率说明弹窗 -->
    <LKPopUpDiaLog
      ref="errorRateDialogRef"
      title="错误率的计算"
      :content="errorRateExplanation"
      :show-cancel-button="false"
      confirm-button-text="我知道了"
    />

    <!-- 全局提示组件 -->
    <LkToast ref="toastRef" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import LkSvg from '@/components/svg/index.vue';
import LkTabGroup from '@/components/LkTabGroup/index.vue';

import LkSelectPopupList from '@/components/LkSelectPopupList/index.vue';
import LkDateRangePicker from '@/components/LkDateRangePicker/index.vue';
import LKPopUpDiaLog from '@/components/LKPopUpDiaLog/index.vue';
import LkToast from '@/components/LkToast/index.vue';
import ErrorRateChart from './components/ErrorRateChart/index.vue';
import MyEmpty from '@/components/MyEmpty/index.vue';
import {
  getSemesterList,
  getSubjectList,
  getErrorBookInfo,
  getErrorBookList,
} from '@/api/students/errorBook';
import type {
  GetSemesterListResponse,
  GetSubjectListResponse,
  GetErrorBookListParams,
  GetErrorBookInfoResponse,
  CategoryData,
} from '@/types/students/errorBook';

// 接口定义
interface ErrorBookStats {
  totalCount: number;
  unPassCount: number;
  passCount: number;
  errorRate: number;
  homeworkQuestionCount: number;
}

interface SubjectItem extends GetSubjectListResponse {
  value: number;
  label: string;
}

// 组件引用
const semesterPopupRef = ref();
const dateRangePickerRef = ref();
const errorRateDialogRef = ref();
const toastRef = ref();

// 响应式数据
const currentSubject = ref(0);
const currentCategory = ref(1);

// 学期数据
const currentSemester = ref<{ value: number; label: string; startDate?: string; endDate?: string }>(
  {
    value: 0,
    label: '',
  }
);

const semesterOptions = ref<{ value: number; label: string; startDate: string; endDate: string }[]>(
  []
);

// 日期范围数据
const currentDateRange = ref<{
  startDate: string;
  endDate: string;
  displayText: string;
}>({
  startDate: '',
  endDate: '',
  displayText: '',
});

// 学科Tab数据
const subjectTabs = ref<SubjectItem[]>([]);

// 分类Tab数据
const categoryTabs = ref([
  { value: 1, label: '作业任务' },
  { value: 2, label: '题型分类' },
  { value: 3, label: '章节分类' },
  { value: 4, label: '知识点分类' },
]);

// 真实的错题本数据
const errorBookData = ref<GetErrorBookInfoResponse | null>(null);

// 当前统计数据
const currentStats = ref<ErrorBookStats>({
  totalCount: 0,
  unPassCount: 0,
  passCount: 0,
  errorRate: 0,
  homeworkQuestionCount: 0,
});

// 当前分类数据
const currentCategoryData = ref<CategoryData[]>([]);

// 错题率图表动画状态
const isChartAnimating = ref(false);

// 格式化日期显示
const formatDateRange = (startDate: string, endDate: string): string => {
  const formatDate = (dateStr: string): string => {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}.${month}.${day}`;
  };
  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
};

// 计算属性
const dateRangeText = computed(() => currentDateRange.value.displayText);

// 计算学科tabs，根据动画状态设置disabled
const computedSubjectTabs = computed(() => {
  return subjectTabs.value.map(tab => ({
    ...tab,
    disabled: isChartAnimating.value,
  }));
});

// 错误率说明内容
const errorRateExplanation = computed(() => [
  '根据上述选择的学年学期，时段和学科，统计对应的错题数和做题总数，进行错误率计算，公式如下：',
  {
    text: '错误率=（错题数 / 总做题数）×100%',
    style: {
      fontWeight: 'bold',
      color: '#7d4dff',
      fontSize: '32rpx',
      marginTop: '16rpx',
    },
  },
]);

// 方法

// 事件处理
const handleBack = () => {
  uni.navigateBack();
};

const handleMore = () => {
  toastRef.value?.show({
    message: '更多功能开发中...',
    type: 'info',
  });
};

const handleSemesterSelect = () => {
  semesterPopupRef.value?.open();
};

const handleDateRangeSelect = () => {
  dateRangePickerRef.value?.open();
};

const handleSemesterConfirm = async (semesterId?: number) => {
  const semester = semesterOptions.value.find(item => item.value === semesterId);
  if (semester) {
    currentSemester.value = semester;
    // 更新日期范围但不触发接口调用
    currentDateRange.value = {
      startDate: semester.startDate,
      endDate: semester.endDate,
      displayText: formatDateRange(semester.startDate, semester.endDate),
    };
    // 重新获取错题本数据
    await getErrorBookInfoData();
  }
};

const handleDateRangeConfirm = async (result: { startDate: string; endDate: string }) => {
  currentDateRange.value = {
    startDate: result.startDate,
    endDate: result.endDate,
    displayText: formatDateRange(result.startDate, result.endDate),
  };
  // 重新获取错题本数据
  await getErrorBookInfoData();
};

const handleSubjectChange = async (subject: number) => {
  // 如果图表正在动画中，阻止切换
  if (isChartAnimating.value) {
    console.log('图表动画进行中，暂时无法切换学科');
    return;
  }

  currentSubject.value = subject;
  await getErrorBookInfoData();
};

// 处理错题率图表动画状态变化
const handleChartAnimationStart = () => {
  isChartAnimating.value = true;
  console.log('错题率图表动画开始，禁用学科切换');
};

const handleChartAnimationEnd = () => {
  isChartAnimating.value = false;
  console.log('错题率图表动画结束，启用学科切换');
};

const handleCategoryChange = async (category: string) => {
  console.log('切换分类:', category);
  await getErrorBookInfoData();
};

const handleShowErrorRateInfo = () => {
  errorRateDialogRef.value?.open();
};

const handleCategoryItemClick = async (item: CategoryData) => {
  console.log('点击分类项:', item);

  let url = `/pages-subpackages/students/error-book/detail`;
  url += `?type=${currentCategory.value.toString()}`;
  url += `&categoryId=${item.categoryId.toString()}`;
  url += `&semesterId=${currentSemester.value.value.toString()}`;
  url += `&startDate=${currentDateRange.value.startDate}`;
  url += `&endDate=${currentDateRange.value.endDate}`;
  url += `&subjectId=${currentSubject.value.toString()}`;
  uni.navigateTo({ url });
};

const handleStatsItemClick = async (status: number) => {
  console.log('点击统计项:', status);

  // 跳转到错题详情页面，按状态筛选
  let url = `/pages-subpackages/students/error-book/detail`;
  url += `?status=${status.toString()}`;
  url += `&semesterId=${currentSemester.value.value.toString()}`;
  url += `&startDate=${currentDateRange.value.startDate}`;
  url += `&endDate=${currentDateRange.value.endDate}`;
  url += `&subjectId=${currentSubject.value.toString()}`;
  uni.navigateTo({ url });
};

const getSemesterListData = async () => {
  const res = await getSemesterList();
  console.log('学期列表:', res);
  semesterOptions.value = res.map((item: GetSemesterListResponse) => ({
    value: item.semesterId,
    label: `${item.year}年${item.termType}`,
    startDate: item.startDate,
    endDate: item.endDate,
  }));
  if (semesterOptions.value.length > 0) {
    handleSemesterConfirm(semesterOptions.value[0].value);
  }
};

const getErrorBookInfoData = async () => {
  try {
    const res = await getErrorBookInfo({
      semesterId: currentSemester.value.value,
      startDate: currentDateRange.value.startDate,
      endDate: currentDateRange.value.endDate,
      subjectId: currentSubject.value,
      category: currentCategory.value,
    });
    console.log('错题本信息:', res);

    // 更新错题本数据
    errorBookData.value = res;

    // 更新统计数据
    currentStats.value = {
      totalCount: res.totalCount,
      unPassCount: res.unPassCount,
      passCount: res.passCount,
      errorRate: res.errorRate,
      homeworkQuestionCount: res.homeworkQuestionCount,
    };

    // 更新分类数据
    currentCategoryData.value = res.categoryDataList || [];
  } catch (error) {
    console.error('获取错题本信息失败:', error);
    toastRef.value?.show({
      message: '获取错题本信息失败',
      type: 'error',
    });
  }
};

const getSubjectListData = async () => {
  const res = await getSubjectList();
  subjectTabs.value = res.map((item: GetSubjectListResponse) => ({
    value: item.subjectId,
    label: item.subjectName,
    ...item,
  }));
  currentSubject.value = subjectTabs.value[0].value;
  await getErrorBookInfoData();
};

// 生命周期
onMounted(async () => {
  // 初始化日期区间为当前学期
  await getSemesterListData();
  await getSubjectListData();
});

// 页面显示时检查是否需要刷新
onShow(async () => {
  // 检查是否从错题详情页删除错题后返回，需要刷新数据
  const needRefreshFromStorage = uni.getStorageSync('needRefreshErrorBookList');
  if (needRefreshFromStorage) {
    // 清除标记
    uni.removeStorageSync('needRefreshErrorBookList');
    // 重新获取错题本信息数据
    await getErrorBookInfoData();
  }
});
</script>

<style scoped lang="scss">
.error-book {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #e5e9fd -2.59%, #fff 87.32%);
  display: flex;
  flex-direction: column;

  // 静态彩色光晕背景
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 60% 20%, rgba(236, 72, 153, 0.06) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
  }

  .bg-icon {
    position: absolute;
    right: 0;
    top: 0;
    color: #d9ddfc;
  }
}

.content-container {
  flex: 1;
  position: relative;
  z-index: 1;
  height: calc(100vh - 44px - var(--status-bar-height));
}

.filter-row {
  display: flex;
  gap: 10rpx;
  margin: 20rpx 30rpx;

  .filter-item {
    padding: 15rpx 20rpx;
    background: #fff;
    border-radius: 8px;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 0;

    .filter-value {
      display: flex;
      align-items: center;
      gap: 10rpx;

      .filter-text {
        font-size: 28rpx;
        color: #1e293b;
        font-weight: 500;
        flex: 8;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.subject-section {
  padding: 0 30rpx;
}

.stats-section {
  padding: 20rpx 32rpx;

  .stats-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 32rpx;

    .error-rate-area {
      margin-bottom: 0;
    }

    .stats-divider {
      height: 1rpx;
      background: #eeeeee;
      margin: 20rpx 0;
    }

    .stats-data-area {
      display: flex;
      justify-content: space-between;

      .stats-item {
        flex: 1;
        text-align: center;
        padding: 10rpx;

        .stats-number {
          display: block;
          font-size: 48rpx;
          font-weight: bold;
          margin-bottom: 8rpx;

          &.all-homework {
            color: #4d8bff;
          }

          &.all-errors {
            color: #7d4dff;
          }

          &.unpassed {
            color: #ffb803;
          }

          &.passed {
            color: #40bd9e;
          }
        }

        .stats-label {
          font-size: 26rpx;
          color: #333333;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.category-section {
  background: white;
  padding: 32rpx;

  .category-header {
    margin-bottom: 24rpx;

    .category-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #1e293b;
      position: relative;
      margin-left: 15px;
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: -15px;
        height: 15px;
        width: 8px;
        background: #7d4dff;
        border-radius: 10px;
        transform: translateY(-50%);
      }
    }
  }

  .category-tabs {
    margin-bottom: 32rpx;
  }

  .category-list {
    .category-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #e7e7e7;
      transition: all 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: #f8fafc;
      }

      .category-item-name {
        font-size: 28rpx;
        color: #1e293b;
        font-weight: 500;
      }

      .category-item-right {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .category-item-count {
          font-size: 24rpx;
        }

        .category-item-arrow {
          width: 32rpx;
          height: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.safe-bottom {
  height: 40rpx;
}

// 弹框按钮样式优化
:deep(.lk-confirm-footer .up-button) {
  height: 100rpx !important;
  font-size: 32rpx !important;
  padding: 0 32rpx !important;
}

/* 平板设备适配 */
@media screen and (min-width: 768px) {
  .filter-section {
    padding: 40rpx;

    .filter-row {
      gap: 32rpx;

      .filter-item {
        padding: 32rpx;
      }
    }
  }

  .stats-section {
    padding: 40rpx;

    .stats-card {
      padding: 40rpx;
    }

    .error-rate-content {
      gap: 48rpx;
    }

    .stats-data-area {
      gap: 40rpx;

      .stats-item {
        padding: 32rpx 24rpx;
      }
    }
  }

  .category-section {
    padding: 40rpx;
  }
}
</style>
