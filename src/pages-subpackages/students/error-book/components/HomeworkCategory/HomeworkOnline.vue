<template>
  <!-- 在线作业 -->
  <view class="online-container">
    <swiper
      class="questions-swiper"
      :current="currentQuestionIndex"
      :duration="300"
      :circular="false"
      :autoplay="false"
      :indicator-dots="false"
      :acceleration="false"
      easing-function="easeInOutCubic"
      @change="onQuestionSwiperChange"
      style="flex: 1; height: 100%; min-height: 0"
      v-if="questions.length > 0"
    >
      <swiper-item v-for="item in questions" :key="item.id" class="swiper-item">
        <scroll-view scroll-y class="question-scroll-view" style="height: 100%">
          <view class="question-container">
            <QuestionRenderer
              :questionId="item.id"
              :state="QuestionEntryState.WRONG"
              :controller="controller"
            />
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
    <!-- 如果题目为空，显示空状态   -->
    <MyEmpty v-if="questions.length === 0" text="暂无数据" />
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { QuestionRenderer } from '../Question';
import MyEmpty from '@/components/MyEmpty/index.vue';
import { QuestionEntryState } from '@/constants/students/errorBook';
import { useWrongStore } from '@/store/wrongStore';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

const props = defineProps({
  controller: {
    type: null,
    default: null,
  },
});

const defaultStore = useWrongStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

const questions = computed(() => controller.currentBatchQuestions);

const currentQuestionIndex = computed(() => controller.currentQuestionIndex);

// swiper change 事件处理
const onQuestionSwiperChange = (e: any) => {
  const newIndex = e.detail.current;
  if (newIndex !== controller.currentQuestionIndex) {
    controller.setCurrentQuestionIndex(newIndex);
  }
};

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  if (index >= 0 && index < questions.value.length) {
    controller.setCurrentQuestionIndex(index);
  }
};

// 上一题
const prevQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    controller.setCurrentQuestionIndex(currentQuestionIndex.value - 1);
  }
};

// 下一题
const nextQuestion = () => {
  if (currentQuestionIndex.value < questions.value.length - 1) {
    controller.setCurrentQuestionIndex(currentQuestionIndex.value + 1);
  }
};

defineExpose({ jumpToQuestion, prevQuestion, nextQuestion });
</script>

<style lang="scss" scoped>
.online-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
}

.header {
  margin-bottom: 20rpx;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 10rpx;
  }

  .desc {
    font-size: 28rpx;
    color: #666;
  }
}

.questions-swiper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 0;
}

.swiper-item {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.question-scroll-view {
  height: 100%;
  width: 100%;
}

.question-container {
  box-sizing: border-box;
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 20rpx;

  .nav-button {
    width: 45%;
    height: 80rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
  }

  .prev-button {
    background-color: #f5f5f5;
    color: #333;
  }

  .next-button {
    background-color: #2979ff;
    color: #fff;
  }
}
</style>
