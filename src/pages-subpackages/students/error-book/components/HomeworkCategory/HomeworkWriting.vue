<template>
  <view class="container">
    <QuestionTitle :question-id="question.id" :showIndex="false" :showCurrentNumber="false" />
    <MyPhotographUploader
      v-model:files="uploadedImages"
      :readonly="!isEdit"
      :ocr="true"
      :draggable="isEdit"
      :max="maxImageCount"
      @change="syncBatchFiles"
      @before-upload="handleBeforeUpload"
    >
      <template #section-header>
        <view class="section-header" v-if="uploadedImages?.length > 0">
          <text class="section-title"
            >提交练习原件
            <template v-if="isEdit">({{ uploadedImages.length }}/{{ maxImageCount }})</template>
          </text>
          <text class="section-subtitle" v-if="isEdit">可长按拖动排序</text>
        </view>
        <MyEmpty v-if="!isUploading && uploadedImages?.length === 0" />
      </template>
    </MyPhotographUploader>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import type { HomeworkFileInfo } from '@/types/students/homework';
import MyPhotographUploader, {
  type ImageItem,
} from '@/pages-subpackages/students/homework/components/MyPhotographUploader/index.vue';
import MyEmpty from '@/components/MyEmpty/index.vue';
import { QuestionTitle } from '@/pages-subpackages/students/homework/components/Question/components';
import { useWrongStore } from '@/store/wrongStore';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

const props = defineProps({
  controller: {
    type: null,
    default: null,
  },
});

const defaultStore = useWrongStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

// 是否编辑模式
const isEdit = computed(() => controller.canEdit);
const question = computed(() => controller.questions[0] || {});
const isUploading = ref(false);

// 最大上传图片数量
const maxImageCount = 9;

// 已上传图片列表
const uploadedImages = ref<ImageItem[]>([]);

// 监听用户答案变化
watch(
  () => controller.userAnswers[question.value.id],
  (newVal: any) => {
    uploadedImages.value = newVal?.answer?.files || [];
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  const files = question.value.backendData?.question?.submitQuestion?.files?.map((item: any) => ({
    ...item.file,
    id: item.id,
    error: false,
    isOcrLoading: false,
  }));
  uploadedImages.value = files;
  syncBatchFiles(files);
});

// 同步图片数组到 store
const syncBatchFiles = (files: ImageItem[]) => {
  isUploading.value = false;
  controller.setUserAnswer(question.value.id, {
    files,
  });
};

const handleBeforeUpload = () => {
  isUploading.value = true;
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  min-height: 400rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}
</style>
