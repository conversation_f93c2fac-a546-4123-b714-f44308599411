<template>
  <view class="container">
    <MyPhotographUploader
      v-model:files="uploadedImages"
      :readonly="!isEdit"
      :ocr="true"
      :draggable="isEdit"
      :max="maxImageCount"
      @change="syncBatchFiles"
      @before-upload="handleBeforeUpload"
    >
      <template #section-header>
        <view class="section-header">
          <text class="section-title"
            >提交练习原件
            <template v-if="isEdit">({{ uploadedImages?.length }}/{{ maxImageCount }})</template>
          </text>
          <text class="section-subtitle" v-if="isEdit">可长按拖动排序</text>
        </view>
        <MyEmpty v-if="!isUploading && uploadedImages?.length === 0" />
      </template>
    </MyPhotographUploader>
    <view class="online-box">
      <HomeworkOnline v-if="isShowHomeworkOnline" :controller="controller" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import HomeworkOnline from './HomeworkOnline.vue';
import { HomeworkStatus } from '@/constants/students/homework';
import type { HomeworkFileInfo } from '@/types/students/homework';
import MyPhotographUploader, {
  type ImageItem,
} from '@/pages-subpackages/students/homework/components/MyPhotographUploader/index.vue';
import MyEmpty from '@/components/MyEmpty/index.vue';
import { useWrongStore } from '@/store/wrongStore';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

const props = defineProps({
  controller: {
    type: null,
    default: null,
  },
});

const defaultStore = useWrongStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

const isEdit = computed(() => {
  // 如果当前不是最后一个批次，则不允许编辑
  if (controller.currentBatchIndex !== controller.batches.length - 1) {
    return false;
  }
  return !controller.readonly;
});
const status = computed(() => controller.status);
const isUploading = ref(false);

const maxImageCount = 9;

const isShowHomeworkOnline = computed(() => {
  if (status.value === HomeworkStatus.COMPLETED) return true;
  if (
    controller.batches.length > 1 &&
    controller.readonly &&
    controller.currentBatchIndex !== controller.batches.length - 1
  ) {
    return true;
  }
  return false;
});

const uploadedImages = ref<ImageItem[]>([]);

// 从当前批次的 files 初始化图片数据
const initUploadedImages = () => {
  const currentFiles = controller.currentBatch?.files || [];
  const currentFileUrls = currentFiles.map((item: any) => item.fileUrl).filter(Boolean);
  const currentUploadedUrls = uploadedImages.value.map(item => item.fileUrl).filter(Boolean);

  // 只有当 files 真正变化时才更新
  if (JSON.stringify(currentFileUrls) !== JSON.stringify(currentUploadedUrls)) {
    if (currentFiles.length > 0) {
      uploadedImages.value = currentFiles.map((item: any) => ({
        ...item,
        error: false,
        isOcrLoading: false,
      }));
    } else {
      uploadedImages.value = [];
    }
  }
};

// 监听当前批次变化，重新初始化图片数据
watch(
  () => [controller.currentBatchIndex, controller.currentBatch?.files],
  () => {
    // 避免闪烁，清空图片数据
    uploadedImages.value = [];
    // 使用 nextTick 确保在下一个 tick 执行，避免死循环
    nextTick(() => {
      initUploadedImages();
    });
  },
  { immediate: true, deep: true }
);

const syncBatchFiles = (files: ImageItem[]) => {
  isUploading.value = false;
  controller.setCurrentBatchFiles(files);
  initUploadedImages();
};

const handleBeforeUpload = () => {
  isUploading.value = true;
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  min-height: 400rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

.online-box {
  margin-top: 50rpx;
  height: calc(100vh - 800rpx);
}
</style>
