<template>
  <view class="selection-template">
    <!-- 单选组件 -->
    <template v-if="isSingleChoice">
      <QuestionSelectionGroup
        v-model="localSingleAnswer"
        :disabled="readonly"
        :correctAnswer="correctSingleAnswer"
        :showAnswer="readonly"
        :checkAnswer="checkAnswer"
        @update:modelValue="val => onSingleChange(val as string)"
      >
        <uni-row :gutter="10">
          <uni-col :xs="24" :sm="24" v-for="option in question?.options" :key="option.id">
            <QuestionSelection
              :label="getOptionLabel(option.id)"
              :text="option.content"
              :value="option.id"
              :image-url="option.imageUrl"
            />
          </uni-col>
        </uni-row>
      </QuestionSelectionGroup>
    </template>

    <!-- 多选组件 -->
    <template v-else-if="isMultipleChoice">
      <QuestionSelectionGroup
        v-model="localMultipleAnswer"
        :multiple="true"
        :disabled="readonly"
        :correctAnswer="correctMultipleAnswer"
        :maxCount="maxSelectCount"
        :showAnswer="readonly"
        :checkAnswer="checkAnswer"
        @update:modelValue="val => onMultipleChange(val as string[])"
      >
        <uni-row :gutter="10">
          <uni-col :xs="24" :sm="24" v-for="option in question?.options" :key="option.id">
            <QuestionSelection
              :label="getOptionLabel(option.id)"
              :text="option.content"
              :value="option.id"
              :image-url="option.imageUrl"
            />
          </uni-col>
        </uni-row>
      </QuestionSelectionGroup>
    </template>

    <!-- 判断题组件 -->
    <template v-else-if="isTrueFalse">
      <QuestionSelectionGroup
        v-model="localSingleAnswer"
        :disabled="readonly"
        :correctAnswer="correctSingleAnswer"
        :showAnswer="readonly"
        :checkAnswer="checkAnswer"
        :showAnalysis="controller.showAnalysis"
        @update:modelValue="val => onSingleChange(val as string)"
      >
        <uni-row :gutter="10">
          <uni-col :xs="24" :sm="24" v-for="option in trueFalseOptions" :key="option.id">
            <QuestionSelection
              :label="getOptionLabel(option.id)"
              :text="option.content"
              :value="option.id"
            />
          </uni-col>
        </uni-row>
      </QuestionSelectionGroup>
    </template>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { HomeworkStatus, QuestionType } from '@/constants/students/homework';
import type { FrontMultipleQuestion } from '@/types/students/homework';
import { QuestionSelection, QuestionSelectionGroup } from '../components';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

// 选项类型定义
interface QuestionOption {
  id: string;
  content: string;
  imageUrl?: string;
}

// 定义组件属性：支持外部传入控制器，以便替换不同的 store 实现
const props = defineProps<{
  questionId: string;
  controller?: any;
  readonly?: boolean;
}>();

// 默认使用原有的作业 store，亦可通过 props.controller 传入替代实现（降耦合）
const defaultStore = useHomeworkStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

// 从控制器获取数据（统一抽象，避免直接依赖具体 store）
const question = computed(() => controller.getQuestion(props.questionId));
const userAnswer = computed(() => controller.getUserAnswer(props.questionId));
const readonly = computed(() => !!props.readonly);
const showAnalysis = computed(() => !!readonly.value);

// 判断题目类型
const isSingleChoice = computed(() => question.value?.type === QuestionType.SINGLE);
const isMultipleChoice = computed(() => question.value?.type === QuestionType.MULTIPLE);
const isTrueFalse = computed(() => question.value?.type === QuestionType.TRUE_FALSE);
const checkAnswer = computed(() => !!readonly.value);

// 获取正确答案（单选题和判断题）
const correctSingleAnswer = computed(() => {
  if (!question.value?.answer) return undefined;

  if (typeof question.value.answer === 'string') {
    return question.value.answer;
  }

  if (Array.isArray(question.value.answer) && question.value.answer.length > 0) {
    return question.value.answer[0];
  }

  return undefined;
});

// 获取正确答案（多选题）
const correctMultipleAnswer = computed(() => {
  if (!question.value?.answer) return undefined;

  if (Array.isArray(question.value.answer)) {
    return question.value.answer;
  }

  if (typeof question.value.answer === 'string') {
    return [question.value.answer];
  }

  return undefined;
});

// 多选题的最大选择数量
const maxSelectCount = computed(() => {
  // 如果题目配置了maxSelectCount，则使用配置值，否则默认为2
  return (question.value as FrontMultipleQuestion)?.maxSelectCount ?? 2;
});

// 本地状态
const localSingleAnswer = ref<string>('');
const localMultipleAnswer = ref<string[]>([]);

// 获取选项标签（A, B, C, D...）
const getOptionLabel = (id: string) => {
  const options = question.value?.options || [];
  const index = options.findIndex((option: QuestionOption) => option.id === id);
  if (index === -1) return '';
  return String.fromCharCode(65 + index); // 65 是 'A' 的 ASCII 码
};

// 初始化本地答案
function initializeAnswers() {
  // console.log(`[Q${props.questionId}] 开始初始化答案`);

  // 先重置本地状态
  localSingleAnswer.value = '';
  localMultipleAnswer.value = [];

  // 如果没有用户答案，直接返回
  if (!userAnswer.value) {
    // console.log(`[Q${props.questionId}] 无用户答案，使用默认值`);
    return;
  }

  const answer = userAnswer.value.answer;
  //   console.log(
  //     `[Q${props.questionId}] 用户答案类型:`,
  //     typeof answer,
  //     Array.isArray(answer) ? "数组" : "非数组"
  //   );

  // 处理单选题
  if (isSingleChoice.value || isTrueFalse.value) {
    if (typeof answer === 'string') {
      localSingleAnswer.value = answer;
      //   console.log(
      //     `[Q${props.questionId}] 设置单选答案:`,
      //     localSingleAnswer.value
      //   );
    }
  }
  // 处理多选题
  else if (isMultipleChoice.value) {
    if (Array.isArray(answer)) {
      // 确保数组中的值都是字符串
      localMultipleAnswer.value = answer.filter((item): item is string => typeof item === 'string');
      //   console.log(
      //     `[Q${props.questionId}] 设置多选答案:`,
      //     localMultipleAnswer.value
      //   );
    } else if (typeof answer === 'string' && answer) {
      // 兼容处理：如果是字符串但应该是多选，转换为数组
      localMultipleAnswer.value = [answer];
      //   console.log(
      //     `[Q${props.questionId}] 单值转多选答案:`,
      //     localMultipleAnswer.value
      //   );
    }
  }
}

// 挂载时初始化
onMounted(() => {
  // console.log(`[Q${props.questionId}] 组件挂载，初始化答案`);
  // 使用nextTick确保DOM更新后再初始化
  nextTick(() => {
    initializeAnswers();
  });
});

// 监听用户答案变化 - 使用immediate确保首次加载也能同步
watch(
  () => userAnswer.value,
  newVal => {
    // console.log(
    //   `[Q${props.questionId}] 用户答案变化，重新初始化:`,
    //   newVal?.answer
    // );
    // 使用nextTick确保DOM更新后再初始化
    nextTick(() => {
      initializeAnswers();
    });
  },
  { deep: true, immediate: true }
);

// 监听题目ID变化，当切换题目时重新初始化
watch(
  () => props.questionId,
  (newVal, oldVal) => {
    // console.log(
    //   `[Q${props.questionId}] 题目ID变化: ${oldVal} -> ${newVal}，重新初始化`
    // );
    // 使用nextTick确保DOM更新后再初始化
    nextTick(() => {
      // 清空旧数据，避免切换题目时数据污染
      localSingleAnswer.value = '';
      localMultipleAnswer.value = [];
      initializeAnswers();
    });
  }
);

// 单选题逻辑
const onSingleChange = (value: string) => {
  if (readonly.value) return;
  // console.log(`[Q${props.questionId}] 单选值变化:`, value);
  controller.setUserAnswer(props.questionId, value);
};

// 多选题逻辑
const onMultipleChange = (values: string[]) => {
  if (readonly.value) return;

  // 直接更新答案，选择数量限制已在组件内部处理
  controller.setUserAnswer(props.questionId, values);
};

// 判断题特有的选项
const trueFalseOptions = computed<QuestionOption[]>(() => {
  if (question.value?.options && question.value.options.length > 0) {
    return question.value.options;
  }
  // 默认判断题选项
  return [
    { id: 'true', content: '正确' },
    { id: 'false', content: '错误' },
  ];
});
</script>

<style lang="scss" scoped>
.selection-template {
  width: 100%;
  padding: 16rpx;
}
</style>
