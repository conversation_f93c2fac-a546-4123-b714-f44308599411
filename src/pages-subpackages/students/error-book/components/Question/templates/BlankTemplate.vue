<template>
  <view class="blank-template">
    <view class="blank-template-index">
      <text>{{ question.index }}、</text>
    </view>
    <!-- <mp-html :content="question.backendData.question.stem" /> -->
    <!-- 结构化填空题模式 - 支持parts数组结构 -->
    <view class="structured-blanks" v-if="question?.blanks && question.blanks.length > 0">
      <QuestionFillLineGroup :readonly="readonly" @update="handleFillLineUpdate">
        <view
          class="structured-blank-item"
          v-for="(item, itemIndex) in question.blanks"
          :key="itemIndex"
        >
          <view class="structured-blank-label" v-if="question.blanks.length > 1">
            <text>({{ itemIndex + 1 }})</text>
          </view>
          <view class="structured-blank-content">
            <template v-for="(part, partIndex) in item.parts" :key="`${itemIndex}-${partIndex}`">
              <!-- 文本部分 - 移除包裹的view，直接使用mp-html -->
              <template v-if="part.type == 'text'">
                <mp-html :content="part.value" class="inline-text" />
              </template>

              <!-- 填空部分 -->
              <QuestionFillLine
                v-else-if="part.type == 'blank'"
                v-model="blankValues[getGlobalBlankIndex(itemIndex, part.index)]"
                :blank-id="getGlobalBlankIndex(itemIndex, part.index)"
                :blank-index="getGlobalBlankIndex(itemIndex, part.index)"
                :readonly="readonly"
                :is-error="isBlankError(getGlobalBlankIndex(itemIndex, part.index))"
                :is-correct="isBlankCorrect(getGlobalBlankIndex(itemIndex, part.index))"
              />
            </template>
          </view>
        </view>
      </QuestionFillLineGroup>
    </view>
    <view v-else class="no-blanks">
      <text>题目数据格式错误，请检查题目配置</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { QuestionFillLine, QuestionFillLineGroup } from '../../Question/components';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

// 定义组件属性：支持外部传入控制器，以便替换不同的 store 实现
const props = defineProps<{
  questionId: string;
  controller?: any;
  readonly?: boolean;
}>();

// 默认使用原有的作业 store，亦可通过 props.controller 传入替代实现（降耦合）
const defaultStore = useHomeworkStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

// 从控制器获取数据（统一抽象，避免直接依赖具体 store）
const question = computed(() => controller.getQuestion(props.questionId));
const userAnswer = computed(() => controller.getUserAnswer(props.questionId));
const readonly = computed(() => !!props.readonly);
const showAnalysis = computed(() => !!readonly.value);

// 答案数据处理 - 数组形式
const arrayAnswers = computed(() => {
  if (userAnswer.value?.answer && Array.isArray(userAnswer.value.answer)) {
    return userAnswer.value.answer as string[];
  }
  return [] as string[];
});

// 填空值的响应式对象
const blankValues = reactive<Record<string, string>>({});

// 计算全局空的下标
const getGlobalBlankIndex = (itemIndex: number, partIndex: number): number => {
  let globalIndex = 0;
  for (let i = 0; i < itemIndex; i++) {
    globalIndex += question.value.blanks[i].parts.filter((p: any) => p.type === 'blank').length;
  }
  return globalIndex + partIndex;
};

// 初始化填空值
watch(
  () => userAnswer.value?.answer,
  newVal => {
    // 清空旧数据，避免切换订正时数据污染
    Object.keys(blankValues).forEach(key => {
      delete blankValues[key];
    });

    if (newVal && Array.isArray(newVal)) {
      const answerArray = newVal as string[];
      answerArray.forEach((value, index) => {
        blankValues[index] = value;
      });
    }
  },
  { immediate: true }
);

// 提取核心判断逻辑：根据后端 answerResult 判断填空状态
const getBlankResultFromBackend = (blankIndex: number): number | null => {
  let answerResult = question.value?.backendData?.question?.submitQuestion?.answerResult;

  // answerResult = "[0,1,0,1,1,1,1,1]";
  if (answerResult && typeof answerResult === 'string') {
    try {
      // 解析字符串格式的数组，如 "[0,1,0,1,...]"
      const resultArray = JSON.parse(answerResult);
      if (Array.isArray(resultArray) && blankIndex < resultArray.length) {
        return resultArray[blankIndex];
      }
    } catch (error) {
      console.warn('解析 answerResult 失败:', error, answerResult);
    }
  }
  return null;
};

// 处理填空线更新
const handleFillLineUpdate = (data: {
  blankId: string | number;
  blankIndex: number;
  value: string;
}) => {
  if (readonly.value) return;

  const { blankIndex, value } = data;

  // 更新本地值
  blankValues[blankIndex] = value;

  // 构造新的答案数组
  const newAnswers = [...arrayAnswers.value];
  newAnswers[blankIndex] = value;

  controller.setUserAnswer(props.questionId, newAnswers);
};

// 判断填空是否错误
const isBlankError = (blankIndex: number): boolean => {
  // 如果 showAnswer 为 false，且不是只读状态，则不显示错误状态
  if (!readonly.value) {
    return false;
  }

  // 如果 showAnswer 为 false，但是处于只读状态，则需要检查作业状态来决定是否显示
  if (readonly.value) {
    // 只有在作业状态允许显示解析时才显示错误状态
    if (!showAnalysis.value) {
      return false;
    }
  }

  // 使用提取的核心判断逻辑
  const result = getBlankResultFromBackend(blankIndex);
  // 0 表示错误，1 表示正确
  return result == 0;
};

// 判断填空是否正确
const isBlankCorrect = (blankIndex: number): boolean => {
  // 如果 showAnswer 为 false，且不是只读状态，则不显示正确状态
  if (!readonly.value) {
    return false;
  }

  // 如果 showAnswer 为 false，但是处于只读状态，则需要检查作业状态来决定是否显示
  if (readonly.value) {
    // 只有在作业状态允许显示解析时才显示正确状态
    if (!showAnalysis.value) {
      return false;
    }
  }

  // 使用提取的核心判断逻辑
  const result = getBlankResultFromBackend(blankIndex);
  // 1 表示正确，0 表示错误
  return result == 1;
};
</script>

<style lang="scss" scoped>
.blank-template {
  width: 100%;
  display: flex;

  .blank-template-index {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  // 结构化填空题样式
  .structured-blanks {
    width: 100%;
  }

  .structured-blank-item {
    margin-bottom: 30rpx;
    display: flex;
  }

  .structured-blank-label {
    font-size: 32rpx;
    font-weight: normal;
    color: #333;
    margin-right: 10rpx;
    flex-shrink: 0; // 防止标签被压缩
  }

  .structured-blank-content {
    margin-top: -6rpx;
    flex: 1;
    line-height: 1.8;
    white-space: normal;
    word-break: break-word;
    word-wrap: break-word;
    text-align: left;
    font-size: 32rpx;
    display: block;
    width: 100%;

    // 确保所有子元素都是内联显示，形成连贯文本流
    :deep(*) {
      display: inline !important;
      vertical-align: baseline;
    }

    :deep(uni-rich-text) {
      display: inline !important;
      vertical-align: baseline;
    }

    // 填空组件内联显示，紧贴文字
    :deep(.question-fill-line) {
      display: inline-block !important;
      vertical-align: baseline;
      margin: 0 !important;
      padding: 0 !important;
    }

    // 填空输入容器内联显示
    :deep(.fill-line-input-wrapper) {
      display: inline-flex !important;
      vertical-align: baseline;
      margin: 0 !important;
      padding: 0 !important;
      height: auto !important;
    }

    // 填空输入框样式 - 完全融入文本流
    :deep(.fill-line-input) {
      display: inline !important;
      vertical-align: baseline;
      white-space: nowrap !important;
      min-width: 60rpx;
      border: none !important;
      border-bottom: 2rpx solid #b694ff !important;
      background: transparent !important;
      padding: 0 4rpx !important;
      margin: 0 !important;
      height: auto !important;
      line-height: inherit !important;
      font-size: inherit !important;
      text-align: center;
      color: #7d4dff;
    }

    // 下划线样式调整
    :deep(.fill-line-underline) {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2rpx;
      background-color: #b694ff;
    }
  }

  .no-blanks {
    padding: 30rpx;
    color: #ff4d4f;
    text-align: center;
  }
}

.inline-text {
  display: inline !important;
  vertical-align: baseline;
  white-space: normal;

  :deep(uni-rich-text) {
    display: inline !important;
    vertical-align: baseline;
    white-space: normal;
  }

  // 确保富文本内部所有元素都是内联的
  :deep(*) {
    display: inline !important;
    vertical-align: baseline;
  }
}
</style>
