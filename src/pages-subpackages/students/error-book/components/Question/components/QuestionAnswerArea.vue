<template>
  <view
    class="question-answer-area"
    :class="{
      correct: status === 'correct',
      incorrect: status === 'incorrect',
    }"
  >
    <view class="answer-header">
      <text class="header-title">作答区</text>
      <!-- 默认状态：全屏图标，只在非只读模式下显示 -->
      <view v-if="!status && !isReadonly" class="header-icon" @tap="handleFullscreen">
        <image
          class="fullscreen-icon"
          src="/static/homework/fullscreen_icon.svg"
          mode="aspectFit"
        />
      </view>
      <!-- 正确状态：绿色对勾 -->
      <view v-else-if="status === 'correct'" class="header-icon">
        <image
          class="status-icon correct-icon"
          src="/static/homework/correct.svg"
          mode="aspectFit"
        />
      </view>
      <!-- 错误状态：红色叉号 -->
      <view v-else-if="status === 'incorrect'" class="header-icon">
        <image
          class="status-icon incorrect-icon"
          src="/static/homework/incorrect.svg"
          mode="aspectFit"
        />
      </view>
    </view>
    <view class="answer-content">
      <!-- 如果有图片URL，显示图片内容 -->
      <template v-if="imageUrl">
        <image class="drawing-image" :src="imageUrl" mode="aspectFit" @tap="previewImage" />
      </template>
      <!-- 否则垂直居中显示 "开始作答" (非只读状态) -->
      <template v-else-if="!isReadonly">
        <view class="start-answer" @tap="handleStartAnswer">
          <text class="start-answer-text">开始作答</text>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  imageUrl: {
    type: String,
    default: '',
  },
  // 添加状态属性：correct, incorrect 或 undefined
  status: {
    type: String,
    default: '',
    validator: (value: string) => ['', 'correct', 'incorrect'].includes(value),
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false,
  },
  // 是否显示答案（影响只读状态）
  showAnswer: {
    type: Boolean,
    default: false,
  },
});

// 计算属性，考虑 showAnswer 状态
const isReadonly = computed(() => props.readonly || props.showAnswer);

const emit = defineEmits(['update:modelValue', 'change', 'fullscreen']);

const answerContent = ref(props.modelValue || '');

const handleInput = () => {
  emit('update:modelValue', answerContent.value);
  emit('change', answerContent.value);
};

const handleFullscreen = () => {
  // 只读模式下不触发全屏事件
  if (isReadonly.value) return;

  emit('fullscreen');
};

// 图片预览功能
const previewImage = () => {
  if (props.imageUrl) {
    uni.previewImage({
      urls: [props.imageUrl],
      current: props.imageUrl,
      longPressActions: {
        itemList: ['保存图片', '分享'],
        success: function (data) {
          // console.log("选中了第" + (data.tapIndex + 1) + "个按钮");
        },
        fail: function (err) {
          // console.log(err.errMsg);
        },
      },
    });
  }
};

// 处理开始作答点击事件
const handleStartAnswer = () => {
  // 只在非只读模式下触发全屏事件
  if (!isReadonly.value) {
    handleFullscreen();
  }
};
</script>

<style lang="scss" scoped>
.question-answer-area {
  width: 100%;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 2rpx solid #dcdcdc;

  // 正确状态样式
  &.correct {
    border-color: #52c41a;
  }

  // 错误状态样式
  &.incorrect {
    border-color: #ff4d4f;
  }

  .answer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 88rpx;
    padding: 0 24rpx;
    background: #f9f9f9;

    .header-title {
      font-size: 32rpx;
      color: #333333;
    }

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;

      .fullscreen-icon {
        width: 40rpx;
        height: 40rpx;
      }

      .status-icon {
        width: 40rpx;
        height: 40rpx;
      }

      .correct-icon {
        color: #52c41a;
      }

      .incorrect-icon {
        color: #ff4d4f;
      }
    }
  }

  .answer-content {
    padding: 24rpx;
    height: 320rpx;

    .answer-textarea {
      width: 100%;
      height: 300rpx;
      font-size: 28rpx;
      line-height: 1.6;
      color: #333333;
    }

    .drawing-image {
      width: 100%;
      height: 280rpx;
    }

    .start-answer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 280rpx;
      width: 100%;

      .start-answer-text {
        font-size: 32rpx;
        color: #999999;
        font-weight: 400;
      }
    }
  }
}
</style>
