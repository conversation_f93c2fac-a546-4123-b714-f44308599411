<template>
  <view
    :class="[
      'question-selection',
      {
        'is-selected': isSelected,
        'is-disabled': isDisabled,
        'is-correct': selectionStatus === 'correct',
        'is-incorrect': selectionStatus === 'incorrect',
      },
    ]"
    @click="onClick"
  >
    <view class="selection-content">
      <text class="selection-letter">{{ label }}</text>
      <view class="selection-divider"></view>
      <text class="selection-text">{{ text }}</text>
    </view>
    <image v-if="imageUrl" class="selection-image" :src="imageUrl" mode="aspectFit" />
    <!-- 状态图标 -->
    <view v-if="selectionStatus !== 'normal'" class="status-icon" :class="selectionStatus">
      <LkSvg
        v-if="selectionStatus === 'correct'"
        src="/static/homework/correct.svg"
        width="48rpx"
        height="48rpx"
      />
      <LkSvg
        v-else-if="selectionStatus === 'incorrect'"
        src="/static/homework/incorrect.svg"
        width="48rpx"
        height="48rpx"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { inject, computed } from 'vue';
import type { SelectionGroupContext } from './QuestionSelectionGroup.vue';

export type SelectionStatus = 'correct' | 'incorrect' | 'normal';

interface SelectionProps {
  label: string;
  text: string;
  value: string;
  imageUrl?: string;
  isSelected?: boolean;
  disabled?: boolean;
  status?: SelectionStatus;
}

const props = defineProps<SelectionProps>();

const emit = defineEmits<{
  (e: 'click', value: string): void;
}>();

// 从Group组件注入的上下文
const selectionGroup = inject<SelectionGroupContext | null>('selection-group', null);

// 是否选中，优先使用Group提供的状态
const isSelected = computed(() => {
  if (selectionGroup) {
    const isSelected = selectionGroup.isSelectionSelected(props.value);
    // console.log(props.value, isSelected);
    return isSelected;
  }
  return props.isSelected || false;
});

// 是否禁用，优先使用Group提供的状态
const isDisabled = computed(() => {
  if (selectionGroup) {
    return selectionGroup.disabled.value;
  }
  return props.disabled || false;
});

// 选项状态，优先使用Group提供的状态
const selectionStatus = computed((): SelectionStatus => {
  if (selectionGroup) {
    return selectionGroup.getSelectionStatus(props.value);
  }
  return props.status || 'normal';
});

// 点击处理
const onClick = () => {
  if (isDisabled.value) return;

  if (selectionGroup) {
    selectionGroup.handleSelectionClick(props.value);
  }

  emit('click', props.value);
};
</script>

<style lang="scss" scoped>
.question-selection {
  width: 100%;
  padding: 24rpx 40rpx;
  border-radius: 16rpx;
  background-color: #f2f3f5;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;

  &.is-selected {
    background-color: #f3ecff;
    border: 2rpx solid #7d4dff;
    transform: scale(1.01);
  }

  &.is-correct {
    border: 2rpx solid #2cd990;
    background: #e3f9e9;

    .selection-letter {
      color: #2cd990;
    }
  }

  &.is-incorrect {
    border: 2rpx solid #ff463c;
    background: #fff0ed;

    .selection-letter {
      color: #ff463c;
    }
  }

  &.is-disabled {
    pointer-events: none;
  }

  &:active {
    opacity: 0.8;
    transform: scale(0.98);
  }

  .selection-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 28rpx;
  }

  .selection-letter {
    font-size: 40rpx;
    font-weight: 600;
    font-family: PingFang SC;
    color: #000000;
    width: 32rpx;
  }

  .selection-divider {
    width: 2rpx;
    height: 36rpx;
    background-color: #c4c4c4;
  }

  .selection-text {
    font-size: 32rpx;
    line-height: 1.5em;
    font-family: PingFang SC;
    color: #000000;
  }

  .selection-image {
    margin-top: 16rpx;
    max-width: 100%;
    max-height: 400rpx;
  }

  .status-icon {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 20rpx;

    &.correct {
      // background-color: #2cd990;
    }

    &.incorrect {
      // background-color: #ff463c;
    }

    .icon-text {
      color: #ffffff;
      font-size: 28rpx;
      font-weight: bold;
      line-height: 1;
    }
  }
}
</style>
