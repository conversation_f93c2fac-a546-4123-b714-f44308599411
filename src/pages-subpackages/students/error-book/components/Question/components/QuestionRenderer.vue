<template>
  <view class="question-renderer">
    <!-- 添加统一的题目标题 -->
    <QuestionTitle
      :question="question"
      :currentQuestionNumber="currentQuestionNumber"
      :questionsLength="questionsLength"
      :showIndex="true"
      style="padding: 0 25px"
    />
    <!-- 复合题模板 -->
    <CompositeTemplate
      v-if="question && question.subQuestions && question.subQuestions.length > 0"
      :questionId="questionId"
      :controller="controller"
      :readonly="readonly"
      :initiative-show="state === QuestionEntryState.MANUAL ? false : initiativeShow"
      :page-type="state"
      style="padding: 0 25px"
    />
    <!-- 选项题模板 -->
    <SelectionTemplate
      v-else-if="
        question.type === QuestionType.SINGLE ||
        question.type === QuestionType.MULTIPLE ||
        question.type === QuestionType.TRUE_FALSE
      "
      :questionId="questionId"
      :controller="controller"
      :readonly="readonly"
      style="padding: 0 25px"
    />

    <!-- 填空题模板 -->
    <BlankTemplate
      v-else-if="
        question.type === QuestionType.FILL_BLANK ||
        question.type === QuestionType.CLOZE ||
        question.type === QuestionType.GRAPHIC_FILL
      "
      :questionId="questionId"
      :controller="controller"
      :readonly="readonly"
      style="padding: 0 25px"
    />

    <!-- 文本区模板 -->
    <TextareaTemplate
      v-else-if="question.type === QuestionType.SHORT_ANSWER"
      :questionId="questionId"
      :controller="controller"
      :readonly="readonly"
      :initiative-show="state === QuestionEntryState.MANUAL ? false : initiativeShow"
      :page-type="state"
      style="padding: 0 25px"
    />

    <!-- 排序题模板 -->

    <!-- 匹配题模板 -->

    <view class="question-not-found" style="padding: 0 25px" v-else>
      <text>未找到对应题型的渲染组件</text>
    </view>

    <view class="submit-btn" style="padding: 0 25px" v-if="!readonly">
      <LkButton type="secondary" shape="round" @tap="handleSubmit"> 提交作答 </LkButton>
    </view>

    <up-gap height="10" bgColor="#F7F7FD" marginTop="10" marginBottom="10" v-if="!!readonly" />

    <!-- 题目解析 -->
    <QuestionAnalysis
      v-if="!!readonly"
      :questionId="questionId"
      :question="question"
      :answerType="answerType"
      :pageType="state as QuestionEntryState"
      showAnalysis
      style="padding: 0 25px"
    />

    <up-gap height="10" bgColor="#F7F7FD" marginTop="10" marginBottom="10" v-if="!!readonly" />

    <!-- 题目笔记 -->
    <QuestionNote
      v-if="!!readonly"
      :questionId="questionId"
      :controller="controller"
      :pageType="state as QuestionEntryState"
      style="padding: 0 25px"
    />

    <view style="height: 40px" />

    <template v-if="state === QuestionEntryState.WRONG">
      <LkSvg
        v-if="showWrongAnswer"
        width="140rpx"
        height="140rpx"
        src="/static/study/passed_inspection.svg"
        customClass="tag-icon"
      />

      <LkSvg
        v-else
        width="140rpx"
        height="140rpx"
        src="/static/study/failed_inspection.svg"
        customClass="tag-icon"
      />
    </template>

    <LkToast ref="lkToastRef" />

    <!-- 可拖拽悬浮答案组件 - 仅在MANUAL状态下显示 -->
    <DraggableQuestionFloat
      v-if="state === QuestionEntryState.MANUAL && showFloatingQuestion"
      :question="question"
      :show="showFloatingQuestion"
      @close="handleCloseFloatingQuestion"
      @position-change="handleFloatingPositionChange"
    />
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import QuestionTitle from './QuestionTitle.vue';
import QuestionAnalysis from './QuestionAnalysis.vue';
import QuestionNote from './QuestionNote.vue';
import DraggableQuestionFloat from './DraggableQuestionFloat.vue';

import { AnswerType, QuestionType } from '@/constants/students/homework';
import { WrongAnswerType, QuestionEntryState } from '@/constants/students/errorBook';
import { checkAnswer } from '@/api/students/errorBook';
import type { CheckAnswerParams } from '@/types/students/errorBook';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';
import { useEventBus } from '@/hooks/useEventBus';

import {
  SelectionTemplate,
  BlankTemplate,
  TextareaTemplate,
  CompositeTemplate,
} from '../templates';

// 定义组件属性：支持外部传入控制器，以便替换不同的 store 实现
const props = defineProps({
  questionId: {
    type: String,
    required: true,
  },
  controller: {
    type: null,
    default: null,
  },
  state: {
    type: String,
    default: QuestionEntryState.WRONG,
  },
});

// 默认使用原有的作业 store，亦可通过 props.controller 传入替代实现（降耦合）
const defaultStore = useHomeworkStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;
const eventBus = useEventBus();

const lkToastRef = ref();
const initiativeShow = ref(props.state === QuestionEntryState.MANUAL ? true : false);

// 悬浮题目组件状态
const showFloatingQuestion = ref(props.state === QuestionEntryState.MANUAL);

// 从控制器获取数据（统一抽象，避免直接依赖具体 store）
const question = computed(() => controller.getQuestion(props.questionId));
// 根据错题状态判断是否显示答案
const showWrongAnswer = computed(
  () => question.value?.backendData?.status === WrongAnswerType.PASSED
);
const readonly = computed(() => initiativeShow.value || showWrongAnswer.value);
const userAnswer = computed(() => controller.getUserAnswer(props.questionId));

const answerType = computed(() => controller.answerType as any);

// 获取题目进度信息（从重构前QuestionTitle迁移的逻辑）
const currentQuestionNumber = computed(() => controller.currentQuestionIndex + 1);
const questionsLength = computed(() => controller.currentBatchQuestions?.length);

// 判断值是否为空
const hasValue = (value: unknown): boolean => {
  if (value === null || value === undefined) {
    return false;
  }

  if (typeof value === 'string' && value.trim() === '') {
    return false;
  }

  if (Array.isArray(value) && value.length === 0) {
    return false;
  }

  if (typeof value === 'object' && Object.keys(value).length === 0) {
    return false;
  }

  return true;
};

// 将用户答案转换为checkAnswer接口需要的格式
const convertUserAnswerToStudentAnswers = (userAnswer: any, question: any): any[] => {
  if (!userAnswer || !userAnswer.answer) {
    return [];
  }

  let studentAnswer;
  const newUserAnswer = userAnswer.answer || userAnswer;

  if (question.type == QuestionType.MULTIPLE || question.type == QuestionType.SINGLE) {
    if (typeof newUserAnswer === 'string') {
      const answerIndex = question.options.findIndex((option: any) => option.id === newUserAnswer);
      answerIndex != -1 && (studentAnswer = [answerIndex.toString()]);
    } else if (Array.isArray(newUserAnswer)) {
      const answerArray: any[] = [];
      newUserAnswer.forEach((answer: any) => {
        const answerIndex = question.options.findIndex((option: any) => option.id === answer);
        answerIndex != -1 && answerArray.push(answerIndex.toString());
      });
      // 序列化数组
      studentAnswer = answerArray;
    }
  } else if (question.type == QuestionType.TRUE_FALSE) {
    studentAnswer = [newUserAnswer];
  } else if (question.type == QuestionType.FILL_BLANK) {
    // 填空题
    if (Array.isArray(newUserAnswer)) {
      studentAnswer = newUserAnswer;
    }
  }

  return studentAnswer || [];
};

const handleSubmit = async () => {
  // 获取当前用户答案
  const currentUserAnswer = controller.getUserAnswer(props.questionId);

  // 如果没有用户答案，直接返回
  if (!hasValue(currentUserAnswer) || !hasValue(currentUserAnswer?.answer)) {
    return lkToastRef.value?.show({
      message: '请先选择或填写答案',
      duration: 2000,
    });
  }

  try {
    // 显示加载状态
    uni.showLoading({
      title: '正在提交...',
    });

    // 简答题直接跳转到手动批改页面
    if (question.value?.backendData?.question?.answerType === AnswerType.ANSWER) {
      const currentUnderTakingQuestion = controller.currentUnderTakingQuestion;
      const questionId = currentUnderTakingQuestion?.id;

      // 为这次手动批改添加事件监听器
      const manualCorrectionEventName = `manualCorrectionResult_${questionId}`;

      // 先移除可能存在的旧监听器
      eventBus.off(manualCorrectionEventName, handleManualCorrectionResult);
      // 添加新的监听器
      addEventListenerWithCleanup(manualCorrectionEventName, handleManualCorrectionResult);

      // 通过页面间通信传递答案数据
      uni.navigateTo({
        url: `/pages-subpackages/students/error-book/question-sub-manual?state=${props.state}&questionId=${questionId}`,
        success: function (res: any) {
          // 通过 eventChannel 向被打开页面传送数据
          res.eventChannel.emit('receiveAnswerData', {
            questionId: props.questionId,
            userAnswer: currentUserAnswer,
            question: question.value,
            state: props.state,
          });
        },
      });
      return;
    }

    // 非简答题调用checkAnswer接口进行答案验证
    if (question.value?.type !== QuestionType.SHORT_ANSWER) {
      const studentAnswers = convertUserAnswerToStudentAnswers(currentUserAnswer, question.value);

      const checkParams: CheckAnswerParams = {
        questionId: Number(question.value?.backendData?.questionId),
        studentAnswers: studentAnswers,
      };

      const result = await checkAnswer(checkParams);

      const correctResult = result.isCorrect ? 1 : 0;

      // 使用store方法设置题目的submitQuestion数据
      controller.setQuestionSubmitQuestion?.(props.questionId, {
        answerResult: JSON.stringify(result.correctResult),
        correctResult,
      });

      // 可以在这里根据验证结果更新题目状态
      controller.setQuestionCorrectStatus?.(props.questionId, correctResult);
    }

    // 切换到显示状态
    initiativeShow.value = true;
  } catch (error) {
    console.error('提交答案失败:', error);
    lkToastRef.value?.show({
      message: '提交失败，请重试',
      duration: 2000,
    });
  } finally {
    uni.hideLoading();
  }
};

// ===== 悬浮题目组件事件处理 =====

/**
 * 关闭悬浮题目组件
 */
const handleCloseFloatingQuestion = () => {
  showFloatingQuestion.value = false;
};

/**
 * 悬浮组件位置改变
 */
const handleFloatingPositionChange = (_x: number, _y: number) => {
  // 可以保存位置信息到本地存储
  // console.log('悬浮组件位置改变:', { x, y });
};

// 存储事件监听器引用，用于清理
const eventListeners = ref<Array<{ event: string; handler: Function }>>([]);

// 添加事件监听器的辅助函数
const addEventListenerWithCleanup = (event: string, handler: Function) => {
  eventBus.on(event, handler);
  eventListeners.value.push({ event, handler });
};

// 监听手动批改结果
const handleManualCorrectionResult = (resultData: any) => {
  // 验证题目ID是否匹配
  if (resultData.questionId !== props.questionId) {
    return;
  }

  // 设置批改状态 - 使用 resultData.questionId 确保一致性
  if (resultData.isCorrect !== undefined) {
    const correctResult = resultData.isCorrect ? 1 : 0;

    // 设置题目正确性状态
    controller.setQuestionCorrectStatus?.(resultData.questionId, correctResult);

    // 重要：同时设置submitQuestion数据，确保答题卡能正确显示状态
    controller.setQuestionSubmitQuestion?.(resultData.questionId, {
      correctResult,
      // 简答题的answerResult可以设置为空数组或根据实际需要设置
      answerResult: '[]',
    });
  }

  // 只设置当前题目的显示状态，不影响全局
  if (resultData.showAnswer) {
    initiativeShow.value = true;
  }

  // 如果controller有设置批改状态的方法，也调用一下
  if (controller.setQuestionCorrectionStatus && resultData.correctionStatus) {
    controller.setQuestionCorrectionStatus?.(resultData.questionId, resultData.correctionStatus);
  }

  // 立即清理这个特定的事件监听器
  const eventName = `manualCorrectionResult_${resultData.questionId}`;
  eventBus.off(eventName, handleManualCorrectionResult);
};
</script>

<style lang="scss" scoped>
.question-renderer {
  margin-bottom: 30rpx;
  position: relative;
}

.question-not-found {
  padding: 20rpx;
  color: #ff4d4f;
  text-align: center;
  background-color: #fff1f0;
  border: 1rpx solid #ffccc7;
  border-radius: 4rpx;
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
}

.tag-icon {
  position: absolute;
  top: 0rpx;
  right: 10rpx;
  z-index: -1;
}
</style>
