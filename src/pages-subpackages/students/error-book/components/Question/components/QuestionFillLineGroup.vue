<template>
  <view class="question-fill-line-group">
    <!-- 渲染子组件（填空线） -->
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, provide, watch } from 'vue';
// 组件属性定义
const props = defineProps<{
  readonly?: boolean;
}>();

// 计算是否只读
const isReadonly = computed(() => props.readonly);

// 组件事件定义
const emit = defineEmits<{
  (e: 'update', data: { blankId: string | number; blankIndex: number; value: string }): void;
}>();

// 处理填空线更新 - 转发子组件的更新事件
const handleFillLineUpdate = (blankId: string | number, blankIndex: number, value: string) => {
  if (isReadonly.value) return;
  emit('update', { blankId, blankIndex, value });
};

// 提供处理方法给子组件
provide('fillLineGroup', {
  readonly: isReadonly.value,
  handleUpdate: handleFillLineUpdate,
});

// 监听只读状态变化，更新提供的值
// watch(
//   () => isReadonly.value,
//   () => {
//     provide('fillLineGroup', {
//       readonly: isReadonly.value,
//       handleUpdate: handleFillLineUpdate,
//     });
//   }
// );
</script>

<style lang="scss" scoped>
.question-fill-line-group {
  position: relative;
  width: 100%;
}
</style>
