<template>
  <MyExtraBlock title="笔记" @header-click="toggleNote">
    <template #header-right>
      <button
        v-if="controller.answerType === HomeworkTaskType.ONLINE"
        class="add-note-btn"
        @click.stop="onNoteClick"
      >
        {{ note?.noteId ? '编辑笔记' : '添加笔记' }}
      </button>
    </template>
    <template #default>
      <view class="note-content">
        <!-- <view class="note-text"> 记录你的解答思路，好记性不如烂笔头 </view> -->
        <mp-html :content="note?.note" />
        <MyEmpty v-if="!note?.note" :showImage="false" />
      </view>
    </template>
  </MyExtraBlock>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { useEventBus } from '@/hooks/useEventBus';
import { HomeworkEventNames } from '@/constants/students/homework';
import { QuestionEntryState, ErrorBookEventNames } from '@/constants/students/errorBook';
import MyExtraBlock from '@/pages-subpackages/students/homework/components/MyExtraBlock/index.vue';
import MyEmpty from '@/components/MyEmpty/index.vue';
import { getStudentsNote } from '@/api/students/homework';
import { HomeworkTaskType } from '@/constants/students/homework';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

// 获取事件总线
const eventBus = useEventBus();

// 定义组件属性：支持外部传入控制器，以便替换不同的 store 实现
const props = defineProps<{
  questionId: string;
  controller?: any;
  /** 页面类型，用于区分事件名称 */
  pageType?: QuestionEntryState;
}>();

// 默认使用原有的作业 store，亦可通过 props.controller 传入替代实现（降耦合）
const defaultStore = useHomeworkStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

// 从控制器获取数据（统一抽象，避免直接依赖具体 store）
const question = computed(() => controller.getQuestion(props.questionId));
const note = computed(() => controller.getQuestionNote(props.questionId));

// 控制笔记内容显示状态
const showContent = ref(true);

onMounted(() => {
  // 判断笔记是否存在，如果不存在初始化笔记
  if (!note.value && question.value?.backendData?.question?.studentsNotes) {
    const noteObj = {
      note: question.value.backendData.question.studentsNotes.noteContent,
      noteId: question.value.backendData.question.studentsNotes.id,
    };
    controller.setQuestionNote(props.questionId, noteObj);
  }
});

const getNote = async () => {
  try {
    const noteRes = await getStudentsNote({
      refId: question.value.backendData.questionId,
      type: 1,
    });
    const noteObj = {
      note: noteRes.noteContent,
      noteId: noteRes.id,
    };
    controller.setQuestionNote(props.questionId, noteObj);
  } catch (error) {
    console.error('获取笔记失败:', error);
  }
};

// 切换笔记显示状态
function toggleNote() {
  showContent.value = !showContent.value;
}

// 处理笔记按钮点击
function onNoteClick(event: Event) {
  // 根据页面类型发送不同的事件
  let eventName = HomeworkEventNames.OPEN_NOTE_POPUP; // 默认作业页面事件

  if (props.pageType === QuestionEntryState.WRONG) {
    eventName = ErrorBookEventNames.WRONG_OPEN_NOTE_POPUP;
  } else if (props.pageType === QuestionEntryState.RECOMMEND) {
    eventName = ErrorBookEventNames.RECOMMEND_OPEN_NOTE_POPUP;
  } else if (props.pageType === QuestionEntryState.MANUAL) {
    eventName = ErrorBookEventNames.MANUAL_OPEN_NOTE_POPUP;
  }

  eventBus.emit(eventName, props.questionId, note.value);
}
</script>

<style lang="scss" scoped>
.note-content {
  padding: 16rpx 0;

  font-size: 32rpx;
  line-height: 1.5;
  color: #000000;

  .note-text {
    font-size: 32rpx;
    line-height: 1.5;
    color: #000000;
    white-space: pre-wrap;
    word-break: break-all;
    margin-bottom: 20rpx;
  }
}

.add-note-btn {
  background-color: #ffffff;
  color: #7d4dff;
  font-size: 26rpx;
  font-weight: 500;
  border: 1px solid #7d4dff;
  border-radius: 100px;
  padding: 8rpx 20rpx;
  line-height: 1.6;
  margin-left: 12rpx;
  height: auto;
}
</style>
