<template>
  <MyExtraBlock title="试题详解" @header-click="toggleAnalysis">
    <template #header-right>
      <button
        v-if="answerType === HomeworkTaskType.ONLINE"
        class="feedback-btn"
        @click.stop="onFeedbackClick"
      >
        试题纠错
      </button>
    </template>
    <template #default>
      <view class="analysis-content" v-if="showContent">
        <view v-if="props.question?.analysis || hasAnswer">
          <!-- <view class="answer-section" v-if="hasAnswer">
            <text class="section-title">[答案] {{ formatAnswer }}</text>
          </view> -->
          <view class="analysis-section" v-if="props.question?.analysis">
            <mp-html :content="props.question?.analysis" />
          </view>
        </view>
        <!-- <view v-else class="empty-analysis">
          <text>暂无解析</text>
        </view> -->
        <MyEmpty :showImage="false" v-else />
      </view>
    </template>
  </MyExtraBlock>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { QuestionType, HomeworkTaskType, HomeworkEventNames } from '@/constants/students/homework';
import { QuestionEntryState, ErrorBookEventNames } from '@/constants/students/errorBook';
import { useEventBus } from '@/hooks/useEventBus';
import MyEmpty from '@/components/MyEmpty/index.vue';
import MyExtraBlock from '@/pages-subpackages/students/homework/components/MyExtraBlock/index.vue';

const props = defineProps<{
  questionId: string;
  question: any;
  answerType: HomeworkTaskType;
  showAnalysis?: boolean;
  /** 页面类型，用于区分事件名称 */
  pageType?: QuestionEntryState;
}>();

// 计算解析显示状态
const showAnalysis = computed(() => props.showAnalysis ?? false);

// 控制解析内容显示状态
const showContent = ref(showAnalysis.value);

// 切换解析显示状态
function toggleAnalysis() {
  showContent.value = !showContent.value;
}

// 获取事件总线
const eventBus = useEventBus();

// 解析按钮点击
function onFeedbackClick(event: Event) {
  event.stopPropagation();

  // 根据页面类型发送不同的事件
  let eventName = HomeworkEventNames.OPEN_FEEDBACK_POPUP; // 默认作业页面事件

  if (props.pageType === QuestionEntryState.WRONG) {
    eventName = ErrorBookEventNames.WRONG_OPEN_FEEDBACK_POPUP;
  } else if (props.pageType === QuestionEntryState.RECOMMEND) {
    eventName = ErrorBookEventNames.RECOMMEND_OPEN_FEEDBACK_POPUP;
  } else if (props.pageType === QuestionEntryState.MANUAL) {
    eventName = ErrorBookEventNames.MANUAL_OPEN_FEEDBACK_POPUP;
  }

  eventBus.emit(eventName, props.questionId);
}

// 判断是否有答案
const hasAnswer = computed(() => {
  if (!props.question) return false;
  switch (props.question.type) {
    case QuestionType.SINGLE:
      return !!props.question.answer;
    case QuestionType.MULTIPLE:
      return !!(props.question.answers && props.question.answers.length > 0);
    case QuestionType.SHORT_ANSWER:
      return !!props.question.answer;
    default:
      return false;
  }
});
</script>

<style lang="scss" scoped>
.analysis-content {
  padding: 16rpx 0;
  font-size: 32rpx;
  line-height: 1.5;
  color: #000000;

  .answer-section {
    margin-bottom: 16rpx;

    .section-title {
      font-weight: 500;
      margin-bottom: 8rpx;
      display: block;
    }
  }

  .analysis-section {
    white-space: pre-wrap;
    word-break: break-all;
  }

  .empty-analysis {
    text-align: center;
    color: #999;
    padding: 30rpx 0;
  }

  :deep(.analysis-tag) {
    font-weight: 500;
    margin-top: 16rpx;
    margin-bottom: 8rpx;
    display: block;
  }
}

.feedback-btn {
  background-color: #ffffff;
  color: #4e5969;
  font-size: 26rpx;
  font-weight: 500;
  border: 1px solid #dcdcdc;
  border-radius: 100px;
  padding: 8rpx 20rpx;
  line-height: 1.6;
  margin-left: 12rpx;
  height: auto;
}
</style>
