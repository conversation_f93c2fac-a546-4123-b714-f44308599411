<template>
  <movable-area
    class="draggable-float-container"
    :style="{
      'z-index': zIndex,
      position: 'fixed',
      top: 0,
      left: 0,
      width: movableAreaBounds.width + 'px',
      height: movableAreaBounds.height + 'px',
      'pointer-events': 'none',
    }"
  >
    <movable-view
      class="draggable-float-view"
      :x="x"
      :y="y"
      direction="all"
      :out-of-bounds="false"
      :damping="20"
      :friction="2"
      :scale="false"
      :animation="true"
      @change="handleChange"
      @touchend="handleTouchEnd"
      @touchstart.stop="handleTouchStart"
      @touchmove.stop="handleTouchMove"
      :style="{
        'pointer-events': 'auto',
        width: componentSizes.width,
        height: isExpanded ? componentSizes.expandedMaxHeight : componentSizes.normalHeight,
      }"
    >
      <view class="float-content" @tap="handleContentClick">
        <view class="float-header">
          <text class="float-title">答案</text>
          <view class="float-close" @tap.stop="handleClose">
            <text class="close-icon">×</text>
          </view>
        </view>

        <view class="float-body">
          <!-- 答案内容 -->
          <view class="answer-content" :class="{ expanded: isExpanded }">
            <view class="answer-wrapper">
              <mp-html
                v-if="answerContent"
                :content="answerContent"
                :domain="domain"
                class="answer-html"
              />
              <text v-else class="no-content">暂无答案</text>
            </view>

            <!-- 展开/收起按钮 -->
            <view v-if="answerContent" class="expand-toggle" @tap.stop="toggleExpanded">
              <image
                :src="isExpanded ? '/static/chat/chevron-up.svg' : '/static/chat/chevron-down.svg'"
                class="expand-icon"
              />
            </view>
          </view>
        </view>
      </view>
    </movable-view>
  </movable-area>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import type { FrontQuestion } from '@/types/students/homework';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { responsiveSize, responsiveFontSize } from '@/utils/responsiveSize';

// 组件属性
const props = withDefaults(
  defineProps<{
    /** 题目数据 */
    question?: FrontQuestion | null;
    /** 层级 */
    zIndex?: number;
    /** 初始位置 */
    initialPosition?: [number, number];
    /** 是否显示 */
    show?: boolean;
  }>(),
  {
    question: null,
    zIndex: 9999,
    initialPosition: () => [20, 100],
    show: true,
  }
);

// 事件定义
const emit = defineEmits<{
  close: [];
  positionChange: [x: number, y: number];
}>();

// 设备检测
const { isTablet, isMobile, getResponsiveSize } = useDeviceDetection();

// 位置状态
const x = ref(props.initialPosition[0]);
const y = ref(props.initialPosition[1]);
const isDragging = ref(false);

// 展开状态
const isExpanded = ref(false);

// 设置mp-html的domain
const domain = ref('');

// 响应式尺寸计算
const componentSizes = computed(() => ({
  width: responsiveSize(250, 'rpx'), // 减小宽度
  normalHeight: responsiveSize(80, 'rpx'), // 减小正常状态高度
  expandedMaxHeight: getResponsiveSize('30vh', '35vh', '40vh'), // 减小展开状态最大高度
  titleFontSize: responsiveFontSize(26, 'rpx'), // 稍微减小标题字体
  contentFontSize: responsiveFontSize(26, 'rpx'), // 稍微减小内容字体
  iconSize: responsiveSize(20, 'rpx'), // 减小图标尺寸
  padding: responsiveSize(16, 'rpx'), // 减小内边距
  headerPadding: responsiveSize(12, 'rpx'), // 减小头部内边距
  borderRadius: responsiveSize(10, 'rpx'), // 稍微减小圆角
}));

// movable-area 的有效区域计算
const movableAreaBounds = computed(() => {
  const { windowWidth, windowHeight, safeArea, statusBarHeight } = uni.getSystemInfoSync();
  const currentSize = getCurrentComponentSize();
  const safeDistance = 10; // 减小安全距离，让组件更贴近边缘

  // 计算顶部安全区域（状态栏 + 导航栏）
  const topSafeArea = Math.max(safeArea?.top || 0, statusBarHeight || 0) + 44; // 44rpx 为导航栏高度

  return {
    width: windowWidth,
    height: windowHeight,
    // movable-view 可移动的范围，确保组件完全在屏幕内
    maxX: windowWidth - currentSize.width - safeDistance,
    maxY: windowHeight - currentSize.height - safeDistance,
    minX: safeDistance,
    minY: topSafeArea + safeDistance,
  };
});

// 计算属性
const answerContent = computed(() => {
  if (!props.question) return '';

  // 获取答案内容
  const answer = props.question.backendData?.question?.answer;
  return answer || '';
});

// 处理触摸开始事件
const handleTouchStart = (e: any) => {
  // 阻止事件冒泡，防止影响底层的 swiper
  e.stopPropagation();
};

// 处理触摸移动事件
const handleTouchMove = (e: any) => {
  // 阻止事件冒泡，防止影响底层的 swiper
  e.stopPropagation();
};

// 获取当前组件尺寸（考虑展开状态）
const getCurrentComponentSize = () => {
  const { windowHeight } = uni.getSystemInfoSync();
  const baseWidth = parseInt(componentSizes.value.width);
  const expandedMaxHeight = componentSizes.value.expandedMaxHeight;
  const baseHeight = isExpanded.value
    ? Math.min(
        windowHeight * 0.6,
        (parseInt(String(expandedMaxHeight).replace('vh', '')) * windowHeight) / 100
      )
    : parseInt(componentSizes.value.normalHeight);

  return {
    width: uni.upx2px(baseWidth),
    height: isExpanded.value ? baseHeight : uni.upx2px(baseHeight),
  };
};

// 获取边界限制
const getBoundaryLimits = () => {
  const { windowWidth, windowHeight, safeArea, statusBarHeight } = uni.getSystemInfoSync();
  const currentSize = getCurrentComponentSize();
  const safeDistance = 10; // 减小安全距离

  // 计算顶部安全区域（状态栏 + 导航栏）
  const topSafeArea = Math.max(safeArea?.top || 0, statusBarHeight || 0) + 44; // 44rpx 为导航栏高度

  return {
    minX: safeDistance,
    maxX: windowWidth - currentSize.width - safeDistance,
    minY: topSafeArea + safeDistance,
    maxY: windowHeight - currentSize.height - safeDistance,
  };
};

// 展开/收起切换
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;

  // 展开后需要重新检查边界
  nextTick(() => {
    const limits = getBoundaryLimits();
    if (x.value > limits.maxX) x.value = limits.maxX;
    if (y.value > limits.maxY) y.value = limits.maxY;
    if (x.value < limits.minX) x.value = limits.minX;
    if (y.value < limits.minY) y.value = limits.minY;

    emit('positionChange', x.value, y.value);
  });
};

// 处理拖动事件
const handleChange = (e: any) => {
  const { x: newX, y: newY } = e.detail;

  if (!isDragging.value) {
    isDragging.value = true;
  }

  // 更新位置状态
  x.value = newX;
  y.value = newY;
  emit('positionChange', newX, newY);
};

// 处理触摸结束事件，实现自动吸附
const handleTouchEnd = () => {
  const bounds = movableAreaBounds.value;
  const threshold = bounds.width * 0.5;

  // 重置拖拽状态
  isDragging.value = false;

  setTimeout(() => {
    // 水平方向自动吸附到边缘
    if (x.value > threshold) {
      x.value = bounds.maxX;
    } else {
      x.value = bounds.minX;
    }

    // 垂直方向边界检测
    if (y.value < bounds.minY) {
      y.value = bounds.minY;
    } else if (y.value > bounds.maxY) {
      y.value = bounds.maxY;
    }

    emit('positionChange', x.value, y.value);
  }, 100);
};

// 处理内容点击
const handleContentClick = () => {
  // 点击内容区域不做任何操作，避免误触
};

// 处理关闭
const handleClose = () => {
  emit('close');
};

// 组件挂载时设置初始位置
onMounted(() => {
  const { windowWidth } = uni.getWindowInfo();
  const currentSize = getCurrentComponentSize();
  const safeDistance = 10; // 减小安全距离

  // 默认吸附到右侧
  x.value = windowWidth - currentSize.width - safeDistance;
  y.value = props.initialPosition[1];

  // 确保初始位置在边界内
  const limits = getBoundaryLimits();
  if (y.value < limits.minY) y.value = limits.minY;
  if (y.value > limits.maxY) y.value = limits.maxY;
});
</script>

<style lang="scss" scoped>
.draggable-float-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
}

.draggable-float-view {
  pointer-events: auto;
}

.float-content {
  width: v-bind('componentSizes.width');
  max-width: v-bind('componentSizes.width');
  background: rgba(255, 255, 255, 0.95);
  border-radius: v-bind('componentSizes.borderRadius');
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.float-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: v-bind('componentSizes.headerPadding') v-bind('componentSizes.padding');
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.float-title {
  font-size: v-bind('componentSizes.titleFontSize');
  font-weight: 500;
}

.float-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.close-icon {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1;
}

.float-body {
  padding: v-bind('componentSizes.padding');
  position: relative;
}

.answer-content {
  position: relative;
  transition: all 0.3s ease-in-out;
  min-height: 50rpx; // 设置最小高度

  &:not(.expanded) {
    max-height: 60rpx; // 正常状态下更小的高度
    overflow: hidden;
    padding-bottom: 50rpx; // 为展开按钮留出空间
  }

  &.expanded {
    max-height: v-bind('componentSizes.expandedMaxHeight');
    overflow-y: auto;
    padding-bottom: 50rpx; // 为展开按钮留出空间

    // 添加滚动条样式
    &::-webkit-scrollbar {
      width: 4rpx;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 2rpx;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(102, 126, 234, 0.3);
      border-radius: 2rpx;

      &:hover {
        background: rgba(102, 126, 234, 0.5);
      }
    }
  }
}

.answer-wrapper {
  // 正常状态下只显示1行内容，溢出隐藏
  .answer-content:not(.expanded) & {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
  }
}

.expand-toggle {
  position: absolute;
  bottom: 6rpx;
  right: 6rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  box-shadow: 0 2rpx 12rpx rgba(102, 126, 234, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: scale(1.15);
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }
}

.expand-icon {
  width: v-bind('componentSizes.iconSize');
  height: v-bind('componentSizes.iconSize');
  transition: transform 0.3s ease;
  filter: brightness(0) invert(1); // 让图标变为白色
}

.answer-html {
  font-size: v-bind('componentSizes.contentFontSize');
  line-height: 1.5;
  color: #333;
  word-wrap: break-word;
  word-break: break-all;
}

.no-content {
  font-size: v-bind('componentSizes.contentFontSize');
  color: #999;
  font-style: italic;
  text-align: center;
  padding: v-bind('componentSizes.padding') 0;
}

/* 平板端优化 */
@media (min-width: 768px) {
  .expand-toggle {
    width: 48rpx;
    height: 48rpx;

    &:hover {
      transform: scale(1.2);
    }
  }

  .answer-content.expanded {
    /* 平板端展开时显示更明显的滚动条 */
    &::-webkit-scrollbar {
      width: 8rpx;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.08);
      border-radius: 4rpx;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(102, 126, 234, 0.4);
      border-radius: 4rpx;

      &:hover {
        background: rgba(102, 126, 234, 0.6);
      }
    }
  }
}
</style>
