<template>
  <view class="question-selection-group">
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import { provide, watch, onMounted, computed } from 'vue';
import type { SelectionStatus } from './QuestionSelection.vue';

// 定义SelectionGroup提供的上下文接口
export interface SelectionGroupContext {
  handleSelectionClick: (value: string) => void;
  isSelectionSelected: (value: string) => boolean;
  getSelectionStatus: (value: string) => 'correct' | 'incorrect' | 'normal';
  disabled: ComputedRef<boolean>;
}

// 定义属性
const props = defineProps<{
  modelValue: string | string[];
  multiple?: boolean;
  disabled?: boolean;
  // 添加正确答案属性
  correctAnswer?: string | string[];
  // 添加最大选择数量限制
  maxCount?: number;
  /**是否显示答案 */
  showAnswer?: boolean;
  /**检查回答内容是否正确 */
  checkAnswer?: boolean;
}>();

// 计算禁用状态，考虑 showAnswer
const isDisabled = computed(() => props.disabled || props.showAnswer);

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | string[]): void;
}>();

// 处理选项点击
const handleSelectionClick = (id: string) => {
  if (isDisabled.value) return;

  // //console.log("选项点击:", id, "当前值:", props.modelValue);

  if (props.multiple) {
    // 确保modelValue始终是数组
    const newValue = [...(Array.isArray(props.modelValue) ? props.modelValue : [])];
    const index = newValue.indexOf(id);

    if (index === -1) {
      // 检查是否已达到最大选择数量
      if (props.maxCount && newValue.length >= props.maxCount) {
        // 已达到最大选择数量，显示提示
        // uni.showToast({
        //   title: `最多只能选择${props.maxCount}项`,
        //   icon: 'none',
        //   duration: 2000
        // });
        return; // 不添加新选项
      }
      newValue.push(id);
    } else {
      newValue.splice(index, 1);
    }

    // //console.log("多选更新后的值:", newValue);
    emit('update:modelValue', newValue);
  } else {
    // 单选：如果点击的是已选中的选项，则取消选中
    if (props.modelValue === id) {
      // //console.log("单选取消选中:", id);
      emit('update:modelValue', '');
    } else {
      // //console.log("单选更新后的值:", id);
      emit('update:modelValue', id);
    }
  }
};

// 判断选项是否被选中
const isSelectionSelected = (id: string) => {
  if (props.multiple) {
    // 确保在多选模式下，即使modelValue不是数组也能正常工作
    const isSelected = Array.isArray(props.modelValue) && props.modelValue.includes(id);
    // //console.log("多选选项状态检查:", id, isSelected, props.modelValue);
    return isSelected;
  }
  const isSelected = props.modelValue === id;
  // //console.log('单选选项状态检查:', id, isSelected, props.modelValue);
  return isSelected;
};

// 获取选项状态
const getSelectionStatus = (id: string): SelectionStatus => {
  // 如果没有正确答案信息，不显示状态
  if (!props.correctAnswer || props.correctAnswer.length === 0) {
    return 'normal';
  }

  // 判断是否为正确答案
  const isCorrect = props.multiple
    ? Array.isArray(props.correctAnswer) && props.correctAnswer.includes(id)
    : props.correctAnswer === id;

  // 判断是否被选中
  const isSelected = isSelectionSelected(id);

  //console.log(id, isSelected);
  // 当 showAnswer 开启时，显示正确答案和错误选择
  if (props.showAnswer) {
    // 如果是正确答案，显示正确状态
    if (isCorrect) {
      return 'correct';
    }

    // 如果被错误选中，显示错误状态
    if (isSelected && !isCorrect) {
      return 'incorrect';
    }
  }

  // 当 checkAnswer 开启时，只显示错误选择（不显示正确答案）
  if (props.checkAnswer && isSelected) {
    if (isCorrect) {
      return 'correct';
    }
    return 'incorrect';
  }
  // 普通状态
  return 'normal';
};

// 组件挂载时记录初始值
onMounted(() => {
  // //console.log(
  //   "SelectionGroup挂载, 初始值:",
  //   props.modelValue,
  //   "多选模式:",
  //   props.multiple
  // );
});

// 监听modelValue变化，确保数据格式正确
watch(
  () => props.modelValue,
  (newVal: string | string[]) => {
    // //console.log("SelectionGroup值变化:", newVal);

    // 多选模式下确保modelValue是数组
    if (props.multiple && !Array.isArray(newVal)) {
      // console.warn(
      //   "QuestionSelectionGroup: multiple模式下modelValue应该是数组"
      // );
      // 如果不是数组，转换为数组
      if (newVal) {
        // //console.log("将单值转换为数组:", [newVal]);
        emit('update:modelValue', [newVal]);
      } else {
        // //console.log("设置为空数组");
        emit('update:modelValue', []);
      }
    }
  },
  { immediate: true }
);

// 提供给子组件的上下文
provide<SelectionGroupContext>('selection-group', {
  handleSelectionClick,
  isSelectionSelected,
  getSelectionStatus,
  disabled: isDisabled,
});

// 监听禁用状态变化
// watch(
//   () => isDisabled.value,
//   () => {
//     // 重新提供上下文
//     provide<SelectionGroupContext>('selection-group', {
//       handleSelectionClick,
//       isSelectionSelected,
//       getSelectionStatus,
//       disabled: isDisabled.value,
//     });
//   }
// );
</script>

<style lang="scss" scoped>
.question-selection-group {
  width: 100%;
  display: flex;
  flex-direction: column;
}
</style>
