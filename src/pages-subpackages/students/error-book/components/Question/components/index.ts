// 导入所有问题组件
import QuestionTitle from './QuestionTitle.vue';
import QuestionNote from './QuestionNote.vue';
import QuestionAnalysis from './QuestionAnalysis.vue';
import QuestionSelection from './QuestionSelection.vue';
import QuestionSelectionGroup from './QuestionSelectionGroup.vue';
import QuestionTextarea from './QuestionTextarea.vue';
import QuestionInput from './QuestionInput.vue';
import QuestionRenderer from './QuestionRenderer.vue';
import QuestionFillLine from './QuestionFillLine.vue';
import QuestionFillLineGroup from './QuestionFillLineGroup.vue';
import QuestionSubItem from './QuestionSubItem.vue';

// 导出所有组件
export {
  QuestionTitle,
  QuestionNote,
  QuestionAnalysis,
  QuestionSelection,
  QuestionSelectionGroup,
  QuestionTextarea,
  QuestionInput,
  QuestionRenderer,
  QuestionFillLine,
  QuestionFillLineGroup,
  QuestionSubItem,
};

// 组件映射表
export const questionComponents = {
  QuestionTitle,
  QuestionNote,
  QuestionAnalysis,
  QuestionSelection,
  QuestionSelectionGroup,
  QuestionTextarea,
  QuestionInput,
  QuestionRenderer,
  QuestionFillLine,
  QuestionFillLineGroup,
  QuestionSubItem,
};

// 默认导出组件映射表
export default questionComponents;
