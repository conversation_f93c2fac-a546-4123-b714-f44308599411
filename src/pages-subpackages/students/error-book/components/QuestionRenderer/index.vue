<template>
  <view class="question-renderer">
    <!-- 添加统一的题目标题 -->
    <QuestionTitle :questionId="questionId" />
    <!-- 复合题模板 -->
    <CompositeTemplate
      v-if="question.subQuestions && question.subQuestions.length > 0"
      :questionId="questionId"
      :readonly="readonly"
    />
    <!-- 选项题模板 -->
    <SelectionTemplate
      v-else-if="
        question.type === QuestionType.SINGLE ||
        question.type === QuestionType.MULTIPLE ||
        question.type === QuestionType.TRUE_FALSE
      "
      :questionId="questionId"
      :readonly="readonly"
    />

    <!-- 填空题模板 -->
    <BlankTemplate
      v-else-if="
        question.type === QuestionType.FILL_BLANK ||
        question.type === QuestionType.CLOZE ||
        question.type === QuestionType.GRAPHIC_FILL
      "
      :questionId="questionId"
      :readonly="readonly"
    />

    <!-- 文本区模板 -->
    <TextareaTemplate
      v-else-if="question.type === QuestionType.SHORT_ANSWER"
      :questionId="questionId"
      :readonly="readonly"
    />

    <!-- 排序题模板 -->

    <!-- 匹配题模板 -->

    <view class="question-not-found" v-else>
      <text>未找到对应题型的渲染组件</text>
    </view>

    <view class="submit-btn" v-if="!readonly">
      <LkButton type="secondary" shape="round" @tap="handleSubmit"> 提交作答 </LkButton>
    </view>
    <!-- 题目解析 -->
    <QuestionAnalysis v-if="!!readonly" showAnalysis :questionId="questionId" />

    <!-- 题目笔记 -->
    <QuestionNote v-if="!!readonly" :questionId="questionId" />

    <LkSvg
      v-if="showWrongAnswer"
      width="140rpx"
      height="140rpx"
      src="/static/study/passed_inspection.svg"
      customClass="tag-icon"
    />

    <LkSvg
      v-else
      width="140rpx"
      height="140rpx"
      src="/static/study/failed_inspection.svg"
      customClass="tag-icon"
    />

    <LkToast ref="lkToastRef" />
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, type Ref } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import {
  QuestionTitle,
  QuestionAnalysis,
  QuestionNote,
} from '@/pages-subpackages/students/homework/components/Question/components';
import {
  getTemplateType,
  SelectionTemplate,
  BlankTemplate,
  TextareaTemplate,
  CompositeTemplate,
} from '../templates';
import { QuestionType } from '@/constants/students/homework';
import { WrongAnswerType, QuestionEntryState } from '@/constants/students/errorBook';

// 定义组件属性
const props = defineProps({
  questionId: {
    type: String,
    required: true,
  },
  state: {
    type: String,
    default: QuestionEntryState.WRONG,
  },
});

// 使用store
const homeworkStore = useHomeworkStore();

const lkToastRef = ref();
const initiativeShow = ref(props.state === QuestionEntryState.RECOMMEND ? true : false);

// 从store获取数据
const question = computed(() => homeworkStore.getQuestion(props.questionId));
// 根据错题状态判断是否显示答案
const showWrongAnswer = computed(
  () => question.value?.backendData?.status === WrongAnswerType.PASSED
);
const readonly = computed(() => initiativeShow.value || showWrongAnswer.value);
const userAnswer = computed(() => homeworkStore.getUserAnswer(props.questionId));

const handleSubmit = () => {
  // 如果没有用户答案，直接返回
  if (!userAnswer.value || !userAnswer.value?.answer) {
    return lkToastRef.value?.show({
      message: '请先选择或填写答案',
      duration: 2000,
    });
  }
  initiativeShow.value = true;
};

// 组件挂载时记录初始值
onMounted(() => {});
</script>

<style lang="scss" scoped>
.question-renderer {
  margin-bottom: 30rpx;
  position: relative;
}

.question-not-found {
  padding: 20rpx;
  color: #ff4d4f;
  text-align: center;
  background-color: #fff1f0;
  border: 1rpx solid #ffccc7;
  border-radius: 4rpx;
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
}

.tag-icon {
  position: absolute;
  top: -15rpx;
  right: -20rpx;
  z-index: -1;
}
</style>
