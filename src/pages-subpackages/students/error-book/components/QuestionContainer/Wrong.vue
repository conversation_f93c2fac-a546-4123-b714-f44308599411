<template>
  <view class="answer-question-container">
    <!-- 作业内容 -->
    <view class="homework-box">
      <HomeworkOnline
        v-if="controller.answerType === HomeworkTaskType.ONLINE"
        :controller="controller"
      />
      <HomeworkPaper
        v-else-if="controller.answerType === HomeworkTaskType.PAPER"
        :controller="controller"
      />
      <!-- <HomeworkWriting v-else-if="controller.answerType === HomeworkTaskType.WRITING" :controller="controller" /> -->
    </view>
    <!-- 工具栏，仅答题tab显示 -->
    <view class="tools-box">
      <view class="left-group">
        <view class="answer-card-btn" @click="showAnswerCard">
          <LkSvg width="44rpx" height="44rpx" src="/static/homework/answer_card.svg" />
          <text>答题卡</text>
        </view>
      </view>
      <view class="right-group">
        <view v-if="!isPassedErrorPage" class="right-group-item">
          <LkButton class="homework-btn default-btn" block type="secondary" @tap="onRecommend">
            推荐练习
          </LkButton>
        </view>
        <view class="right-group-item">
          <LkButton class="homework-btn" block type="primary" @click="onSubmit"> 完成 </LkButton>
        </view>
      </view>
    </view>

    <!-- 答题卡弹出层 -->
    <PopupAnswerCard
      ref="answerCardPopupRef"
      v-model:open="isAnswerCardOpen"
      :controller="controller"
      @clearAnswers="clearAnswers"
      @submit="onSubmit"
      readonly
    />

    <!-- 解析纠错弹出层 -->
    <PopupFeedback
      ref="feedbackPopupRef"
      :questionId="currentQuestionId"
      :controller="controller"
    />

    <!-- 笔记弹出层 -->
    <PopupNote ref="notePopupRef" :questionId="currentQuestionId" :controller="controller" />

    <!-- 上传弹出层 -->
    <MyUploadPopup
      ref="uploadPopupRef"
      @upload-success="handleUploadSuccess"
      @upload-fail="handleUploadFail"
    />

    <!-- 绘图弹出层 -->
    <PopupDrawingBoard
      ref="drawingBoardPopupRef"
      :questionId="currentQuestionId"
      :subQuestionId="currentSubQuestionId"
      :controller="controller"
    />
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch, nextTick, reactive } from 'vue';
import HomeworkOnline from '../HomeworkCategory/HomeworkOnline.vue';
import HomeworkPaper from '../HomeworkCategory/HomeworkPaper.vue';
import { PopupFeedback, PopupNote } from '@/pages-subpackages/students/homework/components';
import PopupDrawingBoard from '../PopupDrawingBoard/index.vue';
import PopupAnswerCard from '../PopupAnswerCard/index.vue';
import { useEventBus } from '@/hooks/useEventBus';
import useVirtualSwiper from '@/hooks/useVirtualSwiper';
import LkSvg from '@/components/svg/index.vue';
import {
  HomeworkEventNames,
  HomeworkStatus,
  HomeworkTaskType,
} from '@/constants/students/homework';
import { WrongAnswerType, ErrorBookEventNames } from '@/constants/students/errorBook';
import { LkButton } from '@/components';
import { showDialogInfo } from '@/components/MyDialog';
import MyUploadPopup, { type UploadFileData } from '@/components/MyUploadPopup';
import MyEmpty from '@/components/MyEmpty/index.vue';
import { showDialogDanger } from '@/components/MyDialog';
import { useWrongStore } from '@/store/wrongStore';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

const props = defineProps({
  controller: {
    type: null,
    default: null,
  },
  status: {
    type: Number,
    default: undefined,
  },
});

const defaultStore = useWrongStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

const eventBus = useEventBus();
const questions = computed(() => controller.questions);
const currentUnderTakingQuestion = computed(() => controller.currentUnderTakingQuestion);

const answerCardPopupRef = ref(null);
const feedbackPopupRef = ref(null);
const notePopupRef = ref(null);
const homeworkComponentRef = ref(null);
const isAnswerCardOpen = ref(false);
const currentQuestionId = ref('');
const uploadPopupRef = ref<InstanceType<typeof MyUploadPopup> | null>(null);
const drawingBoardPopupRef = ref<InstanceType<typeof PopupDrawingBoard> | null>(null);
const currentCorrectionIndex = ref(controller.batches.length - 1);
const userAnswers = computed(() => controller.userAnswers);

const allAnswered = computed(
  () => controller.answeredCount === questions.value.length && questions.value.length > 0
);
const status = computed(() => controller.backendData.status);

// 判断是否为已过关错题页面（status=2表示已过关错题）
const isPassedErrorPage = computed(() => {
  return props.status === 2;
});

const initEvents = () => {
  // 监听纠错弹框事件 - 使用错题本专用事件名称
  eventBus.on(ErrorBookEventNames.WRONG_OPEN_FEEDBACK_POPUP, handleFeedbackEvent);

  // 监听笔记弹框事件 - 使用错题本专用事件名称
  eventBus.on(ErrorBookEventNames.WRONG_OPEN_NOTE_POPUP, handleNoteEvent);

  // 监听上传文件弹框事件
  eventBus.on(HomeworkEventNames.OPEN_UPLOAD_POPUP, handleUploadPopupEvent);

  // 监听绘图弹框事件
  eventBus.on(ErrorBookEventNames.WRONG_OPEN_DRAWING_BOARD_POPUP, openDrawingBoardPopup);
};
initEvents();
// 在组件挂载时注册事件监听
onMounted(() => {
  // 初始化逻辑
});

// 在组件卸载时移除事件监听，避免内存泄漏
onUnmounted(() => {
  eventBus.off(ErrorBookEventNames.WRONG_OPEN_FEEDBACK_POPUP);
  eventBus.off(ErrorBookEventNames.WRONG_OPEN_NOTE_POPUP);
  eventBus.off(HomeworkEventNames.OPEN_UPLOAD_POPUP, handleUploadPopupEvent);
  eventBus.off(ErrorBookEventNames.WRONG_OPEN_DRAWING_BOARD_POPUP, openDrawingBoardPopup);
});

const onSubmit = async () => {
  // if (controller.answerType === HomeworkTaskType.PAPER) {
  //   // 纸质作业
  //   // 检查附件是否存在且是否有错误
  //   let files = [];

  //   files = controller.currentBatch?.files || [];
  //   // 检查附件是否存在
  //   if (files && files.length === 0) {
  //     showDialogInfo({
  //       content: '请上传图片',
  //     });
  //     return;
  //   }
  //   // 检查附件是否存在错误
  //   const isAllRight = files.every(files => !files.error);
  //   console.log('isAllRight', files);
  //   if (!isAllRight) {
  //     showDialogInfo({
  //       content: '存在未识别的内容',
  //     });
  //     return;
  //   }
  // } else {
  //   // 在线作业
  //   // 验证是否完成所有题目
  //   // if (!allAnswered.value) {
  //   //   showDialogInfo({
  //   //     content: '请完成所有题目',
  //   //   });
  //   //   return;
  //   // }
  // }

  uni.navigateBack();

  // let confirmContent = `确定要提交作业吗？`;
  // // 获取未答题的数量
  // const unAnsweredCount = questions.value.length - controller.answeredCount;
  // if (controller.answerType == HomeworkTaskType.ONLINE && unAnsweredCount > 0) {
  //   confirmContent = `还有${unAnsweredCount}道题未完成，确定要提交作业吗？`;
  // }

  // await showDialogConfirm({
  //   title: '提示',
  //   content: confirmContent,
  //   async onConfirm() {
  //     try {
  //       // uni.showLoading({
  //       //   title: '提交中...',
  //       // });

  //       // 待批改时，提交作业，status=0 更新，否则status=1 提交
  //       const status = controller.status === HomeworkStatus.TO_BE_CORRECTED ? 0 : 1;
  //       // 使用store中的公共保存逻辑
  //       const result = await controller.saveHomework({ status });

  //       if (result.success) {
  //         // setTimeout(() => {
  //         //   showDialogInfo({
  //         //     content: '提交成功',
  //         //     onConfirm() {
  //         //       controller.setAutoSaveAnswer(false);
  //         //       // 刷新页面
  //         //       // uni.redirectTo({
  //         //       //   url: '/pages-subpackages/students/homework/index',
  //         //       // });
  //         //       uni.redirectTo({
  //         //         url: `/pages-subpackages/students/homework/submit-success?homeworkId=${controller.backendData.homeworkId}&subTitle=${controller.backendData.subjectName}&title=${controller.backendData.name}`,
  //         //       });
  //         //     },
  //         //   });
  //         // }, 500);
  //       }
  //     } catch (error) {
  //       console.error('提交作业失败:', error);
  //     } finally {
  //       // uni.hideLoading();
  //     }
  //   },
  // });
};

function showAnswerCard() {
  isAnswerCardOpen.value = true;
}

// 处理解析纠错事件
function handleFeedbackEvent(questionId: string) {
  currentQuestionId.value = questionId;
  if (feedbackPopupRef.value) {
    // @ts-ignore - 忽略类型检查错误
    feedbackPopupRef.value.open();
  }
}

// 处理笔记事件
function handleNoteEvent(questionId: string, note: any) {
  currentQuestionId.value = questionId;

  if (notePopupRef.value) {
    // @ts-ignore - 忽略类型检查错误
    notePopupRef.value.open({
      ...note,
    });
  }
}

// 清空答题记录
async function clearAnswers() {
  try {
    const confirmed = await showDialogDanger({
      title: '提示',
      content: '清空答题记录将不可恢复,确定清空全部答题记录吗?',
    });

    if (confirmed) {
      // 调用工具函数清空答题记录
      await controller.clearUserAnswers();
    }
  } catch (error) {
    console.error('清空答题记录出错:', error);
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    });
  }
}

// 当前上传的子题ID
const currentSubQuestionId = ref<string>();

// 处理上传弹窗事件
function handleUploadPopupEvent(data: { questionId: string; subQuestionId: string | null }) {
  // 存储当前上传的题目信息
  currentQuestionId.value = data.questionId;
  currentSubQuestionId.value = data.subQuestionId || '';
  // 打开上传弹窗
  uploadPopupRef.value?.open();
}

function handleUploadSuccess(fileData: UploadFileData) {
  console.log('handleUploadSuccess', fileData);

  // 触发上传成功事件，通知对应的题目组件
  // eventBus.emit(HomeworkEventNames.UPLOAD_SUCCESS, {
  //   fileData,
  //   questionId: currentQuestionId.value,
  //   subQuestionId: currentSubQuestionId.value,
  // });

  const currentAnswer = controller.getUserAnswer(currentQuestionId.value)?.answer || {};

  const newAnswer = {
    ...currentAnswer,
    files: [...(currentAnswer.files || []), fileData],
  };
  controller.setUserAnswer(currentQuestionId.value, newAnswer);
}

function handleUploadFail(error: any) {
  console.error('handleUploadFail', error);

  uni.showToast({
    title: '上传失败',
    icon: 'none',
  });
}

// 处理绘图弹框事件
function openDrawingBoardPopup(data: { parentQuestionId: string; subQuestionId: string | null }) {
  currentQuestionId.value = data.parentQuestionId;
  // 处理subQuestionId可能为null的情况
  currentSubQuestionId.value = data.subQuestionId || '';
  drawingBoardPopupRef.value?.open();
}

function onRecommend() {
  uni.navigateTo({
    url: `/pages-subpackages/students/error-book/recommend-contact?questionId=${currentUnderTakingQuestion.value?.backendData?.questionId}&errorId=${currentUnderTakingQuestion.value?.backendData.id}`,
  });
}
</script>

<style lang="scss" scoped>
@import '@/pages-subpackages/students/homework/styles/index.scss';

page {
  height: 100%;
  overflow: hidden;
}

.answer-question-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.correction-tabs {
  display: flex;
  align-items: center;
  height: 86rpx;
  overflow-x: auto;
  flex-shrink: 0;
  white-space: nowrap;
  flex: 1;

  .correction-tab {
    padding: 32rpx 32rpx;
    font-size: 28rpx;
    height: 44rpx;
    line-height: 44rpx;
    border-radius: 999px;
    margin-right: 8rpx;
    cursor: pointer;
    color: #1d2129;
    background-color: #f3f3f3;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      background-color: #ffffff;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      background: linear-gradient(114deg, rgba(77, 163, 255, 0.1) 0%, rgba(125, 77, 255, 0.1) 100%);

      .correction-tab-text {
        background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 500;
      }
    }
  }
}

.progress-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d2129;
}

/* 添加计时器样式 */
.clock-box {
  // width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.clock-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  width: 70rpx;
  text-align: right;
}

/* 添加 CSS 滤镜来确保图标显示为黑色 */
.clock-icon {
  filter: brightness(0);
}

.homework-box {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  // padding: 0 32rpx;
}

.tools-box {
  height: 120rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  flex-shrink: 0;
  border-top: 1rpx solid #e7e7e7;
  gap: 10rpx;

  .left-group,
  .right-group {
    display: flex;
    align-items: center;
  }

  .right-group {
    flex-grow: 1;
    justify-content: flex-end;
    overflow-x: auto;
    white-space: nowrap;
    gap: 10rpx;
    padding: 0rpx 20rpx;

    .right-group-item {
      flex: 1;
    }
  }

  .answer-card-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #4e5969;
  }

  .tools-btn {
    border-radius: 100rpx;
    font-weight: initial;

    &.default-btn {
      border: 1px solid #dcdcdc;
      background-color: #ffffff;
      color: #4e5969;
    }

    &.submit-btn {
      background-color: #7d4dff;
      color: #fff;
    }
  }
}
</style>
