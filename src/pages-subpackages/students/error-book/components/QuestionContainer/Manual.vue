<template>
  <view class="answer-question-container">
    <!-- 作业内容 -->
    <view class="homework-box">
      <view class="title-waring">
        有{{ questions.length }}道题未批改，需对照答案自己批改哦（{{
          controller.getCorrectedCount
        }}/{{ questions.length }}）
      </view>
      <up-gap height="10" bgColor="#F7F7FD" />
      <view class="homework-container">
        <swiper
          class="questions-swiper"
          :current="currentQuestionIndex"
          :duration="300"
          :circular="false"
          :autoplay="false"
          :indicator-dots="false"
          :acceleration="false"
          easing-function="easeInOutCubic"
          @change="onQuestionSwiperChange"
          style="flex: 1; height: 100%; min-height: 0"
          v-if="questions.length > 0"
        >
          <swiper-item v-for="item in questions" :key="item.id" class="swiper-item">
            <scroll-view scroll-y class="question-scroll-view">
              <view class="question-container">
                <QuestionRenderer
                  :questionId="item.id"
                  :state="QuestionEntryState.MANUAL"
                  :controller="controller"
                />
              </view>
            </scroll-view>
          </swiper-item>
        </swiper>
        <MyEmpty v-if="questions.length === 0" text="暂无数据" />
      </view>
    </view>
    <!-- 工具栏，仅答题tab显示 -->
    <view class="tools-box">
      <!-- 未批改状态：显示"我做对了"和"我做错了"按钮 -->
      <template v-if="currentCorrectionStatus === 'uncorrected'">
        <view class="correction-buttons">
          <LkButton class="correction-btn correct-btn" type="secondary" @click="onMarkCorrect">
            <LkSvg
              width="40rpx"
              height="40rpx"
              src="/static/homework/correct.svg"
              :customStyle="{ 'margin-right': '10rpx' }"
            />
            我做对了
          </LkButton>
          <LkButton class="correction-btn incorrect-btn" type="secondary" @click="onMarkIncorrect">
            <LkSvg
              width="40rpx"
              height="40rpx"
              src="/static/homework/incorrect.svg"
              :customStyle="{ 'margin-right': '10rpx' }"
            />
            我做错了
          </LkButton>
        </view>
      </template>

      <!-- 已批改状态：显示"重新批改"和"下一题"按钮 -->
      <template v-else>
        <view class="left-group">
          <view class="answer-card-btn" @click="onResetCorrection">
            <LkSvg width="44rpx" height="44rpx" src="/static/study/edit.svg" />
            <text>重新批改</text>
          </view>
        </view>
        <view class="right-group">
          <view class="right-group-item">
            <LkButton class="homework-btn" block type="primary" @click="onComplete">
              完成
            </LkButton>
          </view>
        </view>
      </template>
    </view>

    <!-- 解析纠错弹出层 -->
    <PopupFeedback
      ref="feedbackPopupRef"
      :questionId="currentQuestionId"
      :controller="controller"
    />

    <!-- 笔记弹出层 -->
    <PopupNote ref="notePopupRef" :questionId="currentQuestionId" :controller="controller" />

    <!-- 绘图弹出层 -->
    <!-- <PopupDrawingBoard
      ref="drawingBoardPopupRef"
      :questionId="currentQuestionId"
      :subQuestionId="currentSubQuestionId"
      :controller="controller"
    /> -->
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch, nextTick, reactive } from 'vue';
import HomeworkOnline from '../HomeworkCategory/HomeworkOnline.vue';
import HomeworkPaper from '../HomeworkCategory/HomeworkPaper.vue';
import { PopupFeedback, PopupNote } from '@/pages-subpackages/students/homework/components';
import PopupDrawingBoard from '@/pages-subpackages/students/homework/components/PopupDrawingBoard/index.vue';
import PopupAnswerCard from '../PopupAnswerCard/index.vue';
import { useQuestionSubManualStore, ManualCorrectionStatus } from '@/store/questionSubManualStore';
import { useEventBus } from '@/hooks/useEventBus';
import useVirtualSwiper from '@/hooks/useVirtualSwiper';
import LkSvg from '@/components/svg/index.vue';
import { LkButton } from '@/components';
import { showDialogInfo } from '@/components/MyDialog';
import MyUploadPopup, { type UploadFileData } from '@/components/MyUploadPopup';
import MyEmpty from '@/components/MyEmpty/index.vue';
import QuestionRenderer from '../Question';
import { QuestionEntryState } from '@/constants/students/errorBook';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';
import { ErrorBookEventNames } from '@/constants/students/errorBook';

const props = defineProps({
  controller: {
    type: null,
    default: null,
  },
});

const defaultStore = useQuestionSubManualStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

const eventBus = useEventBus();
const questions = computed(() => controller.questions);
const currentQuestionIndex = computed(() => controller.currentQuestionIndex);

// 当前题目的批改状态
const currentCorrectionStatus = computed(() => controller.getCurrentQuestionCorrectionStatus);

const answerCardPopupRef = ref(null);
const feedbackPopupRef = ref(null);
const notePopupRef = ref(null);
const homeworkComponentRef = ref(null);
const isAnswerCardOpen = ref(false);
const currentQuestionId = ref('');
const currentSubQuestionId = ref('');
const uploadPopupRef = ref<InstanceType<typeof MyUploadPopup> | null>(null);
const drawingBoardPopupRef = ref<InstanceType<typeof PopupDrawingBoard> | null>(null);
const currentCorrectionIndex = ref(controller.batches.length - 1);
const userAnswers = computed(() => controller.userAnswers);

const allAnswered = computed(
  () => controller.answeredCount === questions.value.length && questions.value.length > 0
);
const status = computed(() => controller.backendData.status);

const initEvents = () => {
  // 监听纠错弹框事件 - 使用手动批改专用事件名称
  eventBus.on(ErrorBookEventNames.MANUAL_OPEN_FEEDBACK_POPUP, handleFeedbackEvent);

  // 监听笔记弹框事件 - 使用手动批改专用事件名称
  eventBus.on(ErrorBookEventNames.MANUAL_OPEN_NOTE_POPUP, handleNoteEvent);

  // 监听绘图弹框事件 - 使用手动批改专用事件名称
  eventBus.on(ErrorBookEventNames.MANUAL_OPEN_DRAWING_BOARD_POPUP, openDrawingBoardPopup);
};
initEvents();
// 在组件挂载时注册事件监听
onMounted(() => {
  // 初始化逻辑
  controller.setShowAnswer(false);
});

// 在组件卸载时移除事件监听，避免内存泄漏
onUnmounted(() => {
  eventBus.off(ErrorBookEventNames.MANUAL_OPEN_FEEDBACK_POPUP);
  eventBus.off(ErrorBookEventNames.MANUAL_OPEN_NOTE_POPUP);
  eventBus.off(ErrorBookEventNames.MANUAL_OPEN_DRAWING_BOARD_POPUP, openDrawingBoardPopup);
});

// 处理解析纠错事件
function handleFeedbackEvent(questionId: string) {
  currentQuestionId.value = questionId;
  if (feedbackPopupRef.value) {
    // @ts-ignore - 忽略类型检查错误
    feedbackPopupRef.value.open();
  }
}

// 处理笔记事件
function handleNoteEvent(questionId: string, note: any) {
  currentQuestionId.value = questionId;

  if (notePopupRef.value) {
    // @ts-ignore - 忽略类型检查错误
    notePopupRef.value.open({
      ...note,
    });
  }
}

// 处理绘图弹框事件
function openDrawingBoardPopup(data: { parentQuestionId: string; subQuestionId: string | null }) {
  // currentQuestionId.value = data.parentQuestionId;
  // // 处理subQuestionId可能为null的情况
  // currentSubQuestionId.value = data.subQuestionId || '';
  // drawingBoardPopupRef.value?.open();
}

const onSubmit = async () => {};

// 批改相关事件处理
const onMarkCorrect = () => {
  controller.setCurrentQuestionCorrectionStatus(ManualCorrectionStatus.CORRECT);
  controller.setShowAnswer(true);
};

const onMarkIncorrect = () => {
  controller.setCurrentQuestionCorrectionStatus(ManualCorrectionStatus.INCORRECT);
  controller.setShowAnswer(true);
};

const onResetCorrection = () => {
  controller.resetCurrentQuestionCorrectionStatus();
  controller.setShowAnswer(false);
};

const onComplete = () => {
  // 获取当前题目的批改状态和相关信息
  const currentQuestion = controller.currentUnderTakingQuestion;
  if (currentQuestion) {
    const correctionStatus = controller.getCurrentQuestionCorrectionStatus;
    const userAnswer = controller.getUserAnswer(currentQuestion.id);

    // 准备返回给原页面的数据
    const resultData = {
      questionId: currentQuestion.id,
      correctionStatus: correctionStatus,
      isCorrect: correctionStatus === ManualCorrectionStatus.CORRECT,
      showAnswer: controller.showAnswer,
      userAnswer: userAnswer,
    };

    // 通过eventBus传递批改结果给原页面，使用题目特定的事件名称
    const eventName = `manualCorrectionResult_${currentQuestion.id}`;
    eventBus.emit(eventName, resultData);
  }

  uni.navigateBack();
};

// swiper change 事件处理
const onQuestionSwiperChange = (e: any) => {
  const newIndex = e.detail.current;
  if (newIndex !== controller.currentQuestionIndex) {
    controller.setCurrentQuestionIndex(newIndex);
  }
};

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  if (index >= 0 && index < questions.value.length) {
    controller.setCurrentQuestionIndex(index);
  }
};

// 上一题
const prevQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    controller.setCurrentQuestionIndex(currentQuestionIndex.value - 1);
  }
};

// 下一题
const nextQuestion = () => {
  if (currentQuestionIndex.value < questions.value.length - 1) {
    controller.setCurrentQuestionIndex(currentQuestionIndex.value + 1);
  }
};
</script>

<style lang="scss" scoped>
@import '@/pages-subpackages/students/homework/styles/index.scss';

page {
  height: 100%;
  overflow: hidden;
}

.answer-question-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.correction-tabs {
  display: flex;
  align-items: center;
  height: 86rpx;
  overflow-x: auto;
  flex-shrink: 0;
  white-space: nowrap;
  flex: 1;

  .correction-tab {
    padding: 32rpx 32rpx;
    font-size: 28rpx;
    height: 44rpx;
    line-height: 44rpx;
    border-radius: 999px;
    margin-right: 8rpx;
    cursor: pointer;
    color: #1d2129;
    background-color: #f3f3f3;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      background-color: #ffffff;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      background: linear-gradient(114deg, rgba(77, 163, 255, 0.1) 0%, rgba(125, 77, 255, 0.1) 100%);

      .correction-tab-text {
        background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 500;
      }
    }
  }
}

.progress-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d2129;
}

/* 添加计时器样式 */
.clock-box {
  // width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.clock-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  width: 70rpx;
  text-align: right;
}

/* 添加 CSS 滤镜来确保图标显示为黑色 */
.clock-icon {
  filter: brightness(0);
}

.homework-box {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  // padding: 0 32rpx;
  .title-waring {
    $color: #e37318;
    border-top: 1px solid $color;
    border-bottom: 1px solid $color;
    background-color: #fff1e9;
    padding: 20rpx;
    color: $color;
    text-align: center;
    font-size: 32rpx;
  }
  .homework-container {
    padding: 20rpx 0;
    flex: 1;
  }
}

.tools-box {
  height: 120rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  flex-shrink: 0;
  border-top: 1rpx solid #e7e7e7;
  gap: 10rpx;

  .left-group,
  .right-group {
    display: flex;
    align-items: center;
  }

  .right-group {
    flex-grow: 1;
    justify-content: flex-end;
    overflow-x: auto;
    white-space: nowrap;
    gap: 10rpx;
    padding: 0rpx 20rpx;

    .right-group-item {
      flex: 1;
    }
  }

  .answer-card-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #4e5969;
  }

  .tools-btn {
    border-radius: 100rpx;
    font-weight: initial;

    &.default-btn {
      border: 1px solid #dcdcdc;
      background-color: #ffffff;
      color: #4e5969;
    }

    &.submit-btn {
      background-color: #7d4dff;
      color: #fff;
    }
  }

  // 批改按钮样式
  .correction-buttons {
    display: flex;
    gap: 20rpx;
    width: 100%;
    padding: 0 20rpx;
  }

  .correction-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
  }
}

.questions-swiper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 0;
}

.swiper-item {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.question-scroll-view {
  height: 100%;
  width: 100%;
}

.question-container {
  padding: 20rpx 0;
  box-sizing: border-box;
}
</style>
