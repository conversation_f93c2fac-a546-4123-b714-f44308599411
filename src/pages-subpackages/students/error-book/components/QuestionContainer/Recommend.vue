<template>
  <view class="answer-question-container">
    <!-- 作业内容 -->
    <view class="homework-box">
      <swiper
        class="questions-swiper"
        :current="currentQuestionIndex"
        :duration="300"
        :circular="false"
        :autoplay="false"
        :indicator-dots="false"
        :acceleration="false"
        easing-function="easeInOutCubic"
        @change="onQuestionSwiperChange"
        style="flex: 1; height: 100%; min-height: 0"
        v-if="questions.length > 0"
      >
        <swiper-item v-for="item in questions" :key="item.id" class="swiper-item">
          <scroll-view scroll-y class="question-scroll-view">
            <view class="question-container">
              <QuestionRenderer
                :questionId="item.id"
                :state="QuestionEntryState.RECOMMEND"
                :controller="controller"
              />
            </view>
          </scroll-view>
        </swiper-item>
      </swiper>
      <MyEmpty v-if="questions.length === 0" text="暂无数据" />
    </view>
    <!-- 工具栏，仅答题tab显示 -->
    <view class="tools-box">
      <view class="left-group">
        <view class="answer-card-btn" @click="showAnswerCard">
          <LkSvg width="44rpx" height="44rpx" src="/static/homework/answer_card.svg" />
          <text>答题卡</text>
        </view>
      </view>
      <view class="right-group">
        <view class="right-group-item">
          <LkButton class="homework-btn" block type="primary" @click="onSubmit"> 完成 </LkButton>
        </view>
      </view>
    </view>

    <!-- 答题卡弹出层 -->
    <PopupAnswerCard
      ref="answerCardPopupRef"
      v-model:open="isAnswerCardOpen"
      :controller="controller"
      @clearAnswers="clearAnswers"
      readonly
    />

    <!-- 解析纠错弹出层 -->
    <PopupFeedback
      ref="feedbackPopupRef"
      :questionId="currentQuestionId"
      :controller="controller"
    />

    <!-- 笔记弹出层 -->
    <PopupNote ref="notePopupRef" :questionId="currentQuestionId" :controller="controller" />

    <!-- 绘图弹出层 -->
    <PopupDrawingBoard
      ref="drawingBoardPopupRef"
      :questionId="currentQuestionId"
      :subQuestionId="currentSubQuestionId"
      :controller="controller"
    />
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch, nextTick, reactive } from 'vue';
import HomeworkOnline from '../HomeworkCategory/HomeworkOnline.vue';
import HomeworkPaper from '../HomeworkCategory/HomeworkPaper.vue';
import { PopupFeedback, PopupNote } from '@/pages-subpackages/students/homework/components';
import PopupDrawingBoard from '@/pages-subpackages/students/homework/components/PopupDrawingBoard/index.vue';
import PopupAnswerCard from '../PopupAnswerCard/index.vue';
import { useEventBus } from '@/hooks/useEventBus';
import useVirtualSwiper from '@/hooks/useVirtualSwiper';
import LkSvg from '@/components/svg/index.vue';
import {
  HomeworkEventNames,
  HomeworkStatus,
  HomeworkTaskType,
} from '@/constants/students/homework';
import { ErrorBookEventNames } from '@/constants/students/errorBook';
import { LkButton } from '@/components';
import { showDialogInfo } from '@/components/MyDialog';
import MyEmpty from '@/components/MyEmpty/index.vue';
import { showDialogDanger, showDialogConfirm } from '@/components/MyDialog';
import { QuestionRenderer } from '../Question/components';
import { QuestionEntryState } from '@/constants/students/errorBook';
import { getErrorBookPass } from '@/api/students/errorBook';
import { useRecommendStore } from '@/store/recommendStore';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

const props = defineProps({
  controller: {
    type: null,
    default: null,
  },
});

const defaultStore = useRecommendStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

const eventBus = useEventBus();
const questions = computed(() => controller.questions);
const currentQuestionIndex = computed(() => controller.currentQuestionIndex);

const currentSubQuestionId = ref<string>();
const answerCardPopupRef = ref(null);
const feedbackPopupRef = ref(null);
const notePopupRef = ref(null);
const homeworkComponentRef = ref(null);
const isAnswerCardOpen = ref(false);
const currentQuestionId = ref('');
const drawingBoardPopupRef = ref<InstanceType<typeof PopupDrawingBoard> | null>(null);
const currentCorrectionIndex = ref(controller.batches.length - 1);
const userAnswers = computed(() => controller.userAnswers);

const allAnswered = computed(
  () => controller.answeredCount === questions.value.length && questions.value.length > 0
);
const status = computed(() => controller.backendData.status);

const initEvents = () => {
  // 监听纠错弹框事件 - 使用推荐练习专用事件名称
  eventBus.on(ErrorBookEventNames.RECOMMEND_OPEN_FEEDBACK_POPUP, handleFeedbackEvent);

  // 监听笔记弹框事件 - 使用推荐练习专用事件名称
  eventBus.on(ErrorBookEventNames.RECOMMEND_OPEN_NOTE_POPUP, handleNoteEvent);

  // 监听绘图弹框事件
  eventBus.on(ErrorBookEventNames.RECOMMEND_OPEN_DRAWING_BOARD_POPUP, openDrawingBoardPopup);
};
initEvents();
// 在组件挂载时注册事件监听
onMounted(() => {
  // 初始化逻辑
});

// 在组件卸载时移除事件监听，避免内存泄漏
onUnmounted(() => {
  eventBus.off(ErrorBookEventNames.RECOMMEND_OPEN_FEEDBACK_POPUP);
  eventBus.off(ErrorBookEventNames.RECOMMEND_OPEN_NOTE_POPUP);
  eventBus.off(ErrorBookEventNames.RECOMMEND_OPEN_DRAWING_BOARD_POPUP, openDrawingBoardPopup);
});

const onSubmit = async () => {
  let confirmContent = `确定完成作业吗？`;
  // 获取未答题的数量
  const unAnsweredCount = questions.value.length - controller.answeredCount;
  if (unAnsweredCount > 0) {
    confirmContent = `还有${unAnsweredCount}道题未完成，确定要提交作业吗？`;
  }

  await showDialogConfirm({
    title: '提示',
    content: confirmContent,
    async onConfirm() {
      try {
        // 检查是否所有题目都答对了
        if (controller.allQuestionsCorrect) {
          console.log('所有题目都答对了，调用过关接口');

          try {
            // 调用错题本过关接口
            // 从推荐练习页面的路由参数中获取错题ID
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1] as any;
            const errorBookId = currentPage.options?.errorId;

            if (errorBookId) {
              await getErrorBookPass({ id: Number(errorBookId) });

              // 只有调用pass接口成功后，才设置刷新标记
              uni.setStorageSync('needRefreshErrorDetail', true);

              setTimeout(() => {
                showDialogInfo({
                  content: '所有题目都答对啦！',
                  onConfirm() {
                    // 返回错题本详情页面并刷新数据
                    uni.navigateBack();
                  },
                });
              }, 500);
            } else {
              console.warn('未找到错题ID，无法调用过关接口');
            }
          } catch (passError) {
            console.error('调用过关接口失败:', passError);
          }
        } else {
          // 还有题目未答对，正常完成流程
          const correctCount = controller.correctAnsweredCount;
          const totalCount = questions.value.length;

          setTimeout(() => {
            showDialogInfo({
              content: `答对 ${correctCount}/${totalCount} 道题，继续加油！`,
              onConfirm() {
                // 返回错题本详情页面
                uni.navigateBack();
              },
            });
          }, 500);
        }
      } catch (error) {
        console.error('提交作业失败:', error);
      }
    },
  });
};

// swiper change 事件处理
const onQuestionSwiperChange = (e: any) => {
  const newIndex = e.detail.current;
  if (newIndex !== controller.currentQuestionIndex) {
    controller.setCurrentQuestionIndex(newIndex);
  }
};

function showAnswerCard() {
  isAnswerCardOpen.value = true;
}

// 处理解析纠错事件
function handleFeedbackEvent(questionId: string) {
  currentQuestionId.value = questionId;
  if (feedbackPopupRef.value) {
    // @ts-ignore - 忽略类型检查错误
    feedbackPopupRef.value.open();
  }
}

// 处理笔记事件
function handleNoteEvent(questionId: string, note: any) {
  currentQuestionId.value = questionId;

  if (notePopupRef.value) {
    // @ts-ignore - 忽略类型检查错误
    notePopupRef.value.open({
      ...note,
    });
  }
}

// 处理绘图弹框事件
function openDrawingBoardPopup(data: { parentQuestionId: string; subQuestionId: string | null }) {
  currentQuestionId.value = data.parentQuestionId;
  // 处理subQuestionId可能为null的情况
  currentSubQuestionId.value = data.subQuestionId || '';
  drawingBoardPopupRef.value?.open();
}

// 清空答题记录
async function clearAnswers() {
  try {
    const confirmed = await showDialogDanger({
      title: '提示',
      content: '清空答题记录将不可恢复,确定清空全部答题记录吗?',
    });

    if (confirmed) {
      // 调用工具函数清空答题记录
      await controller.clearUserAnswers();
    }
  } catch (error) {
    console.error('清空答题记录出错:', error);
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    });
  }
}

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  if (index >= 0 && index < questions.value.length) {
    controller.setCurrentQuestionIndex(index);
  }
};

// 上一题
const prevQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    controller.setCurrentQuestionIndex(currentQuestionIndex.value - 1);
  }
};

// 下一题
const nextQuestion = () => {
  if (currentQuestionIndex.value < questions.value.length - 1) {
    controller.setCurrentQuestionIndex(currentQuestionIndex.value + 1);
  }
};
</script>

<style lang="scss" scoped>
@import '@/pages-subpackages/students/homework/styles/index.scss';

page {
  height: 100%;
  overflow: hidden;
}

.answer-question-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.correction-tabs {
  display: flex;
  align-items: center;
  height: 86rpx;
  overflow-x: auto;
  flex-shrink: 0;
  white-space: nowrap;
  flex: 1;

  .correction-tab {
    padding: 32rpx 32rpx;
    font-size: 28rpx;
    height: 44rpx;
    line-height: 44rpx;
    border-radius: 999px;
    margin-right: 8rpx;
    cursor: pointer;
    color: #1d2129;
    background-color: #f3f3f3;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      background-color: #ffffff;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      background: linear-gradient(114deg, rgba(77, 163, 255, 0.1) 0%, rgba(125, 77, 255, 0.1) 100%);

      .correction-tab-text {
        background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 500;
      }
    }
  }
}

.progress-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #1d2129;
}

/* 添加计时器样式 */
.clock-box {
  // width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.clock-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  width: 70rpx;
  text-align: right;
}

/* 添加 CSS 滤镜来确保图标显示为黑色 */
.clock-icon {
  filter: brightness(0);
}

.homework-box {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.tools-box {
  height: 120rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  flex-shrink: 0;
  border-top: 1rpx solid #e7e7e7;
  gap: 10rpx;

  .left-group,
  .right-group {
    display: flex;
    align-items: center;
  }

  .right-group {
    flex-grow: 1;
    justify-content: flex-end;
    overflow-x: auto;
    white-space: nowrap;
    gap: 10rpx;
    padding: 0rpx 20rpx;

    .right-group-item {
      flex: 1;
    }
  }

  .answer-card-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #4e5969;
  }

  .tools-btn {
    border-radius: 100rpx;
    font-weight: initial;

    &.default-btn {
      border: 1px solid #dcdcdc;
      background-color: #ffffff;
      color: #4e5969;
    }

    &.submit-btn {
      background-color: #7d4dff;
      color: #fff;
    }
  }
}

.questions-swiper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 0;
}

.swiper-item {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.question-scroll-view {
  height: 100%;
  width: 100%;
}

.question-container {
  padding: 20rpx 0;
  box-sizing: border-box;
}
</style>
