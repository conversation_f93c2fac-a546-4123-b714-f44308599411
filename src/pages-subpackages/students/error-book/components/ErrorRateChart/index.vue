<template>
  <view class="error-rate-chart">
    <canvas
      :id="canvasId"
      :canvas-id="canvasId"
      :style="{
        width: canvasWidth + 'px',
        height: canvasHeight + 'px',
      }"
      class="chart-canvas"
      @touchstart="handleTouch"
    ></canvas>

    <view class="chart-text">
      <view class="rate-title">
        {{ title }}
        <up-icon name="question-circle" style="margin-left: 8rpx" @tap="handleInfo" />
      </view>
      <view class="rate-value">{{ animatedValue }}%</view>
    </view>

    <MyDialog
      ref="errorRateDialogRef"
      type="info"
      title="错误率的计算"
      :content="errorRateExplanationText"
      :visible="showErrorRateDialog"
      confirm-text="我知道了"
      @confirm="handleDialogConfirm"
      @update:visible="updateDialogVisible"
    />
  </view>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onBeforeUnmount,
  watch,
  nextTick,
  getCurrentInstance,
} from 'vue';
import { MyDialog } from '@/components/MyDialog';

interface Props {
  /** 错题率数值，范围0-100 */
  value: number;
  /** 标题文本，默认为"错题率" */
  title?: string;
  /** 图表宽度(px) */
  width?: number;
  /** 图表高度(px) */
  height?: number;
  /** 圆环粗细(px) */
  strokeWidth?: number;
  /** 进度条颜色 */
  activeColor?: string;
  /** 背景条颜色 */
  inactiveColor?: string;
  /** 动画持续时间(ms) */
  duration?: number;
  /** 是否使用渐变色 */
  useGradient?: boolean;
  /** 渐变起始颜色 */
  startColor?: string;
  /** 是否启用值变化动画 */
  enableAnimation?: boolean;
  /** 是否启用初始从0到目标值的动画 */
  initialAnimation?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  value: 0,
  title: '错题率',
  width: 280,
  height: 160,
  strokeWidth: 20,
  activeColor: '#7D4DFF',
  inactiveColor: '#F0F0F0',
  duration: 300,
  useGradient: true,
  startColor: '#4DD8FF',
  enableAnimation: true,
  initialAnimation: true,
});

// 定义事件
const emit = defineEmits<{
  (e: 'animationStart'): void;
  (e: 'animationEnd'): void;
}>();

// 基本配置
const canvasWidth = ref(props.width < 200 ? 280 : props.width);
const canvasHeight = ref(props.height < 100 ? 160 : props.height);
const canvasId = `error-rate-canvas-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
const animatedValue = ref(0);
const isInitialRender = ref(true);
const errorRateDialogRef = ref();
const showErrorRateDialog = ref(false);

// Canvas相关变量
const ctx = ref<UniNamespace.CanvasContext | null>(null);
const pixelRatio = ref(1);
let currentAnimationId: number | null = null;

// 计算属性：判断是否正在动画
const isAnimating = computed(() => currentAnimationId !== null);

// 计算圆心和半径
const center = computed(() => ({
  x: canvasWidth.value / 2,
  y: canvasHeight.value - 30,
}));

const radius = computed(() => (canvasWidth.value - props.strokeWidth * 3) / 2);

// 计算轨道宽度（进度条宽度的3/5）
const trackWidth = computed(() => props.strokeWidth * 0.6);

// 计算动画中的端点位置
const animatedEndPoint = computed(() => {
  if (animatedValue.value <= 0) return { x: center.value.x - radius.value, y: center.value.y };

  // 计算当前进度对应的角度（从左侧开始，顺时针旋转）
  const angle = Math.PI * (animatedValue.value / 100);

  // 计算端点坐标
  const x = center.value.x + radius.value * Math.cos(Math.PI + angle);
  const y = center.value.y + radius.value * Math.sin(Math.PI + angle);

  return { x, y };
});

// 错误率说明内容
const errorRateExplanationText = computed(
  () =>
    '根据上述选择的学年学期，时段和学科，统计对应的错题数和做题总数，进行错误率计算，公式如下：\n\n错误率=（错题数 / 总做题数）×100%'
);

const handleInfo = () => {
  showErrorRateDialog.value = true;
};

const handleDialogConfirm = () => {
  showErrorRateDialog.value = false;
};

const updateDialogVisible = (visible: boolean) => {
  showErrorRateDialog.value = visible;
};

// 触摸事件处理
const handleTouch = () => {
  // 可以添加交互效果
};

// Canvas上下文有效性检查和自动恢复
const checkCanvasContext = async (): Promise<boolean> => {
  if (!ctx.value) {
    await initCanvas();
    return !!ctx.value;
  }

  // App端特殊检查：尝试执行一个简单的Canvas操作来验证上下文是否有效
  // #ifdef APP-PLUS
  try {
    // 尝试获取Canvas信息来验证上下文是否有效
    ctx.value.save();
    ctx.value.restore();
    return true;
  } catch (error) {
    await initCanvas();
    return !!ctx.value;
  }
  // #endif

  // #ifndef APP-PLUS
  return true;
  // #endif
};

// 初始化Canvas
const initCanvas = async () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    pixelRatio.value = systemInfo.pixelRatio || 1;

    // 创建Canvas上下文
    // #ifdef APP-PLUS
    ctx.value = uni.createCanvasContext(canvasId);
    // App端需要额外的初始化时间
    await new Promise(resolve => setTimeout(resolve, 50));
    // #endif
    // #ifndef APP-PLUS
    ctx.value = uni.createCanvasContext(canvasId, getCurrentInstance());
    // #endif

    if (!ctx.value) {
      return;
    }

    // 缩放Canvas以适应设备像素比
    ctx.value.scale(pixelRatio.value, pixelRatio.value);

    // 清除Canvas
    ctx.value.clearRect(0, 0, canvasWidth.value, canvasHeight.value);

    // App端特殊处理：设置Canvas保活属性
    // #ifdef APP-PLUS
    try {
      // 确保Canvas上下文稳定
      ctx.value.save();
      ctx.value.restore();
    } catch (error) {
      // 重试一次
      setTimeout(async () => {
        await initCanvas();
      }, 100);
      return;
    }
    // #endif
  } catch (error) {
    // Canvas初始化失败
  }
};

// 绘制背景半圆
const drawBackground = () => {
  if (!ctx.value) return;

  const centerX = center.value.x;
  const centerY = center.value.y;
  const r = radius.value;

  // 设置阴影效果
  ctx.value.setShadow(0, 1, 2, 'rgba(0,0,0,0.04)');

  // 绘制背景弧
  ctx.value.beginPath();
  ctx.value.arc(centerX, centerY, r, Math.PI, 0, false);
  ctx.value.setStrokeStyle(props.inactiveColor);
  ctx.value.setLineWidth(trackWidth.value);
  ctx.value.setLineCap('round');
  ctx.value.stroke();

  // 清除阴影
  ctx.value.setShadow(0, 0, 0, 'rgba(0,0,0,0)');
};

// 绘制进度条阴影
const drawProgressShadow = () => {
  if (!ctx.value) return;

  const centerX = center.value.x;
  const centerY = center.value.y;
  const r = radius.value;
  const progressAngle = Math.PI * (animatedValue.value / 100);

  try {
    // 创建阴影渐变
    const shadowGradient = ctx.value.createLinearGradient(
      centerX - r,
      centerY,
      centerX + r,
      centerY
    );
    shadowGradient.addColorStop(0, 'rgba(125, 77, 255, 0.05)');
    shadowGradient.addColorStop(0.8, 'rgba(125, 77, 255, 0.02)');
    shadowGradient.addColorStop(1, 'rgba(125, 77, 255, 0)');

    // 绘制阴影弧
    ctx.value.beginPath();
    ctx.value.arc(centerX, centerY, r, Math.PI, Math.PI + progressAngle, false);
    ctx.value.setStrokeStyle(shadowGradient as any);
    ctx.value.setLineWidth(props.strokeWidth + 16);
    ctx.value.setLineCap('round');
    ctx.value.stroke();
  } catch (error) {
    // 绘制进度条阴影失败
  }
};

// 绘制进度条
const drawProgress = () => {
  if (!ctx.value) return;

  const centerX = center.value.x;
  const centerY = center.value.y;
  const r = radius.value;
  const progressAngle = Math.PI * (animatedValue.value / 100);

  if (animatedValue.value <= 0) return;

  try {
    // 设置阴影
    ctx.value.setShadow(0, 2, 3, 'rgba(0,0,0,0.08)');

    // 创建渐变
    let strokeStyle: any = props.activeColor;
    if (props.useGradient) {
      const gradient = ctx.value.createLinearGradient(centerX - r, centerY, centerX + r, centerY);
      gradient.addColorStop(0, props.startColor);
      gradient.addColorStop(1, props.activeColor);
      strokeStyle = gradient;
    }

    // 绘制进度弧
    ctx.value.beginPath();
    ctx.value.arc(centerX, centerY, r, Math.PI, Math.PI + progressAngle, false);
    ctx.value.setStrokeStyle(strokeStyle);
    ctx.value.setLineWidth(props.strokeWidth);
    ctx.value.setLineCap('round');
    ctx.value.stroke();

    // 清除阴影
    ctx.value.setShadow(0, 0, 0, 'rgba(0,0,0,0)');
  } catch (error) {
    // 绘制进度条失败
  }
};

// 绘制端点装饰
const drawEndPoint = () => {
  if (!ctx.value) return;

  const endPoint = animatedEndPoint.value;
  const endX = endPoint.x;
  const endY = endPoint.y;
  const baseRadius = props.strokeWidth / 2;

  try {
    // 外层轻微阴影
    ctx.value.setShadow(0, 0, 0, 'rgba(0,0,0,0)');
    ctx.value.beginPath();
    ctx.value.arc(endX, endY, baseRadius + 8, 0, 2 * Math.PI);
    ctx.value.setFillStyle('rgba(0,0,0,0.02)');
    ctx.value.fill();

    // 外层白色圆环
    ctx.value.beginPath();
    ctx.value.arc(endX, endY, baseRadius + 6, 0, 2 * Math.PI);
    ctx.value.setFillStyle('#FFFFFF');
    ctx.value.fill();

    // 外层边框
    ctx.value.beginPath();
    ctx.value.arc(endX, endY, baseRadius + 6, 0, 2 * Math.PI);
    ctx.value.setStrokeStyle('rgba(0,0,0,0.06)');
    ctx.value.setLineWidth(1);
    ctx.value.stroke();

    // 内层凹陷阴影
    ctx.value.beginPath();
    ctx.value.arc(endX, endY, baseRadius + 2, 0, 2 * Math.PI);
    ctx.value.setFillStyle('rgba(0,0,0,0.03)');
    ctx.value.fill();

    // 内层渐变圆
    ctx.value.beginPath();
    ctx.value.arc(endX, endY, baseRadius, 0, 2 * Math.PI);
    if (props.useGradient) {
      const gradient = ctx.value.createLinearGradient(
        endX - baseRadius,
        endY,
        endX + baseRadius,
        endY
      );
      gradient.addColorStop(0, props.startColor);
      gradient.addColorStop(1, props.activeColor);
      ctx.value.setFillStyle(gradient as any);
    } else {
      ctx.value.setFillStyle(props.activeColor);
    }
    ctx.value.fill();

    // 右下角阴影
    ctx.value.beginPath();
    ctx.value.arc(endX + 1.5, endY + 1.5, baseRadius - 1, 0, 2 * Math.PI);
    ctx.value.setFillStyle('rgba(0,0,0,0.08)');
    ctx.value.fill();

    // 左上角高光
    ctx.value.beginPath();
    ctx.value.arc(endX - 1.5, endY - 1.5, baseRadius - 2, 0, 2 * Math.PI);
    ctx.value.setFillStyle('rgba(255,255,255,0.15)');
    ctx.value.fill();

    // 顶部高光
    ctx.value.beginPath();
    ctx.value.arc(endX - 2, endY - 3, baseRadius / 4, 0, 2 * Math.PI);
    ctx.value.setFillStyle('rgba(255,255,255,0.4)');
    ctx.value.fill();
  } catch (error) {
    // 绘制端点装饰失败
  }
};

// 主渲染函数
const render = async () => {
  // 检查Canvas上下文有效性
  const isContextValid = await checkCanvasContext();
  if (!isContextValid || !ctx.value) {
    return;
  }

  try {
    // 清除Canvas
    ctx.value.clearRect(0, 0, canvasWidth.value, canvasHeight.value);

    // 绘制所有层
    drawBackground();
    drawProgressShadow();
    drawProgress();
    drawEndPoint();

    // 执行绘制
    ctx.value.draw();
  } catch (error) {
    // 尝试重新初始化并重新渲染
    setTimeout(async () => {
      await initCanvas();
      if (ctx.value) {
        await render();
      }
    }, 100);
  }
};

// 获取高精度时间戳 - 跨平台兼容
const getTimestamp = (): number => {
  // #ifdef H5
  return performance.now();
  // #endif
  // #ifndef H5
  return Date.now();
  // #endif
};

// 跨平台requestAnimationFrame兼容
const requestAnimFrame = (callback: (timestamp: number) => void): number => {
  // #ifdef H5
  return requestAnimationFrame(callback);
  // #endif
  // #ifndef H5
  return setTimeout(() => {
    callback(getTimestamp());
  }, 16) as any; // 约等于60fps
  // #endif
};

// 跨平台cancelAnimationFrame兼容
const cancelAnimFrame = (id: number): void => {
  // #ifdef H5
  cancelAnimationFrame(id);
  // #endif
  // #ifndef H5
  clearTimeout(id);
  // #endif
};

// 超丝滑动画函数 - 60fps流畅体验，优化过渡效果
const animateValue = async (target: number) => {
  // 取消之前的动画
  if (currentAnimationId) {
    cancelAnimFrame(currentAnimationId);
    currentAnimationId = null;
  }

  const startValue = animatedValue.value;
  const diffValue = target - startValue;

  // 如果差值很小，直接设置最终值，避免不必要的动画
  if (Math.abs(diffValue) < 0.1) {
    animatedValue.value = target;
    isInitialRender.value = false;
    // 确保即使目标值为0也能正确渲染背景
    await nextTick();
    render(); // 使用防抖渲染
    return;
  }

  // 触发动画开始事件
  emit('animationStart');

  const startTime = getTimestamp();
  const duration = Math.max(props.duration, 200); // 最小200ms确保流畅

  // 使用高精度时间戳和优化的更新策略
  let lastUpdateTime = startTime;
  const targetFPS = 60;
  const frameInterval = 1000 / targetFPS;

  const animate = async (timestamp: number) => {
    const currentTime = getTimestamp();
    const elapsedTime = currentTime - startTime;
    const progress = Math.min(elapsedTime / duration, 1);

    // 帧率控制 - 确保稳定60fps
    if (currentTime - lastUpdateTime >= frameInterval || progress >= 1) {
      // 使用组合缓动函数获得最佳视觉效果
      const easeProgress = easeOutQuart(progress);

      // 高精度计算新值
      const newValue = startValue + diffValue * easeProgress;

      // 批量更新，减少重绘
      animatedValue.value = Math.round(newValue * 10) / 10; // 保留1位小数

      // 重新渲染 - 使用防抖渲染减少闪动
      render();

      lastUpdateTime = currentTime;
    }

    if (progress < 1) {
      currentAnimationId = requestAnimFrame(animate);
    } else {
      // 确保最终值精确
      animatedValue.value = target;
      isInitialRender.value = false;
      currentAnimationId = null;

      // 触发动画结束事件
      emit('animationEnd');

      // 动画结束后的特殊处理：确保显示稳定
      render(); // 立即渲染最终状态

      // App端额外保障：延迟再次渲染确保显示稳定
      // #ifdef APP-PLUS
      setTimeout(async () => {
        if (await checkCanvasContext()) {
          render();
        }
      }, 50);
      // #endif
    }
  };

  currentAnimationId = requestAnimFrame(animate);
};

// 超丝滑的缓动函数集合 - 提供最佳的视觉体验
const easeOutQuart = (t: number): number => {
  return 1 - Math.pow(1 - t, 4);
};

// 监听value变化 - 优化响应速度和防止闪动
watch(
  () => props.value,
  async (newValue, oldValue) => {
    // 避免不必要的动画触发
    if (newValue === oldValue) return;

    // 如果当前有动画在执行，先取消它
    if (currentAnimationId) {
      cancelAnimFrame(currentAnimationId);
      currentAnimationId = null;
    }

    // 启动动画
    await animateValue(newValue);
  },
  { immediate: false }
);

// 初始化和动画处理
onMounted(async () => {
  // 确保DOM已经渲染
  await nextTick();

  // 初始化Canvas
  await initCanvas();

  // App端需要更多时间初始化Canvas
  // #ifdef APP-PLUS
  await new Promise(resolve => setTimeout(resolve, 100));
  // #endif

  // 等待Canvas初始化完成
  await nextTick();

  // 初始化动画
  if (props.enableAnimation) {
    if (props.initialAnimation) {
      // 从0开始动画到目标值
      animatedValue.value = 0;

      // 确保初始状态已应用到DOM
      await nextTick();

      // 先渲染初始状态（背景圆弧）
      render();

      // 开始动画
      await animateValue(props.value);
    } else {
      // 直接从目标值开始，不做从0到目标值的动画
      animatedValue.value = props.value;
      isInitialRender.value = false;
      render();
    }
  } else {
    // 不使用任何动画，直接设置值
    animatedValue.value = props.value;
    isInitialRender.value = false;
    render();
  }

  // 监听窗口大小变化（仅在H5端有效）
  // #ifdef H5
  window.addEventListener('resize', handleResize);
  // #endif
});

// #ifdef H5
// 处理窗口大小变化
const handleResize = async () => {
  // 重新初始化Canvas并重新渲染
  await initCanvas();
  render();
};

// 组件卸载时移除事件监听和清理动画
onBeforeUnmount(() => {
  // 清理动画
  if (currentAnimationId) {
    cancelAnimFrame(currentAnimationId);
    currentAnimationId = null;
  }

  window.removeEventListener('resize', handleResize);
});
// #endif

// 在非H5环境下也需要清理动画
// #ifndef H5
onBeforeUnmount(() => {
  if (currentAnimationId) {
    cancelAnimFrame(currentAnimationId);
    currentAnimationId = null;
  }
});
// #endif
</script>

<style lang="scss" scoped>
.error-rate-chart {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.chart-canvas {
  display: block;
  transform: translateZ(0); /* 启用硬件加速 */
  backface-visibility: hidden; /* 增强硬件加速效果 */
  perspective: 1000px; /* 提升3D渲染性能 */
  will-change: auto; /* 让浏览器自动优化 */

  /* 进一步的性能优化 */
  contain: layout style paint; /* 限制重排重绘范围 */
  isolation: isolate; /* 创建新的层叠上下文 */
}

.chart-text {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -40%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;

  .rate-title {
    font-size: 32rpx;
    font-weight: normal;
    color: #666666;
    display: flex;
    align-items: center;
  }

  .rate-value {
    font-size: 64rpx;
    font-weight: 700;
    color: #333333;
    line-height: 1.2;
  }
}
</style>
