<template>
  <view class="resource-list">
    <template v-if="resources.length > 0">
      <view
        v-for="(item, idx) in resources"
        :key="idx"
        class="resource-item"
        @click="goDetail(item)"
      >
        <view class="icon-wrap">
          <image class="icon" :src="getImageIcon(item.type)" mode="aspectFit"> </image>
        </view>
        <view class="info">
          <view class="title">{{ item.title }}</view>
          <view class="tags">
            <text class="tag" v-for="(tag, i) in item.tags" :key="i">{{ tag }}</text>
          </view>
          <view class="size">{{ getFileSizeText(item.size) }}</view>
        </view>
      </view>
    </template>
    <view v-else-if="resources.length === 0 && !isLoading" class="empty">
      <MyEmpty />
    </view>
    <MyLoading v-if="isLoading" type="spinner" overlay />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { getHomeworkDetail } from '@/api/students/homework';
import { useHomeworkStore } from '@/store/homeworkStore';
import MyEmpty from '@/components/MyEmpty';
import MyLoading from '@/components/MyLoading';
import { getFileSizeText } from '@/utils/common';
import { isEmptyValue } from '@/pages-subpackages/students/homework/utils';

// 定义组件属性：支持外部传入控制器，以便替换不同的 store 实现
const props = defineProps<{
  controller?: any;
}>();

// 默认使用原有的作业 store，亦可通过 props.controller 传入替代实现（降耦合）
const defaultStore = useHomeworkStore();
const controller = isEmptyValue(props.controller) ? defaultStore : props.controller;

const isLoading = ref(false);
const resources = ref<any[]>([]);


// 从控制器获取数据（统一抽象，避免直接依赖具体 store）
const materialFileList = computed(() => {
  // materialFileList存储在controller.backendData中
  const materialFiles = controller.backendData?.materialFileList || [];
  return materialFiles;
});

onMounted(() => {
  fetchResourceList();
});

// 监听materialFileList变化，重新获取资源列表
watch(
  () => materialFileList.value,
  (newValue) => {
    fetchResourceList();
  },
  { deep: true, immediate: false }
);

// 监听controller.backendData变化
watch(
  () => controller.backendData,
  () => {
    fetchResourceList();
  },
  { deep: true, immediate: false }
);

// 获取资源列表（从controller.backendData中的materialFileList获取）
const fetchResourceList = async () => {
  try {
    isLoading.value = true;
    resources.value = materialFileList.value.map((item: any) => {
      return {
        ...item,
        type: item?.file?.fileType?.replace('.', '') || 'unknown',
        title: item?.file?.fileName || item?.title || '未知文件',
        size: item?.file?.fileSize || item?.size || 0,
        tags: item?.tags || ['北师大版(2012)', '小学语文', '学案'],
      };
    });

  } catch (error) {
    console.error('ResourceList fetchResourceList error:', error);
  } finally {
    isLoading.value = false;
  }
};

function getImageIcon(type: string) {
  switch (type) {
    case 'word':
      return '/static/fileTypeIcon/doc.svg';
    case 'pdf':
      return '/static/fileTypeIcon/pdf.svg';
    case 'image':
      return '/static/fileTypeIcon/image.svg';
    default:
      return '/static/fileTypeIcon/doc.svg';
  }
}

function goDetail(item: any) {
  uni.navigateTo({
    url: `/pages-subpackages/students/learning-resource/resource-details?id=${item.resourceId}`,
  });
}
</script>

<style lang="scss" scoped>
.resource-list {
  padding: 16rpx;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  .resource-item {
    display: flex;
    background: #fff;
    border-radius: 20rpx 28rpx 20rpx 20rpx;
    margin-bottom: 28rpx;
    padding: 28rpx;
    box-shadow: 0 0 30rpx 0 rgba(237, 237, 237, 0.62);
    border: 1px solid #f1f1f1;
  }

  .icon-wrap {
    margin-right: 20rpx;

    .icon {
      width: 84rpx;
      height: 84rpx;
      border-radius: 18rpx;
    }
  }

  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .title {
      font-size: 32rpx;
      color: #1d2129;
      font-weight: 400;
      line-height: 1.5;
      letter-spacing: 0.5%;
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .tag {
        font-size: 24rpx;
        color: #4e5969;
        background: #f2f3f5;
        border-radius: 4rpx;
        padding: 3rpx 8rpx;
      }
    }

    .size {
      font-size: 24rpx;
      color: #86909c;
      line-height: 1.8;
    }
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 120rpx;

    .empty-icon {
      font-size: 80rpx;
      color: #eee;
      margin-bottom: 20rpx;
    }

    .empty-text {
      font-size: 28rpx;
      color: #aaa;
    }
  }
}
</style>
