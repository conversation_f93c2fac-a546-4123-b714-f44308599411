<template>
  <view class="blank-template">
    <!-- 结构化填空题模式 - 支持parts数组结构 -->
    <view class="structured-blanks" v-if="question?.blanks && question.blanks.length > 0">
      <QuestionFillLineGroup :readonly="readonly" @update="handleFillLineUpdate">
        <view
          class="structured-blank-item"
          v-for="(item, itemIndex) in question.blanks"
          :key="itemIndex"
        >
          <view class="structured-blank-label" v-if="question.blanks.length > 1">
            <text>({{ itemIndex + 1 }})</text>
          </view>
          <view class="structured-blank-content">
            <template v-for="(part, partIndex) in item.parts" :key="`${itemIndex}-${partIndex}`">
              <!-- 文本部分 -->
              <text v-if="part.type === 'text'" class="structured-text">{{ part.value }}</text>

              <!-- 填空部分 -->
              <QuestionFillLine
                v-else-if="part.type === 'blank'"
                v-model="blankValues[getGlobalBlankIndex(itemIndex, part.index)]"
                :blank-id="getGlobalBlankIndex(itemIndex, part.index)"
                :blank-index="getGlobalBlankIndex(itemIndex, part.index)"
                :readonly="readonly"
                :is-error="isBlankError(getGlobalBlankIndex(itemIndex, part.index))"
                :is-correct="isBlankCorrect(getGlobalBlankIndex(itemIndex, part.index))"
              />
            </template>
          </view>
        </view>
      </QuestionFillLineGroup>
    </view>
    <view v-else class="no-blanks">
      <text>题目数据格式错误，请检查题目配置</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import {
  QuestionFillLine,
  QuestionFillLineGroup,
} from '@/pages-subpackages/students/homework/components/Question/components';

// 定义属性 - 只接收questionId
const props = defineProps<{
  questionId: string;
  readonly?: boolean;
}>();

// 使用store
const homeworkStore = useHomeworkStore();

// 从store获取数据
const question = computed(() => homeworkStore.getQuestion(props.questionId));
const userAnswer = computed(() => homeworkStore.getUserAnswer(props.questionId));

const readonly = computed(() => !!props.readonly); // 添加 showAnswer 控制
const showAnalysis = computed(() => !!readonly.value); // 添加 showAnswer 控制

// 答案数据处理 - 数组形式
const arrayAnswers = computed(() => {
  if (userAnswer.value?.answer && Array.isArray(userAnswer.value.answer)) {
    return userAnswer.value.answer as string[];
  }
  return [] as string[];
});

// 填空值的响应式对象
const blankValues = reactive<Record<string, string>>({});

// 计算全局空的下标
const getGlobalBlankIndex = (itemIndex: number, partIndex: number): number => {
  let globalIndex = 0;
  for (let i = 0; i < itemIndex; i++) {
    globalIndex += question.value.blanks[i].parts.filter((p: any) => p.type === 'blank').length;
  }
  return globalIndex + partIndex;
};

// 初始化填空值
watch(
  () => userAnswer.value?.answer,
  newVal => {
    // 清空旧数据，避免切换订正时数据污染
    Object.keys(blankValues).forEach(key => {
      delete blankValues[key];
    });

    if (newVal && Array.isArray(newVal)) {
      const answerArray = newVal as string[];
      answerArray.forEach((value, index) => {
        blankValues[index] = value;
      });
    }
  },
  { immediate: true }
);

// 处理填空线更新
const handleFillLineUpdate = (data: {
  blankId: string | number;
  blankIndex: number;
  value: string;
}) => {
  if (readonly.value) return;

  const { blankIndex, value } = data;

  // 更新本地值
  blankValues[blankIndex] = value;

  // 构造新的答案数组
  const newAnswers = [...arrayAnswers.value];
  newAnswers[blankIndex] = value;

  homeworkStore.setUserAnswer(props.questionId, newAnswers);
};

// 判断填空是否错误
const isBlankError = (blankIndex: number): boolean => {
  // 如果 showAnswer 为 false，且不是只读状态，则不显示错误状态
  if (!readonly.value) {
    return false;
  }

  // 如果 showAnswer 为 false，但是处于只读状态，则需要检查作业状态来决定是否显示
  if (readonly.value) {
    // 只有在作业状态允许显示解析时才显示错误状态
    if (!showAnalysis.value) {
      return false;
    }
  }

  const userValue = blankValues[blankIndex] || '';

  // 获取正确答案
  let correctAnswer: string | undefined;

  if (question.value?.answer && Array.isArray(question.value.answer)) {
    correctAnswer = question.value.answer[blankIndex];
  }

  if (!correctAnswer) return false;

  // 在 showAnswer 模式下，未填写也应视为错误
  if (readonly.value && userValue === '') {
    return true;
  }

  // 如果用户答案不为空且与正确答案不匹配
  return userValue !== '' && userValue !== correctAnswer;
};

// 判断填空是否正确
const isBlankCorrect = (blankIndex: number): boolean => {
  // 如果 showAnswer 为 false，且不是只读状态，则不显示正确状态
  if (!readonly.value) {
    return false;
  }

  // 如果 showAnswer 为 false，但是处于只读状态，则需要检查作业状态来决定是否显示
  if (readonly.value) {
    // 只有在作业状态允许显示解析时才显示正确状态
    if (!showAnalysis.value) {
      return false;
    }
  }

  const userValue = blankValues[blankIndex] || '';

  // 获取正确答案
  let correctAnswer: string | undefined;

  if (question.value?.answer && Array.isArray(question.value.answer)) {
    correctAnswer = question.value.answer[blankIndex];
  }

  if (!correctAnswer) return false;

  // 如果用户答案与正确答案匹配
  return userValue !== '' && userValue === correctAnswer;
};
</script>

<style lang="scss" scoped>
.blank-template {
  width: 100%;

  // 结构化填空题样式
  .structured-blanks {
    margin-top: 20rpx;
  }

  .structured-blank-item {
    margin-bottom: 30rpx;
    display: flex;
  }

  .structured-blank-label {
    font-size: 32rpx;
    font-weight: normal;
    color: #333;
    margin-right: 10rpx;
    flex-shrink: 0; // 防止标签被压缩
  }

  .structured-blank-content {
    margin-top: -6rpx;
    flex: 1;
    display: block; // 段落容器，内部走内联流
    font-size: 32rpx;
    line-height: 1.6;
    white-space: normal; // 默认换行规则，保证连贯
    word-break: normal; // 不强制打断
    word-wrap: break-word; // 超长单词/连续字符时再换行
    text-align: left;
  }

  .structured-text {
    display: inline;
    white-space: normal; // 不保留源文本中的换行与多空格，保证连贯
  }

  // 确保填空组件也是内联显示，不会强制换行
  :deep(.question-fill-line) {
    display: inline-block !important;
    vertical-align: baseline;
    margin: 0 2rpx;
  }

  // 填空输入容器为内联弹性，不触发换行
  :deep(.fill-line-input-wrapper) {
    display: inline-flex !important;
    vertical-align: baseline;
  }

  // 填空输入文本不换行，宽度自适应由组件控制
  :deep(.fill-line-input) {
    white-space: nowrap !important;
  }

  .no-blanks {
    padding: 30rpx;
    color: #ff4d4f;
    text-align: center;
  }
}
</style>
