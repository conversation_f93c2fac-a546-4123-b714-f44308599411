<template>
  <view class="textarea-template">
    <!-- 简答题内容 - 移除子题概念 -->
    <view class="question-content">
      <QuestionSubItem
        :parent-question-id="questionId"
        :sub-question="createQuestion()"
        :index="0"
        :answer="answerContent"
        :readonly="readonly"
        :show-analysis="showAnalysis"
        :is-short-answer="isShortAnswer"
        @update:answer="handleAnswerUpdate"
        @preview-file="previewFile"
        @delete-file="deleteFile"
        @upload="openUploadPopup"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { useHomeworkStore } from '@/store/homeworkStore';
import { QuestionType } from '@/constants/students/homework';
import { QuestionSubItem } from '@/pages-subpackages/students/homework/components/Question/components';
import { useEventBus } from '@/hooks/useEventBus';
import { HomeworkEventNames } from '@/constants/students/homework';
import type { UploadFileData } from '@/components/MyUploadPopup';
import type { FrontComplexAnswerContent, FrontSubQuestion } from '@/types/students/homework';

// 定义答案内容接口，与 ComplexAnswerContent 保持兼容
interface AnswerContent {
  files: UploadFileData[];
  drawing?: {
    imageUrl: string;
    data: any;
  };
  correct?: boolean;
}

// 定义属性 - 只接收questionId
const props = defineProps<{
  questionId: string;
  readonly?: boolean;
}>();

// 使用store
const homeworkStore = useHomeworkStore();

// 从store获取数据
const question = computed(() => homeworkStore.getQuestion(props.questionId));
const userAnswer = computed(() => {
  return homeworkStore.getUserAnswer(props.questionId);
});
const readonly = computed(() => !!props.readonly);
const showAnalysis = computed(() => !!readonly.value);

// 事件总线
const eventBus = useEventBus();

// 是否是简答题
const isShortAnswer = computed(() => {
  return question.value?.type === QuestionType.SHORT_ANSWER;
});

// 创建题目对象 - 不再需要子题
const createQuestion = (): FrontSubQuestion => {
  return {
    id: props.questionId,
    index: question.value?.index || 0,
    title: question.value?.title || '',
    content: question.value?.content || '',
    type: question.value?.type,
    analysis: question.value?.analysis || '',
  };
};

// 题目答案内容 - 简化为扁平结构
const answerContent = computed<AnswerContent>(() => {
  // debugger
  if (userAnswer.value?.answer) {
    const answer = userAnswer.value.answer;

    const correct = answer.correct;
    const files = answer.files || answer.files || [];
    return {
      files,
      drawing: answer.drawing,
      correct,
    };

    // 如果答案是字符串
    if (typeof answer === 'string') {
      return {
        files: [],
      };
    }
    // 如果答案是对象
    else if (typeof answer === 'object' && !Array.isArray(answer)) {
      // 如果已经是扁平的 AnswerContent 结构
      if (answer.files !== undefined) {
        // 从后端数据中获取答题结果
        // const correct = question.value?.backendData?.submitQuestion?.correctResult == 1;

        const correct = answer.correct;
        return {
          files: Array.isArray(answer.files) ? answer.files : [],
          drawing: answer.drawing,
          correct,
        };
      }

      // 兼容旧格式 - 如果是嵌套结构，则尝试提取
      // if (Object.keys(answer).length > 0) {
      //   // 尝试提取第一个子题答案
      //   const firstKey = Object.keys(answer)[0];
      //   const subAnswer = answer[firstKey];

      //   if (typeof subAnswer === 'string') {
      //     return {
      //       files: [],
      //     };
      //   } else if (typeof subAnswer === 'object' && subAnswer !== null) {
      //     return {
      //       files: Array.isArray(subAnswer.files) ? subAnswer.files : [],
      //       drawing: subAnswer.drawing,
      //       correct: subAnswer.correct,
      //     };
      //   }
      // }
    }
  }

  // 默认返回空答案
  return {
    files: [],
  };
});

// 处理答案更新 - 直接使用扁平结构
const handleAnswerUpdate = (subQuestionId: string, answer: AnswerContent) => {
  if (readonly.value) return;

  // 直接更新答案，不再需要子题结构
  homeworkStore.setUserAnswer(props.questionId, answer);
};

// 打开上传弹窗
const openUploadPopup = () => {
  // 触发打开上传弹窗事件 - 不再需要子题ID
  eventBus.emit(HomeworkEventNames.OPEN_UPLOAD_POPUP, {
    questionId: props.questionId,
    subQuestionId: null,
  });
};

// 预览文件
const previewFile = (file: UploadFileData) => {
  if (isImageFile(file.fileType)) {
    // 预览图片
    uni.previewImage({
      urls: [file.fileUrl],
      current: file.fileUrl,
    });
  } else {
    // 其他文件类型暂不支持预览
    uni.showToast({
      title: '该文件类型暂不支持预览',
      icon: 'none',
    });
  }
};

// 判断是否为图片文件
const isImageFile = (fileType: string): boolean => {
  const imageTypes = ['image', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  return imageTypes.some(type => fileType.toLowerCase().includes(type));
};

// 删除文件
const deleteFile = (file: UploadFileData) => {
  if (readonly.value) return;

  // 获取当前答案
  const currentAnswer = userAnswer.value?.answer || {};

  // 检查答案格式
  let existingAnswer: AnswerContent;
  if (typeof currentAnswer === 'string') {
    existingAnswer = { files: [] };
  } else if (typeof currentAnswer === 'object' && !Array.isArray(currentAnswer)) {
    // 如果已经是扁平的 AnswerContent 结构
    if ('files' in currentAnswer) {
      existingAnswer = {
        files: Array.isArray(currentAnswer.files) ? currentAnswer.files : [],
        drawing: currentAnswer.drawing,
        correct: currentAnswer.correct,
      };
    }
    // 兼容旧格式 - 如果是嵌套结构，尝试提取
    else {
      // 尝试提取第一个子题答案
      const firstKey = Object.keys(currentAnswer)[0];
      const subAnswer = currentAnswer[firstKey];

      if (typeof subAnswer === 'string') {
        existingAnswer = { files: [] };
      } else if (typeof subAnswer === 'object' && subAnswer !== null) {
        existingAnswer = {
          files: Array.isArray(subAnswer.files) ? subAnswer.files : [],
          drawing: subAnswer.drawing,
          correct: subAnswer.correct,
        };
      } else {
        existingAnswer = { files: [] };
      }
    }
  } else {
    existingAnswer = { files: [] };
  }

  // 检查文件是否存在
  if (existingAnswer.files && Array.isArray(existingAnswer.files)) {
    const fileIndex = existingAnswer.files.findIndex(item => item.id === file.id);
    if (fileIndex !== -1) {
      // 创建新的附件数组，移除要删除的文件
      const updatedFiles = [...existingAnswer.files];
      updatedFiles.splice(fileIndex, 1);

      // 构建新的答案对象 - 扁平结构
      const newAnswer: AnswerContent = {
        files: updatedFiles,
        drawing: existingAnswer.drawing,
        correct: existingAnswer.correct,
      };

      homeworkStore.setUserAnswer(props.questionId, newAnswer);
    }
  }
};
</script>

<style lang="scss" scoped>
.textarea-template {
  width: 100%;
  padding: 16rpx;
}

.question-content {
  width: 100%;
}
</style>
