<template>
  <view class="student-notice">
    <PageList
      :fetch-method="fetchNotifications"
      :process-data="processNotificationData"
      :extra-params="queryParams"
      :page-size="10000"
      empty-text="暂无通知"
      empty-icon="/static/common/empty.svg"
    >
      <template #default="{ list }">
        <view class="notice-container">
          <view class="notice-list">
            <view
              v-for="notice in list"
              :key="notice.id"
              class="notice-item"
              @tap="handleNoticeClick(notice)"
            >
              <view class="notice-item-left">
                <view class="notice-icon" :class="{ unread: notice.readStatus === 0 }">
                  <view class="icon-text">
                    <LkSvg width="16px" height="16px" src="/static/study/full_notice.svg" />
                  </view>
                </view>
                <view class="notice-title">{{ notice.title }}</view>
              </view>
              <view class="notice-desc">{{ notice.content }}</view>
              <view class="notice-time">{{ notice.notificationTime }}</view>
            </view>
          </view>
        </view>
      </template>
    </PageList>
  </view>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import type { NotificationItem, NotificationPageParams } from '@/types/api/notification';
import { getNotificationPage, markNotificationAsRead } from '@/api/notification';
import PageList from './PageList.vue';

// 查询参数
const queryParams = reactive<Omit<NotificationPageParams, 'current' | 'size'>>({
  typeCode: 'STUDENT_HOMEWORK',
});

// 获取通知数据的方法
const fetchNotifications = async (params: NotificationPageParams) => {
  try {
    const response = await getNotificationPage(params);
    return response;
  } catch (error) {
    console.error('获取通知数据失败:', error);
    throw error;
  }
};

// 处理通知数据
const processNotificationData = (data: NotificationItem[]) => {
  // 可以在这里对数据进行额外处理，如格式化时间等
  return data.map(item => ({
    ...item,
    // 可以添加数据处理逻辑
  }));
};

// 点击通知处理
const handleNoticeClick = async (notice: NotificationItem) => {
  console.log('点击通知:', notice);

  // 如果通知未读，先标记为已读
  if (notice.readStatus === 0) {
    try {
      await markNotificationAsRead({ notificationId: notice.id });
      console.log('通知已标记为已读:', notice.id);

      // 更新本地通知状态
      notice.readStatus = 1;
      uni.showToast({
        title: `已标记为已读`,
        icon: 'none',
      });
    } catch (error) {
      console.error('标记通知已读失败:', error);
      // 即使标记失败也继续执行后续逻辑
    }
  }
};
</script>

<style scoped lang="scss">
.student-notice {
  background-color: #f7f7fd;
  height: calc(100vh - 44px);
}

.notice-container {
  padding: 40rpx 32rpx;
}

.notice-list {
  display: flex;
  flex-direction: column;
}

.notice-item {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.notice-item-left {
  display: flex;
  align-items: center;
}

.notice-icon {
  width: 50rpx;
  height: 50rpx;
  background: #7d4dff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.unread {
    background: #ff6b6b;
    box-shadow: 0 0 8rpx rgba(255, 107, 107, 0.4);
  }

  .icon-text {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.notice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  margin-left: 25rpx;
}

.notice-desc {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-top: 10px;
}

.notice-time {
  font-size: 24rpx;
  color: #9ca3af;
  margin-top: 10px;
}
</style>
