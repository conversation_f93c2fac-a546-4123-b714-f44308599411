<template>
  <view class="page-list">
    <scroll-view
      scroll-y
      class="scroll-container"
      @scrolltolower="onScrollToLower"
      :lower-threshold="50"
      :refresher-enabled="false"
    >
      <!-- 列表内容 -->
      <view v-if="list.length > 0" class="list-content">
        <slot :list="list"></slot>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading && !firstLoading" class="empty-container">
        <MyEmpty :text="emptyText" :image-url="emptyIcon" :show-image="showEmptyIcon" />
      </view>

      <!-- 底部加载状态 -->
      <view v-if="list.length > 0" class="list-footer">
        <view v-if="loading" class="loading-state">
          <text>{{ loadingText }}</text>
        </view>
        <view v-else-if="!hasMore" class="no-more-state">
          <text>{{ noMoreText }}</text>
        </view>
        <view v-else class="pull-up-hint">
          <text>上拉加载更多</text>
        </view>
      </view>
    </scroll-view>

    <!-- 首次加载状态 -->
    <view v-if="firstLoading" class="first-loading">
      <text>{{ loadingText }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import MyEmpty from '@/components/MyEmpty/index.vue';

interface PageParams {
  current: number;
  size: number;
  [key: string]: any;
}

interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
  [key: string]: any;
}

interface Props<T> {
  // 请求接口方法
  fetchMethod: (params: PageParams) => Promise<PageResponse<T>>;
  // 每页大小
  pageSize?: number;
  // 额外的请求参数
  extraParams?: Record<string, any>;
  // 是否自动加载第一页
  autoLoad?: boolean;
  // 数据处理函数
  processData?: (data: T[]) => T[];
  // 是否启用上拉加载
  enablePullUp?: boolean;
  // 空数据提示文本
  emptyText?: string;
  // 空状态图标
  emptyIcon?: string;
  // 是否显示空状态图标
  showEmptyIcon?: boolean;
  // 加载中提示文本
  loadingText?: string;
  // 无更多数据提示文本
  noMoreText?: string;
  // 禁用请求
  disabledRequest?: boolean;
}

const props = withDefaults(defineProps<Props<any>>(), {
  pageSize: 10,
  extraParams: () => ({}),
  autoLoad: true,
  processData: (data: any[]) => data,
  enablePullUp: true,
  emptyText: '暂无数据',
  emptyIcon: '/static/common/empty.svg',
  showEmptyIcon: true,
  loadingText: '加载中...',
  noMoreText: '没有更多了',
  disabledRequest: false,
});

// 定义事件
const emit = defineEmits(['dataLoaded']);

// 列表数据
const list = ref<any[]>([]);
// 分页参数
const pageParams = reactive<PageParams>({
  current: 1,
  size: props.pageSize,
  ...props.extraParams,
});
// 加载状态
const loading = ref(false);
const firstLoading = ref(true);
// 是否还有更多数据
const hasMore = ref(true);

// 加载数据
const loadData = async (isRefresh = false) => {
  if (loading.value || props.disabledRequest) return;

  // 如果是刷新，重置页码和数据
  if (isRefresh) {
    pageParams.current = 1;
    list.value = [];
    hasMore.value = true;
  }

  loading.value = true;

  try {
    const response = await props.fetchMethod(pageParams);

    // 处理数据
    const processedData = props.processData
      ? props.processData(response?.records || [])
      : response?.records || [];

    if (isRefresh) {
      list.value = processedData;
    } else {
      list.value = [...list.value, ...processedData];
    }

    // 判断是否还有更多数据
    hasMore.value = pageParams.current < response?.pages;

    // 更新页码
    if (hasMore.value) {
      pageParams.current += 1;
    }

    firstLoading.value = false;

    // 触发数据加载完成事件
    emit('dataLoaded', list.value);
  } catch (error) {
    console.error('加载数据失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
    firstLoading.value = false;
  } finally {
    loading.value = false;
  }
};

// 上拉加载更多
const onScrollToLower = () => {
  if (hasMore.value && !loading.value && props.enablePullUp && !props.disabledRequest) {
    loadData();
  }
};

// 刷新数据
const refresh = () => {
  loadData(true);
};

// 监听参数变化，重新加载数据
watch(
  () => props.extraParams,
  (newVal, oldVal) => {
    if (!newVal) return;

    // 更新参数
    Object.keys(newVal).forEach(key => {
      pageParams[key] = newVal[key];
    });

    // 重新加载数据
    if (!props.disabledRequest) {
      refresh();
    }
  },
  { deep: true }
);

// 监听disabledRequest变化
watch(
  () => props.disabledRequest,
  (newVal, oldVal) => {
    // 如果从禁用状态变为启用状态，并且列表为空，则加载数据
    if (oldVal === true && newVal === false && list.value.length === 0) {
      refresh();
    } else if (newVal === true) {
      list.value = [];
    }
  }
);

// 挂载时加载数据
onMounted(() => {
  if (props.autoLoad && !props.disabledRequest) {
    loadData(true);
  }
});

// 暴露方法
defineExpose({
  refresh,
  loadData,
  list,
});
</script>

<style lang="scss" scoped>
.page-list {
  height: 100%;
  position: relative;

  .scroll-container {
    height: 100%;
  }

  .list-content {
    min-height: calc(100% - 80rpx);
  }

  .empty-container {
    padding: 200rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .list-footer {
    padding: 30rpx 0;
    text-align: center;

    .loading-state,
    .no-more-state,
    .pull-up-hint {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #86909c;
      font-size: 24rpx;
    }
  }

  .first-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #86909c;
    font-size: 28rpx;
  }
}
</style>
