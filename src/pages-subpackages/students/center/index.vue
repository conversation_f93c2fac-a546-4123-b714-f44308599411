<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="app-center-header">
      <view class="text-search">
        <view class="text-search-title">应用中心</view>
        <view class="text-search-input" @tap="handleSearch">
          <LkSvg width="24px" height="24px" src="/static/database/search.svg" />
        </view>
      </view>
      <!-- 锚点定位 tab -->
      <u-tabs
        :list="scenesList"
        lineColor="transparent"
        keyName="name"
        v-model:current="currentTab"
        class="app-list-tabs"
        itemStyle="height: 80rpx;"
        :scrollable="true"
      >
        <template #content="{ item, keyName, index }">
          <view class="app-list-tabs-item" @tap="handleTabClick(item, index)">
            <view class="app-list-tabs-item-box">
              <view
                class="app-list-tabs-item-text"
                :class="currentTab === index ? 'active' : 'inactive'"
              >
                {{ item[keyName] }}
              </view>

              <image
                v-show="currentTab === index"
                class="app-list-tabs-item-icon"
                src="https://huayun-ai-obs-public.huayuntiantu.com/a4180d57745271fa2056e474797826f7.png"
              >
              </image>
            </view>
          </view>
        </template>
        <template #left>
          <view class="app-list-tabs_empty"></view>
        </template>
      </u-tabs>
    </view>

    <view class="app-list-container">
      <LkLoading v-if="loading" :loading="loading" class="loading-icon" />

      <!-- 应用列表 -->
      <scroll-view
        scroll-y
        class="app-list"
        :class="{ 'ios-scroll': systemInfo?.platform === 'ios' }"
        :style="{
          height: getAppListHeight(),
        }"
        :scroll-into-view="targetId"
        :show-scrollbar="false"
        :bounces="false"
        :scroll-with-animation="true"
        @scroll="handleScroll"
      >
        <uni-swipe-action
          style="position: relative"
          v-for="(item, outerIndex) in combinedList"
          :id="'tab-' + item?.sceneId"
        >
          <view class="app-list-container" :key="item?.sceneId">
            <view class="app-list-item-title">
              <view class="title-box">
                <view class="title-box-text">{{ item?.name }}</view>
                <image
                  v-if="checkString(item?.name)"
                  class="app-list-item-title-icon"
                  src="https://huayun-ai-obs-public.huayuntiantu.com/77c2698413255275ad29152c8d515e1f.png"
                />
                <image
                  v-else
                  class="app-list-item-title-icon-long"
                  src="https://huayun-ai-obs-public.huayuntiantu.com/33fc7ff8-3dc3-48fc-a832-dc844acc701b.png"
                />
              </view>
            </view>

            <uni-swipe-action-item
              v-for="(appItem, innerIndex) in item?.apps"
              class="app-list-item"
              :auto-close="true"
              :show="openedStates[appItem.id]"
              :disabled="appItem.source !== DataSource.Personal"
              @change="(e: any) => handleSwipeActionChange(e, appItem.id)"
            >
              <view class="app-list-item-content" @tap.stop="handleItemClick(appItem)">
                <view class="icon">
                  <image
                    :src="
                      appItem?.avatarUrl ||
                      'https://huayun-ai-obs-public.huayuntiantu.com/7dd8c2f8d3b0d94cc769ccee58f8b753.svg'
                    "
                    lazy-load
                  />
                </view>

                <view class="box">
                  <view class="box-title">{{ appItem?.name }}</view>
                  <view class="box-text">{{ appItem.intro }}</view>
                  <view class="box-icon">
                    <image :src="appSourceImgObj[appItem?.source]" />
                    <text>{{ appSourceTextObj[appItem?.source] }}</text>
                  </view>
                </view>
              </view>

              <template v-slot:right>
                <view class="slide-btn-container">
                  <view v-if="!deleteStates[appItem.id]" class="slide-btn">
                    <view class="slide-btn-item" @touchstart.stop.prevent="handleEditApp(appItem)">
                      <image
                        src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/d5e3c541edb2e8c317d6d04b35097428.svg"
                      />
                    </view>
                    <view class="slide-btn-item" @touchstart.stop="handleDelete(appItem.id)">
                      <image
                        src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3d7681a3318bfc7f020f70f91973d526.svg"
                      />
                    </view>
                  </view>
                  <view class="slide-btn" v-else>
                    <view
                      class="slide-pop-btn"
                      @touchstart.stop="confirmDelete(appItem.id, appItem.tmbId)"
                    >
                      <image
                        src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3d7681a3318bfc7f020f70f91973d526.svg"
                      />
                      <text>确认删除</text>
                    </view>
                  </view>
                </view>
              </template>
            </uni-swipe-action-item>
          </view>
          <view
            style="height: 10rpx; background: #fff; visibility: hidden"
            v-if="outerIndex === combinedList.length - 1"
            class="app-list-item-placeholder"
          ></view>
        </uni-swipe-action>
      </scroll-view>
    </view>
  </view>
  <LkToast ref="uToastRef" />
  <LkStepGuide
    :steps="guideSteps"
    @complete="onGuideComplete"
    @next="onGuideNext"
    ref="stepGuide"
  />
</template>

<script setup lang="ts">
import {
  deleteClientApp,
  getMyAppList,
  getOtherAppList,
  getTenantSceneList,
  getTenantSceneListOfficialHome,
  rmCommonApp,
  setCommonApp,
} from '@/api/app-center';
import { SearchType } from '@/constants/search';
import { ref, onMounted, computed, watch, toRef, nextTick, onUnmounted } from 'vue';
import { useAppStore } from '@/store/useAppStore';
import type { AppListItemType } from '@/types/api/app-center';
import { onLoad, onResize, onShow } from '@dcloudio/uni-app';
import LkSvg from '@/components/svg/index.vue';
enum DataSource {
  /** 专属 */
  Tenant = 1,
  /** 官方 */
  Offical = 2,
  /** 个人 */
  Personal = 3,
}
const { safeAreaInsets } = uni.getSystemInfoSync();

const appSourceImgObj = {
  [DataSource.Tenant]:
    'https://huayun-ai-obs-public.huayuntiantu.com/0c161930a6b4315e16d593e7ec1cdbb3.svg',
  [DataSource.Offical]:
    'https://huayun-ai-obs-public.huayuntiantu.com/56bcad1ca41befc676c13c77066acd9d.svg',
  [DataSource.Personal]:
    'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3dd9642ef52df696fec7f3ccb3fc3cf1.svg',
};

const appSourceTextObj = {
  [DataSource.Tenant]: '专属',
  [DataSource.Offical]: '官方',
  [DataSource.Personal]: '个人',
};

// 元素高度
const customTabbarHeight = ref(0);
const appCenterHeaderHeight = ref(0);
const systemInfo = ref<UniNamespace.GetSystemInfoResult>();
const tabContainerPaddingBottom = ref('85px');
/** 顶部应用列表 */
const scenesList = ref<any[]>([]);
const otherApps = ref<any[]>([]);
// 当前选中的tab (从0开始，对应数组索引)
const currentTab = ref(0);

const props = defineProps({
  platform: {
    type: String,
    default: 'ios',
  },
});

// 监控currentTab变化，用于调试
watch(
  currentTab,
  (newVal, oldVal) => {
    console.log(`currentTab变化: ${oldVal} -> ${newVal}`);
  },
  { immediate: true }
);
const uToastRef = ref<any>(null);
// 跳转元素id
const targetId = ref('');
// 控制滑动 - 修改为对象形式
const openedStates = ref<Record<string, 'left' | 'right' | 'none'>>({});
// 控制二次确认删除 - 修改为对象形式
const deleteStates = ref<Record<string, boolean>>({});
/** 应用列表 loading */
const loading = ref(false);

// 监控loading状态变化
watch(
  loading,
  (newVal, oldVal) => {
    if (!uni.getStorageSync('Guide')?.AppCenter) {
      startGuide();
    }
  },
  { immediate: true }
);

const appStore = useAppStore();
const loadStuApps = appStore.loadStuApps;

const combinedList = ref<{ name: any; sceneId: any; apps: AppListItemType[] }[]>([]);

// 添加新的状态变量记录正在处理的ID
const processingIds = ref<Record<string, boolean>>({});

// 添加标志位，防止重复初始化tab
const tabInitialized = ref(false);

// 移除tabs组件引用，不再需要
// const tabsRef = ref<any>(null);

// 计算引导目标索引
const guideOuterIdx = ref(-1);
const guideInnerIdx = ref(-1);
watch(
  combinedList,
  val => {
    let found = false;
    for (let i = 0; i < val.length; i++) {
      const apps = val[i].apps || [];
      if (apps.length > 0) {
        guideOuterIdx.value = i;
        guideInnerIdx.value = 0;
        found = true;
        break;
      }
    }
    if (!found) {
      guideOuterIdx.value = -1;
      guideInnerIdx.value = -1;
    }
  },
  { immediate: true }
);
const guideSteps = [
  {
    target: '.app-list-tabs_empty',
    content: '左右滑分类，应用一触即达。',
    position: 'bottom',
  },
  {
    target: '.create-app',
    content: '创建专属智能应用，懂你所需，高效助力。',
    position: 'top',
  },
  {
    target: '.index2-guide-target',
    content: '选择高频使用应用，添加至「我的常用」中，方便快速找到并使用。',
    position: 'bottom',
    lollipop: true,
    offsetX: 17,
    offsetY: -30,
    guideStepsStyle: {
      minWidth: '420rpx',
    },
  },
];

// 引用组件实例
const stepGuide = ref();

// 开始引导
const startGuide = () => {
  stepGuide.value.start();
};

const onGuideNext = () => {
  const secondTabId = scenesList.value[0].id;
  console.log('滑动到第二个tab:', secondTabId, scenesList);
  scrollToAnchor(secondTabId);
};

// 处理引导完成事件
const onGuideComplete = () => {
  const data = uni.getStorageSync('Guide') || {};
  uni.setStorageSync('Guide', { ...data, AppCenter: true });
};

// 新增防抖函数
let debounceTimer: any = null;
function debounce(fn: Function, delay: number = 300) {
  return function (this: any, ...args: any[]) {
    if (debounceTimer) clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

// 动态获取延迟时间
function getDebounceDelay() {
  return systemInfo.value?.platform === 'ios' ? 200 : 50;
}

// 添加滚动监听相关变量
const sectionPositions = ref<
  Array<{ id: string; top: number; height: number; originalIndex?: number } | null>
>([]);

// 可视区域监听相关变量
const visibleSections = ref<Set<number>>(new Set());
const viewportHeight = ref(0);
const scrollContainerTop = ref(0);
const scrollTop = ref(0);
const isScrolling = ref(false);
let scrollTimer: any = null;
let visibilityCheckTimer: any = null;

// 处理滚动事件
function handleScroll(e: any) {
  // 记录当前滚动位置
  scrollTop.value = e.detail.scrollTop;

  // 标记正在滚动中
  isScrolling.value = true;

  // 清除之前的定时器
  if (scrollTimer) clearTimeout(scrollTimer);
  if (visibilityCheckTimer) clearTimeout(visibilityCheckTimer);

  // 立即检查可视区域
  checkSectionVisibility();

  // 设置新的定时器，滚动结束后更新选中的tab
  scrollTimer = setTimeout(() => {
    updateCurrentTabByScrollWithVisibility();
    isScrolling.value = false;
  }, 100);
}

// 检查section可视区域状态 - 兼容安卓和iOS
function checkSectionVisibility() {
  if (sectionPositions.value.length === 0) {
    return;
  }

  // 获取当前视口信息
  const query = uni.createSelectorQuery();

  // 获取滚动容器信息
  query
    .select('.app-list')
    .boundingClientRect((containerRect: any) => {
      if (!containerRect) {
        console.warn('无法获取滚动容器信息');
        return;
      }

      viewportHeight.value = containerRect.height;
      scrollContainerTop.value = containerRect.top;

      console.log('视口信息:', {
        height: viewportHeight.value,
        top: scrollContainerTop.value,
        scrollTop: scrollTop.value,
      });

      // 检查每个section的可视状态
      const newVisibleSections = new Set<number>();

      sectionPositions.value.forEach((section, index) => {
        if (!section) return;

        // 计算section相对于视口的位置
        const sectionTop = section.top - scrollTop.value;
        const sectionBottom = sectionTop + section.height;

        // 判断section是否在可视区域内
        // 考虑一定的容差，section部分可见即认为可见
        const isVisible = sectionBottom > 0 && sectionTop < viewportHeight.value;

        // 更精确的可见性判断：section至少有30%在可视区域内
        const visibleHeight =
          Math.min(sectionBottom, viewportHeight.value) - Math.max(sectionTop, 0);
        const visibilityRatio = visibleHeight / section.height;

        if (isVisible && visibilityRatio > 0.3) {
          newVisibleSections.add(index);
          console.log(
            `Section ${index} (${section.id}) 可见, 可见比例: ${(visibilityRatio * 100).toFixed(1)}%`
          );
        }
      });

      // 更新可见sections
      visibleSections.value = newVisibleSections;
      console.log('当前可见的sections:', Array.from(visibleSections.value));
    })
    .exec();
}

// 基于可视区域状态更新当前选中的tab
function updateCurrentTabByScrollWithVisibility() {
  try {
    console.log('updateCurrentTabByScrollWithVisibility 被调用');
    console.log('当前可见sections:', Array.from(visibleSections.value));

    // 如果是tab点击导致的滚动，跳过更新
    if (isTabClickScrolling.value) {
      console.log('tab点击滚动中，跳过更新');
      return;
    }

    // 如果没有可见的sections，使用原来的逻辑
    if (visibleSections.value.size === 0) {
      console.log('没有可见sections，使用原来的滚动逻辑');
      updateCurrentTabByScroll();
      return;
    }

    // 找到最合适的section作为当前选中项
    let targetSectionIndex = 0;

    if (visibleSections.value.size === 1) {
      // 只有一个可见section，直接选择它
      targetSectionIndex = Array.from(visibleSections.value)[0];
      console.log('只有一个可见section:', targetSectionIndex);
    } else {
      // 多个可见sections，选择最靠近视口中心的
      let minDistanceToCenter = Infinity;
      const viewportCenter = viewportHeight.value / 2;

      visibleSections.value.forEach(index => {
        const section = sectionPositions.value[index];
        if (!section) return;

        const sectionTop = section.top - scrollTop.value;
        const sectionCenter = sectionTop + section.height / 2;
        const distanceToCenter = Math.abs(sectionCenter - viewportCenter);

        if (distanceToCenter < minDistanceToCenter) {
          minDistanceToCenter = distanceToCenter;
          targetSectionIndex = index;
        }
      });

      console.log('选择最靠近中心的section:', targetSectionIndex);
    }

    const tabListLength = scenesList.value.length;
    if (targetSectionIndex >= tabListLength) {
      console.warn('计算出的索引超出范围:', targetSectionIndex, '最大索引:', tabListLength - 1);
      targetSectionIndex = tabListLength - 1;
    }

    if (targetSectionIndex !== currentTab.value) {
      nextTick(() => {
        try {
          currentTab.value = targetSectionIndex;
          console.log('基于可视区域更新tab为:', currentTab.value);
        } catch (error) {
          console.warn('更新tab失败:', error);
        }
      });
    } else {
      console.log('tab索引相同，无需更新');
    }
  } catch (error) {
    console.warn('updateCurrentTabByScrollWithVisibility执行失败:', error);
  }
}

// 根据滚动位置更新当前选中的tab (保留原来的逻辑作为备用)
function updateCurrentTabByScroll() {
  try {
    console.log(
      'updateCurrentTabByScroll 被调用, isScrolling:',
      isScrolling.value,
      'scrollTop:',
      scrollTop.value
    );

    if (sectionPositions.value.length === 0) {
      console.log('sectionPositions为空，重新计算');
      calculateSectionPositions();
      return;
    }

    console.log('当前sectionPositions:', sectionPositions.value);

    let currentSectionIndex = 0;

    console.log('sectionPositions数组长度:', sectionPositions.value.length);
    console.log(
      'sectionPositions详细信息:',
      sectionPositions.value.map((item, index) => ({
        index,
        exists: !!item,
        id: item?.id,
        top: item?.top,
      }))
    );

    const validSections = sectionPositions.value
      .map((section, index) => ({ section, originalIndex: index }))
      .filter(item => item.section !== null && item.section !== undefined);

    console.log('有效的sections:', validSections);

    for (let i = validSections.length - 1; i >= 0; i--) {
      const { section, originalIndex } = validSections[i];

      if (section) {
        console.log(
          `检查section ${originalIndex} (${section.id}): top=${section.top}, scrollTop=${scrollTop.value}`
        );

        if (scrollTop.value >= section.top - 30) {
          currentSectionIndex = originalIndex;
          console.log(`选择section ${originalIndex} (${section.id})`);
          break;
        }
      }
    }

    console.log(
      '计算出的currentSectionIndex:',
      currentSectionIndex,
      '当前currentTab:',
      currentTab.value
    );

    if (!isScrolling.value) {
      console.log('不是滚动导致的，跳过更新');
      return;
    }

    if (isTabClickScrolling.value) {
      console.log('tab点击滚动中，跳过更新');
      return;
    }

    const tabListLength = scenesList.value.length;
    if (currentSectionIndex >= tabListLength) {
      console.warn('计算出的索引超出范围:', currentSectionIndex, '最大索引:', tabListLength - 1);
      currentSectionIndex = tabListLength - 1;
    }

    if (currentSectionIndex !== currentTab.value) {
      nextTick(() => {
        try {
          currentTab.value = currentSectionIndex;
          console.log('自动更新tab为:', currentTab.value);
        } catch (error) {
          console.warn('更新tab失败:', error);
        }
      });
    } else {
      console.log('tab索引相同，无需更新');
    }
  } catch (error) {
    console.warn('updateCurrentTabByScroll执行失败:', error);
  }
}

// 计算各个section的位置信息
function calculateSectionPositions() {
  console.log('开始计算section位置, combinedList长度:', combinedList.value.length);

  if (combinedList.value.length === 0) {
    console.warn('combinedList为空，无法计算section位置');
    return;
  }

  const query = uni.createSelectorQuery();

  sectionPositions.value = new Array(combinedList.value.length).fill(null);

  let completedQueries = 0;
  const totalQueries = combinedList.value.length;

  combinedList.value.forEach((item, index) => {
    const sectionId = `tab-${item.sceneId}`;
    console.log(`查询section: ${sectionId}, index: ${index}`);

    query.select(`#${sectionId}`).boundingClientRect((rect: any) => {
      completedQueries++;

      if (rect) {
        sectionPositions.value[index] = {
          id: item.sceneId,
          top: rect.top,
          height: rect.height,
          originalIndex: index,
        };
        console.log(`找到section ${sectionId}:`, rect, `原始索引: ${index}`);
      } else {
        console.warn(`未找到section元素: ${sectionId}`);
        sectionPositions.value[index] = null;
      }

      if (completedQueries === totalQueries) {
        console.log('所有section查询完成');
        validateSectionPositions();
      }
    });
  });

  query.exec();
}

// 验证section位置的函数
function validateSectionPositions() {
  console.log('Section positions calculated:', sectionPositions.value);
  console.log('Section positions (保持原始顺序):', sectionPositions.value);

  sectionPositions.value.forEach((section, index) => {
    if (section) {
      console.log(`验证: index ${index} -> sceneId ${section.id}, top: ${section.top}`);

      if (combinedList.value[index]) {
        const expectedSceneId = combinedList.value[index].sceneId;
        if (section.id !== expectedSceneId) {
          console.error(
            `索引不匹配! index ${index}: section.id=${section.id}, expected=${expectedSceneId}`
          );
        } else {
          console.log(`✓ 索引匹配: index ${index} -> ${section.id}`);
        }
      }
    } else {
      console.warn(`index ${index} 的section为null`);
    }
  });

  const tabList = scenesList.value;
  console.log('Tab列表:', tabList);
  console.log(
    'CombinedList:',
    combinedList.value.map((item, index) => ({ index, id: item.sceneId, name: item.name }))
  );
}

// 初始化可视区域监听
function initializeVisibilityMonitoring() {
  console.log('初始化可视区域监听');

  uni.getSystemInfo({
    success: res => {
      viewportHeight.value = res.windowHeight;
      console.log('设置视口高度:', viewportHeight.value);

      setTimeout(() => {
        checkSectionVisibility();
      }, 200);
    },
  });
}

// 定期检查可视区域状态（作为备用机制）
function startPeriodicVisibilityCheck() {
  if (visibilityCheckTimer) {
    clearInterval(visibilityCheckTimer);
  }

  visibilityCheckTimer = setInterval(() => {
    if (!isTabClickScrolling.value) {
      checkSectionVisibility();
    }
  }, 500);
}

// 停止定期检查
function stopPeriodicVisibilityCheck() {
  if (visibilityCheckTimer) {
    clearInterval(visibilityCheckTimer);
    visibilityCheckTimer = null;
  }
}

// 监听combinedList变化，重新计算section位置
watch(
  combinedList,
  () => {
    nextTick(() => {
      setTimeout(() => {
        calculateSectionPositions();
      }, 300);
    });
  },
  { deep: true }
);

// 添加防抖变量，避免重复点击
let tabClickTimer: any = null;

// 添加点击状态，用于视觉反馈
const isTabClicking = ref(false);

// 添加一个标志位，用于暂时禁用滚动跟随
const isTabClickScrolling = ref(false);

// 处理tab点击事件（包括重复点击）
function handleTabClick(item: any, index: number) {
  try {
    console.log('点击tab:', item, index, '当前currentTab:', currentTab.value);

    if (tabClickTimer) {
      clearTimeout(tabClickTimer);
    }

    isTabClicking.value = true;

    if (!item || !item.id) {
      console.warn('handleTabClick: item或item.id不存在', item);
      isTabClicking.value = false;
      return;
    }

    currentTab.value = index;

    isScrolling.value = false;
    isTabClickScrolling.value = true;

    tabClickTimer = setTimeout(() => {
      console.log('滚动到:', item);
      scrollToAnchor(item.id);

      setTimeout(() => {
        isTabClicking.value = false;
        setTimeout(() => {
          isTabClickScrolling.value = false;
          console.log('重新启用滚动跟随');
        }, 1000);
      }, 200);
    }, getDebounceDelay());
  } catch (error) {
    console.warn('handleTabClick执行失败:', error);
    isTabClicking.value = false;
    isTabClickScrolling.value = false;
  }
}

// handleChange函数已移除，现在使用handleTabClick处理所有tab点击事件

const fetchloadStuApps = () => {
  const hasProcessingItem = Object.values(processingIds.value).some(status => status);

  if (!hasProcessingItem) {
    loading.value = true;
  }
  const startTime = Date.now();
  return loadStuApps()
    .then(stuApps => {
      const data = computed(() => {
        return scenesList.value.map((scene: any) => {
          const apps = stuApps.filter((item: any) =>
            item.labelList?.some((it: any) => String(it.tenantSceneId) === String(scene.id))
          );
          if (scene.name === 'AI评价' && otherApps.value.length > 0) {
            apps.push(...otherApps.value);
          }
          return {
            name: scene.name,
            sceneId: scene.id,
            apps,
          };
        });
      });
      combinedList.value = data.value;
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log('combinedList', combinedList.value);
      console.log('应用列表---请求时间', duration);
      return combinedList.value;
    })
    .finally(() => {
      loading.value = false;

      nextTick(() => {
        setTimeout(() => {
          console.log('应用列表loading完成，开始初始化tab');

          calculateSectionPositions();

          initializeVisibilityMonitoring();

          if (scenesList.value.length > 0) {
            initializeSecondTab();
          }
        }, 300);
      });
    });
};

onShow(async () => {
  try {
    fetchGetTenantSceneList();
    fetchGetOtherAppList();

    await fetchloadStuApps();
  } catch (error) {
    console.error('加载数据失败:', error);
    uToastRef.value?.show({
      message: '加载数据失败，请重试',
      type: 'error',
    });
  }
});

onResize(() => {
  console.log('onResize窗口尺寸变化：');
  getDeviceInfo();
});

onMounted(async () => {
  try {
    await nextTick();
    console.log('safeAreaInsets', safeAreaInsets);
    getDeviceInfo();

    originalConsoleError = console.error;
    console.error = function (...args) {
      const errorMessage = args.join(' ');
      if (errorMessage.includes("Cannot read properties of undefined (reading 'rect')")) {
        console.warn('捕获到u-tabs组件rect错误，已忽略:', ...args);
        return;
      }
      originalConsoleError.apply(console, args);
    };

    setTimeout(() => {
      console.log('onMounted: 基础初始化完成，等待应用列表loading完成后进行tab初始化');
    }, 500);

    uni.onWindowResize(res => {
      console.log('onWindowResize窗口尺寸变化：', res);
      getDeviceInfo();
      setTimeout(() => {
        calculateSectionPositions();
      }, 100);
    });
  } catch (error) {
    console.error('onMounted执行失败:', error);
  }
});

let originalConsoleError: any = null;

onUnmounted(() => {
  uni.offWindowResize(res => {
    console.log('onUnmounted窗口尺寸变化：', res);
  });

  if (originalConsoleError) {
    console.error = originalConsoleError;
  }

  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  if (tabClickTimer) {
    clearTimeout(tabClickTimer);
  }
  if (visibilityCheckTimer) {
    clearInterval(visibilityCheckTimer);
  }
});

function getDeviceInfo() {
  const query = uni.createSelectorQuery();
  query
    .select('.app-center-header')
    .boundingClientRect((data: any) => {
      if (data) {
        appCenterHeaderHeight.value = data.height;
        console.log('appCenterHeaderHeight', appCenterHeaderHeight.value);
      } else {
        console.error('未能获取到.app-center-header元素');
        appCenterHeaderHeight.value = 120;
      }
    })
    .exec();

  query
    .select('.custom-tabbar-wrap')
    .boundingClientRect((data: any) => {
      if (data) {
        customTabbarHeight.value = data.height;
        console.log('customTabbarHeight', customTabbarHeight.value);
      } else {
        console.error('未能获取到.custom-tabbar-wrap元素');
        customTabbarHeight.value = 50;
      }
    })
    .exec();

  query
    .select('.tab-container')
    .fields(
      {
        computedStyle: ['paddingBottom'],
      },
      (data: any) => {
        const paddingBottom = data.paddingBottom;
        tabContainerPaddingBottom.value = paddingBottom;
      }
    )
    .exec();

  uni.getSystemInfo({
    success: res => {
      systemInfo.value = res;

      if (res.platform === 'ios') {
        setTimeout(() => {
          const query = uni.createSelectorQuery();
          query
            .select('.app-list')
            .boundingClientRect(data => {
              if (data) {
                console.log('找到app-list元素，应用iOS优化');
              }
            })
            .exec();
        }, 300);
      }
    },
  });
}

function fetchGetTenantSceneList() {
  return getTenantSceneList().then(res => {
    scenesList.value = res;
    console.log('场景列表获取完成:', res.length, '个场景');

    return res;
  });
}

function fetchGetTenantSceneListOfficialHome() {
  return getTenantSceneListOfficialHome({}).then(res => {
    console.log('wdnmd-fetchGetTenantSceneListOfficialHome', res);
    return res;
  });
}

function fetchGetOtherAppList() {
  return getOtherAppList().then(res => {
    console.log('获取其他应用', res);
    otherApps.value = res;
    return res;
  });
}

function handleItemClick(appItem: any) {
  uni.setStorageSync('selectApp', appItem);
  uni.navigateTo({
    url: '/pages/chat/index',
  });
  console.log('点击应用项', appItem);
  if (openedStates.value[appItem.id] === 'right') {
    openedStates.value[appItem.id] = 'none';
    deleteStates.value[appItem.id] = false;
  }
}

function handleDelete(id: string) {
  deleteStates.value[id] = true;
}

function confirmDelete(id: string, tmbId: string) {
  console.log('确认删除应用id', id, tmbId);

  deleteClientApp({ id, tmbId })
    .then(res => {
      console.log('删除应用', res);
      uToastRef.value.show({
        message: '删除成功',
        type: 'success',
      });
    })
    .finally(() => {
      Object.keys(openedStates.value).forEach(key => {
        openedStates.value[key] = 'none';
      });
      Object.keys(deleteStates.value).forEach(key => {
        deleteStates.value[key] = false;
      });

      fetchloadStuApps();
    });
}

function handleSwipeActionChange(status: 'left' | 'right' | 'none', id: string) {
  console.log('滑动状态改变', status, id);

  openedStates.value[id] = status;

  if (status === 'none') {
    deleteStates.value[id] = false;
  }
}

function handleCreateApp() {
  uni.navigateTo({
    url: '/pages-subpackages/app-center-pkg/create-app/index',
  });
}

function handleSearch() {
  uni.setStorageSync('searchActiveType', SearchType.APP);
  uni.navigateTo({
    url: '/pages/search/index',
  });
}

async function scrollToAnchor(anchorId: string) {
  if (systemInfo.value?.platform === 'ios') {
    targetId.value = '';

    setTimeout(() => {
      targetId.value = `tab-${anchorId}`;

      setTimeout(() => {
        nextTick(() => {
          console.log('iOS滚动处理完成');
        });
      }, 50);
    }, 100);
  } else {
    targetId.value = `tab-${anchorId}`;
  }
}

function handleSetCommonApp(id: string) {
  processingIds.value[id] = true;

  setCommonApp({ id })
    .then(res => {
      uToastRef.value.show({
        message: '已添加至首页-我的常用',
        type: 'success',
      });
    })
    .finally(() => {
      processingIds.value[id] = false;
      fetchloadStuApps();
    });
}

function handleRemoveCommonApp(id: string) {
  processingIds.value[id] = true;

  rmCommonApp({ id })
    .then(res => {
      uToastRef.value.show({
        message: '已从首页-我的常用中移除',
        type: 'success',
      });
    })
    .finally(() => {
      processingIds.value[id] = false;
      fetchloadStuApps();
    });
}

function handleEditApp(params: any) {
  console.log('点击编辑应用', params);
  if (params.id) {
    openedStates.value[params.id] = 'none';
  }

  uni.navigateTo({
    url: `/pages-subpackages/app-center-pkg/edit-app/index?id=${params.id}`,
  });
}

function getAppListHeight() {
  const safeAreaBottom = safeAreaInsets?.bottom || 0;

  if (systemInfo.value?.platform === 'ios') {
    return `calc(100vh - (${appCenterHeaderHeight.value}px + ${customTabbarHeight.value}px)`;
  } else {
    return `calc(100vh - (${appCenterHeaderHeight.value}px + ${customTabbarHeight.value}px))`;
  }
}

function checkString(str: string) {
  const isAllChinese = /^[\u4e00-\u9fa5]+$/.test(str);
  const hasAlpha = /[a-zA-Z]/.test(str);

  if (str.length < 4) {
    return true;
  } else if (hasAlpha && str.length <= 4) {
    return true;
  } else if (isAllChinese && str.length <= 4) {
    return false;
  } else {
    return false;
  }
}
console.log('checkString', checkString('评价'));

function initializeSecondTab() {
  try {
    if (tabInitialized.value || scenesList.value.length === 0) {
      return;
    }

    tabInitialized.value = true;

    setTimeout(() => {
      try {
        currentTab.value = 0;
        console.log('初始化设置currentTab为:', currentTab.value);

        isScrolling.value = false;
        isTabClickScrolling.value = true;

        const delay = systemInfo.value?.platform === 'ios' ? 300 : 150;

        setTimeout(() => {
          if (scenesList.value.length > 0) {
            const secondTabId = scenesList.value[0].id;
            console.log('初始化第二个tab:', secondTabId);
            scrollToAnchor(secondTabId);

            setTimeout(() => {
              isTabClickScrolling.value = false;
              console.log('初始化滚动完成，启用滚动跟随');

              console.log('Tab切换完成，内容区域已更新');
            }, 800);
          }
        }, delay);
      } catch (error) {
        console.warn('initializeSecondTab内部执行失败:', error);
        isTabClickScrolling.value = false;
      }
    }, 500);
  } catch (error) {
    console.warn('initializeSecondTab执行失败:', error);
    isTabClickScrolling.value = false;
  }
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.ios-scroll {
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
}

.container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: url('https://huayun-ai-obs-public.huayuntiantu.com/83fb33156350a87984c65ad3d7493509.png')
    no-repeat center center;
  background-size: 100% 100%;
  overflow: hidden;
  padding-top: 14px;
}

.create-app {
  position: fixed;
  bottom: 70 * 2rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  display: flex;
  width: 135 * 2rpx;
  height: 40 * 2rpx;
  padding: 8 * 2rpx 16 * 2rpx;
  justify-content: center;
  align-items: center;
  gap: 4 * 2rpx;
  flex-shrink: 0;
  border-radius: 100 * 2rpx;
  background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
  box-shadow: 0px 4 * 2rpx 4 * 2rpx 0px rgba(154, 142, 255, 0.23);
  color: #fff;
  image {
    width: 20 * 2rpx;
    height: 20 * 2rpx;
  }
  .create-app-text {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14 * 2rpx;
    image {
      width: 20 * 2rpx;
      height: 20 * 2rpx;
    }
  }
}

.app-center-header {
  flex-shrink: 0;
  z-index: 10;
}

.text-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16 * 2rpx;
  padding-top: 0;
  padding-bottom: 0;
  padding-top: var(--status-bar-height);
  .text-search-title {
    font-size: 20 * 2rpx;
    font-weight: 700;
  }
}

.app-list-tabs {
  position: relative;
  &_empty {
    position: absolute;
    top: -2rpx;
    left: 8rpx;
    right: 8rpx;
    bottom: -2rpx;
    border-radius: 8px;
  }

  .app-list-tabs-item {
    display: flex;
    align-items: center;
    height: 48 * 2rpx;
  }

  .active {
    font-size: 36rpx;
    color: #1d2129;
    font-weight: 600;
  }

  .inactive {
    font-size: 32rpx;
    color: #4e5969;
  }

  .app-list-tabs-item-box {
    position: relative;
    overflow: visible;
    height: 24 * 2rpx;
    text-align: center;
    line-height: 24 * 2rpx;
  }

  .app-list-tabs-item-text {
    display: inline-block;
    position: relative;
    white-space: nowrap;
    overflow: auto;
    height: 100%;
    z-index: 2;
  }

  .app-list-tabs-item-icon {
    position: absolute;
    top: -16%;
    right: -20%;
    width: 23 * 2rpx;
    height: 20 * 2rpx;
    z-index: 1;
  }
}
.app-list-container {
  position: relative;
  margin: 0 16 * 2rpx;
  overflow: hidden;
}

.app-list {
  flex: 1;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  position: relative;
  box-sizing: border-box;

  .app-list-container {
    width: 100%;
    background-color: #fff;
    border-radius: 14 * 2rpx;
    margin: 0 auto;
    margin-bottom: 14 * 2rpx;
  }

  .app-list-item-title {
    position: relative;
    color: #000;
    font-size: 18 * 2rpx;
    font-weight: 500;
    height: 51 * 2rpx;
    border-bottom: 1px solid #f2f3f5;
    padding-top: 12 * 2rpx;
    padding-left: 16 * 2rpx;

    .title-box {
      position: relative;
      display: inline-block;
      .title-box-text {
        position: relative;
        z-index: 2;
      }
      .app-list-item-title-icon {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 53 * 2rpx;
        width: 100%;
        height: 9 * 2rpx;
        z-index: 1;
      }
      .app-list-item-title-icon-long {
        position: absolute;
        left: 0;
        bottom: -4 * 2rpx;
        width: 63 * 2rpx;
        width: 100%;
        height: 16 * 2rpx;
        z-index: 1;
      }
    }
  }

  .app-list-item {
    width: 100%;
    height: 88 * 2rpx;
    margin: 0 auto;

    .app-list-item-content {
      display: flex;
      width: 100%;
      height: 88 * 2rpx;
      align-items: center;
      padding: {
        left: 16 * 2rpx;
        right: 14 * 2rpx;
        top: 12 * 2rpx;
        bottom: 12 * 2rpx;
      }
      .icon {
        width: 58 * 2rpx;
        height: 58 * 2rpx;
        background-color: #f1f2f4;
        border-radius: 50 * 2rpx;
        overflow: hidden;
        image,
        .image {
          width: 100%;
          height: 100%;
        }
      }
      .box-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 24rpx;
        min-height: 88rpx;
        min-width: 88rpx;
        width: 88rpx;
        height: 88rpx;
      }

      .box {
        width: 65%;
        flex: 1;
        margin: 0 10 * 2rpx;
        margin-right: 5rpx;
        .box-title {
          font-size: 16 * 2rpx;
          font-weight: 500;
          color: #000;
          line-height: 24 * 2rpx;
        }
        .box-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #606266;
          font-size: 12 * 2rpx;
          font-weight: 400;
          line-height: 20 * 2rpx;
        }
        .box-icon {
          display: flex;
          align-items: center;
          font-size: 12 * 2rpx;
          color: #909399;
          font-weight: 400;
          line-height: 16 * 2rpx;
          image {
            width: 14 * 2rpx;
            height: 14 * 2rpx;
            margin-right: 4 * 2rpx;
          }
        }
      }

      .btn {
        border-radius: 100 * 2rpx;
        .btn-add {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28 * 2rpx;
          height: 28 * 2rpx;
          background: linear-gradient(180deg, #ab57ff 0%, #7d4dff 100%);
          border-radius: 100 * 2rpx;
          image {
            width: 18 * 2rpx;
            height: 18 * 2rpx;
          }

          &.btn-disabled {
            opacity: 0.7;
            pointer-events: none;
          }
        }
        .btn-done {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28 * 2rpx;
          height: 28 * 2rpx;
          background-color: #f2f3f5;
          border-radius: 100 * 2rpx;
          image {
            width: 18 * 2rpx;
            height: 18 * 2rpx;
          }

          &.btn-disabled {
            opacity: 0.7;
            pointer-events: none;
          }
        }
      }
    }
  }

  .slide-btn-container {
    width: 51 * 2 * 2rpx;

    .slide-btn {
      display: flex;
      height: 100%;

      .slide-pop-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        height: 100%;
        background-color: #db443c;
        text {
          color: #fff;
          font-size: 14 * 2rpx;
        }
        image {
          width: 20 * 2rpx;
          height: 20 * 2rpx;
        }
      }

      .slide-btn-item {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        height: 100%;

        image {
          width: 20 * 2rpx;
          height: 20 * 2rpx;
        }
        &:nth-child(1) {
          background-color: #7d4dff;
        }
        &:nth-child(2) {
          background-color: #db443c;
        }
      }
    }
  }
}

.loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10001;
}

::v-deep .u-tabs__wrapper__nav__item {
  padding: 0 16 * 2rpx;
}
</style>
