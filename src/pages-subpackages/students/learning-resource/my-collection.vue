<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { HeaderNav } from '@/components/LkNavBar';
import LkSvg from '@/components/svg/index.vue';
import FileContent from './component/filecontent.vue';
import FilterArea from './component/filter-area.vue';
import {
  getSearchCollectResources,
  getSubjects,
  getResourceTypes,
  getResourceFormats,
  getCancelCollectResources,
  getGradesList,
} from '@/api/students/resourceCenter';
import type {
  AppResourceCollectSearchRequest,
  CollectedResourceItem,
  SubjectsItem,
  ResourceTypesItem,
  ResourceFormatsItem,
  GradesItem,
} from '@/types/students/resourceCenter';
import LkGradeSelector from '@/components/LkGradeSelector/index.vue';

// 类型定义
interface GradeInfo {
  name: string;
  version: string;
  semester?: string;
  volume?: string;
  textbookVersionId?: number;
  textbookVolumeId?: number;
  gradeId?: number;
  textbookVolumeCategory?: number;
}

// 响应式数据
const keyword = ref('');
const isLoading = ref(false);
const collectionList = ref<CollectedResourceItem[]>([]);

// 分页相关
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
const totalPages = ref(0);

// 筛选相关
const currentGrade = ref<GradeInfo>({ name: '全部', version: '全部' });
const currentSubject = ref('全部');
const currentSource = ref(0);
const currentType = ref<number[]>([0]); // 修改为数组格式，与 index.vue 保持一致
// 格式相关
const currentFormat = ref<number[]>([0]);
const sourceList = ref(['官方资源', '校本资源', '个人资源', '第三方资源']);
// 筛选选项列表
const subjectList = ref<SubjectsItem[]>([]);
const typeList = ref<ResourceTypesItem[]>([]);
const formatList = ref<ResourceFormatsItem[]>([]);
const selectedItems = ref<CollectedResourceItem[]>([]);
const showGradePopup = ref(false);

// 年级列表相关
const gradesList = ref<GradesItem[]>([]);
const selectedGradeId = ref<number>(0);

// 年级下拉选择相关
const showGradeDropdown = ref(false);

// 格式选择弹窗相关
const showFormatPopup = ref(false);
const currentFormatCategory = ref<any>(null);
const tempSelectedFormats = ref<number[]>([]);

onMounted(() => {
  initializeData();
  loadCollectionList();

  // 添加点击外部关闭下拉框的监听器
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  // 移除监听器
  document.removeEventListener('click', handleClickOutside);
});

// 点击外部关闭下拉框
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const gradeSelector = document.querySelector('.grade-selector');

  if (gradeSelector && !gradeSelector.contains(target)) {
    showGradeDropdown.value = false;
  }
};
watch(currentGrade, () => {
  console.log('currentGrade44455455', currentGrade.value);
  fetchSubjectList();
});
// 初始化数据
const initializeData = async () => {
  // 获取学生默认年级和教材版本信息
  const studentInfo = uni.getStorageSync('defultgrade');
  if (studentInfo) {
    // 更新当前年级信息
    currentGrade.value = {
      name: studentInfo.gradeName,
      version: studentInfo.textbookVersionName,
      semester: '上册', // 默认上册
      volume: '1',
      textbookVersionId: studentInfo.textbookVersionId,
      textbookVolumeId: 1, // 默认上册对应的册别ID
      gradeId: studentInfo.gradeId,
    };

    // 设置默认选中的年级ID
    selectedGradeId.value = studentInfo.gradeId;

    console.log('学生默认信息已设置到我的收藏页面:', currentGrade.value);
  }

  await Promise.all([
    fetchSubjectList(),
    fetchResourceTypes(),
    fetchResourceFormats(),
    fetchGradesList(),
  ]);
};

const handleCloseGradePopup = () => {
  showGradePopup.value = false;
};
const handleGradeConfirm = (data: {
  gradeName: string;
  gradeId: number;
  versionName: string;
  semesterId: number;
  textbookVersionId: number;
  textbookVolumeId: number;
  textbookVolumeCategory?: number;
}) => {
  console.log('年级选择确认数据:', data);

  // 更新当前年级信息
  currentGrade.value = {
    name: data.gradeName,
    version: data.versionName,
    gradeId: data.gradeId,
    semester: data.semesterId.toString(),
    volume: data.textbookVolumeId.toString(),
    textbookVolumeCategory: data.textbookVolumeCategory,
    textbookVersionId: data.textbookVersionId,
    textbookVolumeId: 0,
  };

  // 重新获取资源列表
  loadCollectionList();
  showGradePopup.value = false;
};

// 获取学科列表
const fetchSubjectList = async () => {
  try {
    const res = await getSubjects({ gradeId: currentGrade.value?.gradeId });
    subjectList.value = res.subjects;
  } catch (error) {
    console.error('获取学科列表失败:', error);
  }
};

// 获取资源类型列表
const fetchResourceTypes = async () => {
  try {
    const res = await getResourceTypes();
    typeList.value = res.resourceTypes;
  } catch (error) {
    console.error('获取资源类型失败:', error);
  }
};

// 获取资源格式列表
const fetchResourceFormats = async () => {
  try {
    const res = await getResourceFormats();
    formatList.value = res.resourceFormats;
  } catch (error) {
    console.error('获取资源格式失败:', error);
  }
};

// 获取年级列表
const fetchGradesList = async () => {
  try {
    const res = await getGradesList();
    gradesList.value = res.grades;
    console.log('获取年级列表成功:', gradesList.value);
  } catch (error) {
    console.error('获取年级列表失败:', error);
    gradesList.value = [];
  }
};

// 构建搜索参数
const buildSearchParams = (): AppResourceCollectSearchRequest => {
  // 获取选中的学科ID
  const selectedSubjectId =
    currentSubject.value === '全部' ? 0 : parseInt(currentSubject.value) || 0;

  // 获取选中的资源类型ID数组
  const selectedTypeIds = currentType.value.includes(0) ? [] : currentType.value;

  // 获取选中的格式ID数组
  const selectedFormatIds = currentFormat.value.includes(0) ? [] : currentFormat.value;

  return {
    searchInfo: keyword.value.trim() || undefined,
    attribution: Number(currentSource.value),
    subjectId: selectedSubjectId || undefined,
    resourceTypeIds: selectedTypeIds.length > 0 ? selectedTypeIds : undefined,
    fileFormatIds: selectedFormatIds.length > 0 ? selectedFormatIds : undefined,
    gradeId: currentGrade.value.gradeId,
    textbookVersionId: currentGrade.value.textbookVersionId,
    textbookVolumeId: 0, // 使用存储的教材册别ID
    textbookVolumeCategory: currentGrade.value.textbookVolumeCategory,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    subjectName: currentSubject.value,
    gradeName: currentGrade.value.name,
  };
};

// 加载收藏列表
const loadCollectionList = async (resetPage = true) => {
  if (resetPage) {
    pageNum.value = 1;
  }

  isLoading.value = true;

  try {
    const params = buildSearchParams();
    console.log('搜索收藏资源参数:', params);

    const response = await getSearchCollectResources(params);
    console.log('搜索收藏资源响应:', response);

    collectionList.value = response.records;
    total.value = response.total;
    totalPages.value = response.totalPages;
    pageNum.value = response.pageNum;
    pageSize.value = response.pageSize;
  } catch (error) {
    console.error('获取收藏资源失败:', error);
    uni.showToast({
      title: '获取收藏资源失败',
      icon: 'none',
    });
    collectionList.value = [];
  } finally {
    isLoading.value = false;
  }
};

const handleBack = () => {
  uni.navigateBack();
};

const handleSearch = () => {
  loadCollectionList();
};

const handleClearKeyword = () => {
  keyword.value = '';
  loadCollectionList();
};
const handleCancelFavorite = async (resourceIds: any[]) => {
  const res = await getCancelCollectResources({
    resourceIds: resourceIds.map(item => item.resourceId),
  });
  console.log('取消收藏资源结果:', res);
  loadCollectionList();
};
// 筛选变更事件处理（暂时保留，可能在 FilterArea 组件中需要）

const handleSelectSubject = (subject: string) => {
  currentSubject.value = subject;
  loadCollectionList();
};

const handleOpenGradeFilter = () => {
  showGradePopup.value = true;
};

// 处理年级下拉框打开/关闭
const handleOpenGradeDropdown = () => {
  showGradeDropdown.value = !showGradeDropdown.value;
};

// 处理年级下拉选择
const handleGradeSelect = (item: GradesItem) => {
  console.log('年级选择:', item);
  selectedGradeId.value = item.id;

  // 更新当前年级信息
  currentGrade.value = {
    ...currentGrade.value,
    name: item.name,
    gradeId: item.id,
  };

  // 关闭下拉列表
  showGradeDropdown.value = false;

  // 重新获取资源列表
  loadCollectionList();
};

const handleSelectSource = (source: number) => {
  console.log('handleSelectSource', source);
  // 单选逻辑，直接设置选中的来源
  currentSource.value = source;
  loadCollectionList();
};

const handleSelectType = (types: number[]) => {
  // 将数组转换为逗号分隔的字符串用于显示和API调用
  currentType.value = [...types];
  console.log('收藏页面类型选择:', currentType.value);
  loadCollectionList();
};

const handleSelectFormat = (formats: number[]) => {
  console.log('handleSelectFormat', formats);
  currentFormat.value = formats;
  // 触发搜索
  loadCollectionList();
};

// 处理格式筛选弹窗打开
const handleOpenFormatFilter = (data: { category: any; currentSelected: number[] }) => {
  currentFormatCategory.value = data.category;
  tempSelectedFormats.value = [...data.currentSelected];
  showFormatPopup.value = true;
};

// 处理格式筛选弹窗重制
const handleResetFormatFilter = () => {
  tempSelectedFormats.value = [];
};

const handleCloseFormatFilter = () => {
  showFormatPopup.value = false;
};

// 弹窗中的格式选择 - "全部"和其他选项互斥
const handleSelectFormatInPopup = (format: number) => {
  console.log('handleSelectFormatInPopup', format);

  // 如果点击的是"全部"选项 (format = 0)
  if (format === 0) {
    // 获取所有格式选项的ID
    const allFormatIds = currentFormatCategory.value?.children?.map((item: any) => item.id) || [];

    // 如果当前已经选择了全部，则清空选择
    if (tempSelectedFormats.value.includes(0)) {
      tempSelectedFormats.value = [];
    } else {
      // 选择全部：包含0和所有格式ID，但UI上只显示"全部"选中
      tempSelectedFormats.value = [0, ...allFormatIds];
    }
  } else {
    // 处理单个格式选择
    // 如果当前选中了"全部"，先清除"全部"选择
    if (tempSelectedFormats.value.includes(0)) {
      tempSelectedFormats.value = tempSelectedFormats.value.filter(f => f !== 0);
      // 清除所有格式ID，只保留当前点击的格式
      const allFormatIds = currentFormatCategory.value?.children?.map((item: any) => item.id) || [];
      tempSelectedFormats.value = tempSelectedFormats.value.filter(f => !allFormatIds.includes(f));
      tempSelectedFormats.value.push(format);
    } else {
      // 正常的多选逻辑
      if (tempSelectedFormats.value.includes(format)) {
        // 取消选择该格式
        tempSelectedFormats.value = tempSelectedFormats.value.filter(f => f !== format);
      } else {
        // 选择该格式
        tempSelectedFormats.value = [...tempSelectedFormats.value, format];
      }
    }
  }
};

// 保存格式筛选
const handleSaveFormatFilter = () => {
  // 触发格式选择事件
  handleSelectFormat(tempSelectedFormats.value);
  // 关闭弹窗
  showFormatPopup.value = false;
};

// 处理选择资源
const handleSelect = (adaptedResource: any) => {
  console.log('handleSelect4444', adaptedResource);
  // 从适配后的资源中找到原始资源
  const originalResource = collectionList.value.find(
    item => item.resourceId === adaptedResource.id
  );
  if (originalResource) {
    const isAlreadySelected = selectedItems.value.some(
      item => item.resourceId === originalResource.resourceId
    );
    if (isAlreadySelected) {
      // 取消选择
      selectedItems.value = selectedItems.value.filter(
        item => item.resourceId !== originalResource.resourceId
      );
    } else {
      // 添加选择
      selectedItems.value = [...selectedItems.value, originalResource];
    }
  }
};

// 将 CollectedResourceItem 转换为 FileContent 组件期望的格式
const adaptResourceForFileContent = (resource: CollectedResourceItem) => {
  return {
    id: resource.resourceId, // 使用 collectId 作为 id
    resourceId: resource.resourceId, // 保留原始 resourceId
    title: resource.title,
    format: resource.fileFormatName.toLowerCase(), // 转换为小写
    fileSize: resource.fileSize, // 格式化文件大小
    updateTime: formatDate(resource.updateTime), // 格式化日期
    type: resource.resourceTypeName,
    gradeName: currentGrade.value.name,
    attribution: resource.attribution, // 根据 attribution 转换
    source: resource.attributionName,
    tags: [resource.attributionName],
    fileFormatName: resource.fileFormatName,
    subjectName: resource.subjectName,
    resourceTypeName: resource.resourceTypeName,
  };
};

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

// 资源相关方法
const handleResourceClick = (resource: any) => {
  // 跳转到资源详情页，使用 resourceId
  const resourceId = resource.resourceId || resource.id;
  uni.navigateTo({
    url: `/pages-subpackages/students/learning-resource/resource-details?id=${resourceId}`,
  });
};
</script>

<template>
  <view class="collection-page">
    <!-- 导航栏 -->
    <HeaderNav title="我的收藏" @back="handleBack" :centerTitle="true">
      <template #right>
        <view class="grade-selector" @click.stop="handleOpenGradeDropdown">
          <view class="grade-selector-content">
            <text class="grade-text">{{ currentGrade.name }}</text>
            <LkSvg
              width="24px"
              height="24px"
              src="/static/learning-resource/down.svg"
              class="arrow-icon"
              :class="{ 'arrow-up': showGradeDropdown }"
            />
          </view>

          <!-- 年级下拉列表 -->
          <view v-if="showGradeDropdown" class="grade-dropdown">
            <view
              v-for="grade in gradesList"
              :key="grade.id"
              class="grade-dropdown-item"
              :class="{ active: grade.id === selectedGradeId }"
              @click.stop="handleGradeSelect(grade)"
            >
              {{ grade.name }}
            </view>
          </view>
        </view>
      </template>
    </HeaderNav>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-input-box">
        <LkSvg
          width="20px"
          height="20px"
          src="/static/learning-resource/search.svg"
          class="search-icon"
        />
        <input
          class="input"
          type="text"
          v-model="keyword"
          placeholder="请输入"
          @confirm="handleSearch"
          maxlength="50"
        />
        <view v-if="keyword" class="clear-btn" @click="handleClearKeyword">
          <LkSvg width="16px" height="16px" src="/static/icons/close.svg" />
        </view>
      </view>
    </view>
    <!-- 筛选区域 -->
    <view class="filter-area">
      <FilterArea
        ref="filterAreaRef"
        :currentGrade="currentGrade"
        :currentSubject="currentSubject"
        :currentSource="currentSource"
        :currentType="currentType"
        :currentFormat="currentFormat"
        :subjectList="subjectList"
        :sourceList="sourceList"
        :typeList="typeList"
        @openGradeFilter="handleOpenGradeFilter"
        @selectSubject="handleSelectSubject"
        @selectSource="handleSelectSource"
        @selectType="handleSelectType"
        @selectFormat="handleSelectFormat"
        @openFormatFilter="handleOpenFormatFilter"
      />
    </view>

    <!-- 收藏列表 -->
    <view class="collection-list-container">
      <!-- 骨架屏 -->
      <template v-if="isLoading">
        <view class="skeleton-container">
          <view v-for="index in 4" :key="`skeleton-${index}`" class="skeleton-item">
            <view class="skeleton-main-wrapper">
              <view class="skeleton-main">
                <view class="skeleton-checkbox"></view>
                <view class="skeleton-icon"></view>
                <view class="skeleton-info">
                  <view class="skeleton-title"></view>
                  <view class="skeleton-tags">
                    <view class="skeleton-tag"></view>
                    <view class="skeleton-tag"></view>
                    <view class="skeleton-tag"></view>
                  </view>
                </view>
              </view>
            </view>
            <view class="skeleton-footer">
              <view class="skeleton-meta">
                <view class="skeleton-meta-item"></view>
                <view class="skeleton-meta-item"></view>
              </view>
              <view class="skeleton-source-tag"></view>
            </view>
          </view>
        </view>
      </template>

      <view v-else-if="collectionList.length > 0" class="collection-list">
        <FileContent
          v-for="resource in collectionList"
          :key="resource.collectId"
          :item="adaptResourceForFileContent(resource)"
          :onItemClick="handleResourceClick"
          :check="true"
          :selectedItems="selectedItems.map(item => adaptResourceForFileContent(item))"
          @select="handleSelect"
          @cancelFavorite="handleCancelFavorite"
        />
      </view>

      <view v-else class="no-data">
        <LkSvg width="130px" height="130px" src="/static/learning-resource/nodata.svg" />
        <view class="no-data-text">暂无收藏资源</view>
      </view>
    </view>
    <!-- 年级选择弹窗 -->
    <LkGradeSelector
      :show="showGradePopup"
      @close="handleCloseGradePopup"
      @confirm="handleGradeConfirm"
    />

    <!-- 格式选择弹窗 -->
    <u-popup
      mode="bottom"
      :round="24"
      :safeAreaInsetBottom="true"
      :show="showFormatPopup"
      @close="handleCloseFormatFilter"
    >
      <view class="format-popup-content">
        <view class="popup-header">
          <view class="popup-title">选择{{ currentFormatCategory?.name || '格式' }}</view>
          <view class="popup-close" @click="handleCloseFormatFilter">
            <LkSvg width="20px" height="20px" src="/static/learning-resource/close.svg" />
          </view>
        </view>
        <view class="popup-body">
          <view class="popup-section">
            <view class="section-options">
              <view
                class="section-option"
                :class="{ active: tempSelectedFormats.includes(0) }"
                @click="handleSelectFormatInPopup(0)"
              >
                全部
              </view>
              <view
                class="section-option"
                v-for="(format, index) in currentFormatCategory?.children || []"
                :key="index"
                :class="{
                  active:
                    tempSelectedFormats.includes(format.id) && !tempSelectedFormats.includes(0),
                }"
                @click="handleSelectFormatInPopup(format.id)"
              >
                {{ format.name }}
              </view>
            </view>
          </view>
        </view>
        <view class="popup-footer">
          <view class="popup-btn cancel" @click="handleResetFormatFilter">重制</view>
          <view class="popup-btn confirm" @click="handleSaveFormatFilter">保存</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<style lang="scss" scoped>
.collection-page {
  min-height: 100vh;
  background-color: #f7f7fd;
  overflow: hidden;
  position: relative;
}

/* 年级选择器样式 */
.grade-selector {
  position: relative;
  display: flex;
  align-items: center;

  .grade-selector-content {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }

    .grade-text {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      white-space: nowrap;
    }

    .arrow-icon {
      transition: transform 0.3s ease;

      &.arrow-up {
        transform: rotate(180deg);
      }
    }
  }
}

.grade-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;

  .grade-dropdown-item {
    padding: 12px 16px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f5f5f5;
      text-align: center;
    }

    &.active {
      background-color: #e6f3ff;
      color: #1890ff;
      font-weight: 500;
    }
  }
}

.filter-area {
  background-color: #fff;
}
.search-container {
  padding: 16px;
  padding-bottom: 0;
  background-color: #fff;
}

.search-input-box {
  display: flex;
  border-radius: 8px;
  background: #f5f6f8;
  height: 40px;
  width: 100%;
  padding: 0 12px;
  align-items: center;
  gap: 4px;
  position: relative;
  align-self: stretch;
  .search-icon {
    flex-shrink: 0;
    opacity: 0.6;
  }

  .input {
    flex: 1;
    height: 100%;
    font-size: 14px;
    color: #333;
    border: none;
    outline: none;
    background: transparent;

    &::placeholder {
      color: #86909c;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-weight: 400;
    }
  }

  .clear-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    opacity: 0.6;

    &:active {
      opacity: 0.4;
    }
  }
}

.collection-list-container {
  padding: 16px;
  height: calc(100vh - 180px); // 调整高度，减去顶部导航栏、搜索框和筛选区域的高度
  overflow-y: auto;
  background-color: #fff;
}

.collection-list {
  display: flex;
  flex-direction: column;
}

/* 骨架屏样式 */
.skeleton-container {
  padding: 32rpx;
}

.skeleton-item {
  margin: 0 0 14px 0;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid var(--Gray-Gray3, #e7e7e7);
  background: #fff;

  .skeleton-main-wrapper {
    border-bottom: 1px solid #f3f3f3;
    margin-bottom: 12rpx;
    padding-bottom: 12rpx;

    .skeleton-main {
      display: flex;
      align-items: center;

      .skeleton-checkbox {
        width: 42rpx;
        height: 42rpx;
        margin-right: 16rpx;
        border-radius: 50%;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        flex-shrink: 0;
      }

      .skeleton-icon {
        width: 48px;
        height: 48px;
        margin-right: 24rpx;
        border-radius: 12rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        flex-shrink: 0;
      }

      .skeleton-info {
        flex: 1;
        overflow: hidden;

        .skeleton-title {
          height: 30rpx;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4rpx;
          margin-bottom: 12rpx;
          width: 75%;
        }

        .skeleton-tags {
          display: flex;
          gap: 8rpx;
          margin: 12rpx 0;

          .skeleton-tag {
            height: 22rpx;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 8rpx;
            width: 60rpx;
          }
        }
      }
    }
  }

  .skeleton-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .skeleton-meta {
      display: flex;
      align-items: center;
      gap: 8px;

      .skeleton-meta-item {
        height: 22rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
        width: 80rpx;
      }
    }

    .skeleton-source-tag {
      height: 20px;
      background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      border-radius: 999px;
      width: 40px;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  gap: 24rpx;
}

.no-data-text {
  color: #4e5969;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

/* 格式选择弹窗样式 */
.format-popup-content {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  max-height: 80vh;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    position: relative;

    .popup-title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      text-align: center;
    }

    .popup-close {
      position: absolute;
      right: 32rpx;
      top: 50%;
      transform: translateY(-50%);
      padding: 8rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        transform: translateY(-50%) scale(0.95);
      }
    }
  }

  .popup-body {
    padding: 24rpx 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .popup-section {
      .section-options {
        display: flex;
        flex-wrap: wrap;
        margin: -8rpx;

        .section-option {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 16rpx 24rpx;
          margin: 8rpx;
          border-radius: 999px;
          background: #f3f6fd;
          font-size: 28rpx;
          color: #374151;
          transition: all 0.3s ease;
          position: relative;
          width: calc((100% - 48rpx) / 3); /* 一行显示3个，减去margin */
          flex-shrink: 0;

          &:active {
            transform: scale(0.95);
          }

          &.active {
            background: #efecff;
            border: 1px solid #7f72f9;
            color: #667eea;
            font-weight: 500;
          }
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    padding: 24rpx 32rpx 32rpx;
    border-top: 1px solid #f0f0f0;

    .popup-btn {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 16rpx;
      font-size: 32rpx;
      font-weight: 500;
      transition: all 0.3s ease;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 150% */
      border-radius: 100px;

      &:not(:last-child) {
        margin-right: 16rpx;
      }

      &.cancel {
        color: var(--text-icon-text-2, #4e5969);
        border: 1px solid var(--Gray-Gray4, #dcdcdc);
        background: var(--Gray-White, #fff);
      }

      &.confirm {
        background: var(--Brand-Brand5, #7d4dff);
        color: #fff;
      }
    }
  }
}
</style>
