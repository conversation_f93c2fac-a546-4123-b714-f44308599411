<template>
  <!-- 固定图标 - 当AI助手隐藏时显示 -->
  <view
    v-if="isAIHidden"
    class="fixed-icon-container"
    :class="{ 'fixed-icon-left': isOnLeftSide, 'fixed-icon-right': !isOnLeftSide }"
    :style="{ top: fixedIconTop + 'px' }"
    @click="showAIAssistant"
  >
    <image
      :style="{
        width: '27px',
        height: '60px',
        transform: isOnLeftSide ? 'scaleX(-1)' : 'scaleX(1)',
      }"
      src="/static/learning-resource/hi.png"
    ></image>
  </view>

  <!-- AI助手悬浮按钮 -->
  <MylFab
    v-if="!isAIHidden"
    ref="mylFab"
    :position="fabPosition"
    :draggable="true"
    :autosorption="enableAutosorption"
    :layout="'circle'"
    :direction="'top'"
    :menuItems="menuItems"
    :zIndex="9999"
    @open="handleFabOpen"
    @close="handleFabClose"
    @select="handleFabSelect"
    @touchend="handleFabTouchEnd"
    @dragstart="handleFabDragStart"
    @drag="handleFabDrag"
    :resource="resource"
  >
    <!-- 自定义主按钮 - AI助手头像 -->
    <template #main-button>
      <view class="ai-avatar" @click.stop="preventDefaultClick">
        <!-- 上方头像图标 -->
        <view class="avatar-top" @click.stop="handleAvatarClick">
          <LkSvg width="36px" height="36px" src="/static/learning-resource/ai-avatar.svg" />
        </view>
        <!-- 下方助手图标 -->
        <view class="avatar-bottom" @click.stop="handleTakeNote">
          <LkSvg
            width="78px"
            height="78px"
            src="/static/learning-resource/ai-assistant-avata.svg"
            customClass="assistant-icon"
          />
        </view>
        <view
          v-show="showAvatarRight"
          class="avatar-right"
          @click.stop="hideAIAssistant"
          :style="
            !isOnLeftSide ? { left: '-23px', right: 'auto' } : { right: '-23px', left: 'auto' }
          "
        >
          <LkSvg
            width="24px"
            height="57px"
            src="/static/learning-resource/fixed.svg"
            customClass="assistant-icon"
            :style="{ transform: isOnLeftSide ? 'scaleX(-1)' : 'scaleX(1)' }"
          />
        </view>
      </view>
    </template>

    <!-- 自定义菜单项 -->
    <template #menu-item="{ data, index }">
      <view class="custom-menu-item">
        <text class="menu-text">{{ data.text }}</text>
      </view>
    </template>
  </MylFab>
</template>

<script>
import LkSvg from '@/components/svg/index.vue';
import MylFab from '@/components/myl-uniapp-fab/index.vue';

export default {
  name: 'AIAssistant',
  components: {
    LkSvg,
    MylFab,
  },
  props: {
    resource: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      // 悬浮按钮初始位置 [x, y] - 设置在屏幕可见区域
      fabPosition: [300, 400], // 初始值，将在mounted中动态计算为屏幕右边中间位置
      // 菜单项配置（环形布局建议不超过4个）
      menuItems: [
        { action: 'file-analysis', text: '文件解读' },
        { action: 'mind-map', text: '思维导图' },
        { action: 'file-qa', text: '文件问答' },
      ],
      // AI助手隐藏状态
      isAIHidden: false,
      // 是否在左侧
      isOnLeftSide: false,
      // 固定图标的top位置
      fixedIconTop: 300,
      // 是否启用自动吸附
      enableAutosorption: true,
      // 是否显示avatar-right按钮
      showAvatarRight: true,
    };
  },
  mounted() {
    // 动态计算初始位置 - 设置在屏幕右边中间
    const { windowWidth, windowHeight } = uni.getWindowInfo();
    const aiWidth = 56; // AI助手的宽度
    const safeDistance = 8; // 距离屏幕边缘的安全距离

    // 计算右边中间位置
    const rightX = windowWidth - safeDistance - aiWidth; // 右边位置
    const centerY = windowHeight * 0.5; // 屏幕中间高度

    // 更新初始位置为屏幕右边中间
    this.fabPosition = [rightX, centerY];

    console.log('AIAssistant组件已挂载', {
      isAIHidden: this.isAIHidden,
      fabPosition: this.fabPosition,
      windowWidth,
      windowHeight,
      platform: uni.getSystemInfoSync().platform,
    });

    // 初始化isOnLeftSide
    const threshold = windowWidth * 0.5;
    // 根据初始位置判断是左侧还是右侧
    this.isOnLeftSide = this.fabPosition[0] < threshold;
  },
  methods: {
    // 悬浮按钮打开事件
    handleFabOpen() {
      console.log('AI助手菜单打开');
      // 菜单打开时隐藏avatar-right
      this.showAvatarRight = false;
    },

    // 悬浮按钮关闭事件
    handleFabClose() {
      console.log('AI助手菜单关闭');
      // 菜单关闭时显示avatar-right
      this.showAvatarRight = true;
    },

    // 悬浮按钮菜单项选择事件
    handleFabSelect(item) {
      console.log('选择AI助手功能:', item.action);

      switch (item.action) {
        case 'file-analysis':
          this.handleFileAnalysis();
          break;
        case 'mind-map':
          this.handleMindMap();
          break;
        case 'file-qa':
          this.handleFileQA();
          break;
        case 'settings':
          this.handleSettings();
          break;
        default:
          console.warn('未知的操作:', item.action);
      }
    },

    // 阻止默认的主按钮点击事件
    preventDefaultClick(e) {
      e.stopPropagation();
    },

    // 处理fab拖拽开始事件
    handleFabDragStart() {
      // 用户开始拖拽时，启用自动吸附
      this.enableAutosorption = true;
    },

    // 添加拖拽过程中的处理方法
    handleFabDrag(position) {
      if (!this.$refs.mylFab) return;

      // 获取当前屏幕宽度
      const { windowWidth } = uni.getWindowInfo();
      const threshold = windowWidth * 0.5;

      // 根据当前x位置判断是左侧还是右侧
      this.isOnLeftSide = this.$refs.mylFab.x < threshold;
    },

    // 处理fab触摸结束事件
    handleFabTouchEnd() {
      // 延迟检查，避免初始化时立即触发隐藏
      setTimeout(() => {
        // 检查是否已经吸附到边缘
        if (this.isNearEdge()) {
          this.hideAIAssistant();
        }
      }, 200); // 延迟200ms，确保fab组件完全初始化完成
    },

    // 检查是否贴边（已经吸附到边缘）
    isNearEdge() {
      if (!this.$refs.mylFab) return false;

      const { windowWidth } = uni.getWindowInfo();
      const currentX = this.$refs.mylFab.x;
      const edgeThreshold = 5; // 只有真正贴近边缘（5px以内）才算贴边
      const aiWidth = 56; // AI助手的宽度
      const rightEdgePosition = windowWidth - edgeThreshold - aiWidth; // 右边缘吸附位置

      // 检查是否已经吸附到左边缘或右边缘
      return currentX <= edgeThreshold || currentX >= rightEdgePosition;
    },

    // 隐藏AI助手
    hideAIAssistant() {
      if (!this.$refs.mylFab) return;

      // 获取当前fab的位置信息
      const { windowWidth } = uni.getWindowInfo();
      const threshold = windowWidth * 0.5;

      // 判断fab当前在左侧还是右侧
      this.isOnLeftSide = this.$refs.mylFab.x < threshold;

      // 记录固定图标的位置（与fab的y位置对应）
      this.fixedIconTop = this.$refs.mylFab.y;

      // 隐藏AI助手
      this.isAIHidden = true;
    },

    // 显示AI助手
    showAIAssistant() {
      // 计算fab应该出现的位置（距离边界8px）
      const { windowWidth } = uni.getWindowInfo();
      const safeDistance = 8; // 距离屏幕边缘8px
      const aiWidth = 56; // AI助手的宽度
      const newX = this.isOnLeftSide ? safeDistance : windowWidth - safeDistance - aiWidth;

      // 更新fab的初始位置
      this.fabPosition = [newX, this.fixedIconTop];

      // 暂时禁用自动吸附，避免初始化时立即吸附到边缘
      this.enableAutosorption = false;

      // 显示AI助手
      this.isAIHidden = false;

      // 延迟启用自动吸附，给用户时间看到AI助手
      setTimeout(() => {
        this.enableAutosorption = true;
      }, 1000); // 1秒后启用自动吸附
    },

    // 处理头像点击事件
    handleAvatarClick(e) {
      // 阻止事件冒泡，避免触发父元素的点击事件
      e.stopPropagation();

      // 通过ref调用fab组件的主按钮点击方法来控制菜单
      this.$refs.mylFab.handleMainButtonClick();
      // 触发头像点击事件
      this.$emit('avatar-click');
    },

    // AI功能处理方法
    handleFileAnalysis() {
      // 保持原有的事件发射，以防父组件需要监听
      this.$emit('file-analysis');
    },

    handleMindMap() {
      this.$emit('mind-map');
    },

    handleFileQA() {
      this.$emit('file-qa');
    },

    handleSettings() {
      this.$emit('settings');
    },

    // 处理记笔记点击事件
    handleTakeNote() {
      console.log('AI助手：记笔记按钮被点击');
      this.$emit('take-note');
    },
  },
};
</script>

<style lang="scss" scoped>
// 固定图标样式
.fixed-icon-container {
  position: fixed;
  z-index: 9999; // 设置极高的层级，确保能覆盖web-view等原生组件
  cursor: pointer;
  transition: all 0.3s ease;

  &.fixed-icon-left {
    left: 0px; // 部分隐藏在左侧
  }

  &.fixed-icon-right {
    right: 0px; // 部分隐藏在右侧
  }

  &:hover {
    transform: translateX(0); // 悬停时完全显示
  }
}

// AI助手头像样式

.ai-avatar {
  width: 56px;
  height: 116px;
  border-radius: 999px;
  background: linear-gradient(180deg, #e1edff 0%, #deebff 47.6%, #e0ddff 100%);
  box-shadow: 0px 0px 4.9px 0px #deddf0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  position: relative; /* 添加相对定位，作为avatar-right的定位参考 */

  &:active {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
  }

  .avatar-top {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .avatar-icon {
      width: 36px;
      height: 36px;
      pointer-events: none;
    }
  }

  .avatar-bottom {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .assistant-icon {
      width: 36px;
      height: 36px;
      pointer-events: none;
    }
  }

  .avatar-right {
    position: absolute;
    top: 50%; /* 垂直居中 */
    transform: translateY(-50%); /* 垂直居中调整 */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1; /* 确保在上层 */

    // 移除了right和left属性，因为它们将通过v-bind动态设置

    .assistant-icon {
      width: 16px;
      height: 16px;
      pointer-events: none;
    }
  }
}

// 自定义菜单项样式
.custom-menu-item {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 100%;
  background-color: #f3ecff;
  text-align: center;
  .menu-text {
    font-size: 13px;
    color: #000;
    font-weight: 500;
  }
}
</style>
