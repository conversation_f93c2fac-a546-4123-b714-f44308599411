<template>
  <view class="resource-actions">
    <!-- 收藏按钮 -->
    <view class="action-item" @click="$emit('collect')">
      <image
        class="icon-placeholder"
        :src="
          props.isCollected === 0
            ? '/static/learning-resource/star.svg'
            : '/static/learning-resource/star_light.svg'
        "
      />
      <view class="action-text">收藏</view>
    </view>

    <!-- 沉浸式阅读/播放按钮 -->
    <view class="immersive-btn" @click="handleImmersiveClick" @tap="handleImmersiveClick">
      <view class="immersive-text">{{ immersiveButtonText }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  isCollected: number; // 0表示未收藏，1表示已收藏
  resourceType?: 'document' | 'video' | 'audio' | 'image' | 'office';
}>();

console.log('666isCollected', props.isCollected);

const emit = defineEmits<{
  collect: [];
  immersive: [];
  'video-fullscreen': [];
}>();

// 根据资源类型计算按钮文本
const immersiveButtonText = computed(() => {
  return props.resourceType === 'video' ? '沉浸播放' : '沉浸阅读';
});

// 处理沉浸式按钮点击
const handleImmersiveClick = () => {
  if (props.resourceType === 'video') {
    // 如果是视频类型，发出视频全屏事件让父组件处理
    emit('video-fullscreen');
  } else {
    // 其他类型发出普通的沉浸式事件
    emit('immersive');
  }
};
</script>

<style lang="scss" scoped>
.resource-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  z-index: 100;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;

  .icon-placeholder {
    width: 20px;
    height: 20px;
    margin-bottom: 4px;
  }

  .action-text {
    font-size: 12px;
    color: #666;
  }
}

.immersive-btn {
  flex: 1;
  background: #7d4dff;
  border-radius: 100px;
  display: flex;
  padding: 12px 20px;
  justify-content: center;
  align-items: center;
  gap: 4px;

  .immersive-text {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
