<template>
  <view v-if="show" class="note-modal">
    <view class="modal-mask" @click="handleClose"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">添加学习笔记</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>

      <view class="modal-body">
        <textarea
          v-model="noteContent"
          class="note-input"
          placeholder="请输入学习笔记内容..."
          :maxlength="500"
          auto-height
        />
        <view class="char-count">{{ noteContent.length }}/500</view>
      </view>

      <view class="modal-footer">
        <view class="btn cancel-btn" @click="handleClose">取消</view>
        <view class="btn save-btn" @click="handleSave">保存</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  show: boolean;
  note?: string;
}>();

const emit = defineEmits<{
  'update:show': [value: boolean];
  save: [note: string];
}>();

const noteContent = ref('');

// 监听props变化
watch(
  () => props.show,
  newVal => {
    if (newVal && props.note) {
      noteContent.value = props.note;
    }
  }
);

const handleClose = () => {
  emit('update:show', false);
  noteContent.value = '';
};

const handleSave = () => {
  if (!noteContent.value.trim()) {
    uni.showToast({
      title: '请输入笔记内容',
      icon: 'none',
    });
    return;
  }

  emit('save', noteContent.value.trim());
  handleClose();
};
</script>

<style lang="scss" scoped>
.note-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  position: relative;
  z-index: 1;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  .modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    font-size: 24px;
    color: #999;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.modal-body {
  padding: 20px;

  .note-input {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    line-height: 20px;
    resize: none;

    &:focus {
      border-color: #1890ff;
      outline: none;
    }
  }

  .char-count {
    text-align: right;
    font-size: 12px;
    color: #999;
    margin-top: 8px;
  }
}

.modal-footer {
  display: flex;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  gap: 12px;
}

.btn {
  flex: 1;
  padding: 10px;
  border-radius: 6px;
  text-align: center;
  font-size: 14px;

  &.cancel-btn {
    background-color: #f5f5f5;
    color: #666;
  }

  &.save-btn {
    background-color: #1890ff;
    color: #fff;
  }
}
</style>
