<template>
  <view class="office-preview">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading">
      <view class="loading-spinner"></view>
      <view class="loading-text">{{ loadingText }}</view>
    </view>

    <!-- 文件预览 - 统一使用LKOnlyoffice组件 -->
    <LKOnlyoffice
      v-if="!error && documentConfig.fileType"
      :fileType="documentConfig.fileType"
      :key="
        documentConfig.key ||
        'doc_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
      "
      :title="documentConfig.title"
      :url="documentConfig.url"
      :token="documentConfig.token"
      :showControls="showControls"
      :mode="mode"
      :lang="lang"
      @onDocumentReady="onDocumentReady"
      @onDocumentError="onDocumentError"
    />

    <!-- 调试信息 -->
    <view v-if="!error && !documentConfig.fileType" class="debug-info">
      <text>调试信息: documentConfig.fileType 为空</text>
      <text>error: {{ error }}</text>
      <text>documentConfig: {{ JSON.stringify(documentConfig) }}</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <view class="error-icon">❌</view>
      <view class="error-text">{{ error }}</view>
      <button class="retry-button" @click="handlePreview">重试</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import LKOnlyoffice from '@/components/LKOnlyoffice/index.vue';

interface FileData {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  [key: string]: any;
}

const props = defineProps<{
  file: FileData;
}>();

const loading = ref(false);
const loadingText = ref('文件加载中...');
const error = ref('');

// LKOnlyoffice 组件相关配置
const showControls = ref(false);
const mode = ref('view');
const lang = ref('zh-CN');
const documentConfig = ref({
  fileType: '',
  key: '',
  title: '',
  url: '',
  token: '',
});

// 平台检测
const platformInfo = ref('');
const isAndroid = ref(false);
const isIOS = ref(false);

// 检测当前平台
const detectPlatform = () => {
  // #ifdef APP-PLUS
  const platform = uni.getSystemInfoSync().platform;
  isAndroid.value = platform === 'android';
  isIOS.value = platform === 'ios';

  if (isAndroid.value) {
    platformInfo.value = 'Android (X5 Webview)';
  } else if (isIOS.value) {
    platformInfo.value = 'iOS (WKWebview/UIWebview)';
  } else {
    platformInfo.value = 'Unknown Platform';
  }
  // #endif

  // #ifndef APP-PLUS
  platformInfo.value = 'H5/小程序';
  // #endif
};

// 移除webview相关配置和消息处理，现在使用LKOnlyoffice组件

// 计算文件名称
const fileName = computed(() => {
  return props.file.fileName || '未知文件';
});

// 移除formatFileSize函数，当前不需要显示文件大小

// 获取文件类型
const getFileExtension = () => {
  const fileType = props.file.fileType?.toLowerCase() || '';
  if (fileType.startsWith('.')) {
    return fileType.substring(1);
  }
  return fileType;
};

// 移除isPdfFile判断，现在所有文件都使用LKOnlyoffice组件

// 检查文件类型是否支持预览
const isSupportedFileType = computed(() => {
  const extension = getFileExtension();
  console.log('extension', extension);
  // 现在所有文件类型都通过LKOnlyoffice组件预览，包括PDF
  const supportedTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'docs'];
  return supportedTypes.includes(extension);
});

// LKOnlyoffice 组件事件处理
const onDocumentReady = () => {
  loading.value = false;
  loadingText.value = '文档加载完成';
  console.log('OnlyOffice文档预览已准备就绪');
};

const onDocumentError = (errorMsg: string) => {
  console.log('OnlyOffice文档预览错误:', errorMsg);
  loading.value = false;

  // 根据错误类型提供更友好的错误信息
  let friendlyError = errorMsg;
  if (errorMsg.includes('配置') || errorMsg.includes('config')) {
    friendlyError = 'OnlyOffice服务配置错误，请联系管理员';
  } else if (errorMsg.includes('网络') || errorMsg.includes('fetch')) {
    friendlyError = '网络连接失败，请检查网络连接';
  } else if (errorMsg.includes('脚本') || errorMsg.includes('script')) {
    friendlyError = 'OnlyOffice服务不可用，请稍后重试';
  }

  error.value = `文档预览失败: ${friendlyError}`;
};

// 生成文档唯一标识
const generateDocumentKey = () => {
  return `doc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// 初始化文档配置
const initDocumentConfig = () => {
  const fileExtension = getFileExtension();
  const token = uni.getStorageSync('token');

  documentConfig.value = {
    fileType: fileExtension,
    key: generateDocumentKey(),
    title: fileName.value,
    url: props.file.fileUrl,
    token: token || '',
  };

  console.log('OfficePreview documentConfig 已初始化:', documentConfig.value);
};

// 处理文件预览
const handlePreview = async () => {
  try {
    if (!props.file.fileUrl) {
      error.value = '文件链接不存在';
      return;
    }

    if (!isSupportedFileType.value) {
      error.value = '不支持预览该类型的文件';
      return;
    }

    loading.value = true;
    loadingText.value = '正在加载文档预览...';
    error.value = '';

    // 统一使用LKOnlyoffice组件预览所有文件类型（包括PDF）
    initDocumentConfig();
    // loading状态会由LKOnlyoffice组件的事件来控制
  } catch (err) {
    console.error('预览文件失败:', err);
    error.value = '预览文件失败，请稍后重试';
    loading.value = false;
  }
};

// 移除setupPdfPreview函数，现在PDF也使用LKOnlyoffice组件

// 移除所有webview相关代码，现在使用LKOnlyoffice组件统一处理

// 移除沉浸式模式相关功能，现在使用LKOnlyoffice组件统一处理

// 移除adjustWebviewSize函数，现在使用LKOnlyoffice组件不需要调整webview大小

// 组件挂载时自动预览
onMounted(() => {
  // 首先检测平台
  detectPlatform();
  // 然后自动预览
  handlePreview();
});

// 移除defineExpose，现在不需要导出沉浸式模式相关函数
</script>

<style lang="scss" scoped>
.office-preview {
  position: relative;
  width: 100%;
  height: 100%;
  flex: 1; /* 确保能响应父容器的flex布局 */
  min-height: 0; /* 允许flex子项收缩 */
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;

  /* 根据第二个博客：确保容器有明确的高度约束 */
  /* #ifdef APP-PLUS */
  /* 在移动端，给容器设置明确的高度，让web-view自然填充 */
  display: flex;
  flex-direction: column;
  /* #endif */
}

/* 加载状态 */
.loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1890ff;
    border-radius: 50%;
    margin-bottom: 12px;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 14px;
    color: #666;
  }
}

/* 移除webview相关样式，现在使用LKOnlyoffice组件 */

/* 调试信息 */
.debug-info {
  padding: 20px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  margin: 10px;
  border-radius: 4px;

  text {
    display: block;
    margin-bottom: 8px;
    font-size: 12px;
    color: #666;
  }
}

/* 错误状态 */
.error-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #fff;

  .error-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .error-text {
    font-size: 14px;
    color: #ff4d4f;
    margin-bottom: 16px;
    text-align: center;
  }

  .retry-button {
    background-color: #1890ff;
    color: #fff;
    font-size: 14px;
    padding: 6px 16px;
    border-radius: 4px;
    border: none;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
