<template>
  <view class="resource-header">
    <!-- 骨架屏 -->
    <view v-if="loading" class="skeleton-container">
      <!-- 内容骨架 -->
      <view class="skeleton-content">
        <!-- 标题骨架（包含图标和文字） -->
        <view class="skeleton-title-row">
          <view class="skeleton-icon"></view>
          <view class="skeleton-title"></view>
        </view>

        <!-- 标签骨架 -->
        <view class="skeleton-tags">
          <view class="skeleton-tag"></view>
          <view class="skeleton-tag"></view>
          <view class="skeleton-tag"></view>
        </view>

        <!-- 文件信息骨架 -->
        <view class="skeleton-meta">
          <view class="skeleton-meta-left">
            <view class="skeleton-text short"></view>
            <view class="skeleton-text short"></view>
          </view>
          <view class="skeleton-meta-right">
            <view class="skeleton-tag-right"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 实际内容 -->
    <view v-else-if="resource" class="resource-info">
      <!-- 资源详情 -->
      <view class="resource-details">
        <!-- 资源名称 -->
        <view class="resource-name">
          <view class="file-icon">
            <image :src="`/static/fileTypeIcon/${resource.fileFormatName}.svg`" />
          </view>
          <view class="resource-title">
            {{ resource.title }}
          </view>
          <LkSvg
            v-if="resource.curation === 1"
            class="curation-icon"
            width="16px"
            height="16px"
            src="/static/learning-resource/suggestions.svg"
          />
        </view>

        <!-- 标签区域 -->
        <view class="tags-container">
          <!-- 资源标签 -->
          <view class="resource-tags">
            <text v-if="resource.resourceTypeName" class="tag">
              {{ resource.resourceTypeName }}
            </text>
            <text v-if="resource.subjectName" class="tag">
              {{ resource.subjectName }}
            </text>
            <text v-if="resource.gradeName" class="tag">
              {{ resource.gradeName }}
            </text>
          </view>
        </view>

        <!-- 文件信息 -->
        <view class="resource-meta-bottom">
          <view class="resource-meta">
            <view class="file-size-container">
              <LkSvg
                width="18px"
                height="18px"
                src="/static/learning-resource/file_more_line.svg"
              />
              <view class="file-size">{{ formatFileSize(resource.fileSize || 0) }}</view>
              <view
                style="margin: 0 10px; background-color: #86909c; width: 1px; height: 12px"
              ></view>
              <LkSvg width="18px" height="18px" src="/static/learning-resource/time_line.svg" />
              <view class="file-size">{{ formatUpdateTime(resource.updateTime) }}</view>
            </view>
            <view class="file-size-container-right">
              <view class="file-tag-wrapper" :class="getSourceClass(resource.attribution)">
                <text class="file-tag">
                  {{ getAttributionText(resource.attribution) }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Resource {
  videoId: string;
  id: number;
  title: string;
  description: string;
  areaId: number;
  areaName: string;
  attribution: number;
  attributionToId: number;
  attributionToName: string;
  createTime: string;
  createUserName: string;
  curation: number;
  downloadCount: number;
  fileFormatId: number;
  fileFormatName: string;
  fileName: string;
  fileSize: number | null;
  fileUrl: string;
  gradeId: number;
  gradeName: string;
  resourceTypeId: number;
  resourceTypeName: string;
  stageId: number;
  stageName: string;
  status: number;
  subjectId: number;
  subjectName: string;
  textbookChapterIds: number[];
  textbookChapterNames: string[];
  textbookVersionId: number;
  textbookVersionName: string;
  textbookVolumeId: number;
  textbookVolumeName: string;
  updateTime: string;
  viewCount: number;
  vintages: number;
  // Legacy fields for backward compatibility
  name?: string;
  source?: string;
  tags?: string[];
  size?: string;
  isCollected?: number;
  isCuration?: number;
  fileTypes?: {
    fileType: number;
    fileName?: string;
    format?: string;
  };
}

const props = withDefaults(
  defineProps<{
    resource?: Resource;
    loading?: boolean;
  }>(),
  {
    loading: false,
  }
);

defineEmits<{
  back: [];
}>();

// 转换归属值为可读文本
const getAttributionText = (attribution: number) => {
  switch (attribution) {
    case 1:
      return '个人资源';
    case 2:
      return '校本资源';
    case 3:
      return '官方资源';
    case 4:
      return '第三方资源';
    default:
      return '未知来源';
  }
};
// 格式化更新时间
const formatUpdateTime = (time: string | number | Date) => {
  if (!time) return '';
  // 确保时间格式为 YYYY-MM-DD
  const date = new Date(time);
  if (isNaN(date.getTime())) return String(time);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const getSourceClass = (source: number) => {
  return source === 3 ? 'official' : 'school';
};
</script>

<style lang="scss" scoped>
.school {
  background: #e8f7ff;
  color: #3491fa;
}

.official {
  background: #fff7e8;
  color: #ff7d00;
}
.resource-header {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 8px;
  position: relative;
  min-height: 120px; /* 确保有足够的高度显示骨架屏 */
  flex-shrink: 0; /* 防止在flex容器中被压缩 */
}

/* 骨架屏样式 */
.skeleton-container {
  width: 100%;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  width: 100%;

  & > * {
    margin-bottom: 12px;
  }
}

.skeleton-title-row {
  display: flex;
  align-items: center;

  & > *:not(:last-child) {
    margin-right: 8px;
  }
}

.skeleton-icon {
  width: 24px;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  flex-shrink: 0;
}

.skeleton-title {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  width: 200px;
  flex: 1;
}

.skeleton-tags {
  display: flex;

  & > *:not(:last-child) {
    margin-right: 8px;
  }
}

.skeleton-tag {
  height: 16px;
  width: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
}

.skeleton-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skeleton-meta-left {
  display: flex;

  & > *:not(:last-child) {
    margin-right: 16px;
  }
}

.skeleton-text {
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-text.short {
  width: 50px;
}

.skeleton-tag-right {
  height: 16px;
  width: 40px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.resource-info {
  display: flex;
  align-items: flex-start;
  width: 100%;

  & > *:not(:last-child) {
    margin-right: 12px;
  }
}

.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;

  image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.curation-icon {
  flex-shrink: 0;
}

.resource-details {
  width: 100%;
}

.resource-name {
  display: flex;
  align-items: center;
  color: var(--text-primary, #303133);
  font-family: 'PingFang SC';
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  width: 100%;

  & > *:not(:last-child) {
    margin-right: 8px;
  }
}

.resource-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* 对于text组件，使用lines属性可能更有效 */
  /* 但这里先用CSS方式 */
}

.tags-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  overflow: hidden;
}

.source-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: #fff;
  margin-right: 8px;
  flex-shrink: 0;
}

.resource-tags {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-bottom: 1px solid #f3f3f3;
  padding: 9px 0;

  .tag {
    display: inline-block;
    padding: 2px 6px;
    background-color: #f5f5f5;
    color: #666;
    font-size: 12px;
    border-radius: 4px;
    margin-right: 4px;
  }
}

.file-info {
  display: flex;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 16px;

  .icon-placeholder {
    font-size: 12px;
    margin-right: 4px;
  }

  .info-text {
    font-size: 12px;
    color: #666;
  }
}
.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;

  .icon-placeholder {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .btn-text {
    font-size: 12px;
    color: #666;
  }
}

.resource-meta-bottom {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  .resource-meta {
    display: flex;
    justify-content: space-between;
    width: 100%;
    font-size: 24rpx;
    color: #94a3b8;
    .file-size-container {
      display: flex;
      align-items: center;

      & > *:not(:last-child) {
        margin-right: 4px;
      }
      .file-size {
        color: #86909c;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }
    }

    .update-time {
      color: #cbd5e1;
    }

    .file-size-container-right {
      display: flex;
      justify-content: flex-end;
      flex: 1;

      .file-tag-wrapper {
        display: inline-flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        padding: 5px 9px;
        border-radius: 999px;

        &.school {
          background: #e8f7ff;
          color: #3491fa;
        }

        &.official {
          background: #fff7e8;
          color: #ff7d00;
        }

        .file-tag {
          display: flex;
          align-items: center;
          margin-right: 4px;
          border-radius: 999px;
          font-size: 12px;
          line-height: 20px;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
