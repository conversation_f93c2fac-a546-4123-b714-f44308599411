<template>
  <view class="resource-content">
    <!-- 文档内容 -->
    <view v-if="type === 'document'" class="document-content">
      <view class="content-title">{{ content.title }}</view>

      <view class="document-sections">
        <view v-for="(section, index) in content.sections" :key="index" class="content-section">
          <view class="section-title">{{ section.title }}</view>
          <view
            v-for="(item, itemIndex) in section.content"
            :key="itemIndex"
            class="section-content"
            @longpress="handleLongPress(item)"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>

    <!-- 视频内容 -->
    <view v-else-if="type === 'video'" class="video-content">
      <LkVideoPlayer
        :video-id="videoId || ''"
        :play-auth="playAuth || ''"
        :title="content.fileName || ''"
        v-if="content.fileUrl"
        ref="videoPlayerRef"
        :source="content.fileUrl"
        :file-key="content.fileKey"
        width="100%"
        height="300px"
        :autoplay="false"
        :muted="true"
        class="video-player"
        @ready="onVideoReady"
        @timeupdate="handleVideoProgress"
        @ai-chat="handleVideoAIChat"
        @take-note="handleVideoTakeNote"
        :current-video-play-time="currentVideoPlayTime"
        @onPlayerTimeUpdate="testhandleVideoProgress"
      />
      <view v-else class="video-error">
        <text>视频地址无效</text>
      </view>
    </view>

    <!-- 图片内容 -->
    <view v-else-if="type === 'image'" class="image-content">
      <image :src="content.url" mode="widthFix" class="content-image" />
    </view>

    <!-- Office文件内容 -->
    <view v-else-if="type === 'office'" class="office-content">
      <OfficePreview ref="officePreviewRef" :file="officeFileData" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import OfficePreview from './OfficePreview.vue';
import { LkVideoPlayer } from '@/components';
import {
  getVideoProgressDetail,
  getVideoProgressSaveOrUpdate,
} from '@/api/students/resourceCenter';

interface ContentData {
  title?: string;
  sections?: Array<{
    title: string;
    content: string[];
  }>;
  id?: string;
  fileName?: string;
  fileUrl?: string;
  fileSize?: number;
  fileType?: string;
  videoId?: string;
  playAuth?: string;
  [key: string]: any;
}

interface FileData {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  [key: string]: any;
}

const props = defineProps<{
  content: ContentData;
  videoId?: string;
  playAuth?: string;
  type: 'document' | 'video' | 'audio' | 'image' | 'office';
}>();

const emit = defineEmits<{
  copy: [content: string];
  'add-note': [];
}>();

const selectedText = ref('');
const showAIButton = ref(false);
const currentVideoPlayTime = ref(0);
let hasInitialSeek = false; // 标记是否已经执行过初始定位

// 视频播放器引用
const videoPlayerRef = ref();


onMounted(() => {
  getVideoProgressDetailFn();
});

// 将ContentData转换为FileData，确保类型兼容
const officeFileData = computed<FileData>(() => {
  return {
    id: props.content.id || '',
    fileName: props.content.fileName || '',
    fileUrl: props.content.fileUrl || '',
    fileSize: props.content.fileSize || 0,
    fileType: props.content.fileType || '',
    ...props.content,
  };
});

const getVideoProgressDetailFn = () => {
  getVideoProgressDetail({ fileKey: props.content.fileKey || '' })
    .then(res => {
      if (res && res.currentPosition > 0) {
        currentVideoPlayTime.value = res.currentPosition;

        // 如果播放器已经准备好，立即执行定位
        if (videoPlayerRef.value && videoPlayerRef.value.seekToTime && !hasInitialSeek) {
          hasInitialSeek = true;
          setTimeout(() => {
            videoPlayerRef.value.seekToTime(res.currentPosition);
          }, 500);
        }
      }
    })
    .catch(error => {
      console.error('ResourceContent: 获取视频播放进度失败:', error);
    });
};

// 进度保存节流相关变量
let lastSaveTime = 0;
let lastSavedProgress = 0;
const SAVE_INTERVAL = 2000; // 临时改为2秒内最多保存一次，便于调试
const MIN_PROGRESS_DIFF = 1; // 临时改为进度差异至少1秒才保存，便于调试

// const saveVideoProgress = (time: number) => {
//   const now = Date.now();
//   const progressDiff = Math.abs(time - lastSavedProgress);

//   // 节流控制：2秒内最多保存一次，且进度差异至少1秒
//   if (now - lastSaveTime < SAVE_INTERVAL && progressDiff < MIN_PROGRESS_DIFF) {
//     return;
//   }

//   // 只有当播放时间大于0且有文件key时才保存
//   if (time > 0 && props.content.fileKey) {
//     lastSaveTime = now;
//     lastSavedProgress = time;

//     getVideoProgressSaveOrUpdate({
//       fileKey: props.content.fileKey,
//       currentPosition: time,
//     })
//       .then(res => {
//       })
//       .catch(error => {
//         // 保存失败时重置时间，允许下次重试
//         lastSaveTime = 0;
//       });
//   }
// };

// 长按选择文本
const handleLongPress = (text: string) => {
  selectedText.value = text;
  showAIButton.value = true;

  // 显示操作菜单
  uni.showActionSheet({
    itemList: ['复制', 'AI解读'],
    success: res => {
      if (res.tapIndex === 0) {
        emit('copy', text);
      } else if (res.tapIndex === 1) {
        handleAIAnalysis();
      }
    },
  });
};

// AI解读
const handleAIAnalysis = () => {
  if (selectedText.value) {
    emit('copy', selectedText.value);
  }
  showAIButton.value = false;
};

// 视频播放器事件处理
const onVideoReady = () => {


  // 如果还没有执行过初始定位且有播放时间，直接调用播放器的定位方法
  if (!hasInitialSeek && currentVideoPlayTime.value > 0) {
    hasInitialSeek = true; // 标记已经执行过定位

    // 稍微延迟一下，确保播放器完全准备好
    setTimeout(() => {
      if (videoPlayerRef.value && videoPlayerRef.value.seekToTime) {
        videoPlayerRef.value.seekToTime(currentVideoPlayTime.value);
      } else {
      }
    }, 500);
  } 
};

// 当前播放时间（用于组件卸载时保存最后进度）
let currentPlayTime = 0;

// 视频进度处理
const handleVideoProgress = (currentTime: number) => {
  console.log('handleVideoProgress', currentTime);
  // 更新当前播放时间
  currentPlayTime = currentTime;
};

const testhandleVideoProgress = (currentTime: number) => {
  console.log('testhandleVideoProgress', currentTime);
};




// 处理视频智能答疑
const handleVideoAIChat = () => {
  // 获取当前播放时间作为上下文
  const currentTime = videoPlayerRef.value?.getCurrentTime() || 0;


  // 可以发送事件给父组件或调用API
  // emit('ai-chat', { time: currentTime, type: 'video' });
};

// 处理视频做笔记
const handleVideoTakeNote = (noteData: { content: string; time: number; timestamp: string }) => {
  // 显示保存成功提示
  uni.showToast({
    title: '笔记保存成功',
    icon: 'success',
  });

};


// OfficePreview组件引用
const officePreviewRef = ref();

// 沉浸式模式控制函数
const enterImmersiveMode = () => {
  if (officePreviewRef.value && officePreviewRef.value.enterImmersiveMode) {
    officePreviewRef.value.enterImmersiveMode();
  }
};

const exitImmersiveMode = () => {
  if (officePreviewRef.value && officePreviewRef.value.exitImmersiveMode) {
    officePreviewRef.value.exitImmersiveMode();
  }
};

// 组件卸载时保存最后的播放进度
onUnmounted(() => {
  // 移除 uni 全局事件监听
  if (typeof uni !== 'undefined' && uni.$off) {
    uni.$off('video-save-progress');
  }
  // 如果是视频类型且有播放时间，保存最后的进度
  if (props.type === 'video' && currentPlayTime > 0 && props.content.fileKey) {
    // 强制保存最后的进度，忽略节流限制
    getVideoProgressSaveOrUpdate({
      fileKey: props.content.fileKey,
      currentPosition: currentPlayTime,
    })
      .then(res => {
        console.log('最后进度保存成功:', res);
      })
      .catch(error => {
        console.error('最后进度保存失败:', error);
      });
  }
});

// 导出供父组件使用的函数
defineExpose({
  enterImmersiveMode,
  exitImmersiveMode,
  videoPlayerRef, // 暴露视频播放器引用
});
</script>

<style lang="scss" scoped>
.resource-content {
  background-color: #fff;
  height: 100%; /* 占满父容器高度 */
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 允许垂直滚动 */
  box-sizing: border-box; /* 确保padding不会影响总高度 */
  /* 强制占满整个可用空间，不受内容影响 */
  flex: 1;
  position: relative;
  z-index: 1;
}

.document-content {
  flex: 1;
  min-height: 100%; /* 确保占满整个可用高度 */
  height: 100%; /* 强制高度 */
  display: flex;
  flex-direction: column;

  .content-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 24px;
    text-align: center;
    flex-shrink: 0; /* 标题不被压缩 */
  }

  .document-sections {
    flex: 1; /* 占满剩余空间 */
    min-height: 0; /* 允许内容滚动 */
    height: 100%; /* 强制高度 */
  }
}

.content-section {
  margin-bottom: 24px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
  }

  .section-content {
    font-size: 14px;
    line-height: 24px;
    color: #666;
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;

    &:active {
      background-color: #f0f8ff;
    }
  }
}

.video-content,
.audio-content {
  flex: 1;
  min-height: 100%; /* 确保占满整个可用高度 */
  height: 100%; /* 强制高度 */
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  .video-player {
    width: 100%;
    height: 300px;
    max-height: 60%; /* 限制视频最大高度，留出空间给其他元素 */
  }

  .video-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    background-color: #f5f5f5;
    border-radius: 8px;
    color: #999;
    font-size: 14px;
  }

  .audio-player {
    width: 100%;
  }
}

.image-content {
  flex: 1;
  min-height: 100%; /* 确保占满整个可用高度 */
  height: 100%; /* 强制高度 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;

  .content-image {
    width: 100%;
    max-height: 80%; /* 限制图片最大高度，留出空间给其他元素 */
    object-fit: contain;
    border-radius: 8px;
  }
}

.office-content {
  flex: 1;
  min-height: 100%; /* 确保占满整个可用高度 */
  height: 100%; /* 强制高度 */
  display: flex;
  flex-direction: column;
  z-index: 1;
  /* 确保OfficePreview占满剩余空间 */
  > :first-child {
    flex: 1;
    min-height: 0; /* 允许flex子项收缩 */
  }
}

.ai-analysis-btn {
  position: fixed;
  top: 50%;
  right: 20px;
  background-color: #1890ff;
  color: #fff;
  padding: 8px 16px;
  border-radius: 20px;
  z-index: 100;

  .ai-btn-text {
    font-size: 12px;
  }
}

.add-note-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8ff;
  border: 1px dashed #1890ff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 20px;
  flex-shrink: 0; /* 防止按钮被压缩 */

  .icon-placeholder {
    font-size: 16px;
    margin-right: 8px;
  }

  .note-btn-text {
    font-size: 14px;
    color: #1890ff;
  }
}
</style>
