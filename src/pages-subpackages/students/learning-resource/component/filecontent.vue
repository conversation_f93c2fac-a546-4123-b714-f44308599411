<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// 接收props
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  onItemClick: {
    type: Function,
    required: true,
  },
  // 是否开启多选模式
  check: {
    type: Boolean,
    default: false,
  },
  // 已选中的项目ID数组
  selectedItems: {
    type: Array,
    default: () => [],
  },
  // 高亮关键词
  hightlightwords: {
    type: String,
    default: '',
  },
});

// 定义事件
const emit = defineEmits(['select', 'cancelFavorite']);
// 计算属性：当前项是否被选中
const isSelected = computed(() => {
  // 确保类型一致，将两边都转为字符串进行比较
  return props.selectedItems.some((item: any) => String(item.id) === String(props.item.id));
});

// 选中/取消选中项目
const handleSelect = (e?: Event) => {
  if (e) {
    e.stopPropagation(); // 阻止事件冒泡
  }
  if (props.check) {
    emit('select', props.item);
  }
};

// 处理项目点击
const handleItemClick = () => {
  // 如果是多选模式，点击项目时选中/取消选中
  uni.navigateTo({
    url: `/pages-subpackages/students/learning-resource/resource-details?id=${props.item.id}`,
  });
};

// 取消收藏
const handleCancelFavorite = () => {
  emit('cancelFavorite', props.selectedItems);
};

// 计算属性：是否显示底部悬浮按钮
const showFloatingButton = computed(() => {
  return props.check && props.selectedItems.length > 0;
});

// 引入需要的工具函数
const fileIcon = (item: any) => {
  return `/static/fileTypeIcon/${item.fileFormatName}.svg`;
};

// 转换归属值为可读文本
const getAttributionText = (attribution: number) => {
  switch (attribution) {
    case 1:
      return '个人资源';
    case 2:
      return '校本资源';
    case 3:
      return '官方资源';
    case 4:
      return '第三方资源';
    default:
      return '未知来源';
  }
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
};

// 格式化日期时间，统一跨平台显示格式
const formatDateTime = (dateTime: string | number | Date) => {
  if (!dateTime) return '';

  const date = new Date(dateTime);

  // 检查日期是否有效
  if (isNaN(date.getTime())) return '';

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  // 统一返回 YYYY-MM-DD 格式
  return `${year}-${month}-${day}`;
};

// 处理资源名称显示长度控制和高亮
const truncateTitle = (title: string, maxLength = 30, hightlightwords: string = '') => {
  console.log('truncateTitle', title);
  if (!title) return '';

  let displayTitle = title;

  // 如果有高亮词，先进行高亮处理
  if (hightlightwords && hightlightwords.trim() !== '') {
    // 高亮匹配的词汇
    const highlightWords = hightlightwords.trim().split(/\s+/); // 支持多个关键词，用空格分隔

    highlightWords.forEach(word => {
      if (word) {
        // 使用全局不区分大小写的正则表达式
        const regex = new RegExp(`(${word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        displayTitle = displayTitle.replace(regex, '<span style="color: #FF0E00;">$1</span>');
      }
    });
  }

  // 最后进行截断处理
  // 注意：如果包含HTML标签，需要考虑标签长度，这里简化处理
  if (title.length > maxLength) {
    // 计算纯文本长度（不包含HTML标签）
    const textLength = title.length;
    if (textLength > maxLength) {
      // 如果有高亮标签，需要保持标签完整性
      if (displayTitle.includes('<span')) {
        // 简化处理：如果有高亮且原文本超长，显示原始高亮结果
        // 可以根据需要进一步优化截断逻辑
        displayTitle = displayTitle;
      } else {
        displayTitle = displayTitle.substring(0, maxLength) + '...';
      }
    }
  }

  return displayTitle;
};
</script>

<template>
  <view class="resource-item" @click.stop="handleItemClick">
    <view class="resource-item-top">
      <!-- 添加checkbox -->
      <view v-if="check" class="checkbox-container" @tap.stop="handleSelect">
        <view class="checkbox" :class="{ 'checkbox-selected': isSelected }">
          <LkSvg
            v-if="isSelected"
            width="24px"
            height="24px"
            src="/static/learning-resource/check-circle-filled.svg"
          />
        </view>
      </view>

      <view class="resource-icon">
        <LkSvg
          class="fileIcon"
          width="42px"
          height="42px"
          :src="fileIcon(item)"
          :errorSrc="`/static/fileTypeIcon/unknown.svg`"
        />
      </view>
      <view class="resource-info">
        <view class="resource-title">
          <LkSvg
            v-if="item.isCuration === 1"
            class="curation-icon"
            width="16px"
            height="16px"
            src="/static/learning-resource/suggestions.svg"
          />
          <span v-html="truncateTitle(item.title, 30, props.hightlightwords)"></span>
        </view>
        <view class="resource-tags">
          <view v-if="item.resourceTypeName" class="resource-tag">
            {{ item.resourceTypeName }}
          </view>
          <view v-if="item.subjectName" class="resource-tag">
            {{ item.subjectName }}
          </view>
          <view v-if="item.gradeName" class="resource-tag">
            {{ item.gradeName }}
          </view>
        </view>
      </view>
    </view>
    <view class="line"></view>
    <view class="resource-meta-bottom">
      <view class="resource-meta">
        <view class="file-size-container">
          <LkSvg width="18px" height="18px" src="/static/learning-resource/file_more_line.svg" />
          <view class="file-size">{{ formatFileSize(item.fileSize || 0) }}</view>
          <view style="margin: 0 10px; background-color: #86909c; width: 1px; height: 12px"></view>
          <LkSvg width="18px" height="18px" src="/static/learning-resource/time_line.svg" />
          <view class="file-size">{{ formatDateTime(item.updateTime) }}</view>
        </view>
        <view class="file-size-container-right">
          <view class="file-tag-wrapper">
            <view class="file-tag official">
              {{ getAttributionText(item.attribution) }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部悬浮按钮 -->
  <view v-if="showFloatingButton" class="floating-button-container">
    <view class="floating-button" @click="handleCancelFavorite">
      <view class="floating-button-top">
        <LkSvg width="24px" height="24px" src="/static/learning-resource/forbid_circle_line.svg" />
      </view>
      <view class="floating-button-bottom">
        <text>取消收藏</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.checkbox-selected {
  background-color: #7d4dff;
}
.resource-item {
  transition: all 0.3s ease;
  margin: 0 0 14px 0; /* 移除负边距，只保留底部边距 */
  padding: 16px; /* 添加内边距来保持内容间距 */
  border-radius: 10px;
  border: 1px solid var(--Gray-Gray3, #e7e7e7);
  background: #fff;
  position: relative;

  &:active {
    background-color: #f8fafc;
    transform: scale(0.98);
  }
  .resource-item-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f3f3f3;
    margin-bottom: 12rpx;
    padding-bottom: 12rpx;

    .resource-icon {
      width: 48px;
      height: 48px;
      margin-right: 24rpx;
      border-radius: 12rpx;
      background: #f8fafc;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .resource-info {
      flex: 1;
      overflow: hidden;

      .resource-title {
        font-size: 30rpx;
        color: #1e293b;
        font-weight: 600;
        line-height: 1.4;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;
        align-items: center;
        gap: 4rpx;

        .curation-icon {
          flex-shrink: 0;
        }

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .resource-tags {
        display: flex;
        align-items: center;
        margin: 12rpx 0;
        overflow: hidden;

        .source-tag {
          padding: 6rpx 12rpx;
          font-size: 22rpx;
          border-radius: 8rpx;
          font-weight: 500;
          margin-right: 8rpx;

          &.official {
            background: linear-gradient(
              135deg,
              rgba(59, 130, 246, 0.1) 0%,
              rgba(37, 99, 235, 0.1) 100%
            );
            color: #3b82f6;
            border: 1rpx solid rgba(59, 130, 246, 0.2);
          }

          &.school {
            background: linear-gradient(
              135deg,
              rgba(139, 92, 246, 0.1) 0%,
              rgba(124, 58, 237, 0.1) 100%
            );
            color: #8b5cf6;
            border: 1rpx solid rgba(139, 92, 246, 0.2);
          }
        }

        .resource-tag {
          background-color: #f1f5f9;
          color: #64748b;
          font-size: 22rpx;
          padding: 6rpx 12rpx;
          border-radius: 8rpx;
          margin-right: 8rpx;

          &.more {
            background-color: #e2e8f0;
            color: #94a3b8;
            font-weight: 500;
          }
        }
      }
    }
  }
  .resource-meta-bottom {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;
    .resource-meta {
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-size: 24rpx;
      color: #94a3b8;
      .file-size-container {
        display: flex;
        align-items: center;
        gap: 4px;
        .file-size {
          color: #86909c;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        }
      }

      .update-time {
        color: #cbd5e1;
      }

      .file-size-container-right {
        display: flex;
        justify-content: flex-end;
        flex: 1;

        .file-tag-wrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 8rpx;
          justify-content: flex-end;

          .file-tag {
            display: inline-flex;
            padding: 1px 8px;
            align-items: center;
            gap: 4px;
            border-radius: 999px;
            font-size: 12px;
            line-height: 20px;
            white-space: nowrap;

            &.school {
              background: #e8f7ff;
              color: #3491fa;
            }

            &.official {
              background: #fff7e8;
              color: #ff7d00;
            }
          }
        }
      }
    }
  }
}

/* Checkbox样式 */
.checkbox-container {
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx; /* 增加点击区域 */
  cursor: pointer; /* 添加手型光标 */
}

.checkbox {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  transition: all 0.2s ease;

  &-selected {
    border: none;
  }

  &-inner {
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background-color: #fff;
  }
}

/* 底部悬浮按钮样式 */
.floating-button-container {
  position: fixed;
  bottom: 40rpx;
  left: 0;
  right: 0;
  justify-content: center;
  z-index: 999;
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  padding: 0 32rpx; /* 添加左右内边距，与内容区域对齐 */
}

.floating-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%; /* 设置宽度为100%，占满容器 */
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 0px -1px 15.9px 0px rgba(0, 0, 0, 0.05);
  padding: 12px 16px;
  text {
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
  }
}
</style>
