<template>
  <u-popup :show="show" mode="bottom" :round="12" :safeAreaInsetBottom="true" @close="handleClose">
    <view class="voice-popup">
      <!-- 弹窗头部 -->
      <view class="voice-header">
        <view class="voice-cancel" @click="handleCancel">取消</view>
        <view class="voice-confirm" @click="handleConfirm">说完了</view>
      </view>

      <!-- 弹窗内容 -->
      <view class="voice-content">
        <!-- 上滑取消提示 -->
        <view class="voice-tip" v-show="isRecording && !isTouchInside">
          <text>松开取消</text>
        </view>

        <!-- 上滑提示 -->
        <view class="voice-tip" v-show="isRecording && isTouchInside">
          <text>松开发送/上滑取消</text>
        </view>

        <!-- 文字回显区域 -->
        <view v-show="voiceText" class="voice-result">
          <text>{{ voiceText }}</text>
        </view>

        <!-- 语音录制区域 - 统一容器 -->
        <view
          class="voice-record-area"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @touchcancel="handleTouchEnd"
        >
          <!-- 录音按钮 -->
          <view v-show="!isRecording" class="voice-record-btn">
            <LkSvg
              width="48px"
              height="48px"
              src="/static/learning-resource/mic_fill.svg"
              class="mic-icon"
            />
          </view>

          <!-- 声纹动画 -->
          <view v-show="isRecording" class="voice-wave-container">
            <view class="voice-wave">
              <view
                v-for="(bar, index) in waveBars"
                :key="index"
                class="wave-bar"
                :style="{
                  height: bar.height + 'px',
                  animationDelay: bar.delay + 's',
                  backgroundColor: isTouchInside ? '#4da3ff' : '#f44336',
                }"
              ></view>
            </view>
            <view class="voice-recording-text">{{ isTouchInside ? '正在说话' : '松开取消' }}</view>
          </view>
        </view>

        <view class="voice-title">{{ voiceText ? '继续说话' : '按住说话' }}</view>
      </view>
    </view>

    <!-- 权限弹窗 -->
    <PermissionModal
      v-model:show="isShowVoicePermissionModal"
      :title="permissionModalTitle"
      :content="permissionModalContent"
      cancel-text="取消"
      confirm-text="前往设置"
      @cancel="handleModalCancel"
      @confirm="handleModalConfirm"
    />
  </u-popup>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import LkSvg from '@/components/svg/index.vue';
import PermissionModal from '@/components/LKpermissionModal/index.vue';
import { checkPermission } from '@/utils/permission';
import { startVoice } from '@/common/ai/voice';

// Props
interface Props {
  show: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean];
  close: [];
  confirm: [text: string];
  cancel: [];
}>();

// 响应式数据
const isRecording = ref(false);
const voiceText = ref('');
const voice = ref<any>(null);
const isTouchInside = ref(true);
const startY = ref(0); // 记录触摸开始的Y坐标

// 权限弹窗相关状态
const isShowVoicePermissionModal = ref(false);
const permissionModalTitle = ref('');
const permissionModalContent = ref('');
const permissionOpenSettings = ref<(() => void) | null>(null);

// 声纹动画数据
const waveBars = ref([
  { height: 4, delay: 0 },
  { height: 8, delay: 0.1 },
  { height: 12, delay: 0.2 },
  { height: 6, delay: 0.3 },
  { height: 16, delay: 0.4 },
  { height: 10, delay: 0.5 },
  { height: 14, delay: 0.6 },
  { height: 8, delay: 0.7 },
  { height: 18, delay: 0.8 },
  { height: 12, delay: 0.9 },
  { height: 6, delay: 1.0 },
  { height: 20, delay: 1.1 },
  { height: 14, delay: 1.2 },
  { height: 8, delay: 1.3 },
  { height: 16, delay: 1.4 },
  { height: 10, delay: 1.5 },
  { height: 12, delay: 1.6 },
  { height: 6, delay: 1.7 },
  { height: 14, delay: 1.8 },
  { height: 8, delay: 1.9 },
]);

// 监听show变化，重置状态
watch(
  () => props.show,
  newVal => {
    if (newVal) {
      // 弹窗打开时重置状态
      voiceText.value = '';
      isRecording.value = false;
      isTouchInside.value = true;
      // 停止任何正在进行的录音
      if (voice.value) {
        voice.value.stop('cancel');
        voice.value = null;
      }
    }
  }
);

// 检查并请求语音权限
const checkAndRequestVoicePermission = async (): Promise<boolean> => {
  try {
    const result = await checkPermission('voice');

    if (result.granted) {
      return true;
    } else {
      // 权限被拒绝，显示权限弹窗
      if (result.details) {
        permissionModalTitle.value = result.details.deniedTitle;
        permissionModalContent.value = result.details.deniedMessage;
        permissionOpenSettings.value = result.openSettings || null;
        showPermissionModal();
      }
      return false;
    }
  } catch (error) {
    return false;
  }
};

// 显示权限弹窗
const showPermissionModal = () => {
  isShowVoicePermissionModal.value = true;
};

// 权限弹窗的取消操作
const handleModalCancel = () => {
  isShowVoicePermissionModal.value = false;
};

// 权限弹窗的确认操作 (前往设置)
const handleModalConfirm = () => {
  isShowVoicePermissionModal.value = false;
  if (permissionOpenSettings.value) {
    permissionOpenSettings.value();
  }
};

// 事件处理
const handleClose = () => {
  // 停止录音
  if (voice.value) {
    voice.value.stop('cancel');
    voice.value = null;
  }
  emit('update:show', false);
  emit('close');
};

const handleCancel = () => {
  // 停止录音
  if (voice.value) {
    voice.value.stop('cancel');
    voice.value = null;
  }
  emit('update:show', false);
  emit('cancel');
};

const handleConfirm = () => {
  if (voiceText.value.trim()) {
    emit('confirm', voiceText.value);
    emit('update:show', false);
  } else {
    emit('update:show', false);
  }
};

const handleTouchStart = async (event: TouchEvent) => {
  // 检查权限
  const hasPermission = await checkAndRequestVoicePermission();
  if (!hasPermission) {
    return;
  }

  // 记录开始触摸的Y坐标
  if (event.touches && event.touches.length > 0) {
    startY.value = event.touches[0].clientY;
  }

  isTouchInside.value = true;
  startRecording();
};

const handleTouchMove = (event: TouchEvent) => {
  if (!isRecording.value) return;

  event.preventDefault();

  if (event.touches && event.touches.length > 0) {
    const currentY = event.touches[0].clientY;
    const moveDistance = startY.value - currentY;

    // 上滑超过50px判定为要取消录音
    if (moveDistance > 50) {
      isTouchInside.value = false;
    } else {
      isTouchInside.value = true;
    }
  }
};

const handleTouchEnd = () => {
  if (!isRecording.value) return;

  if (!isTouchInside.value) {
    // 如果触摸移出了区域，取消录音
    if (voice.value) {
      voice.value.stop('cancel');
      voice.value = null;
    }
    console.log('取消录音');
  } else {
    // 否则正常结束录音
    stopRecording();
  }

  isRecording.value = false;
  isTouchInside.value = true;
  stopWaveAnimation();
};

const startRecording = () => {
  isRecording.value = true;
  console.log('开始录音');

  // 开始声纹动画
  startWaveAnimation();

  // 使用统一的录音方法
  voice.value = startVoice({
    onResult: result => {
      console.log('语音识别结果:', result);
      // 如果已有文字，则追加到后面（添加适当分隔），否则直接设置
      if (voiceText.value.trim()) {
        // 检查是否需要添加分隔符
        const needsSeparator =
          !voiceText.value.endsWith('，') &&
          !voiceText.value.endsWith('。') &&
          !voiceText.value.endsWith('？') &&
          !voiceText.value.endsWith('！') &&
          !voiceText.value.endsWith(' ');
        voiceText.value = voiceText.value + (needsSeparator ? '' : '') + result;
      } else {
        voiceText.value = result;
      }
      isRecording.value = false;
      stopWaveAnimation();
      voice.value = null;
    },
    onError: error => {
      console.error('语音识别错误:', error);
      isRecording.value = false;
      stopWaveAnimation();
      voice.value = null;
      uni.showToast({
        title: '语音识别失败',
        icon: 'none',
      });
    },
    onCancel: () => {
      console.log('语音识别取消');
      isRecording.value = false;
      stopWaveAnimation();
      voice.value = null;
    },
  });
};

// 声纹动画控制
let waveInterval: any = null;

const startWaveAnimation = () => {
  // 随机更新声纹高度
  waveInterval = setInterval(() => {
    waveBars.value = waveBars.value.map(bar => ({
      ...bar,
      height: Math.random() * 20 + 4, // 4-24px 随机高度
    }));
  }, 150);
};

const stopWaveAnimation = () => {
  if (waveInterval) {
    clearInterval(waveInterval);
    waveInterval = null;
  }
};

const stopRecording = () => {
  if (voice.value) {
    voice.value.stop();
  }
  console.log('停止录音');
};
</script>

<style lang="scss" scoped>
/* 语音输入弹窗样式 */
.voice-popup {
  border-radius: 12px 12px 0px 0px;
  background: var(--Gray-White, #fff);
  min-height: 336px;
  max-height: 500px;
  padding: 0;
}

.voice-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.voice-cancel,
.voice-confirm {
  color: var(--text-icon-text-1, #1d2129);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px; /* 162.5% */
}

.voice-title {
  font-size: 14px;
  font-weight: 400;
  color: #4e5969;
  margin-top: 8px;
}

.voice-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(336px - 60px);
  padding: 20px;
  flex: 1;
}

/* 提示样式 */
.voice-tip {
  display: flex;
  width: 280rpx;
  padding: 4rpx 16rpx;
  justify-content: center;
  align-items: center;
  gap: 16rpx;
  border-radius: 100rpx;
  background: rgba(0, 0, 0, 0.6);
  margin: 8rpx auto 24rpx;
}

.voice-tip text {
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 40rpx;
}

/* 语音录制区域 - 新增 */
.voice-record-area {
  position: relative;
  width: 65px;
  height: 65px;
  margin-bottom: 20px;
  touch-action: none; /* 防止触摸事件被浏览器默认行为干扰 */
  user-select: none; /* 防止文本选择干扰触摸事件 */
}

.voice-result {
  font-size: 16px;
  color: #333;
  text-align: left;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  min-height: 60px;
  max-height: 120px;
  overflow-y: auto;
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
  border: 1px solid #e9ecef;
  width: 100%;
  box-sizing: border-box;
}

.voice-record-btn {
  width: 65px;
  height: 65px;
  border-radius: 50%;
  background: var(---1, linear-gradient(180deg, #4da3ff 0%, #7d4dff 100%));
  box-shadow:
    0px 3px 14px 2px rgba(96, 87, 182, 0.19),
    0px 8px 10px 1px rgba(96, 87, 182, 0.06),
    0px 5px 5px -3px rgba(96, 87, 182, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(116, 185, 255, 0.3);
  position: absolute;
  top: 0;
  left: 0;

  &:active {
    transform: scale(0.95);
  }

  &.recording {
    background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
    box-shadow: 0 8px 24px rgba(255, 118, 117, 0.4);
    animation: pulse 1.5s infinite;
  }

  .mic-icon {
    color: white;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 24px rgba(255, 118, 117, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 32px rgba(255, 118, 117, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8px 24px rgba(255, 118, 117, 0.4);
  }
}

/* 声纹动画样式 */
.voice-wave-container {
  position: absolute;
  top: -27.5px; /* 使声纹动画垂直居中显示 */
  left: -17.5px; /* 使声纹动画水平居中显示 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  width: 100px;
}

.voice-wave {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  height: 60px;
  margin-bottom: 20px;
}

.wave-bar {
  width: 3px;
  background: #4da3ff;
  border-radius: 2px;
  animation: waveAnimation 1.5s ease-in-out infinite;
  min-height: 4px;
}

.voice-recording-text {
  font-size: 16px;
  color: #666;
  text-align: center;
  font-weight: 400;
}

@keyframes waveAnimation {
  0%,
  100% {
    transform: scaleY(0.3);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}
</style>
