<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import LkSvg from '@/components/svg/index.vue';
import {
  getSubjects,
  getResourceTypes,
  getResourceFormats,
  getTextbookVersionsList,
} from '@/api/students/resourceCenter';
import type {
  SubjectsItem,
  ResourceTypesItem,
  ResourceFormatsItem,
} from '@/types/students/resourceCenter';

const sourceList = ref([
  { name: '全部', code: '0' },
  { name: '官方资源', code: '3' },
  { name: '校本资源', code: '2' },
  { name: '个人资源', code: '1' },
  { name: '第三方资源', code: '4' },
]);

// 定义组件属性
const props = defineProps({
  currentGrade: {
    type: Object,
    required: true,
    default: () => ({ name: '五年级', version: '苏科版' }),
  },
  currentSubject: {
    type: String,
    default: '全部',
  },
  currentFormat: {
    type: Array as () => number[],
    default: () => [0],
  },
  subjectList: {
    type: Array,
    default: () => [],
  },
});

const subjectList = ref<SubjectsItem[]>([]);
const versionList = ref<any[]>([]);
const selectedVersion = ref<string>('0');
// 修改为单选
const selectedSources = ref<string>('0'); // 默认选中"全部"
const typeList = ref<ResourceTypesItem[]>([]);
// 修改为多选数组
const selectedTypes = ref<number[]>([0]); // 默认选中"全部"

const formatList = ref<ResourceFormatsItem[]>([]); // 顶级格式列表
const currentFormatCategory = ref<ResourceFormatsItem | null>(null); // 当前选中的格式分类
// 修改为多选数组，存储格式ID
const selectedFormats = ref<number[]>([0]); // 默认选中"全部"，0表示全部
// 筛选参数
const filterParams = ref({
  grade: '',
  version: '',
  subject: '',
  source: '',
  type: '',
  format: '',
});
// 定义事件
const emit = defineEmits([
  'openGradeFilter',
  'selectSubject',
  'selectSource',
  'selectType',
  'openFormatFilter',
  'selectFormat',
  'openFormatFilter', // 新增格式筛选弹窗事件
]);

watch(
  () => props.currentGrade,
  () => {
    getTextbookVersions();
  }
);

const getTextbookVersions = async () => {
  console.log('props.currentGrade', props.currentGrade);
  const res = await getTextbookVersionsList({
    gradeId: props.currentGrade.gradeId,
  });
  versionList.value = res;
  console.log('versionList.value', versionList.value);
};

const getSubjectsList = async () => {
  try {
    const res = await getSubjects({ gradeId: props.currentGrade.gradeId });
    subjectList.value = res.subjects;

    // 如果有学科数据，自动选中第一个学科（而不是"全部"）
    if (subjectList.value.length > 0) {
      const firstSubject = subjectList.value[0];
      // 通知父组件选中第一个学科
      emit('selectSubject', firstSubject.id.toString());
    }
  } catch (error) {
    console.error('获取学科列表失败:', error);
  }
};

const getResourceTypesList = async () => {
  try {
    const res = await getResourceTypes();
    typeList.value = res.resourceTypes;
  } catch (error) {
    console.error('获取资源类型列表失败:', error);
  }
};

const getResourceFormatsList = async () => {
  try {
    const res = await getResourceFormats();
    formatList.value = res.resourceFormats;
  } catch (error) {
    console.error('获取资源格式列表失败:', error);
  }
};

// 监听父组件传递的格式选择变化
watch(
  () => props.currentFormat,
  newFormats => {
    selectedFormats.value = [...newFormats];
  },
  { immediate: true }
);

watch(
  () => props.currentGrade,
  () => {
    getSubjectsList();
  }
);

onMounted(async () => {
  getSubjectsList();
  getResourceTypesList();
  getResourceFormatsList();
  getTextbookVersions();
});

// 处理年级筛选弹窗
const handleOpenGradeFilter = () => {
  emit('openGradeFilter');
};

// 处理学科选择
const handleSelectSubject = (subject: string) => {
  emit('selectSubject', subject);
};

// 处理来源选择 - 单选逻辑
const handleSelectSource = (source: string) => {
  // 直接设置选中的来源
  selectedSources.value = source;
  emit('selectSource', selectedSources.value);
};

// 处理类型选择 - 多选逻辑
const handleSelectType = (type: number) => {
  if (type === 0) {
    // 选择"全部"，清空其他选择
    selectedTypes.value = [0];
  } else {
    // 移除"全部"选项
    selectedTypes.value = selectedTypes.value.filter(t => t !== 0);

    // 切换选择状态
    const index = selectedTypes.value.indexOf(type);
    if (index > -1) {
      selectedTypes.value.splice(index, 1);
    } else {
      selectedTypes.value.push(type);
    }

    // 如果没有选择任何项，默认选择"全部"
    if (selectedTypes.value.length === 0) {
      selectedTypes.value = [0];
    }
  }
  console.log('selectedType666s', [...selectedTypes.value]);
  emit('selectType', [...selectedTypes.value]);
};

// 处理格式分类选择 - 多选逻辑
const handleSelectFormatCategory = (formatCategory: ResourceFormatsItem | null) => {
  currentFormatCategory.value = formatCategory;

  if (!formatCategory) {
    // 选择"全部"，清空其他选择
    selectedFormats.value = [0];
    filterParams.value.format = '0';
    emit('selectFormat', selectedFormats.value);
  } else if (formatCategory.children && formatCategory.children.length > 0) {
    // 有子项，触发事件打开父组件的弹窗
    // 如果当前选中的是"全部"，传递空数组给弹窗
    const currentSelected = selectedFormats.value.includes(0) ? [] : [...selectedFormats.value];
    emit('openFormatFilter', {
      category: formatCategory,
      currentSelected: currentSelected,
    });
  } else {
    // 没有子项，直接选择该格式 - 多选逻辑
    // 移除"全部"选项
    selectedFormats.value = selectedFormats.value.filter(f => f !== 0);

    const index = selectedFormats.value.indexOf(formatCategory.id);
    if (index > -1) {
      selectedFormats.value.splice(index, 1);
    } else {
      selectedFormats.value.push(formatCategory.id);
    }

    // 如果没有选择任何项，默认选择"全部"
    if (selectedFormats.value.length === 0) {
      selectedFormats.value = [0];
    }

    filterParams.value.format = selectedFormats.value.join(',');
    emit('selectFormat', selectedFormats.value);
  }
};

// 格式弹窗相关函数已移到父组件

// 弹窗相关函数已移到父组件处理

// 暴露选中的值给父组件
defineExpose({
  selectedSources,
  selectedTypes,
  selectedFormats,
  currentFormatCategory,
});
</script>

<template>
  <view class="filter-area">
    <view class="filter-area-wrapper">
      <!-- 学科切换 -->
      <scroll-view scroll-x class="subject-scroll" show-scrollbar="false">
        <view class="subject-list">
          <!-- 全部选项 -->
          <!-- <view
            class="subject-item"
            :class="{ active: currentSubject === '全部' }"
            @click="handleSelectSubject('全部')"
          >
            全部
          </view> -->
          <!-- 学科列表 -->
          <view
            class="subject-item"
            v-for="(subject, index) in subjectList"
            :key="index"
            :class="{ active: currentSubject === subject.id.toString() }"
            @click="handleSelectSubject(subject.id.toString())"
          >
            {{ subject.name }}
          </view>
        </view>
      </scroll-view>
      <view class="filter-icon" @click="handleOpenGradeFilter">
        <LkSvg width="22px" height="22px" src="/static/learning-resource/filter.svg" />
      </view>
    </view>

    <!-- 来源筛选 -->
    <view class="filter-row">
      <view class="filter-label">来源：</view>
      <scroll-view scroll-x class="filter-options-scroll" show-scrollbar="false">
        <view class="filter-options">
          <view
            class="filter-option"
            v-for="(source, index) in sourceList"
            :key="index"
            :class="{ active: selectedSources === source.code }"
            @click="handleSelectSource(source.code)"
          >
            {{ source.name }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 类型筛选 -->
    <view class="filter-row">
      <view class="filter-label">类型：</view>
      <scroll-view scroll-x class="filter-options-scroll" show-scrollbar="false">
        <view class="filter-options">
          <view
            class="filter-option"
            :class="{ active: selectedTypes.includes(0) }"
            @click="handleSelectType(0)"
          >
            全部
          </view>
          <view
            class="filter-option"
            v-for="(type, index) in typeList"
            :key="index"
            :class="{ active: selectedTypes.includes(type.id) }"
            @click="handleSelectType(type.id)"
          >
            {{ type.name }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 格式筛选 -->
    <view class="filter-row">
      <view class="filter-label">格式：</view>
      <scroll-view scroll-x class="filter-options-scroll" show-scrollbar="false">
        <view class="filter-options">
          <view
            class="filter-option"
            :class="{ active: selectedFormats.includes(0) }"
            @click="handleSelectFormatCategory(null)"
          >
            全部
          </view>
          <view
            class="filter-option-with-arrow"
            v-for="(format, index) in formatList"
            :key="index"
            :class="{
              active:
                selectedFormats.includes(format.id) ||
                (format.children &&
                  format.children.some(child => selectedFormats.includes(child.id))),
            }"
            @click="handleSelectFormatCategory(format)"
          >
            <text>{{ format.name }}</text>
            <LkSvg width="16px" height="16px" src="/static/learning-resource/down_black.svg" />
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 格式选择弹窗已移到父组件 -->
  </view>
</template>

<style lang="scss" scoped>
.filter-area-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  width: 100%;
}

.filter-area {
  //   border-radius: 24rpx 24rpx 0 0;
  padding: 16px;
  //   box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;

  .grade-filter {
    display: flex;
    align-items: center;
    border-radius: 16rpx;
    transition: all 0.3s ease;
    flex-shrink: 0; /* 防止收缩 */
    height: auto; /* 允许高度自适应 */
    min-height: 80rpx; /* 设置最小高度 */

    &:active {
      transform: scale(0.98);
    }

    .grade-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      flex: 1;

      .grade-name {
        font-size: 28rpx;
        font-weight: 600;
        color: #1e293b;
        line-height: 1.2;
        white-space: nowrap;
        margin-bottom: 4rpx; /* 替代 gap */
      }

      .grade-version {
        font-size: 24rpx;
        color: #64748b;
        line-height: 1.2;
        white-space: nowrap;
      }
    }

    .arrow-icon {
      margin-left: 12rpx;
      flex-shrink: 0;
      opacity: 0.6;
      transition: transform 0.3s ease;
    }
  }

  .subject-scroll {
    flex: 1; /* 占据剩余空间 */
    overflow-x: auto; /* 启用水平滚动 */
    overflow-y: visible; /* 确保垂直方向不裁剪内容 */
    margin: 0; /* 移除上下边距 */
    display: flex;
    align-items: flex-start; /* 改为靠上对齐，与子元素保持一致 */

    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    /* 确保在uni-app中的scroll-view正常工作 */
    ::v-deep .uni-scroll-view {
      height: 100%;
      display: flex;
      align-items: center;
      border-radius: 24px;
    }

    ::v-deep .uni-scroll-view-content {
      height: 100%;
      display: flex;
      align-items: center;
    }

    .subject-list {
      display: inline-flex;
      align-items: flex-start; /* 靠上对齐 */
      min-width: max-content; /* 确保内容不被压缩 */
      height: 100%; /* 占满父容器高度 */
      padding-bottom: 8px; /* 为指示器留出底部空间 */

      .subject-item {
        display: inline-flex;
        align-items: flex-start;
        justify-content: center;
        padding: 0 16px;
        margin: 4rpx; /* 使用 margin 替代 gap */
        font-size: 17px;
        color: var(--text-primary, #303133);
        position: relative;
        border-radius: 12rpx;
        transition: all 0.3s ease;
        white-space: nowrap;
        min-width: 80rpx;
        height: 48rpx; /* 调整高度以适应新布局 */
        flex-shrink: 0; /* 防止收缩 */
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        text-align: center;

        &:active {
          transform: scale(0.95);
        }

        &.active {
          background: linear-gradient(90deg, #5199ff 0%, #774cff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
          font-weight: 600;

          &::after {
            content: '';
            bottom: -6px; /* 改为从底部定位，避免被遮挡 */
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 4px;
            border-radius: 50px;
            background: linear-gradient(90deg, #5199ff 0%, #774cff 100%);
            z-index: 10; /* 确保在最上层显示 */
          }
        }
      }
    }
  }

  .filter-row {
    display: flex;
    align-items: center;

    .filter-label {
      width: 88rpx;
      font-size: 28rpx;
      color: #374151;
      font-weight: 500;
      flex-shrink: 0;
    }

    .filter-options-scroll {
      flex: 1;
      overflow-x: auto;
      overflow-y: hidden;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
    }

    .filter-options {
      display: flex;
      flex-wrap: nowrap;
      white-space: nowrap;
      padding: 4rpx 0;
      min-width: 100px;

      .filter-option,
      .filter-option-with-arrow {
        display: flex;
        height: 32px;
        padding: 3px 16px;
        margin: 0 8px; /* 使用 margin 替代 gap */
        margin-bottom: 8px;
        justify-content: center;
        align-items: center;
        border-radius: 9999px;
        background: rgba(184, 184, 184, 0.1);
        transition: all 0.3s ease;
        position: relative;
        flex-shrink: 0;

        &:active {
          border: 1px solid #36f;
          background: #efecff;
          color: #7d4dff;
        }

        &.active {
          display: flex;
          height: 32px;
          padding: 3px 16px;
          justify-content: center;
          align-items: center;
          border-radius: 999px;
          border: 1px solid #7f72f9;
          background: #efecff;
          color: #667eea;
          font-weight: 500;
        }

        .selected-count {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          width: 32rpx;
          height: 32rpx;
          background: #667eea;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20rpx;
          font-weight: bold;
          z-index: 1;
        }
      }

      .filter-option-with-arrow {
        display: flex;
        align-items: center;

        .iconfont {
          font-size: 24rpx;
          margin-left: 8rpx;
          transition: transform 0.3s ease;
        }
      }
    }
  }
}
.filter-icon {
  padding: 0 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
// 弹窗样式已移到父组件
</style>
