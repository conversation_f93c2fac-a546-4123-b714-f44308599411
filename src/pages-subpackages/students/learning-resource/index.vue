<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import LkSvg from '@/components/svg/index.vue';
import LkButton from '@/components/LkButton/index.vue';
import FileContent from './component/filecontent.vue';
import FilterArea from './component/filter-area.vue';
import { getHotSearch, getSearchResources, getGradesList } from '@/api/students/resourceCenter';
import type {
  SearchResourcesParams,
  ResourceDetailItem,
  GradesItem,
} from '@/types/students/resourceCenter';
import LkGradeSelector from '@/components/LkGradeSelector/index.vue';
import VoicePopup from './component/voicePopup.vue';
import useDeviceDetection from '@/hooks/useDeviceDetection';

// 类型定义
interface ResourceItem {
  areaId: number;
  areaName: string;
  attribution: string;
  createTime: string;
  description: string;
  fileFormatId: number;
  fileFormatName: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileUrl: string;
  gradeId: number;
  gradeName: string;
  id: number;
  resourceTypeId: number;
  resourceTypeName: string;
  stageId: number;
  stageName: string;
  subjectId: number;
  subjectName: string;
  textbookVersionId: number;
  textbookVersionName: string;
  textbookVolumeId: number;
  textbookVolumeName: string;
  title: string;
  updateTime: string;
}

interface GradeInfo {
  name: string;
  versionName: string;
  semester: string;
  volume: string;
  textbookVersionId: number;
  textbookVolumeId: number;
  gradeId: number;
  textbookVolumeCategory: number;
}

interface FilterParams {
  grade?: string;
  volume?: string;
  versionName?: string;
  subject?: string;
  source?: number;
  type?: string;
  format?: string;
  keyword?: string;
  gradeId?: number;
}

// 用户信息

// 响应式数据
const keyword = ref('');
const hotSearchList = ref<string[]>([]);
const isLoading = ref(false);
const searchResultInfo = ref({
  total: 0,
  totalPages: 0,
  pageNum: 1,
  pageSize: 10,
});

// 年级和版本相关 - 根据学生身份信息初始化
const currentGrade = ref<GradeInfo>({
  name: '',
  versionName: '',
  semester: '',
  volume: '1',
  textbookVersionId: 0,
  textbookVolumeId: 0,
  gradeId: 0,
  textbookVolumeCategory: 0,
});

// 学科相关 - 根据年级动态获取
const currentSubject = ref('全部');
const subjectList = ref<string[]>([]);

// 来源相关
const currentSource = ref(0);
const sourceList = ref(['官方资源', '校本资源', '个人资源', '第三方资源']);

// 类型相关
const currentType = ref<number[]>([0]);
const typeList = ref<string[]>([]);

// 格式相关
const currentFormat = ref<number[]>([0]);

// 组件引用
const filterAreaRef = ref();
const showGradePopup = ref(false);

// 语音输入弹窗相关
const showVoicePopup = ref(false);

// 格式选择弹窗相关
const showFormatPopup = ref(false);
const currentFormatCategory = ref<any>(null);
const tempSelectedFormats = ref<number[]>([]);

const { isMobile, isTablet } = useDeviceDetection();

// 筛选参数
const filterParams = ref<FilterParams>({
  grade: currentGrade.value.name,
  versionName: currentGrade.value.versionName,
  subject: currentSubject.value,
  source: currentSource.value,
  type: currentType.value.join(','),
  format: currentFormat.value.join(','),
  gradeId: currentGrade.value.gradeId,
});

const aiResourceIds = ref<number[]>([]);

// 滑动相关状态
const scrollPosition = ref(0);
const isHeaderVisible = ref(true);
const isContentOverlaying = ref(false);
const lastScrollTop = ref(0);
const scrollThreshold = ref(50); // 滑动阈值，超过这个值开始覆盖
const headerHeight = ref(0); // header区域的高度
const scrollTop = ref(0); // 用于控制scroll-view的滚动位置
const isScrollHandlerDisabled = ref(false); // 用于临时禁用滚动处理
const scrollViewKey = ref(0); // 用于强制重新渲染scroll-view

// 增强的滑动检测状态
const scrollHistory = ref<number[]>([]); // 记录最近几次滚动位置
const scrollStartTime = ref(0); // 滚动开始时间
const scrollStartPosition = ref(0); // 滚动开始位置
const isScrolling = ref(false); // 是否正在滚动
const scrollEndTimer = ref<number | null>(null); // 滚动结束定时器

// 触摸事件相关状态
const touchStartY = ref(0);
const touchStartX = ref(0);
const touchMoveY = ref(0);
const touchMoveX = ref(0);
const minSwipeDistance = 30; // 最小滑动距离，小于这个值不触发
const isVerticalSwipe = ref(false); // 是否为垂直滑动

// 处理触摸开始事件
const handleTouchStart = (e: any) => {
  const touch = e.touches[0];
  touchStartY.value = touch.clientY;
  touchStartX.value = touch.clientX;
  isVerticalSwipe.value = false;
};

// 处理触摸移动事件
const handleTouchMove = (e: any) => {
  if (isContentOverlaying.value || isScrollHandlerDisabled.value) return;

  const touch = e.touches[0];
  touchMoveY.value = touch.clientY;
  touchMoveX.value = touch.clientX;

  // 计算水平和垂直方向的移动距离
  const deltaY = touchStartY.value - touchMoveY.value;
  const deltaX = Math.abs(touchStartX.value - touchMoveX.value);

  // 如果垂直移动距离大于水平移动距离的1.5倍，且垂直移动距离大于最小滑动距离，则判定为垂直滑动
  if (deltaY > 0 && deltaY > deltaX * 1.5 && deltaY > minSwipeDistance) {
    isVerticalSwipe.value = true;

    // 如果是明显的向上滑动，触发覆盖模式
    if (deltaY > minSwipeDistance) {
      console.log('Touch detected upward swipe:', {
        deltaY,
        deltaX,
        touchStartY: touchStartY.value,
        touchMoveY: touchMoveY.value,
      });

      // 激活覆盖模式，但不阻止默认行为，保持原有滚动效果
      activateOverlayMode();
    }
  }
};

// 处理触摸结束事件
const handleTouchEnd = (e: any) => {
  // 重置触摸状态
  isVerticalSwipe.value = false;
};

// 激活覆盖模式的函数，抽取公共逻辑
const activateOverlayMode = () => {
  // 如果已经是覆盖模式或滚动处理被禁用，直接返回
  if (isContentOverlaying.value || isScrollHandlerDisabled.value) return;

  // 激活覆盖模式
  isContentOverlaying.value = true;
  isHeaderVisible.value = false;

  // 重置滚动相关状态
  isScrolling.value = false;
  scrollHistory.value = [];

  // 强制重置滚动位置到顶部
  scrollViewKey.value = Date.now();
  scrollTop.value = 0;
  lastScrollTop.value = 0;

  console.log('Content overlaying activated, scroll reset to top with key:', scrollViewKey.value);
};

// 资源列表
const resourceList = ref<ResourceItem[]>([]);

// 年级列表相关
const gradesList = ref<GradesItem[]>([]);
const selectedGradeId = ref<number>(0);

// 年级下拉选择相关
const showGradeDropdown = ref(false);

// 页面加载
onLoad(() => {
  initPageData();
});

// 在onMounted中计算header高度
onMounted(() => {
  setStatusBarHeight();
  updateSearchAreaHeight(); // 初始化搜索区域高度
  calculateHeaderHeight();
  calculateHeaderAreaHeight(); // 计算header区域高度
  // 确保初始状态正确
  isScrollHandlerDisabled.value = false;
  // 监听窗口大小变化

  // 添加全局点击事件监听器，用于关闭下拉列表
  document.addEventListener('click', handleClickOutside);
});

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
  // 清理滚动定时器
  if (scrollEndTimer.value) {
    clearTimeout(scrollEndTimer.value);
  }
});

// 点击外部区域关闭下拉列表
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const gradeSelector = document.querySelector('.grade-selector');

  if (gradeSelector && !gradeSelector.contains(target)) {
    showGradeDropdown.value = false;
  }
};

// 将来源代码转换为归属值
const getAttributionFromSource = (sources: string[]): number => {
  // 如果选择了"全部"或者多个来源，返回0表示不限制
  if (sources.includes('0') || sources.length > 1) {
    return 0;
  }

  // 根据来源代码转换为归属值
  const sourceCode = sources[0];
  switch (sourceCode) {
    case '3': // 官方资源
      return 3;
    case '2': // 校本资源
      return 2;
    default:
      return 0;
  }
};

// 从筛选器数据构建搜索参数
const buildSearchParams = (pageNum: number = 1, pageSize: number = 10): SearchResourcesParams => {
  // 处理学科ID：如果是"全部"或无效值，则传空数组表示不限制学科
  const getSubjectIds = () => {
    if (currentSubject.value === '全部' || !currentSubject.value) {
      return [];
    }
    const subjectId = parseInt(currentSubject.value);
    return isNaN(subjectId) ? [] : [subjectId];
  };

  return {
    aiResourceIds: aiResourceIds.value,
    attribution: Number(currentSource.value),
    fileFormatIds: [...currentFormat.value], // 需要根据格式名称获取ID
    gradeId: currentGrade.value.gradeId, // 需要根据年级名称获取ID
    pageNum,
    pageSize,
    resourceTypeIds: [...currentType.value], // 需要根据类型名称获取ID
    searchInfo: keyword.value,
    subjectIds: getSubjectIds(),
    textbookVersionId: currentGrade.value.textbookVersionId,
    textbookVolumeId: 0,
    textbookVolumeCategory: currentGrade.value.textbookVolumeCategory,
  };
};

// 将搜索结果转换为ResourceItem格式
const convertToResourceItem = (item: ResourceDetailItem): ResourceItem => {
  return {
    areaId: item.areaId || 0,
    areaName: item.areaName || '',
    attribution: item.attribution || '',
    createTime: item.createTime || '',
    description: item.description || '',
    fileFormatId: item.fileFormatId || 0,
    fileFormatName: item.fileFormatName || '',
    fileKey: '', // ResourceDetailItem 中没有 fileKey 字段，设为空字符串
    fileName: item.fileName || '',
    fileSize: item.fileSize || 0,
    fileUrl: item.fileUrl || '',
    gradeId: item.gradeId || 0,
    gradeName: item.gradeName || '',
    id: item.id,
    resourceTypeId: item.resourceTypeId || 0,
    resourceTypeName: item.resourceTypeName || '',
    stageId: item.stageId || 0,
    stageName: item.stageName || '',
    subjectId: item.subjectId || 0,
    subjectName: item.subjectName || '',
    textbookVersionId: item.textbookVersionId || 0,
    textbookVersionName: item.textbookVersionName || '',
    textbookVolumeId: item.textbookVolumeId || 0,
    textbookVolumeName: item.textbookVolumeName || '',
    title: item.title,
    updateTime: item.updateTime || '',
  };
};

// 执行搜索
const searchResources = async (pageNum: number = 1, pageSize: number = 10) => {
  try {
    isLoading.value = true;
    const params = buildSearchParams(pageNum, pageSize);
    console.log('搜索参数:', params);

    const res = await getSearchResources(params);
    aiResourceIds.value = res.aiResourceIds;

    // 更新搜索结果信息
    searchResultInfo.value = {
      total: res.total,
      totalPages: res.totalPages,
      pageNum: res.pageNum,
      pageSize: res.pageSize,
    };

    // 转换并更新资源列表
    if (pageNum === 1) {
      // 第一页，替换整个列表
      resourceList.value = res.resources.map(convertToResourceItem);
    } else {
      // 后续页，追加到列表
      resourceList.value.push(...res.resources.map(convertToResourceItem));
    }
  } catch (error) {
    console.error('搜索失败:', error);
    uni.showToast({
      title: '搜索失败',
      icon: 'error',
    });
  } finally {
    isLoading.value = false;
  }
};

// 加载学生默认年级和教材版本信息
const loadStudentDefaults = async () => {
  try {
    const studentInfo = uni.getStorageSync('defultgrade');
    console.log('获取到学生默认信息:', studentInfo);

    if (studentInfo) {
      // 更新当前年级信息
      currentGrade.value = {
        name: studentInfo.gradeName,
        versionName: studentInfo.textbookVersionName,
        semester: '上册', // 默认上册
        volume: '1',
        textbookVersionId: studentInfo.textbookVersionId,
        textbookVolumeId: 1, // 默认上册对应的册别ID
        gradeId: studentInfo.gradeId,
        textbookVolumeCategory: 1,
      };

      // 设置默认选中的年级ID
      selectedGradeId.value = studentInfo.gradeId;

      // 更新筛选参数
      filterParams.value.grade = studentInfo.gradeName;
      filterParams.value.versionName = studentInfo.textbookVersionName;

      console.log('学生默认信息已设置到页面:', currentGrade.value);
    }
  } catch (error) {
    console.warn('获取学生默认年级和教材版本失败:', error);
    // 如果获取失败，保持原有的默认值
  }
};

// 计算header高度的函数
const calculateHeaderHeight = () => {};

// 初始化页面数据
const initPageData = async () => {
  setStatusBarHeight();
  await loadStudentDefaults();
  await fetchGradesList(); // 获取年级列表
  await fetchResourceList();
  await fetchHotSearchList();
  // await Promise.all([, , ]);
};

// 获取热搜资源列表
const fetchHotSearchList = async () => {
  try {
    const res = await getHotSearch();
    hotSearchList.value = res.resourceNames.slice(0, 2);
    nextTick(() => {
      updateSearchAreaHeight();
      calculateHeaderHeight();
      calculateHeaderAreaHeight(); // 重新计算header区域高度
    });
  } catch (error) {
    console.error('获取热搜资源失败:', error);
    hotSearchList.value = [];
    // 即使失败也要重新计算高度
    nextTick(() => {
      updateSearchAreaHeight();
      calculateHeaderHeight();
      calculateHeaderAreaHeight(); // 重新计算header区域高度
    });
  }
};

// 获取年级列表
const fetchGradesList = async () => {
  try {
    const res = await getGradesList();
    gradesList.value = res.grades;
    console.log('获取年级列表成功:', gradesList.value);
  } catch (error) {
    console.error('获取年级列表失败:', error);
    gradesList.value = [];
  }
};

// 显示header
const showHeader = () => {
  // 临时禁用滚动处理，防止在重置过程中触发覆盖模式
  isScrollHandlerDisabled.value = true;

  const wasOverlaying = isContentOverlaying.value;

  // 重置所有状态
  isHeaderVisible.value = true;
  isContentOverlaying.value = false;
  scrollPosition.value = 0;
  lastScrollTop.value = 0;

  // 重置增强滑动检测状态
  isScrolling.value = false;
  scrollHistory.value = [];
  scrollStartTime.value = 0;
  scrollStartPosition.value = 0;

  // 清理滚动定时器
  if (scrollEndTimer.value) {
    clearTimeout(scrollEndTimer.value);
    scrollEndTimer.value = null;
  }

  // 如果之前是覆盖状态，需要重置滚动位置
  if (wasOverlaying) {
    // 更新key强制重新渲染scroll-view并重置滚动位置
    scrollViewKey.value = Date.now();
    scrollTop.value = 0;
    nextTick(() => {
      // 延迟重新启用滚动处理
      setTimeout(() => {
        isScrollHandlerDisabled.value = false;
      }, 300);
    });
  } else {
    // 如果不是从覆盖状态返回，立即重新启用
    setTimeout(() => {
      isScrollHandlerDisabled.value = false;
    }, 100);
  }
};

// 计算header区域高度
const calculateHeaderAreaHeight = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery();
    query
      .select('.fixed-header')
      .boundingClientRect((data: any) => {
        if (data && typeof data.height === 'number') {
          headerHeight.value = data.height;
          console.log('Header area height calculated:', headerHeight.value);
        }
      })
      .exec();
  });
};

// 处理滚动事件
const handleScroll = (e: any) => {
  console.log('handleScroll', e);
  // 如果滚动处理被禁用，直接返回
  if (isScrollHandlerDisabled.value) {
    return;
  }

  const currentScrollTop = e.detail.scrollTop;
  const deltaY = currentScrollTop - lastScrollTop.value;
  const { scrollHeight, clientHeight } = e.detail;
  const currentTime = Date.now();

  // 记录滚动历史，用于计算滑动趋势
  scrollHistory.value.push(currentScrollTop);
  if (scrollHistory.value.length > 5) {
    scrollHistory.value.shift(); // 只保留最近5次记录
  }

  // 检测滚动开始
  if (!isScrolling.value) {
    isScrolling.value = true;
    scrollStartTime.value = currentTime;
    scrollStartPosition.value = currentScrollTop;
  }

  // 在非覆盖模式下，检查是否接近底部，触发加载更多
  if (!isContentOverlaying.value) {
    const threshold = 100; // 距离底部100px时开始加载
    if (scrollHeight - currentScrollTop - clientHeight < threshold) {
      // 检查是否还有更多数据可以加载
      if (searchResultInfo.value.pageNum < searchResultInfo.value.totalPages && !isLoading.value) {
        console.log('Loading more resources in normal mode...');
        searchResources(searchResultInfo.value.pageNum + 1, searchResultInfo.value.pageSize);
      }
    }
  }

  // 简化的覆盖模式触发逻辑 - 更灵敏地检测向上滑动
  if (!isContentOverlaying.value) {
    // 判断是否为向上滑动
    const isUpwardScroll = deltaY > 0;

    // 降低触发阈值，只要有明显的向上滑动就触发
    const minScrollThreshold = 20; // 最小滚动阈值，降低为20px

    // 计算滑动速度
    const scrollDuration = currentTime - scrollStartTime.value;
    const scrollDistance = Math.abs(currentScrollTop - scrollStartPosition.value);
    const scrollSpeed = scrollDuration > 0 ? scrollDistance / scrollDuration : 0;

    // 滑动趋势检测 - 检查最近的滚动是否都是向上的
    const hasUpwardTrend =
      scrollHistory.value.length >= 2 &&
      scrollHistory.value.slice(-2).every((pos, idx, arr) => idx === 0 || pos > arr[idx - 1]);

    // 触发条件：向上滑动 + (滚动距离超过最小阈值 或 有明显的向上趋势)
    const shouldTrigger =
      isUpwardScroll &&
      (deltaY > minScrollThreshold ||
        (hasUpwardTrend && scrollSpeed > 0.3) ||
        currentScrollTop > scrollThreshold.value);

    if (shouldTrigger) {
      console.log('Triggering overlay mode from scroll:', {
        deltaY,
        currentScrollTop,
        scrollSpeed,
        hasUpwardTrend,
        scrollDistance,
        scrollDuration,
      });

      // 使用公共函数激活覆盖模式
      activateOverlayMode();
      return;
    }
  }

  lastScrollTop.value = currentScrollTop;

  // 滚动结束检测（延迟重置滚动状态）
  if (scrollEndTimer.value) {
    clearTimeout(scrollEndTimer.value);
  }
  scrollEndTimer.value = setTimeout(() => {
    isScrolling.value = false;
    scrollHistory.value = [];
  }, 150);
};

// 处理覆盖模式下的滚动事件
const handleOverlayScroll = (e: any) => {
  // 在覆盖模式下，滚动事件主要用于内容滚动
  const { scrollTop, scrollHeight, clientHeight } = e.detail;

  // 检查是否接近底部，触发加载更多
  const threshold = 100; // 距离底部100px时开始加载
  if (scrollHeight - scrollTop - clientHeight < threshold) {
    // 检查是否还有更多数据可以加载
    if (searchResultInfo.value.pageNum < searchResultInfo.value.totalPages && !isLoading.value) {
      console.log('Loading more resources...');
      searchResources(searchResultInfo.value.pageNum + 1, searchResultInfo.value.pageSize);
    }
  }

  console.log('Overlay scroll:', scrollTop);
};

const handleBack = () => {
  console.log('handleBack');
  const pages = getCurrentPages();
  if (pages.length <= 1) {
    uni.switchTab({
      url: '/pages/index/index',
    });
  } else {
    uni.navigateBack();
  }
};

const handleToCollection = () => {
  uni.navigateTo({
    url: '/pages-subpackages/students/learning-resource/my-collection',
  });
};

const handleSearch = () => {
  if (!keyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索内容',
      icon: 'none',
    });
    return;
  }
  uni.setStorageSync('searchParams', {
    aiResourceIds: aiResourceIds.value,
    searchInfo: keyword.value,
  });
  uni.navigateTo({
    url: `/pages-subpackages/students/learning-resource/search-result`,
  });
};

const handleVoiceSearch = () => {
  console.log('handleVoiceSearch');
  // 打开语音输入弹窗
  showVoicePopup.value = true;
};

// 语音弹窗事件处理
const handleVoiceClose = () => {
  showVoicePopup.value = false;
};

const handleVoiceCancel = () => {
  showVoicePopup.value = false;
};

const handleVoiceConfirm = (text: string) => {
  showVoicePopup.value = false;
  if (text.trim()) {
    keyword.value = text;
    handleSearch();
  }
};

const handleSelectHotSearch = (searchKeyword: string) => {
  keyword.value = searchKeyword;
};

const handleOpenGradeFilter = () => {
  // 切换下拉列表显示状态
  showGradeDropdown.value = !showGradeDropdown.value;
};

const handleOpenshowGradePopup = () => {
  showGradePopup.value = true;
};

const handleCloseGradePopup = () => {
  showGradePopup.value = false;
};

const handleGradeConfirm = (data: any) => {
  console.log('年级选择确认数据:', data);

  // 更新当前年级信息
  currentGrade.value = {
    name: currentGrade.value.name,
    versionName: data.versionName,
    gradeId: currentGrade.value.gradeId,
    semester: data.semesterId.toString(),
    volume: data.textbookVolumeId.toString(),
    textbookVersionId: data.textbookVersionId,
    textbookVolumeId: data.textbookVolumeId,
    textbookVolumeCategory: data.textbookVolumeCategory,
  };

  // 更新筛选参数
  filterParams.value.grade = currentGrade.value.name;
  filterParams.value.versionName = data.versionName;
  filterParams.value.gradeId = currentGrade.value.gradeId;
  // 重新获取资源列表
  searchResources();
  showGradePopup.value = false;
};

// 处理年级下拉选择
const handleGradeSelect = (item: GradesItem) => {
  console.log('年级选择:', item);
  selectedGradeId.value = item.id;

  // 更新当前年级信息
  currentGrade.value = {
    ...currentGrade.value,
    name: item.name,
    gradeId: item.id,
  };

  // 更新筛选参数
  filterParams.value.grade = item.name;
  filterParams.value.gradeId = item.id;

  // 关闭下拉列表
  showGradeDropdown.value = false;

  // 重新获取资源列表
  searchResources();
};

const handleSelectSubject = (subject: string) => {
  console.log('handleSelectSubject', subject);
  currentSubject.value = subject;
  // 触发搜索
  searchResources();
};

const handleSelectSource = (source: number) => {
  // 单选逻辑，直接设置选中的来源
  currentSource.value = source;
  filterParams.value.source = source;
  // 触发搜索
  searchResources();
};

const handleSelectType = (types: number[]) => {
  // 将数组转换为逗号分隔的字符串用于显示和API调用
  currentType.value = [...types];
  console.log('789789', currentType.value);
  // 触发搜索
  searchResources();
};

const handleSelectFormat = (formats: number[]) => {
  console.log('handleSelectFormat', formats);
  currentFormat.value = formats;
  // 触发搜索
  searchResources();
};

// 处理格式筛选弹窗打开
const handleOpenFormatFilter = (data: { category: any; currentSelected: number[] }) => {
  currentFormatCategory.value = data.category;
  tempSelectedFormats.value = [...data.currentSelected];
  showFormatPopup.value = true;
};

// 处理格式筛选弹窗重制
const handleResetFormatFilter = () => {
  tempSelectedFormats.value = [];
};

const handleCloseFormatFilter = () => {
  showFormatPopup.value = false;
};

// 弹窗中的格式选择 - "全部"和其他选项互斥
const handleSelectFormatInPopup = (format: number) => {
  console.log('handleSelectFormatInPopup', format);

  // 如果点击的是"全部"选项 (format = 0)
  if (format === 0) {
    // 获取所有格式选项的ID
    const allFormatIds = currentFormatCategory.value?.children?.map((item: any) => item.id) || [];

    // 如果当前已经选择了全部，则清空选择
    if (tempSelectedFormats.value.includes(0)) {
      tempSelectedFormats.value = [];
    } else {
      // 选择全部：包含0和所有格式ID，但UI上只显示"全部"选中
      tempSelectedFormats.value = [0, ...allFormatIds];
    }
  } else {
    // 处理单个格式选择
    // 如果当前选中了"全部"，先清除"全部"选择
    if (tempSelectedFormats.value.includes(0)) {
      tempSelectedFormats.value = tempSelectedFormats.value.filter(f => f !== 0);
      // 清除所有格式ID，只保留当前点击的格式
      const allFormatIds = currentFormatCategory.value?.children?.map((item: any) => item.id) || [];
      tempSelectedFormats.value = tempSelectedFormats.value.filter(f => !allFormatIds.includes(f));
      tempSelectedFormats.value.push(format);
    } else {
      // 正常的多选逻辑
      if (tempSelectedFormats.value.includes(format)) {
        // 取消选择该格式
        tempSelectedFormats.value = tempSelectedFormats.value.filter(f => f !== format);
      } else {
        // 选择该格式
        tempSelectedFormats.value = [...tempSelectedFormats.value, format];
      }
    }
  }
};

// 保存格式筛选
const handleSaveFormatFilter = () => {
  // 触发格式选择事件
  handleSelectFormat(tempSelectedFormats.value);
  // 关闭弹窗
  showFormatPopup.value = false;
};

// 获取资源列表
const fetchResourceList = async () => {
  // 使用搜索接口获取资源列表
  await searchResources();
};

const setStatusBarHeight = () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    const safeAreaInsetBottom = systemInfo.safeAreaInsets?.bottom || 0;

    const rootElement = document.documentElement || document.querySelector(':root');
    if (rootElement && rootElement.style) {
      rootElement.style.setProperty('--status-bar-height', `${statusBarHeight}px`);
      rootElement.style.setProperty('--safe-area-inset-bottom', `${safeAreaInsetBottom}px`);
    }
  } catch (error) {
    console.warn('获取状态栏高度失败:', error);
  }
};

// 动态设置搜索区域高度
const updateSearchAreaHeight = () => {
  try {
    const rootElement = document.documentElement || document.querySelector(':root');
    if (rootElement && rootElement.style) {
      // 基础高度：197px
      const baseHeight = 197;
      // 热搜高度：如果有热搜数据则添加56px，否则为0
      const hotSearchHeight = hotSearchList.value.length > 0 ? 56 : 0;
      // 总高度
      const totalHeight = baseHeight + hotSearchHeight;

      rootElement.style.setProperty('--search-area-height', `${totalHeight}px`);
      console.log('Search area height updated:', totalHeight, 'hotSearch:', hotSearchHeight);
    }
  } catch (error) {
    console.warn('设置搜索区域高度失败:', error);
  }
};

// 处理资源详情查看
const handleViewResourceDetail = (item: ResourceItem) => {
  uni.navigateTo({
    url: `/pages-subpackages/students/learning-resource/detail?id=${item.id}&title=${encodeURIComponent(item.title)}&format=${encodeURIComponent(item.fileFormatName)}`,
  });
};

// 这些函数已迁移到 FileContent 组件

defineOptions({ name: 'learningResource' });

// 处理覆盖模式下的触摸开始事件
const handleOverlayTouchStart = (e: any) => {
  // 在覆盖模式下，我们只需要记录触摸开始位置，不需要其他处理
  const touch = e.touches[0];
  touchStartY.value = touch.clientY;
  touchStartX.value = touch.clientX;
};

// 处理覆盖模式下的触摸移动事件
const handleOverlayTouchMove = (e: any) => {
  // 在覆盖模式下，我们不需要特殊处理，让系统默认滚动行为生效
  // 但我们可以记录当前触摸位置，以备后续需要
  const touch = e.touches[0];
  touchMoveY.value = touch.clientY;
  touchMoveX.value = touch.clientX;
};

// 处理覆盖模式下的触摸结束事件
const handleOverlayTouchEnd = (e: any) => {
  // 在覆盖模式下，我们不需要特殊处理触摸结束事件
};
</script>

<template>
  <view class="resource-plaza-container">
    <!-- 固定的header-nav和搜索区域 -->
    <view
      class="fixed-header"
      :class="{ 'header-hidden': !isHeaderVisible || isContentOverlaying }"
      :style="{
        transform: `translateY(${scrollPosition}px)`,
        opacity: isContentOverlaying ? 0 : 1,
        pointerEvents: isContentOverlaying ? 'none' : 'auto',
      }"
    >
      <header-nav title="" :showBack="true" :transparent="true" @back="handleBack">
        <template #right>
          <view class="grade-selector">
            <view class="grade-filter" @click="handleOpenGradeFilter">
              <view class="grade-info">
                <view class="grade-name">{{ currentGrade.name }}</view>
                <LkSvg
                  width="24px"
                  height="24px"
                  src="/static/learning-resource/down.svg"
                  class="arrow-icon"
                  :class="{ 'arrow-up': showGradeDropdown }"
                />
              </view>
            </view>

            <!-- 年级下拉列表 -->
            <view v-if="showGradeDropdown" class="grade-dropdown">
              <view
                v-for="grade in gradesList"
                :key="grade.id"
                class="grade-dropdown-item"
                :class="{ active: grade.id === selectedGradeId }"
                @click.stop="handleGradeSelect(grade)"
              >
                {{ grade.name }}
              </view>
            </view>
          </view>
        </template>
      </header-nav>

      <!-- 搜索区域 -->
      <view :class="isMobile ? 'search-area-mobile' : 'search-area'">
        <view :class="isMobile ? 'search-title-mobile' : 'search-title'">
          <view :class="isMobile ? 'title-mobile' : 'title'"
            >欢迎来到，<span :class="isMobile ? 'title-highlight-mobile' : 'title-highlight'"
              >资源广场</span
            >
          </view>
          <view :class="isMobile ? 'subtitle-mobile' : 'subtitle'"
            >AI帮你在海量资源中找到最优质的学习资源</view
          >
        </view>
        <view class="search-input-box">
          <view class="search-input">
            <input
              class="input"
              type="text"
              v-model="keyword"
              :placeholder="isMobile ? '请输入你想要的资源' : '请输入关键词,搜索你想要的资源'"
              @confirm="handleSearch"
              maxlength="50"
            />
            <view class="voice-btn" @click="handleVoiceSearch">
              <LkSvg
                width="24px"
                height="24px"
                src="/static/learning-resource/voice.svg"
                class="voice-icon"
              />
            </view>
          </view>
          <view class="search-btn" @click="handleSearch">搜索</view>
        </view>

        <!-- 热搜资源 -->
        <view
          :class="isMobile ? 'hot-search-mobile' : 'hot-search'"
          v-if="hotSearchList.length > 0"
        >
          <view :class="isMobile ? 'hot-search-title-mobile' : 'hot-search-title'">热搜资源：</view>
          <view :class="isMobile ? 'hot-search-list-mobile' : 'hot-search-list'">
            <view
              :class="isMobile ? 'hot-search-item-mobile' : 'hot-search-item'"
              v-for="(item, index) in hotSearchList"
              :key="index"
              @click="handleSelectHotSearch(item)"
            >
              {{ item }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 可滚动的内容区域 -->
    <scroll-view
      :key="scrollViewKey"
      class="scrollable-content"
      :class="{ 'content-overlaying': isContentOverlaying }"
      scroll-y
      :scroll-top="scrollTop"
      @scroll="handleScroll"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      :style="{
        marginTop: isContentOverlaying ? `-${headerHeight}px` : '0',
        zIndex: isContentOverlaying ? 1000 : 1,
      }"
      enable-back-to-top
      :enhanced="true"
      :show-scrollbar="true"
      :bounces="true"
      :fast-deceleration="false"
      :scroll-with-animation="true"
      :enable-passive="true"
    >
      <!-- 资源详情导航栏 - 当内容覆盖时显示 -->
      <view v-if="isContentOverlaying" class="detail-header-nav">
        <HeaderNav title="资源详情" @back="showHeader" :centerTitle="true" />
      </view>

      <!-- 筛选区域 - 在覆盖模式下固定 -->
      <view v-if="isContentOverlaying" class="fixed-filter-area">
        <FilterArea
          ref="filterAreaRef"
          :currentGrade="currentGrade"
          :currentSubject="currentSubject"
          :currentSource="currentSource"
          :currentType="currentType"
          :currentFormat="currentFormat"
          :subjectList="subjectList"
          :sourceList="sourceList"
          :typeList="typeList"
          @openGradeFilter="handleOpenshowGradePopup"
          @selectSubject="handleSelectSubject"
          @selectSource="handleSelectSource"
          @selectType="handleSelectType"
          @selectFormat="handleSelectFormat"
          @openFormatFilter="handleOpenFormatFilter"
        />
      </view>

      <!-- 非覆盖模式下的筛选区域 -->
      <FilterArea
        v-if="!isContentOverlaying"
        ref="filterAreaRef"
        :currentGrade="currentGrade"
        :currentSubject="currentSubject"
        :currentSource="currentSource"
        :currentType="currentType"
        :currentFormat="currentFormat"
        :subjectList="subjectList"
        :sourceList="sourceList"
        :typeList="typeList"
        @openGradeFilter="handleOpenshowGradePopup"
        @selectSubject="handleSelectSubject"
        @selectSource="handleSelectSource"
        @selectType="handleSelectType"
        @selectFormat="handleSelectFormat"
        @openFormatFilter="handleOpenFormatFilter"
      />

      <!-- 资源展示区 -->
      <template v-if="isContentOverlaying">
        <!-- 覆盖模式下：资源列表容器 -->
        <view class="overlay-content-wrapper">
          <!-- 覆盖模式下：资源列表独立滚动 -->
          <scroll-view
            class="resource-list resource-list-overlay"
            scroll-y
            :style="{ height: '100%' }"
            @scroll="handleOverlayScroll"
            @touchstart="handleOverlayTouchStart"
            @touchmove="handleOverlayTouchMove"
            @touchend="handleOverlayTouchEnd"
            enable-back-to-top
            :enhanced="true"
            :show-scrollbar="true"
            :bounces="true"
            :fast-deceleration="false"
            :scroll-with-animation="true"
            :enable-passive="true"
          >
            <!-- 骨架屏 -->
            <template v-if="isLoading">
              <view v-for="index in 4" :key="`skeleton-${index}`" class="skeleton-item">
                <view class="skeleton-main-wrapper">
                  <view class="skeleton-main">
                    <view class="skeleton-icon"></view>
                    <view class="skeleton-info">
                      <view class="skeleton-title"></view>
                      <view class="skeleton-tags">
                        <view class="skeleton-tag"></view>
                        <view class="skeleton-tag"></view>
                        <view class="skeleton-tag"></view>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="skeleton-footer">
                  <view class="skeleton-meta">
                    <view class="skeleton-meta-item"></view>
                    <view class="skeleton-meta-item"></view>
                  </view>
                  <view class="skeleton-source-tag"></view>
                </view>
              </view>
            </template>

            <!-- 资源列表 -->
            <template v-else-if="resourceList.length > 0">
              <FileContent
                v-for="(item, index) in resourceList"
                :key="index"
                :item="item"
                :onItemClick="handleViewResourceDetail"
              />
            </template>

            <!-- 空状态 -->
            <view v-else class="empty-state">
              <view class="empty-icon">
                <LkSvg width="80px" height="80px" src="/static/learning-resource/nodata.svg" />
              </view>
              <view class="empty-text">暂无相关资源</view>
            </view>
          </scroll-view>
        </view>
      </template>

      <template v-else>
        <!-- 非覆盖模式下：正常显示 -->
        <view class="resource-list">
          <!-- 骨架屏 -->
          <template v-if="isLoading">
            <view v-for="index in 4" :key="`skeleton-${index}`" class="skeleton-item">
              <view class="skeleton-main-wrapper">
                <view class="skeleton-main">
                  <view class="skeleton-icon"></view>
                  <view class="skeleton-info">
                    <view class="skeleton-title"></view>
                    <view class="skeleton-tags">
                      <view class="skeleton-tag"></view>
                      <view class="skeleton-tag"></view>
                      <view class="skeleton-tag"></view>
                    </view>
                  </view>
                </view>
              </view>
              <view class="skeleton-footer">
                <view class="skeleton-meta">
                  <view class="skeleton-meta-item"></view>
                  <view class="skeleton-meta-item"></view>
                </view>
                <view class="skeleton-source-tag"></view>
              </view>
            </view>
          </template>

          <!-- 资源列表 -->
          <template v-else-if="resourceList.length > 0">
            <FileContent
              v-for="(item, index) in resourceList"
              :key="index"
              :item="item"
              :onItemClick="handleViewResourceDetail"
            />
            <!-- 添加占位内容确保有足够高度触发滚动 -->
            <view class="content-placeholder"></view>
          </template>

          <!-- 空状态 -->
          <view v-else class="empty-state">
            <view class="empty-icon">
              <LkSvg width="80px" height="80px" src="/static/learning-resource/nodata.svg" />
            </view>
            <view class="empty-text">暂无相关资源</view>
            <!-- 添加占位内容确保有足够高度触发滚动 -->
            <view class="empty-placeholder"></view>
          </view>
        </view>
      </template>
    </scroll-view>
    <!-- 年级选择弹窗 -->
    <LkGradeSelector
      :show="showGradePopup"
      :currentSubject="currentSubject"
      :value="{
        gradeName: currentGrade.name,
        gradeId: currentGrade.gradeId, // 这里需要根据年级名称获取对应的ID，暂时设为0
        versionName: currentGrade.versionName,
        versionId: currentGrade.textbookVersionId,
        semesterId: 1, // 默认上册
        textbookVersionId: currentGrade.textbookVersionId,
        textbookVolumeId: currentGrade.textbookVolumeId,
        textbookVolumeCategory: currentGrade.textbookVolumeCategory,
      }"
      @close="handleCloseGradePopup"
      @confirm="handleGradeConfirm"
      @selectSubject="handleSelectSubject"
    />
    <!-- 语音输入弹窗 -->
    <VoicePopup
      v-model:show="showVoicePopup"
      @close="handleVoiceClose"
      @cancel="handleVoiceCancel"
      @confirm="handleVoiceConfirm"
    />

    <!-- 格式选择弹窗 -->
    <u-popup
      mode="bottom"
      :round="24"
      :safeAreaInsetBottom="true"
      :show="showFormatPopup"
      @close="handleCloseFormatFilter"
    >
      <view class="format-popup-content">
        <view class="popup-header">
          <view class="popup-title">选择{{ currentFormatCategory?.name || '格式' }}</view>
          <view class="popup-close" @click="handleCloseFormatFilter">
            <LkSvg width="20px" height="20px" src="/static/learning-resource/close.svg" />
          </view>
        </view>
        <view class="popup-body">
          <view class="popup-section">
            <view class="section-options">
              <view
                class="section-option"
                :class="{ active: tempSelectedFormats.includes(0) }"
                @click="handleSelectFormatInPopup(0)"
              >
                全部
              </view>
              <view
                class="section-option"
                v-for="(format, index) in currentFormatCategory?.children || []"
                :key="index"
                :class="{
                  active:
                    tempSelectedFormats.includes(format.id) && !tempSelectedFormats.includes(0),
                }"
                @click="handleSelectFormatInPopup(format.id)"
              >
                {{ format.name }}
              </view>
            </view>
          </view>
        </view>
        <view class="popup-footer">
          <view class="popup-btn cancel" @click="handleResetFormatFilter">重制</view>
          <view class="popup-btn confirm" @click="handleSaveFormatFilter">保存</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<style lang="scss" scoped>
:root {
  --status-bar-height: 44px; /* 默认状态栏高度，实际使用时应该通过JS动态设置 */
  --search-area-base-height: 197px; /* 搜索区域基础高度：padding(60px) + title(44px) + subtitle(28.8px) + margins(16px) + input(48px) */
  --hot-search-height: 56px; /* 热搜区域高度：margin-top(16px) + content(40px) */
  --search-area-height: 253px; /* 搜索区域总高度（包含热搜）*/
  --safe-area-inset-bottom: 0px; /* 默认底部安全区域高度，实际使用时应该通过JS动态设置 */
}

.resource-plaza-container {
  height: 100vh;
  overflow: hidden;
  // background: url('https://huayun-ai-obs-public.huayuntiantu.com/1c9ea0ca-cb7b-4bb8-adce-135692ce10c4.png');
  // background-size: cover;
  // background-position: top;
  // background-repeat: no-repeat;
  background: #fff;
}

/* 调试面板样式 */
.debug-panel {
  position: fixed;
  top: 100px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 9999;
  min-width: 150px;

  .debug-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    text {
      line-height: 1.4;
    }
  }
}

/* 固定的header和搜索区域 */
.fixed-header {
  position: relative;
  background: url('https://huayun-ai-obs-public.huayuntiantu.com/1c9ea0ca-cb7b-4bb8-adce-135692ce10c4.png');
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;

  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
  z-index: 999;

  &.header-hidden {
    opacity: 0;
    pointer-events: none;
  }
}

/* 可滚动的内容区域 */
.scrollable-content {
  border-radius: 24px 24px 0 0;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  transform: translateY(0);
  transition:
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    margin-top 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 60vh; /* 确保最小高度，保证可以滑动 */
  height: calc(
    100vh - var(--search-area-height) - var(--status-bar-height) - 44px
  ); /* 减去header和状态栏的高度 */
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  position: relative; /* 改为相对定位，避免iOS滚动bug */
  /* 优化滚动性能 */
  will-change: transform;
  contain: layout style paint;
  overflow-y: auto; /* 确保可以垂直滚动 */

  &.content-overlaying {
    position: relative; /* 恢复原来的相对定位 */
    z-index: 1000;
    border-radius: 16px 16px 0 0;
    height: calc(100vh - var(--safe-area-inset-bottom));
    overflow: hidden; /* 修改为hidden，防止内部滚动影响外部 */
    /* 覆盖模式下的动画优化 */
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex; /* 使用flex布局 */
    flex-direction: column; /* 垂直排列子元素 */
  }
}

/* 资源详情导航栏样式 */
.detail-header-nav {
  position: sticky;
  top: 0;
  z-index: 1001;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

/* 固定的筛选区域样式 */
.fixed-filter-area {
  position: sticky;
  top: calc(var(--status-bar-height)); /* 状态栏高度 + 导航栏内容高度 */
  z-index: 1000;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

/* 覆盖模式下的资源列表样式 */
.resource-list-overlay {
  flex: 1;
  margin-top: 0;
  min-height: 0; /* 配合 flex: 1 使用，让元素占用剩余空间 */
  height: calc(100% - 100px); /* 设置明确的高度，减去头部导航和筛选区域的高度 */
  padding-bottom: var(--safe-area-inset-bottom); /* 添加底部安全区域间距 */
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  /* 优化滚动性能 */
  will-change: scroll-position;
  contain: layout style paint;
  /* 增强滚动体验 */
  scroll-behavior: smooth;
  overflow-y: auto; /* 确保可以垂直滚动 */
  position: relative; /* 确保定位正确 */
  z-index: 5; /* 确保在其他元素之上 */

  &::after {
    content: '';
    display: block;
    min-height: 1px; /* 确保内容高度足够触发滚动条 */
  }
}

/* 导航栏样式 */
::v-deep .u-navbar__content__title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

::v-deep .u-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.my-collection {
  display: flex;
  width: 92px;
  height: 28px;
  padding: 5px 8px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 6px;
  background: #fff;
  color: #000;
  font-size: 14px;
  font-weight: 500;
  margin-right: 8px;

  /* 禁用点击效果 */
  &:hover,
  &:active,
  &:focus {
    background: #fff !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 1 !important;
  }

  .lk-svg {
    display: block;
    flex-shrink: 0;
    margin-right: 4px; /* 替代 gap */
  }

  .collection-text {
    font-size: 12px;
    color: #000;
  }
}

/* 搜索区域样式 */
.search-area {
  padding: 46px;
  .search-title {
    text-align: center;
    margin-bottom: 32rpx;

    .title {
      color: var(--Neutral-01, #0a0a0a);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 32px;
      font-style: normal;
      font-weight: 600;
      line-height: 44px; /* 191.304% */
      margin-bottom: 10px;
    }
    .title-highlight {
      background: linear-gradient(95deg, #27b0ff 45.79%, #7f4dff 98.28%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-family: 'PingFang SC';
      font-size: 32px;
      font-style: normal;
      font-weight: 600;
      line-height: 44px;
    }

    .subtitle {
      color: var(--text-icon-text-2, #4e5969);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 28.8px; /* 205.714% */
    }
  }

  .search-input-box {
    display: flex;
    align-items: center;
    margin-top: 32rpx;
    border-radius: 60rpx;
    position: relative;
    /* 渐变边框实现 - 紫色到蓝色 */
    background: linear-gradient(90deg, #b866ff 0%, #4a90ff 100%);
    padding: 4rpx; /* 边框宽度 */

    .search-input {
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #fff;
      padding: 0 32rpx;
      height: 40px; /* 内部高度 */
      border-radius: 56rpx; /* 完全圆角 */
      position: relative;

      .search-icon {
        margin-right: 16rpx;
        opacity: 0.6;
      }

      .voice-btn {
        position: absolute;
        right: 65px; /* 在搜索按钮左侧留出空间 */
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 4px;
        cursor: pointer;
        z-index: 3; /* 确保在搜索按钮之上 */
      }

      .input {
        flex: 1;
        height: 100%;
        font-size: 32rpx;
        color: #666;
        border: none;
        outline: none;
        padding-right: 70px; /* 为语音按钮和搜索按钮预留空间 */

        &::placeholder {
          color: #4e5969;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 28.8px;
        }
      }
    }

    .search-btn {
      position: absolute;
      right: 15rpx; /* 距离边框的距离 */
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      width: 60px;
      height: 32px;
      padding: 5px 16px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      background: linear-gradient(90deg, #5199ff 0%, #774cff 100%);
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
      border-radius: 50rpx;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(75, 144, 255, 0.3);
      z-index: 2;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      &:active {
        border-radius: 100px;
        border: 1px solid var(--Brand-Brand5, #7d4dff);
        background: var(--Brand-Brand1-Light, #f2f3ff);
      }
    }
  }

  .hot-search {
    margin-top: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    .hot-search-title {
      color: #7d4dff;
      font-family: 'PingFang SC';
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0.06px;
      flex-shrink: 0;
      margin-right: 16rpx;
    }

    .hot-search-list {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .hot-search-item {
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(10rpx);
        font-size: 14px;
        color: #606266;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: all 0.3s ease;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        height: 25px;
        padding: 2px 13px;

        &:not(:last-child) {
          margin-right: 16rpx; /* 为除了最后一个项目外的所有项目添加右边距 */
        }
      }
    }
  }
}

.search-area-mobile {
  padding: 37px 30px;
  .search-title-mobile {
    text-align: center;

    .title-mobile {
      color: var(--Neutral-01, #0a0a0a);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 23px;
      font-style: normal;
      font-weight: 600;
      line-height: 23px; /* 191.304% */
    }
    .title-highlight-mobile {
      background: linear-gradient(95deg, #27b0ff 45.79%, #7f4dff 98.28%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-family: 'PingFang SC';
      font-size: 23px;
      font-style: normal;
      font-weight: 600;
      line-height: 44px;
    }

    .subtitle-mobile {
      color: var(--text-icon-text-2, #4e5969);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px; /* 205.714% */
    }
  }

  .search-input-box {
    display: flex;
    align-items: center;
    margin-top: 32rpx;
    border-radius: 60rpx;
    position: relative;
    /* 渐变边框实现 - 紫色到蓝色 */
    background: linear-gradient(90deg, #b866ff 0%, #4a90ff 100%);
    padding: 4rpx; /* 边框宽度 */

    .search-input {
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #fff;
      padding: 0 32rpx;
      height: 40px; /* 内部高度 */
      border-radius: 56rpx; /* 完全圆角 */
      position: relative;

      .search-icon {
        margin-right: 16rpx;
        opacity: 0.6;
      }

      .voice-btn {
        position: absolute;
        right: 65px; /* 在搜索按钮左侧留出空间 */
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 4px;
        cursor: pointer;
        z-index: 3; /* 确保在搜索按钮之上 */
      }

      .input {
        flex: 1;
        height: 100%;
        font-size: 32rpx;
        color: #666;
        border: none;
        outline: none;
        padding-right: 70px; /* 为语音按钮和搜索按钮预留空间 */

        &::placeholder {
          color: #4e5969;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 28.8px;
        }
      }
    }

    .search-btn {
      position: absolute;
      right: 15rpx; /* 距离边框的距离 */
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      width: 60px;
      height: 32px;
      padding: 5px 16px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      background: linear-gradient(90deg, #5199ff 0%, #774cff 100%);
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
      border-radius: 50rpx;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(75, 144, 255, 0.3);
      z-index: 2;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      &:active {
        border-radius: 100px;
        border: 1px solid var(--Brand-Brand5, #7d4dff);
        background: var(--Brand-Brand1-Light, #f2f3ff);
      }
    }
  }

  .hot-search-mobile {
    margin-top: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    .hot-search-title-mobile {
      color: #7d4dff;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0.06px;
      flex-shrink: 0;
      margin-right: 6px;
    }

    .hot-search-list-mobile {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .hot-search-item-mobile {
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(10rpx);
        font-size: 12px;
        color: #606266;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: all 0.3s ease;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        height: 25px;
        padding: 2px 13px;
        line-height: 20px;

        &:not(:last-child) {
          margin-right: 16rpx; /* 为除了最后一个项目外的所有项目添加右边距 */
        }
      }
    }
  }
}

/* 资源展示区样式 */
.resource-list {
  background-color: #fff;
  padding: 0 30rpx calc(32rpx + var(--safe-area-inset-bottom)); /* 底部padding包含安全区域 */
  flex: 1;
  overflow: hidden;
  min-height: 50vh; /* 确保最小高度，即使内容不足也能滑动 */
  /* 添加以下属性以确保内容高度足够触发滚动条 */
  &::after {
    content: '';
    display: block;
    min-height: 1px; /* 确保内容高度足够触发滚动条 */
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    min-height: 50vh; /* 空状态也要有足够的高度 */

    .empty-icon {
      margin-bottom: 24rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 32rpx;
      color: #666;
      margin-bottom: 12rpx;
      font-weight: 500;
    }

    .empty-tip {
      font-size: 26rpx;
      color: #999;
      text-align: center;
      line-height: 1.4;
    }

    .empty-placeholder {
      height: 30vh; /* 占位高度，确保总高度足够触发滚动 */
      width: 100%;
    }
  }

  .content-placeholder {
    height: 20vh; /* 内容占位高度，确保有足够空间触发滚动 */
    width: 100%;
  }
}

/* 弹窗样式 */
.popup-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    padding-bottom: 16rpx;

    .popup-title {
      font-size: 34rpx;
      font-weight: 600;
      color: #1e293b;
    }

    .popup-close {
      cursor: pointer;
    }
  }

  .popup-body {
    overflow-y: auto;

    .popup-section {
      margin-bottom: 32rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 28rpx;
        color: #374151;
        margin-bottom: 20rpx;
        font-weight: 500;
      }

      .section-options {
        display: flex;
        flex-wrap: wrap;
        margin: -6rpx; /* 负边距来抵消子元素的边距 */

        .section-option {
          width: calc((100% - 24rpx) / 3);
          height: 80rpx;
          margin: 6rpx; /* 使用 margin 替代 gap */
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f8fafc;
          border-radius: 16rpx;
          font-size: 28rpx;
          color: #64748b;
          border: 2rpx solid transparent;
          transition: all 0.3s ease;
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    margin-top: 32rpx;

    .popup-btn {
      display: flex;
      width: 166px;
      padding: 12px 20px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: 100px;
    }

    // .popup-btn {
    //   flex: 1;
    //   height: 88rpx;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   border-radius: 20rpx;
    //   font-size: 30rpx;
    //   font-weight: 500;
    //   transition: all 0.3s ease;

    //   &:not(:last-child) {
    //     margin-right: 16rpx; /* 为除了最后一个按钮外的所有按钮添加右边距 */
    //   }

    //   &:active {
    //     transform: scale(0.98);
    //   }

    //   &.cancel {
    //     background-color: #f8fafc;
    //     color: #64748b;
    //     border: 2rpx solid #e2e8f0;

    //     &:active {
    //       background-color: #f1f5f9;
    //     }
    //   }

    //   &.confirm {
    //     background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    //     color: #fff;

    //     &:active {
    //       background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    //     }
    //   }
    // }
  }
}

/* 骨架屏样式 */
.skeleton-item {
  margin: 0 0 14px 0;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid var(--Gray-Gray3, #e7e7e7);
  background: #fff;

  .skeleton-main-wrapper {
    border-bottom: 1px solid #f3f3f3;
    margin-bottom: 12rpx;
    padding-bottom: 12rpx;

    .skeleton-main {
      display: flex;
      align-items: center;

      .skeleton-icon {
        width: 48px;
        height: 48px;
        margin-right: 24rpx;
        border-radius: 12rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        flex-shrink: 0;
      }

      .skeleton-info {
        flex: 1;
        overflow: hidden;

        .skeleton-title {
          height: 30rpx;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4rpx;
          margin-bottom: 12rpx;
          width: 75%;
        }

        .skeleton-tags {
          display: flex;
          margin: 12rpx 0;

          .skeleton-tag {
            height: 22rpx;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 8rpx;
            width: 60rpx;

            &:not(:last-child) {
              margin-right: 8rpx; /* 为除了最后一个标签外的所有标签添加右边距 */
            }
          }
        }
      }
    }
  }

  .skeleton-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .skeleton-meta {
      display: flex;
      align-items: center;

      .skeleton-meta-item {
        height: 22rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
        width: 80rpx;

        &:not(:last-child) {
          margin-right: 8px; /* 为除了最后一个项目外的所有项目添加右边距 */
        }
      }
    }

    .skeleton-source-tag {
      height: 20px;
      background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      border-radius: 999px;
      width: 40px;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 格式选择弹窗样式 */
.format-popup-content {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  max-height: 80vh;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    position: relative;

    .popup-title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      text-align: center;
    }

    .popup-close {
      position: absolute;
      right: 32rpx;
      top: 50%;
      transform: translateY(-50%);
      padding: 8rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        transform: translateY(-50%) scale(0.95);
      }
    }
  }

  .popup-body {
    padding: 24rpx 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .popup-section {
      .section-options {
        display: flex;
        flex-wrap: wrap;
        margin: -8rpx;

        .section-option {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 16rpx 24rpx;
          margin: 8rpx;
          border-radius: 999px;
          background: #f3f6fd;
          font-size: 28rpx;
          color: #374151;
          transition: all 0.3s ease;
          position: relative;
          width: calc((100% - 48rpx) / 3); /* 一行显示3个，减去margin */
          flex-shrink: 0;

          &:active {
            transform: scale(0.95);
          }

          &.active {
            background: #efecff;
            border: 1px solid #7f72f9;
            color: #667eea;
            font-weight: 500;
          }
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    padding: 24rpx 32rpx 32rpx;
    border-top: 1px solid #f0f0f0;

    .popup-btn {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 16rpx;
      font-size: 32rpx;
      font-weight: 500;
      transition: all 0.3s ease;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 150% */
      border-radius: 100px;
      &:not(:last-child) {
        margin-right: 16rpx;
      }

      &.cancel {
        color: var(--text-icon-text-2, #4e5969);

        border: 1px solid var(--Gray-Gray4, #dcdcdc);
        background: var(--Gray-White, #fff);
      }

      &.confirm {
        background: var(--Brand-Brand5, #7d4dff);
        color: #fff;
      }
    }
  }
}
.grade-selector {
  position: relative;

  /* 确保在所有设备上都有正确的样式 */
  .grade-filter {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0; /* 防止收缩 */
    background-color: #fff;
    padding: 0 10px !important; /* 使用 !important 确保优先级 */
    margin: 0 !important; /* 确保没有外边距影响 */
    border-radius: 6px;
    cursor: pointer;
    box-sizing: border-box !important; /* 确保 padding 计算正确 */
    min-width: 80px; /* 确保最小宽度 */
  }
}

/* 备用样式，确保在任何情况下都有左右边距 */
.grade-filter {
  padding-left: 10px !important;
  padding-right: 10px !important;

  .grade-info {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;

    .grade-name {
      font-size: 28rpx;
      font-weight: 600;
      color: #1e293b;
      line-height: 1.2;
      white-space: nowrap;
      margin-bottom: 4rpx; /* 替代 gap */
    }

    .grade-version {
      font-size: 24rpx;
      color: #64748b;
      line-height: 1.2;
      white-space: nowrap;
    }
  }

  .arrow-icon {
    transition: transform 0.3s ease;

    &.arrow-up {
      transform: rotate(180deg);
    }
  }
}

.grade-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 4px;

  .grade-dropdown-item {
    padding: 12px 16px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f5f5f5;
      text-align: center;
    }

    &.active {
      background-color: #e6f3ff;
      color: #1890ff;
      font-weight: 500;
    }

    &:active {
      background-color: #d9f0ff;
    }
  }
}

/* 调试按钮样式 */
.debug-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 覆盖模式下的内容包装器 */
.overlay-content-wrapper {
  flex: 1; /* 占据剩余空间 */
  overflow: hidden; /* 防止溢出 */
  position: relative; /* 确保定位正确 */
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直排列子元素 */
}
</style>
