<template>
  <view class="resource-details-page">
    <!-- 导航栏 -->
    <HeaderNav title="资源详情" @back="handleBack" :centerTitle="true" />

    <!-- 资源信息区域 -->
    <ResourceHeader :resource="resourceData" :loading="loading" @back="handleBack" />

    <!-- 资源内容展示区 -->
    <view v-if="!loading" class="content-container" :class="{ immersive: isImmersiveMode }">
      <ResourceContent
        ref="resourceContentRef"
        :content="resourceData.file"
        :video-id="resourceData.videoId"
        :play-auth="playAuth"
        :type="resourceData.type as 'office' | 'audio' | 'video' | 'image' | 'document'"
        @copy="handleCopy"
        @add-note="handleAddNote"
      />
    </view>
    <view style="z-index: 10000">
      <AIAssistant
        v-if="!isImmersiveMode"
        :resource="resourceData"
        @file-analysis="handleFileAnalysis"
        @mind-map="handleMindMap"
        @file-qa="handleFileQA"
        @take-note="handleTakeNoteFromAI"
      />
    </view>

    <!-- 沉浸式阅读模式UI -->
    <template v-if="isImmersiveMode">
      <!-- 沉浸式模式顶部标题 -->
      <view class="immersive-header" :style="{ paddingTop: `${12 + safeAreaTop}px` }">
        <text class="immersive-title">{{ resourceData.title || '文档预览' }}</text>
      </view>

      <!-- 沉浸式阅读模式退出按钮 - 使用view确保点击事件正常 -->
      <view class="immersive-exit" @click="exitImmersiveMode" @tap="exitImmersiveMode">
        <view class="exit-text">退出阅读</view>
      </view>
    </template>

    <!-- 底部操作区 -->
    <ResourceActions
      v-if="!isImmersiveMode"
      :is-collected="resourceData.isCollected"
      :resource-type="resourceData.type"
      @collect="handleCollect"
      @immersive="enterImmersiveMode"
      @video-fullscreen="handleVideoFullscreen"
    />

    <!-- 添加笔记弹窗 -->
    <NoteModal v-model:show="showNoteModal" :note="currentNote" @save="handleSaveNote" />

    <!-- AI助手记笔记弹窗 - 从底部弹出 -->
    <LkPopup
      :show="showAINoteModal"
      mode="bottom"
      :round="24"
      :safeAreaInsetBottom="true"
      :zIndex="10100"
      @close="closeAINoteModal"
    >
      <view class="ai-note-popup-content">
        <view class="ai-note-popup-header">
          <text class="ai-note-popup-title">做笔记</text>
          <view class="ai-note-popup-close" @click="closeAINoteModal">
            <LkSvg width="24px" height="24px" src="/static/learning-resource/close.svg" />
          </view>
        </view>

        <view class="ai-note-popup-body">
          <text class="ai-note-popup-subtitle">笔记</text>
          <textarea
            v-model="aiNoteContent"
            class="ai-note-popup-textarea"
            placeholder="记录你的想法和感悟，好记性不如烂笔头"
            maxlength="500"
          ></textarea>
        </view>

        <view class="ai-note-popup-footer">
          <button class="ai-note-save-btn" @click="saveAINote">保存</button>
        </view>
      </view>
    </LkPopup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import HeaderNav from '@/components/LkNavBar/HeaderNav.vue';
import ResourceHeader from './components/ResourceHeader.vue';
import ResourceContent from './components/ResourceContent.vue';
import ResourceActions from './components/ResourceActions.vue';
import AIAssistant from './components/AIAssistant.vue';
import NoteModal from './components/NoteModal.vue';
import LkSvg from '@/components/svg/index.vue';
import {
  getResourceDetail,
  addStudentsNotes,
  getSearchCollectResources,
  getCollectResources,
  getCancelCollectResources,
  getVideoPlayAuth,
} from '@/api/students/resourceCenter';
import { updateStudentsNote, getStudentsNote } from '@/api/students/homework';
import { updateStudentNote } from '@/api/students/studentnote';
import type { AppResourceDetailVO } from '@/types/students/resourceCenter';
import { useChatStore } from '@/store/chatStore';
import { getAppointedAppList } from '@/api/database';
import { getSafeArea } from '@/utils/respDims.js';

// 页面状态
const isImmersiveMode = ref(false);
const resourceContentRef = ref();
const showNoteModal = ref(false);
const currentNote = ref('');
const showAINoteModal = ref(false);
const aiNoteContent = ref('');
const loading = ref(false);
const resourceId = ref<number>(0);
const playAuth = ref('');

// 状态栏高度（用于沉浸式模式顶部适配）
const safeAreaTop = ref(0);

// 存储从URL参数传入的笔记内容
const initialNoteContent = ref('');
// 页面是否处于笔记模式
const isNoteMode = ref(false);
// 存储笔记ID，用于更新笔记
const noteId = ref<number | string>('');

const chatStore = useChatStore();
// 禁用/启用右划返回手势的函数
const disableSwipeBack = () => {
  // // #ifdef APP-PLUS
  // // App端禁用右划返回
  // const currentWebview = plus.webview.currentWebview();
  // currentWebview.setStyle({
  //   popGesture: 'none',
  // });
  // // #endif
  // // #ifdef H5
  // // H5端禁用浏览器后退
  // window.history.pushState({}, '', window.location.href);
  // window.addEventListener('popstate', preventBack);
  // // #endif
};

const enableSwipeBack = () => {
  // // #ifdef APP-PLUS
  // // App端恢复右划返回
  // const currentWebview = plus.webview.currentWebview();
  // currentWebview.setStyle({
  //   popGesture: 'close',
  // });
  // // #endif
  // // #ifdef H5
  // // H5端恢复浏览器后退
  // window.removeEventListener('popstate', preventBack);
  // // #endif
};

const preventBack = (event: PopStateEvent) => {
  // 阻止浏览器后退
  window.history.pushState({}, '', window.location.href);
  event.preventDefault();
  return false;
};

const getResourceNote = () => {
  getStudentsNote({ refId: resourceId.value.toString(), type: 2 }).then(res => {
    console.log('getResourceNote', res);
    if (res) {
      isNoteMode.value = true;
      noteId.value = res.id;
      initialNoteContent.value = res.noteContent;
    }
  });
};

// 初始化安全区域（兼容安卓和iOS）
const initSafeArea = () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    const safeArea = getSafeArea() as {
      top: number;
      left: number;
      right: number;
      bottom: number;
      width: number;
      height: number;
    };

    // 对于安卓设备，使用状态栏高度作为顶部安全区域
    if (systemInfo.platform === 'android') {
      safeAreaTop.value = systemInfo.statusBarHeight || 0;
    } else {
      // iOS设备使用安全区域的top值
      safeAreaTop.value = safeArea.top || systemInfo.statusBarHeight || 0;
    }

    console.log('沉浸式模式安全区域初始化完成:', {
      platform: systemInfo.platform,
      safeAreaTop: safeAreaTop.value,
      statusBarHeight: systemInfo.statusBarHeight,
      safeArea: safeArea,
    });
  } catch (error) {
    console.error('初始化安全区域失败:', error);
    // 设置默认值
    safeAreaTop.value = 20;
  }
};

// 页面生命周期
onMounted(() => {
  // 初始化安全区域
  initSafeArea();
  // 禁用右划返回手势
  disableSwipeBack();
  getResourceNote();
});

onUnmounted(() => {
  // 页面销毁时恢复右划返回手势
  enableSwipeBack();
});

// 资源数据
const resourceData = reactive({
  id: 0,
  title: '',
  description: '',
  areaId: 0,
  areaName: '',
  attribution: 0,
  attributionToId: 0,
  attributionToName: '',
  createTime: '',
  createUserName: '',
  curation: 0,
  downloadCount: 0,
  fileFormatId: 0,
  fileFormatName: '',
  fileName: '',
  fileSize: null as number | null,
  fileUrl: '',
  gradeId: 0,
  gradeName: '',
  resourceTypeId: 0,
  resourceTypeName: '',
  stageId: 0,
  stageName: '',
  status: 0,
  subjectId: 0,
  subjectName: '',
  textbookChapterIds: [] as number[],
  textbookChapterNames: [] as string[],
  textbookVersionId: 0,
  textbookVersionName: '',
  textbookVolumeId: 0,
  textbookVolumeName: '',
  videoId: '',
  updateTime: '',
  viewCount: 0,
  vintages: 0,
  // Legacy fields for backward compatibility
  name: '',
  type: 'office' as 'office' | 'audio' | 'video' | 'image' | 'document',
  source: '',
  tags: [] as string[],
  size: '',
  isCollected: 0,
  isCuration: 0,
  file: {
    id: '',
    createTime: '',
    updateTime: '',
    isDeleted: 0,
    fileName: '',
    fileUrl: '',
    fileKey: '',
    fileSize: 0,
    fileJson: '',
    fileType: '',
  },
  fileTypes: {
    fileType: 1,
    fileName: '',
    format: '',
  },
  content: {
    title: '',
    sections: [] as any[],
  },
});

// 页面加载
onLoad((options: any) => {
  if (options.id) {
    resourceId.value = parseInt(options.id);
    resourceData.id = options.id;
    loadResourceData(resourceId.value);
  }

  // 检查是否需要显示AI笔记弹窗
  if (options.type === 'note' && options.noteContent) {
    // 设置页面为笔记模式
    isNoteMode.value = true;

    if (options.noteId) {
      noteId.value = options.noteId;
    }
    initialNoteContent.value = decodeURIComponent(options.noteContent);

    // 设置延迟，等待页面加载完成后显示笔记
    setTimeout(() => {
      try {
        showAINoteModal.value = true;
        aiNoteContent.value = initialNoteContent.value;
      } catch (error) {
        console.error('显示笔记弹窗出错:', error);
      }
    }, 1000);
  }
});

// 加载资源数据
const loadResourceData = async (id: number) => {
  try {
    loading.value = true;

    const response = await getResourceDetail({ resourceId: id });
    // 更新资源数据
    updateResourceData(response);
    console.log('response', response);
    if (response.videoId) {
      getVideoInfo(response.videoId);
    }
  } catch (error) {
    console.error('加载资源失败:', error);

    // 显示更详细的错误信息
    let errorMessage = '加载资源失败';
    if (error && typeof error === 'object') {
      const err = error as any;
      if (err.msg) {
        errorMessage = err.msg;
      } else if (err.message) {
        errorMessage = err.message;
      } else if (err.code) {
        errorMessage = `错误代码: ${err.code}`;
      }
    }

    uni.showToast({
      title: errorMessage,
      icon: 'error',
      duration: 2000,
      complete: () => {
        // 错误提示显示完成后返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 500);
      },
    });
  } finally {
    loading.value = false;
  }
};

// 更新资源数据
const updateResourceData = (data: AppResourceDetailVO) => {
  resourceData.id = data.id;
  resourceData.title = data.title;
  resourceData.description = data.description;
  resourceData.areaId = data.areaId;
  resourceData.areaName = data.areaName;
  resourceData.attribution = data.attribution;
  resourceData.attributionToId = data.attributionToId;
  resourceData.attributionToName = data.attributionToName;
  resourceData.createTime = data.createTime;
  resourceData.createUserName = data.createUserName;
  resourceData.curation = data.curation;
  resourceData.downloadCount = data.downloadCount;
  resourceData.fileFormatId = data.fileFormatId;
  resourceData.fileFormatName = data.fileFormatName;
  resourceData.fileName = data.fileName;
  resourceData.fileSize = data.fileSize;
  resourceData.fileUrl = data.fileUrl;
  resourceData.gradeId = data.gradeId;
  resourceData.gradeName = data.gradeName;
  resourceData.resourceTypeId = data.resourceTypeId;
  resourceData.resourceTypeName = data.resourceTypeName;
  resourceData.stageId = data.stageId;
  resourceData.stageName = data.stageName;
  resourceData.status = data.status;
  resourceData.subjectId = data.subjectId;
  resourceData.subjectName = data.subjectName;
  resourceData.textbookChapterIds = data.textbookChapterIds;
  resourceData.textbookChapterNames = data.textbookChapterNames;
  resourceData.textbookVersionId = data.textbookVersionId;
  resourceData.textbookVersionName = data.textbookVersionName;
  resourceData.textbookVolumeId = data.textbookVolumeId;
  resourceData.textbookVolumeName = data.textbookVolumeName;
  resourceData.updateTime = data.updateTime;
  resourceData.viewCount = data.viewCount;
  resourceData.vintages = data.vintages;
  resourceData.videoId = data.videoId;
  resourceData.isCuration = data.isCuration;
  // Legacy fields for backward compatibility
  resourceData.name = data.title;
  resourceData.source = data.attributionToName || '未知来源';
  resourceData.size = formatFileSize(data.fileSize || 0);
  resourceData.isCollected = data.isCollected; // 0表示未收藏，1表示已收藏

  // 根据文件格式设置类型
  resourceData.type = getResourceType(data.fileFormatName);

  // 设置标签
  resourceData.tags = data.textbookChapterNames || [getAttributionText(data.attribution)];

  // 更新文件信息
  resourceData.file = {
    id: data.id.toString(),
    createTime: data.createTime,
    updateTime: data.updateTime,
    isDeleted: 0,
    fileName: data.fileName,
    fileUrl: data.fileUrl,
    fileKey: data.fileKey,
    fileSize: data.fileSize || 0,
    fileJson: ' ',
    fileType: data.fileFormatName,
  };

  resourceData.fileTypes = {
    fileType: data.fileFormatId,
    fileName: data.fileName,
    format: data.fileFormatName.toLowerCase(),
  };
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
};

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toISOString().split('T')[0];
};

// 获取资源类型
const getResourceType = (
  fileFormat: string
): 'office' | 'audio' | 'video' | 'image' | 'document' => {
  const format = fileFormat.toLowerCase();
  if (['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx', 'docs'].includes(format)) {
    return 'office';
  } else if (['mp4', 'avi', 'mov', 'wmv'].includes(format)) {
    return 'video';
  } else if (['mp3', 'wav', 'aac'].includes(format)) {
    return 'audio';
  } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(format)) {
    return 'image';
  }
  return 'document';
};

// 获取归属文本
const getAttributionText = (attribution: number): string => {
  switch (attribution) {
    case 1:
      return '个人';
    case 2:
      return '学校';
    case 3:
      return '官方';
    case 4:
      return '第三方';
    default:
      return '未知';
  }
};

// 返回资源列表
const handleBack = () => {
  uni.navigateBack();
};

const getVideoInfo = async (videoId: string) => {
  const response = await getVideoPlayAuth({ videoId });
  console.log('视频信息:', response);
  playAuth.value = response;
};

// 收藏操作状态
const isCollecting = ref(false);

// 收藏/取消收藏
const handleCollect = async () => {
  // 防止重复点击
  if (isCollecting.value) {
    return;
  }

  try {
    isCollecting.value = true;
    const currentResourceId = resourceData.id;

    if (resourceData.isCollected === 0) {
      // 执行收藏操作
      await getCollectResources({ resourceIds: [currentResourceId] });
      resourceData.isCollected = 1;
      uni.showToast({
        title: '收藏成功',
        icon: 'success',
      });
    } else {
      // 执行取消收藏操作
      await getCancelCollectResources({ resourceIds: [currentResourceId] });
      resourceData.isCollected = 0;
      uni.showToast({
        title: '取消收藏',
        icon: 'success',
      });
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    });
  } finally {
    isCollecting.value = false;
  }
};

// 进入沉浸式阅读
const enterImmersiveMode = () => {
  isImmersiveMode.value = true;

  // 通知OfficePreview组件进入沉浸式模式
  if (resourceContentRef.value && resourceContentRef.value.enterImmersiveMode) {
    resourceContentRef.value.enterImmersiveMode();
  }
};

// 退出沉浸式阅读
const exitImmersiveMode = () => {
  isImmersiveMode.value = false;

  // 通知OfficePreview组件退出沉浸式模式
  if (resourceContentRef.value && resourceContentRef.value.exitImmersiveMode) {
    resourceContentRef.value.exitImmersiveMode();
  }
};

// 复制内容
const handleCopy = (content: string) => {
  uni.setClipboardData({
    data: content,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success',
      });
      // 跳转到AI助手页面，回显复制内容
      uni.navigateTo({
        url: `/pages/ai-assistant/index?content=${encodeURIComponent(content)}`,
      });
    },
  });
};

// 添加笔记
const handleAddNote = () => {
  showNoteModal.value = true;
};

// 保存笔记
const handleSaveNote = (note: string) => {
  currentNote.value = note;
  showNoteModal.value = false;
  uni.showToast({
    title: '笔记保存成功',
    icon: 'success',
  });
};

// 处理AI助手记笔记点击事件
const handleTakeNoteFromAI = () => {
  console.log('AI助手记笔记按钮被点击');
  showAINoteModal.value = true;

  // 如果是笔记模式且有初始笔记内容，则显示初始笔记内容
  if (isNoteMode.value && initialNoteContent.value) {
    aiNoteContent.value = initialNoteContent.value;
  } else {
    aiNoteContent.value = '';
  }
};

// 关闭AI记笔记弹窗
const closeAINoteModal = () => {
  showAINoteModal.value = false;
  // 仅在非笔记模式下清空内容
  if (!isNoteMode.value) {
    aiNoteContent.value = '';
  }
};

// 保存AI记笔记
const saveAINote = async () => {
  if (!aiNoteContent.value.trim()) {
    uni.showToast({
      title: '请输入笔记内容',
      icon: 'none',
    });
    return;
  }

  try {
    if (isNoteMode.value && noteId.value) {
      await updateStudentNote({
        id: typeof noteId.value === 'string' ? parseInt(noteId.value) : noteId.value,
        noteContent: aiNoteContent.value,
      });

      initialNoteContent.value = aiNoteContent.value;

      uni.showToast({
        title: '笔记修改成功',
        icon: 'success',
      });
    } else {
      await addStudentsNotes({
        refId: resourceData.id,
        noteContent: aiNoteContent.value,
        type: 2,
      });

      uni.showToast({
        title: '笔记保存成功',
        icon: 'success',
      });
    }

    closeAINoteModal();
  } catch (error) {
    console.error('笔记操作失败:', error);
    uni.showToast({
      title: isNoteMode.value ? '修改失败，请重试' : '保存失败，请重试',
      icon: 'none',
    });
  }
};

// AI助手功能
const handleFileAnalysis = () => {
  getAppointedAppList({}).then(res => {
    const appId = res.find((item: any) => item.appointedType === 2).id;
    const fileObjStr = JSON.stringify(resourceData.file);
    // 使用URL查询字符串传递参数
    const params = {
      resource: resourceData,
      fileObj: resourceData.file,
      chatType: 22,
    };
    chatStore.setToolGoChatParams(params);
    uni.navigateTo({
      url: `/pages-subpackages/students/chat/index?chatType=22&fileObj=${encodeURIComponent(fileObjStr)}`,
    });
  });
};

const handleMindMap = () => {
  getAppointedAppList({}).then(res => {
    const appId = res.find((item: any) => item.appointedType === 1).id;
    const fileObjStr = JSON.stringify(resourceData.file);
    console.log('fileObjStr', fileObjStr);
    // 使用URL查询字符串传递参数
    const params = {
      resource: resourceData,
      fileObj: resourceData.file,
      chatType: 23,
    };
    chatStore.setToolGoChatParams(params);
    uni.navigateTo({
      url: `/pages-subpackages/students/chat/index?chatType=23&fileObj=${encodeURIComponent(fileObjStr)}`,
    });
  });
};

const handleFileQA = () => {
  getAppointedAppList({}).then(res => {
    const appId = res.find((item: any) => item.appointedType === 2).id;
    const fileObjStr = JSON.stringify(resourceData.file);
    // 使用URL查询字符串传递参数
    const params = {
      resource: resourceData,
      fileObj: resourceData.file,
      chatType: 24,
    };
    chatStore.setToolGoChatParams(params);
    uni.navigateTo({
      url: `/pages-subpackages/students/chat/index?chatType=24&fileObj=${encodeURIComponent(fileObjStr)}`,
    });
  });
};

// 处理视频全屏播放
const handleVideoFullscreen = () => {
  // 获取视频播放器引用，通过ResourceContent组件
  if (resourceContentRef.value && resourceContentRef.value.videoPlayerRef) {
    const videoPlayer = resourceContentRef.value.videoPlayerRef;
    if (videoPlayer && videoPlayer.openFullscreenPage) {
      videoPlayer.openFullscreenPage();
    } else {
      console.warn('视频播放器不支持全屏播放方法');
    }
  } else {
    console.warn('未找到视频播放器引用');
  }
};
</script>

<style lang="scss" scoped>
.resource-details-page {
  height: 100vh; /* 强制高度为视口高度 */
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  padding-bottom: 80px; /* 为底部固定的ResourceActions预留空间 */
  box-sizing: border-box; /* 确保padding不会影响总高度 */
}

.content-container {
  flex: 1;
  transition: all 0.3s ease;
  min-height: 0; /* 允许flex子项收缩 */
  overflow: hidden; /* 防止内容溢出 */
  position: relative; /* 为drag-button-follow提供定位上下文 */
  z-index: 1;
  &.immersive {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: #fff;
    padding-bottom: 0; /* 沉浸式模式下不需要底部间距 */
  }
}

/* 沉浸式模式顶部标题 */
.immersive-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  box-shadow: 0 4px 6.5px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  /* 使用动态计算的 paddingTop，兼容安卓和iOS */
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 12px;
  gap: 6px;
  flex-shrink: 0;
}

.immersive-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  max-width: 80%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.immersive-exit {
  position: fixed !important;
  bottom: 0px !important;
  left: 0 !important; /* 改为全宽布局 */
  right: 0 !important;
  z-index: 2147483647 !important;
  background-color: #fff !important;
  border-top: 1px solid var(--Gray-Gray3, #e7e7e7);
  /* 适配手机底部安全区域 */
  padding: 14px 26px calc(14px + env(safe-area-inset-bottom)) 26px !important;
  pointer-events: auto !important;
  /* 强制显示 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  /* 移动端优化 */
  -webkit-tap-highlight-color: transparent !important;

  .exit-text {
    border-radius: 100px;
    border: 1px solid var(--Gray-Gray4, #dcdcdc);
    background: var(--Gray-White, #fff);
    display: flex !important;
    padding: 12px 20px;
    justify-content: center !important;
    align-items: center !important;
    width: 100%;
    color: var(--text-icon-text-2, #4e5969);
    text-align: center !important;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px !important; /* 改为24px确保文字垂直居中 */
    /* 移动端文字垂直居中优化 */
    box-sizing: border-box !important;
    /* 移动端触摸优化 */
    -webkit-tap-highlight-color: transparent !important;
    user-select: none !important;
    /* 确保点击区域足够大 */
    min-height: 48px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:active {
      background-color: #f5f5f5 !important;
    }
  }
}

// AI记笔记弹窗样式
.ai-note-popup-content {
  background: #fff;
  padding: 0;
  min-height: 400px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.ai-note-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  flex-shrink: 0;
}

.ai-note-popup-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.ai-note-popup-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }
}

.ai-note-popup-body {
  flex: 1;
  padding: 16px 20px;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.ai-note-popup-subtitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.ai-note-popup-textarea {
  flex: 1;
  min-height: 200px;
  width: 100%;
  border-radius: 8px;
  background: #f9f9f9;
  border: 1px solid #e5e5e5;
  padding: 12px;
  font-size: 16px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  line-height: 22px;
  color: #333;
  resize: none;
  box-sizing: border-box;

  &::placeholder {
    color: #86909c;
  }

  &:focus {
    border-color: #7d4dff;
    outline: none;
  }
}

.ai-note-popup-footer {
  padding: 16px 20px 20px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  background: #fff;
  flex-shrink: 0;
}

.ai-note-save-btn {
  border-radius: 100px;
  background: #7d4dff;
  color: #fff;
  border: none;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  &:hover {
    background: #6a3dff;
  }

  &:active {
    background: #5a2dff;
  }
}
</style>
