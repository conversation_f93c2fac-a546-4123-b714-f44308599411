<template>
  <view class="fullscreen-video-page">
    <!-- 顶部半透明header -->
    <view class="video-header">
      <view class="header-left">
        <view class="back-button" @click="exitFullscreen">
          <text class="back-icon">←</text>
        </view>
        <text class="video-title">{{ videoTitle || '视频播放' }}</text>
      </view>
    </view>

    <!-- 视频播放器 -->
    <LkVideoPlayer
      ref="videoPlayerRef"
      :video-id="videoId"
      :play-auth="playAuth"
      :width="videoPlayerWidth"
      :height="videoPlayerHeight"
      :autoplay="true"
      :current-time="currentTime"
      @ai-chat="handleAIChat"
      @take-note="handleTakeNote"
      @timeupdate="handleTimeUpdate"
    />

    <!-- 悬浮按钮 - 固定在右边 -->
    <view class="floating-buttons" v-show="!showNoteModal && !showAIChatModal">
      <!-- 智能答疑按钮 -->
      <view class="floating-button ai-button" @click="handleAIChat">
        <image src="/static/learning-resource/ai-logo.svg" class="button-icon-svg" />
        <view class="button-text">智能答疑</view>
      </view>

      <!-- 做笔记按钮 -->
      <view class="floating-button ai-button" @click="handleTakeNote">
        <image src="/static/learning-resource/biji.svg" class="button-icon-svg" />
        <view class="button-text">做笔记</view>
      </view>
      <!-- 退出全屏按钮 -->
      <!-- 退出全屏按钮 -->
      <!-- <view class="floating-button exit-button" @click="exitFullscreen">
        <view class="button-icon">✕</view>
        <view class="button-text">退出</view>
      </view> -->
    </view>

    <!-- 做笔记弹窗 - 右侧滑入效果 -->
    <view v-if="showNoteModal" class="note-popup-overlay" @click="closeNoteModal">
      <view class="note-popup-container note-popup-slide-in" @click.stop>
        <view class="note-popup-content">
          <view class="note-popup-header">
            <text class="note-popup-title"></text>
            <text class="note-popup-title">做笔记</text>
            <view class="note-popup-close" @click="closeNoteModal">
              <image src="/static/learning-resource/close.svg" class="button-icon-svg" />
            </view>
          </view>

          <view class="note-popup-body">
            <text class="note-popup-title note-popup-title-text">笔记</text>

            <textarea
              v-model="noteContent"
              class="note-popup-textarea"
              placeholder="记录你的想法和感悟，好记性不如烂笔头"
              maxlength="500"
            ></textarea>
          </view>

          <view class="note-popup-footer">
            <button class="note-save-btn" @click="saveNote">保存</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 智能答疑弹窗 - 右侧滑入效果 -->
    <view v-if="showAIChatModal">
      <!-- 透明遮罩层 -->
      <view class="ai-chat-mask" @click="closeAIChatModal"></view>
      <!-- 弹窗容器 -->
      <view class="note-popup-overlay">
        <view class="note-popup-container note-popup-slide-in" @click.stop>
          <view class="note-popup-content">
            <view class="note-popup-body chat-body">
              <!-- 内嵌AIChat组件 -->
              <AIChat />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import LkVideoPlayer from '@/components/LkVideoPlayer/index.vue';
import AIChat from '@/components/AIChat/index.vue';
import { addStudentsNotes } from '@/api/students/resourceCenter';
import { updateStudentsNote, getStudentsNote } from '@/api/students/homework';
import { updateStudentNote } from '@/api/students/studentnote';
// 响应式数据
const videoPlayerRef = ref();
const videoId = ref('');
const playAuth = ref('');
const videoTitle = ref(''); // 视频标题
const currentTime = ref(0);
const showNoteModal = ref(false);
const showAIChatModal = ref(false); // 智能答疑弹窗状态
const noteContent = ref('');
const resourceId = ref(''); // 保存资源ID
const isNoteMode = ref(false);
const noteId = ref<number | string>('');

// 弹窗宽度常量
const POPUP_WIDTH = 400; // px

// 计算视频播放器的宽度和高度
const videoPlayerWidth = computed(() => {
  // 如果有弹窗打开，则减去弹窗宽度
  if (showNoteModal.value || showAIChatModal.value) {
    return `calc(100vw - ${POPUP_WIDTH}px)`;
  }
  return '100vw';
});

const videoPlayerHeight = computed(() => {
  return '100vh';
});

const getResourceNote = () => {
  getStudentsNote({ refId: resourceId.value.toString(), type: 2 }).then(res => {
    console.log('getResourceNote', res);
    if (res) {
      isNoteMode.value = true;
      noteId.value = res.id;
      noteContent.value = res.noteContent;
    }
  });
};

// 页面生命周期 - 使用uni-app的onLoad
onLoad((options: any) => {
  console.log('全屏页面接收参数:', options);

  if (options.videoId) {
    videoId.value = decodeURIComponent(options.videoId);
    console.log('视频ID:', videoId.value);
  }

  if (options.playAuth) {
    playAuth.value = decodeURIComponent(options.playAuth);
    console.log('播放凭证:', playAuth.value);
  }

  if (options.title) {
    videoTitle.value = decodeURIComponent(options.title);
    console.log('视频标题:', videoTitle.value);
  }

  if (options.currentTime) {
    currentTime.value = parseFloat(options.currentTime) || 0;
    console.log('播放时间:', currentTime.value);
  }

  // 保存资源ID，用于退出全屏时跳转
  if (options.id) {
    resourceId.value = options.id;
    console.log('资源ID:', resourceId.value);
  } else if (options.resourceId) {
    resourceId.value = options.resourceId;
    console.log('资源ID:', resourceId.value);
  } else {
    // 如果没有传递ID，尝试从其他地方获取
    console.warn('未接收到资源ID参数');
  }

  // 页面加载完成后获取笔记内容
  if (resourceId.value) {
    getResourceNote();
  }
});

// 生命周期
onMounted(() => {
  // 强制横屏
  // #ifdef APP-PLUS
  if (typeof plus !== 'undefined' && plus.screen && plus.screen.lockOrientation) {
    try {
      plus.screen.lockOrientation('landscape-primary');
      console.log('全屏页面：已设置强制横屏');
    } catch (error) {
      console.warn('全屏页面：设置横屏失败:', error);
    }
  }
  // #endif

  // 隐藏状态栏
  // #ifdef APP-PLUS
  try {
    // 使用plus API隐藏状态栏
    if (typeof plus !== 'undefined' && plus.navigator) {
      plus.navigator.setStatusBarStyle('dark');
      // 注意：hideStatusBar可能不存在，我们跳过这个调用
      console.log('状态栏样式已设置');
    }
  } catch (error) {
    console.warn('设置状态栏失败:', error);
  }
  // #endif
});

onUnmounted(() => {
  // 恢复竖屏
  // #ifdef APP-PLUS
  if (typeof plus !== 'undefined' && plus.screen && plus.screen.lockOrientation) {
    try {
      plus.screen.lockOrientation('portrait-primary');
      console.log('全屏页面：已恢复竖屏');
    } catch (error) {
      console.warn('全屏页面：恢复竖屏失败:', error);
    }
  }
  // #endif

  // 恢复状态栏
  // #ifdef APP-PLUS
  try {
    // 使用plus API恢复状态栏
    if (typeof plus !== 'undefined' && plus.navigator) {
      plus.navigator.setStatusBarStyle('light');
      console.log('状态栏样式已恢复');
    }
  } catch (error) {
    console.warn('恢复状态栏失败:', error);
  }
  // #endif
});

// 页面生命周期已通过onLoad钩子正确注册

// 事件处理
const handleAIChat = () => {
  console.log('全屏页面：智能答疑按钮被点击');
  showAIChatModal.value = true;
};

const handleTakeNote = () => {
  console.log('全屏页面：做笔记按钮被点击');
  console.log('当前 showNoteModal 值:', showNoteModal.value);
  showNoteModal.value = true;
  console.log('设置后 showNoteModal 值:', showNoteModal.value);
  // 不再清空笔记内容，保持从getResourceNote获取的内容
  // noteContent.value = '';
};

const handleTimeUpdate = (time: number) => {
  currentTime.value = time;
};

const exitFullscreen = () => {
  console.log('退出全屏，资源ID:', resourceId.value);

  // 跳转到资源详情页面，带上资源ID
  if (resourceId.value) {
    const url = `/pages-subpackages/students/learning-resource/resource-details?id=${resourceId.value}`;
    console.log('跳转到资源详情页面:', url);

    uni.redirectTo({
      url: url,
      success: () => {
        console.log('成功跳转到资源详情页面');
      },
      fail: error => {
        console.error('跳转到资源详情页面失败:', error);
        // 如果跳转失败，回退到返回上一页
        uni.navigateBack({
          delta: 1,
        });
      },
    });
  } else {
    console.warn('没有资源ID，返回上一页');
    // 如果没有资源ID，返回上一页
    uni.navigateBack({
      delta: 1,
    });
  }
};

const closeNoteModal = () => {
  showNoteModal.value = false;
  // 不清空笔记内容，保持用户输入的内容
  // noteContent.value = '';
  console.log('关闭笔记弹窗');
};

const closeAIChatModal = () => {
  showAIChatModal.value = false;
  console.log('关闭智能答疑弹窗');
};

const saveNote = () => {
  if (!noteContent.value.trim()) {
    uni.showToast({
      title: '请输入笔记内容',
      icon: 'none',
    });
    return;
  }

  // 根据是否已有笔记来决定新增还是更新
  if (isNoteMode.value && noteId.value) {
    // 更新已有笔记
    updateStudentsNote({
      id: noteId.value,
      noteContent: noteContent.value,
    })
      .then(res => {
        uni.showToast({
          title: '笔记更新成功',
          icon: 'success',
        });
        closeNoteModal();
      })
      .catch(error => {
        console.error('更新笔记失败:', error);
        uni.showToast({
          title: '更新笔记失败',
          icon: 'none',
        });
      });
  } else {
    // 新增笔记
    addStudentsNotes({
      noteContent: noteContent.value,
      refId: resourceId.value,
      type: 2,
    })
      .then(res => {
        uni.showToast({
          title: '笔记保存成功',
          icon: 'success',
        });
        // 保存成功后更新状态
        if (res && res.id) {
          isNoteMode.value = true;
          noteId.value = res.id;
        }
        closeNoteModal();
      })
      .catch(error => {
        console.error('保存笔记失败:', error);
        uni.showToast({
          title: '保存笔记失败',
          icon: 'none',
        });
      });
  }
};

const formatCurrentTime = () => {
  const time = currentTime.value;
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};
</script>

<style scoped>
.fullscreen-video-page {
  width: 100vw;
  height: 100vh;
  background: #000;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: row;
}

/* 顶部半透明header样式 */
.video-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 20px;
  transition: opacity 0.3s ease;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 16px;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.back-button:active {
  transform: scale(0.95);
}

.back-icon {
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.video-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.2;
  max-width: calc(100vw - 200px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 视频播放器容器过渡动画和定位 */
.lk-video-player {
  transition: width 0.3s ease-in-out;
  position: relative;
  flex-shrink: 0;
}

/* 确保视频播放器容器能够正确缩放 */
.lk-video-player .player-container {
  transition: width 0.3s ease-in-out;
}

/* 悬浮按钮容器 */
.floating-buttons {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

.floating-buttons .floating-button {
  margin-bottom: 16px;
}

.floating-buttons .floating-button:last-child {
  margin-bottom: 0;
}

.floating-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 59px;
  background: rgba(0, 0, 0, 0.28);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

/* AI按钮特殊样式 - 现在只需要背景色覆盖 */
.ai-button {
  background: rgba(0, 0, 0, 0.28) !important;
}

.floating-button:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.05);
}

.floating-button:active {
  transform: scale(0.95);
}

.button-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.button-icon-svg {
  width: 27px;
  height: 27px;
  margin-bottom: 2px;
}

.button-text {
  font-size: 10px;
  color: white;
  color: #fff;
  line-height: 1.1;
}

.ai-button:hover {
  background: rgba(74, 144, 226, 0.8) !important;
  transform: scale(1.05) !important;
}

.ai-button:active {
  background: rgba(74, 144, 226, 0.9) !important;
  transform: scale(0.95) !important;
}

.note-button:hover {
  background: rgba(139, 69, 19, 0.8) !important;
  transform: scale(1.05) !important;
}

.note-button:active {
  background: rgba(139, 69, 19, 0.9) !important;
  transform: scale(0.95) !important;
}

.exit-button:hover {
  background: rgba(220, 53, 69, 0.8) !important;
  transform: scale(1.05) !important;
}

.exit-button:active {
  background: rgba(220, 53, 69, 0.9) !important;
  transform: scale(0.95) !important;
}

/* 笔记弹窗样式 */
.note-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.note-modal {
  width: 90%;
  max-width: 500px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.note-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.note-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.note-modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #e9ecef;
  cursor: pointer;
  font-size: 16px;
  color: #666;
}

.note-modal-content {
  padding: 20px;
}

.note-textarea {
  width: 100%;
  height: 120px;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  box-sizing: border-box;
}

.note-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 12px;
  color: #666;
}

.note-modal-footer {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  text-align: right;
}

.note-save-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 24px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

/* 智能答疑弹窗透明遮罩 */
.ai-chat-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 999; /* 比弹窗层级低一点 */
}

/* 右侧滑入做笔记弹窗样式 */
.note-popup-overlay {
  position: fixed;
  top: 0;
  right: 0; /* 只覆盖右侧区域 */
  bottom: 0;
  width: 400px; /* 只覆盖弹窗宽度的区域 */
  background: transparent;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
}

.note-popup-container {
  width: 400px;
  height: 100vh;
  border-radius: 12px 0 0 12px;
  background: #fff;
  display: flex;
  flex-direction: column;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

/* 弹窗滑入动画 */
.note-popup-slide-in {
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.note-popup-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.note-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  flex-shrink: 0;
  border-radius: 12px 0 0 12px;
}

.note-popup-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.note-popup-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
}

.note-popup-body {
  flex: 1;
  padding: 0px 16px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.note-popup-title-text {
  margin-bottom: 12px;
}

.note-popup-textarea {
  flex: 1;
  width: 100%;
  border-radius: 8px;
  background: #f9f9f9;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  box-sizing: border-box;
  margin-bottom: 60px;
  color: var(--text-3, #86909c);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 137.5% */
}

.note-popup-footer {
  padding: 14px 16px;
  border-top: 1px solid #e7e7e7;
  display: flex;
  justify-content: flex-end;
  background: #fff;
  flex-shrink: 0;
}

.note-popup-footer button {
  margin-left: 12px;
}

.note-popup-footer button:first-child {
  margin-left: 0;
}

.note-save-btn {
  border-radius: 100px;
  background: var(--Brand-Brand5, #7d4dff);
  color: #666;
  color: var(--text-icon-text-5, #fff);
  text-align: center;
  /* Mark/Large */
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  width: 100%;
}

.note-save-btn:hover {
  background: #0056b3;
}
</style>
