<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import LkSvg from '@/components/svg/index.vue';
import { useSystemStore } from '@/store/systemStore';
import FileContent from './component/filecontent.vue';
import VoicePopup from './component/voicePopup.vue';
import LkGradeSelector from '@/components/LkGradeSelector/index.vue';
import LkPopup from '@/components/LkPopup/index.vue';
import {
  getSubjects,
  getResourceTypes,
  getResourceFormats,
  getSearchResources,
  getCollectResources,
  getGradesList,
} from '@/api/students/resourceCenter';
import type {
  SubjectsItem,
  ResourceTypesItem,
  ResourceFormatsItem,
  SearchResourcesParams,
  ResourceSearchResponse,
  GradesItem,
} from '@/types/students/resourceCenter';

// 类型定义
interface ResourceItem {
  id: number;
  title: string;
  description: string;
  areaId: number;
  areaName: string;
  attribution: number;
  attributionToId: number;
  attributionToName: string;
  createTime: string;
  fileFormatId: number;
  fileFormatName: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileUrl: string;
  gradeId: number;
  gradeName: string;
  resourceTypeId: number;
  resourceTypeName: string;
  stageId: number;
  stageName: string;
  subjectId: number;
  subjectName: string;
  textbookVersionId: number;
  textbookVersionName: string;
  textbookVolumeId: number;
  textbookVolumeName: string;
  updateTime: string;
  isCuration: number;
}

interface GradeInfo {
  name: string;
  version: string;
  semester: number;
  volume: string;
  textbookVersionId: number;
  textbookVolumeId: number;
  gradeId: number;
  textbookVolumeCategory: number;
}
const tabList = ref([
  {
    name: '年级',
    value: 'grade',
  },
  {
    name: '学科',
    value: 'subject',
  },
  {
    name: '来源',
    value: 'source',
  },
  {
    name: '资源类型',
    value: 'type',
  },
  {
    name: '格式',
    value: 'format',
  },
]);
// 响应式数据
const keyword = ref('');
const isLoading = ref(false);
const aiSelectedResources = ref<ResourceItem[]>([]);
const regularSearchResults = ref<ResourceItem[]>([]);
const showAiSelected = ref(false);

// 语音输入弹窗相关
const showVoicePopup = ref(false);

// 筛选相关 - 默认全部选择"全部"
const currentSubject = ref('全部');
const currentSubjectId = ref(0);
const currentSource = ref('全部');
const currentType = ref('全部');
const currentFormat = ref('全部');
const selectedGrade = ref<GradeInfo>();
const searchText = ref('');
const currentGrade = ref<any>();
const gradeList = ref<GradesItem[]>([]);
const tempSelectedGrades = ref<(number | string)[]>([]);

// 筛选选项列表
const subjectList = ref<SubjectsItem[]>([]);
const sourceList = ref([
  { name: '官方资源', code: '3' },
  { name: '校本资源', code: '2' },
  { name: '个人资源', code: '1' },
]);
const typeList = ref<ResourceTypesItem[]>([]);
const formatList = ref<ResourceFormatsItem[]>([]);

// 弹窗状态管理
const showSubjectPopup = ref(false);
const showSourcePopup = ref(false);
const showTypePopup = ref(false);
const showFormatPopup = ref(false);

// 弹窗引用和状态
const showGradePopup = ref(false);
const showGradeSelector = ref(false);

onMounted(() => {
  initializeData();
  performSearch();
  getGradeList();
});

watch(currentGrade, () => {
  fetchSubjectList();
});

const getGradeList = async () => {
  const res = await getGradesList();
  gradeList.value = res.grades;
  console.log('gradeList', res.grades);
};

// 初始化数据
const initializeData = async () => {
  // 获取学生默认年级和教材版本信息
  const studentInfo = uni.getStorageSync('defultgrade');
  console.log('studentInfo', studentInfo);
  if (studentInfo) {
    // 更新当前年级信息
    selectedGrade.value = {
      name: studentInfo.gradeName,
      version: studentInfo.textbookVersionName,
      semester: 1, // 默认上册
      volume: '1',
      textbookVersionId: studentInfo.textbookVersionId || 0,
      textbookVolumeId: 1, // 默认上册对应的册别ID
      gradeId: studentInfo.gradeId,
      textbookVolumeCategory: 1, // 默认为 1上册，2下册
    };

    currentGrade.value = {
      name: studentInfo.gradeName,
      gradeId: studentInfo.gradeId,
    };
  }

  fetchSubjectList();
  fetchTypeList();
  fetchFormatList();
};

// 获取学科列表
const fetchSubjectList = async () => {
  try {
    const res = await getSubjects({ gradeId: currentGrade.value?.gradeId });
    subjectList.value = res.subjects;

    // 如果当前没有选中学科或选中的是"全部"，自动选中第一个学科
    if (
      (!currentSubject.value || currentSubject.value === '全部') &&
      subjectList.value.length > 0
    ) {
      currentSubject.value = subjectList.value[0].name;
      currentSubjectId.value = subjectList.value[0].id;
    }
  } catch (error) {
    console.error('获取学科列表失败:', error);
  }
};

// 获取资源类型列表
const fetchTypeList = async () => {
  try {
    const res = await getResourceTypes();
    typeList.value = res.resourceTypes;
  } catch (error) {
    console.error('获取资源类型失败:', error);
  }
};

// 获取文件格式列表
const fetchFormatList = async () => {
  try {
    const res = await getResourceFormats();
    formatList.value = res.resourceFormats;
  } catch (error) {
    console.error('获取文件格式失败:', error);
  }
};

// 将来源名称转换为归属值
const getAttributionFromSources = (sources: string[]): number => {
  // 如果选择了"全部"或者多个来源，返回0表示不限制
  if (sources.includes('全部') || sources.length > 1) {
    return 0;
  }

  // 根据来源名称转换为归属值
  const sourceName = sources[0];
  switch (sourceName) {
    case '官方资源':
      return 3;
    case '校本资源':
      return 2;
    default:
      return 0;
  }
};

// 获取学科ID列表
const getSubjectIds = (subjects: string[]): number[] => {
  if (subjects.includes('全部') || subjects.length === 0) {
    return [];
  }

  return subjectList.value
    .filter(subject => subjects.includes(subject.name))
    .map(subject => subject.id);
};

// 获取资源类型ID列表
const getTypeIds = (types: string[]): number[] => {
  if (types.includes('全部') || types.length === 0) {
    return [];
  }

  return typeList.value.filter(type => types.includes(type.name)).map(type => type.id);
};

// 获取格式ID列表
const getFormatIds = (formats: string[]): number[] => {
  console.log('getFormatIds', formats);
  if (formats.includes('全部') || formats.length === 0) {
    return [];
  }

  // 查找所有匹配的格式ID，包括子格式
  const ids: number[] = [];

  formatList.value.forEach(format => {
    // 检查子格式
    format.children.forEach(childFormat => {
      if (formats.includes(childFormat.name)) {
        ids.push(childFormat.id);
      }
    });
  });

  return ids;
};

// 将 ResourceDetailItem 转换为 ResourceItem
const convertToResourceItem = (item: any): ResourceItem => {
  return {
    id: item.id,
    title: item.title,
    description: item.description || '',
    areaId: item.areaId || 0,
    areaName: item.areaName || '',
    attribution: item.attribution || 0,
    attributionToId: item.attributionToId || 0,
    attributionToName: item.attributionToName || '',
    createTime: item.createTime || '',
    fileFormatId: item.fileFormatId || 0,
    fileFormatName: item.fileFormatName || '',
    fileKey: item.fileKey || '',
    fileName: item.fileName || '',
    fileSize: item.fileSize || 0,
    fileUrl: item.fileUrl || '',
    gradeId: item.gradeId || 0,
    gradeName: item.gradeName || '',
    resourceTypeId: item.resourceTypeId || 0,
    resourceTypeName: item.resourceTypeName || '',
    stageId: item.stageId || 0,
    stageName: item.stageName || '',
    subjectId: item.subjectId || 0,
    subjectName: item.subjectName || '',
    textbookVersionId: item.textbookVersionId || 0,
    textbookVersionName: item.textbookVersionName || '',
    textbookVolumeId: item.textbookVolumeId || 0,
    textbookVolumeName: item.textbookVolumeName || '',
    updateTime: item.updateTime || '',
    isCuration: item.isCuration || 0,
  };
};

// 构建搜索参数
const buildSearchParams = (
  pageNum: number = 1,
  pageSize: number = 10,
  storedParams?: any
): SearchResourcesParams => {
  // 获取当前所有选中的筛选项
  const selectedSources =
    currentSource.value === '全部' ? ['全部'] : currentSource.value.split(',').filter(Boolean);
  const selectedTypes =
    currentType.value === '全部' ? ['全部'] : currentType.value.split(',').filter(Boolean);
  const selectedFormats =
    currentFormat.value === '全部' ? ['全部'] : currentFormat.value.split(',').filter(Boolean);

  // 获取对应的ID列表
  const typeIds = getTypeIds(selectedTypes);
  const formatIds = getFormatIds(selectedFormats);

  // 直接使用 currentSubjectId，如果为0则表示不限制学科
  const subjectIds = currentSubjectId.value > 0 ? [currentSubjectId.value] : [];

  // 使用存储的搜索信息或当前输入的关键词
  const searchInfo = keyword.value;
  const aiResourceIds = storedParams?.aiResourceIds || undefined;
  return {
    searchInfo,
    aiResourceIds,
    attribution: getAttributionFromSources(selectedSources),
    fileFormatIds: formatIds.length > 0 ? formatIds : [0], //
    gradeId: currentGrade.value?.gradeId || 0, // 年级ID
    pageNum,
    pageSize,
    resourceTypeIds: typeIds.length > 0 ? typeIds : [0], //
    subjectIds: subjectIds.length > 0 ? subjectIds : [0], //学科id
    textbookVersionId: selectedGrade.value?.textbookVersionId || 0, // 教材版本ID
    textbookVolumeId: 0, // 使用存储的教材册别ID
    textbookVolumeCategory: selectedGrade.value?.textbookVolumeCategory || 1, // 教材册别分类：0不区分，1上册，2下册
  };
};

// 执行搜索
const performSearch = async () => {
  try {
    isLoading.value = true;
    // 获取存储的搜索参数
    const params = uni.getStorageSync('searchParams');
    console.log('searchParamsQ', params);

    // 如果有存储的搜索信息，设置到输入框
    if (params?.searchInfo && !keyword.value) {
      keyword.value = params.searchInfo;
      searchText.value = params.searchInfo;
    }

    // 如果没有搜索关键词且没有存储的搜索信息，则不执行搜索
    if (!keyword.value.trim() && !params?.searchInfo) {
      isLoading.value = false;
      return;
    }

    // 构建搜索参数，传入存储的参数
    const searchParams = buildSearchParams(1, 10, params);

    console.log('433searchParams', searchParams);

    const response: ResourceSearchResponse = await getSearchResources(searchParams);

    // 处理搜索结果
    // 转换AI推荐结果
    if (response.aiSearchResults && response.aiSearchResults.length > 0) {
      aiSelectedResources.value = response.aiSearchResults.map(convertToResourceItem);
      showAiSelected.value = true;
    } else {
      aiSelectedResources.value = [];
      showAiSelected.value = false;
    }

    // 转换常规搜索结果
    if (response.resources && response.resources.length > 0) {
      regularSearchResults.value = response.resources.map(convertToResourceItem);
    } else {
      regularSearchResults.value = [];
    }
  } catch (error) {
    console.error('搜索失败:', error);
    uni.showToast({
      title: '搜索失败',
      icon: 'none',
    });

    // 搜索失败时清空结果
    aiSelectedResources.value = [];
    regularSearchResults.value = [];
    showAiSelected.value = false;
  } finally {
    isLoading.value = false;
  }
};

// 事件处理方法
const handleBack = () => {
  uni.navigateBack();
};

const handleSearch = () => {
  if (!keyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索内容',
      icon: 'none',
    });
    return;
  }
  searchText.value = keyword.value;
  performSearch();
};

const handleVoiceSearch = () => {
  console.log('handleVoiceSearch');
  // 打开语音输入弹窗
  showVoicePopup.value = true;
};

// 语音弹窗事件处理
const handleVoiceClose = () => {
  showVoicePopup.value = false;
};

const handleVoiceCancel = () => {
  showVoicePopup.value = false;
};

const handleVoiceConfirm = (text: string) => {
  showVoicePopup.value = false;
  if (text.trim()) {
    keyword.value = text;
    handleSearch();
  }
};

const handleVoiceResult = (text: string) => {
  keyword.value = text;
  if (text.trim()) {
    performSearch();
  }
};

const handleVoiceError = (error: string) => {
  console.error('语音识别错误:', error);
};

const handleVoicePermissionDenied = () => {
  uni.showToast({
    title: '语音权限被拒绝',
    icon: 'none',
  });
};

// 筛选相关方法
const handleOpenGradeFilter = () => {
  showGradeSelector.value = true;
};

const handleSelectGrade = (grade: string, id?: number) => {
  if (grade === '全部') {
    tempSelectedGrades.value = ['全部'];
  } else if (id !== undefined) {
    tempSelectedGrades.value = [id];
  }
};

const handleResetGrade = () => {
  tempSelectedGrades.value = ['全部'];
};

const handleConfirmGrade = () => {
  console.log('tempSelectedGrades.value', tempSelectedGrades.value);
  if (tempSelectedGrades.value.length === 1 && tempSelectedGrades.value[0] === '全部') {
    currentGrade.value = '全部';
  } else {
    // 将选中的年级ID转换为对应的年级信息
    const selectedGradeIds = tempSelectedGrades.value.filter(item => item !== '全部') as number[];
    if (selectedGradeIds.length > 0) {
      // 找到对应的年级信息
      const selectedGradeInfo = gradeList.value?.find(grade => grade.id === selectedGradeIds[0]);
      if (selectedGradeInfo) {
        currentGrade.value = {
          name: selectedGradeInfo.name,
          gradeId: selectedGradeInfo.id,
        };
      }
    }
  }
  showGradePopup.value = false;
  performSearch();
};

const handleGradeConfirm = (data: {
  gradeName: string;
  versionName: string;
  semesterId: number;
  textbookVersionId: number;
  textbookVolumeId: number;
  gradeId: number;
  textbookVolumeCategory: number;
}) => {
  // 更新当前年级信息
  selectedGrade.value = {
    name: data.gradeName,
    version: data.versionName,
    semester: data.semesterId,
    volume: '1',
    textbookVersionId: data.textbookVersionId,
    textbookVolumeId: data.textbookVolumeId,
    gradeId: data.gradeId,
    textbookVolumeCategory: data.textbookVolumeCategory,
  };

  // 重新获取资源列表
  performSearch();
  showGradeSelector.value = false;
};

const handleCloseGradePopup = () => {
  showGradePopup.value = false;
};

const handleTabClick = async (value: string) => {
  console.log('handleTabClick', value);

  switch (value) {
    case 'subject':
      // 确保学科数据已加载
      if (subjectList.value.length === 0) {
        await fetchSubjectList();
      }
      // 初始化临时选择状态
      if (currentSubject.value && currentSubject.value !== '全部') {
        tempSelectedSubjects.value = currentSubject.value
          .split(',')
          .filter(item => item && item !== '全部');
      } else {
        // 如果没有选中学科或选中的是"全部"，默认选中第一个学科
        if (subjectList.value.length > 0) {
          tempSelectedSubjects.value = [subjectList.value[0].name];
        }
      }
      showSubjectPopup.value = true;
      break;

    case 'source':
      // 初始化临时选择状态
      if (currentSource.value === '全部') {
        tempSelectedSources.value = ['全部'];
      } else {
        tempSelectedSources.value = currentSource.value
          .split(',')
          .filter(item => item && item !== '全部');
        if (tempSelectedSources.value.length === 0) {
          tempSelectedSources.value = ['全部'];
        }
      }
      showSourcePopup.value = true;
      break;

    case 'type':
      // 确保资源类型数据已加载
      if (typeList.value.length === 0) {
        await fetchTypeList();
      }
      // 初始化临时选择状态
      if (currentType.value === '全部') {
        tempSelectedTypes.value = ['全部'];
      } else {
        tempSelectedTypes.value = currentType.value
          .split(',')
          .filter(item => item && item !== '全部');
        if (tempSelectedTypes.value.length === 0) {
          tempSelectedTypes.value = ['全部'];
        }
      }
      showTypePopup.value = true;
      break;

    case 'format':
      // 确保格式数据已加载
      if (formatList.value.length === 0) {
        await fetchFormatList();
      }
      // 初始化临时选择状态
      if (currentFormat.value === '全部') {
        tempSelectedFormats.value = ['全部'];
      } else {
        tempSelectedFormats.value = currentFormat.value
          .split(',')
          .filter(item => item && item !== '全部');
        if (tempSelectedFormats.value.length === 0) {
          tempSelectedFormats.value = ['全部'];
        }
      }
      showFormatPopup.value = true;
      break;

    case 'grade':
      showGradePopup.value = true;
      break;

    default:
      break;
  }
};

// 临时存储选中的项目
const tempSelectedSubjects = ref<string[]>([]);
const tempSelectedSources = ref<string[]>(['全部']);
const tempSelectedTypes = ref<string[]>(['全部']);
const tempSelectedFormats = ref<string[]>(['全部']);

const handleSelectSubject = (subject: string, id?: number) => {
  // 如果只传递了一个参数（来自 LkGradeSelector），则 subject 实际上是 subjectId
  if (id === undefined) {
    // 来自 LkGradeSelector，subject 参数实际上是 subjectId 字符串
    const subjectId = parseInt(subject);
    const subjectInfo = subjectList.value.find(s => s.id === subjectId);
    if (subjectInfo) {
      currentSubject.value = subjectInfo.name;
      currentSubjectId.value = subjectInfo.id;
      tempSelectedSubjects.value = [subjectInfo.name];
      performSearch();
    }
  } else {
    // 来自学科选择弹窗，有完整的参数
    tempSelectedSubjects.value = [subject];

    // 如果是来自 LkGradeSelector 的选择，立即更新当前学科并触发搜索
    // 通过检查弹窗状态来判断来源
    if (!showSubjectPopup.value) {
      // 来自 LkGradeSelector，立即应用选择
      currentSubject.value = subject;
      currentSubjectId.value = id;
      performSearch();
    }
  }
};

// 清空学科选择
const handleResetSubject = () => {
  // 清空为第一个学科
  if (subjectList.value.length > 0) {
    tempSelectedSubjects.value = [subjectList.value[0].name];
    // 注意：这里不立即更新 currentSubjectId，等用户确认后再更新
  }
};

// 确认学科选择
const handleConfirmSubject = () => {
  // 直接使用选中的学科
  const selectedSubjectName = tempSelectedSubjects.value.join(',');
  currentSubject.value = selectedSubjectName;

  // 根据学科名称找到对应的ID
  const subjectInfo = subjectList.value.find(s => s.name === selectedSubjectName);
  if (subjectInfo) {
    currentSubjectId.value = subjectInfo.id;
  }

  showSubjectPopup.value = false;
  performSearch();
};

// 关闭学科弹窗
const handleSubjectPopupClose = () => {
  showSubjectPopup.value = false;
};

const handleSelectSource = (source: string) => {
  if (source === '全部') {
    tempSelectedSources.value = ['全部'];
  } else {
    // 移除"全部"选项
    tempSelectedSources.value = tempSelectedSources.value.filter(s => s !== '全部');

    // 切换选择状态
    const index = tempSelectedSources.value.indexOf(source);
    if (index > -1) {
      tempSelectedSources.value.splice(index, 1);
    } else {
      tempSelectedSources.value.push(source);
    }

    // 如果没有选择任何项，默认选择"全部"
    if (tempSelectedSources.value.length === 0) {
      tempSelectedSources.value = ['全部'];
    }
  }
};

// 清空来源选择
const handleResetSource = () => {
  tempSelectedSources.value = ['全部'];
};

// 确认来源选择
const handleConfirmSource = () => {
  if (tempSelectedSources.value.length === 1 && tempSelectedSources.value[0] === '全部') {
    currentSource.value = '全部';
  } else {
    currentSource.value = tempSelectedSources.value.join(',');
  }
  showSourcePopup.value = false;
  performSearch();
};

const handleSelectType = (type: string) => {
  if (type === '全部') {
    tempSelectedTypes.value = ['全部'];
  } else {
    // 移除"全部"选项
    tempSelectedTypes.value = tempSelectedTypes.value.filter(t => t !== '全部');

    // 切换选择状态
    const index = tempSelectedTypes.value.indexOf(type);
    if (index > -1) {
      tempSelectedTypes.value.splice(index, 1);
    } else {
      tempSelectedTypes.value.push(type);
    }

    // 如果没有选择任何项，默认选择"全部"
    if (tempSelectedTypes.value.length === 0) {
      tempSelectedTypes.value = ['全部'];
    }
  }
};

// 清空资源类型选择
const handleResetType = () => {
  tempSelectedTypes.value = ['全部'];
};

// 确认资源类型选择
const handleConfirmType = () => {
  if (tempSelectedTypes.value.length === 1 && tempSelectedTypes.value[0] === '全部') {
    currentType.value = '全部';
  } else {
    currentType.value = tempSelectedTypes.value.join(',');
  }
  showTypePopup.value = false;
  performSearch();
};

// 处理格式选择
const handleSelectFormat = (format: string, id?: number) => {
  if (format === '全部') {
    tempSelectedFormats.value = ['全部'];
  } else {
    // 移除"全部"选项
    tempSelectedFormats.value = tempSelectedFormats.value.filter(f => f !== '全部');

    // 切换选择状态
    const index = tempSelectedFormats.value.indexOf(format);
    if (index > -1) {
      tempSelectedFormats.value.splice(index, 1);
    } else {
      tempSelectedFormats.value.push(format);
    }

    // 如果没有选择任何项，默认选择"全部"
    if (tempSelectedFormats.value.length === 0) {
      tempSelectedFormats.value = ['全部'];
    }
  }
};

// 清空格式选择
const handleResetFormat = () => {
  tempSelectedFormats.value = ['全部'];
};

// 确认格式选择
const handleConfirmFormat = () => {
  if (tempSelectedFormats.value.length === 1 && tempSelectedFormats.value[0] === '全部') {
    currentFormat.value = '全部';
  } else {
    currentFormat.value = tempSelectedFormats.value.join(',');
  }
  showFormatPopup.value = false;
  performSearch();
};

// 获取选中项的显示文本（最多显示2个，超过用省略号）
const getSelectedDisplay = (tabValue: string) => {
  let selectedItems: string[] = [];

  switch (tabValue) {
    case 'subject':
      if (!currentSubject.value || currentSubject.value === '全部') {
        return '学科';
      }
      selectedItems = currentSubject.value.split(',').filter(item => item && item !== '全部');
      break;

    case 'source':
      if (currentSource.value === '全部') {
        return '来源';
      }
      selectedItems = currentSource.value.split(',').filter(item => item && item !== '全部');
      break;

    case 'type':
      if (currentType.value === '全部') {
        return '资源类型';
      }
      selectedItems = currentType.value.split(',').filter(item => item && item !== '全部');
      break;

    case 'format':
      if (currentFormat.value === '全部') {
        return '格式';
      }
      selectedItems = currentFormat.value.split(',').filter(item => item && item !== '全部');
      break;

    case 'grade':
      console.log('currentGrade.value', currentGrade.value);
      if (currentGrade.value === '全部') {
        return '年级';
      }
      // 如果currentGrade.value是对象，显示其name属性
      if (typeof currentGrade.value === 'object' && currentGrade.value?.name) {
        return currentGrade.value.name;
      }
      return '年级';

    case 'version':
      return '版本';

    case 'difficulty':
      return '难度';

    default:
      return tabValue;
  }

  if (selectedItems.length === 0) {
    // 根据 tabValue 返回默认名称
    const defaultNames = {
      subject: '学科',
      source: '来源',
      type: '资源类型',
      format: '格式',
      grade: '年级',
      version: '版本',
      difficulty: '难度',
    };
    return defaultNames[tabValue] || tabValue;
  }

  if (selectedItems.length === 1) {
    return selectedItems[0];
  }

  if (selectedItems.length === 2) {
    return selectedItems.join('、');
  }

  // 超过2个，显示前2个加省略号
  return `${selectedItems[0]}、${selectedItems[1]}...`;
};

// 资源相关方法
const handleResourceClick = (resource: ResourceItem) => {
  // 跳转到资源详情页
  uni.navigateTo({
    url: `/pages-subpackages/students/learning-resource/resource-details?id=${resource.id}`,
  });
};

const handleCollectAll = async () => {
  if (aiSelectedResources.value.length === 0) {
    uni.showToast({
      title: '没有可收藏的资源',
      icon: 'none',
    });
    return;
  }

  // 收藏操作
  const res = await getCollectResources({
    resourceIds: aiSelectedResources.value.map(item => item.id),
  });
  console.log('收藏操作结果:', res);

  uni.showToast({
    title: res.message,
    icon: 'success',
  });
};

// 直接使用关键词作为高亮词
const parsedKeywordsText = computed(() => {
  return searchText.value;
});
</script>

<template>
  <view class="search-result-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-bar-left">
        <u-icon name="arrow-left" size="20" color="#1D2129" @click="handleBack"></u-icon>
      </view>
      <view class="nav-bar-right">
        <view class="search-input-box">
          <input
            class="input"
            type="text"
            v-model="keyword"
            @confirm="handleSearch"
            maxlength="50"
          />
          <view class="voice-btn" @click="handleVoiceSearch">
            <LkSvg
              width="20px"
              height="20px"
              src="/static/learning-resource/voice.svg"
              class="voice-icon"
            />
          </view>
          <view class="search-btn" @click="handleSearch">搜索</view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 筛选区域 -->
      <view class="filter-area">
        <view class="filter-area-wrapper">
          <!-- 年级和版本筛选 -->
          <!-- <view class="grade-filter" @click="handleOpenGradeFilter">
            <view class="grade-info">
              <view class="grade-name">{{ selectedGrade?.name }}</view>
              <view class="grade-version">{{ selectedGrade?.version }}</view>
            </view>
            <LkSvg
              width="24px"
              height="24px"
              src="/static/learning-resource/down.svg"
              class="arrow-icon"
            />
          </view> -->

          <!-- 学科切换 -->
          <scroll-view
            scroll-x
            class="subject-scroll"
            :show-scrollbar="false"
            :enhanced="true"
            :bounces="false"
            :scroll-with-animation="true"
            :enable-passive="false"
          >
            <view class="subject-list">
              <view
                class="subject-item"
                v-for="item in tabList"
                :key="item.value"
                @click="handleTabClick(item.value)"
                :class="{ 'has-selection': getSelectedDisplay(item.value) !== item.name }"
              >
                <text class="subject-text">{{ getSelectedDisplay(item.value) }}</text>
                <LkSvg width="14px" height="14px" src="/static/learning-resource/down_black.svg" />
              </view>
            </view>
          </scroll-view>
          <view class="filter-icon" @click="handleOpenGradeFilter">
            <LkSvg width="22px" height="22px" src="/static/learning-resource/filter.svg" />
          </view>
        </view>
      </view>

      <!-- 搜索结果区域 -->
      <view class="search-results" v-if="!isLoading">
        <!-- AI精选区 -->
        <view v-if="showAiSelected && aiSelectedResources.length > 0" class="ai-selected-section">
          <view class="section-header">
            <view class="section-title">
              <LkSvg
                width="65px"
                height="24px"
                style="margin-right: 8px"
                src="/static/learning-resource/ai_helper.svg"
              />
              AI智能检索已为您精准匹配<span class="section-title-text"
                >「{{ parsedKeywordsText }}」</span
              >精选资源
            </view>
          </view>

          <view class="resource-list">
            <FileContent
              :hightlightwords="parsedKeywordsText"
              v-for="resource in aiSelectedResources"
              :key="resource.id"
              :item="resource"
              :onItemClick="handleResourceClick"
            />
          </view>

          <view class="section-footer">
            <view class="collect-all-btn" @click="handleCollectAll">
              <LkSvg width="16px" height="16px" src="/static/learning-resource/collect.svg" />
              全部收藏
            </view>
          </view>
        </view>

        <!-- 普通搜索结果区 -->
        <view v-if="regularSearchResults.length > 0" class="regular-results-section">
          <view class="section-title">搜索到的资源</view>

          <view class="resource-list">
            <FileContent
              :hightlightwords="parsedKeywordsText"
              v-for="resource in regularSearchResults"
              :key="resource.id"
              :item="resource"
              :onItemClick="handleResourceClick"
            />
          </view>
        </view>

        <!-- 无结果提示 -->
        <view v-if="!showAiSelected && regularSearchResults.length === 0" class="no-results">
          <LkSvg width="130px" height="130px" src="/static/learning-resource/nodata.svg" />
          <view class="no-results-text">暂无数据</view>
        </view>
      </view>

      <!-- 骨架屏 -->
      <template v-if="isLoading">
        <view class="skeleton-container">
          <view v-for="index in 4" :key="`skeleton-${index}`" class="skeleton-item">
            <view class="skeleton-main-wrapper">
              <view class="skeleton-main">
                <view class="skeleton-icon"></view>
                <view class="skeleton-info">
                  <view class="skeleton-title"></view>
                  <view class="skeleton-tags">
                    <view class="skeleton-tag"></view>
                    <view class="skeleton-tag"></view>
                    <view class="skeleton-tag"></view>
                  </view>
                </view>
              </view>
            </view>
            <view class="skeleton-footer">
              <view class="skeleton-meta">
                <view class="skeleton-meta-item"></view>
                <view class="skeleton-meta-item"></view>
              </view>
              <view class="skeleton-source-tag"></view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <LkGradeSelector
      :show="showGradeSelector"
      :currentSubject="currentSubjectId.toString()"
      :value="{
        gradeName: currentGrade.name,
        gradeId: currentGrade.gradeId, // 这里需要根据年级名称获取对应的ID，暂时设为0
        versionName: currentGrade.versionName,
        versionId: currentGrade.textbookVersionId,
        semesterId: 1, // 默认上册
        textbookVersionId: currentGrade.textbookVersionId,
        textbookVolumeId: currentGrade.textbookVolumeId,
        textbookVolumeCategory: currentGrade.textbookVolumeCategory,
      }"
      @close="handleCloseGradePopup"
      @confirm="handleGradeConfirm"
      @selectSubject="handleSelectSubject"
    />
    <!-- 语音输入弹窗 -->
    <VoicePopup
      v-model:show="showVoicePopup"
      @close="handleVoiceClose"
      @cancel="handleVoiceCancel"
      @confirm="handleVoiceConfirm"
    />

    <!-- 学科选择弹窗 -->
    <LkPopup
      :show="showSubjectPopup"
      mode="bottom"
      :round="24"
      :safeAreaInsetBottom="true"
      :constrainToContainer="true"
      @close="handleSubjectPopupClose"
    >
      <view class="filter-popup-content">
        <view class="filter-popup-header">
          <view class="filter-popup-title">选择学科</view>
          <view class="filter-popup-close" @click="showSubjectPopup = false">
            <!-- <LkSvg width="24px" height="24px" src="/static/icons/close.svg" /> -->
            <image src="/static/learning-resource/close.svg" class="button-icon-svg" />
          </view>
        </view>
        <view class="filter-popup-body">
          <view class="filter-grid">
            <view
              class="filter-item"
              v-for="subject in subjectList"
              :key="subject.id"
              :class="{ active: tempSelectedSubjects.includes(subject.name) }"
              @click="handleSelectSubject(subject.name, subject.id)"
            >
              {{ subject.name }}
            </view>
          </view>
        </view>
        <view class="filter-popup-footer">
          <view class="filter-btn reset-btn" @click="handleResetSubject">清空</view>
          <view class="filter-btn confirm-btn" @click="handleConfirmSubject">完成</view>
        </view>
      </view>
    </LkPopup>

    <!-- 来源选择弹窗 -->
    <LkPopup
      :show="showSourcePopup"
      mode="bottom"
      :round="24"
      :safeAreaInsetBottom="true"
      :constrainToContainer="true"
      @close="showSourcePopup = false"
    >
      <view class="filter-popup-content">
        <view class="filter-popup-header">
          <view class="filter-popup-title">选择来源</view>
          <view class="filter-popup-close" @click="showSourcePopup = false">
            <!-- <LkSvg width="20px" height="20px" src="/static/icons/close.svg" /> -->
            <image src="/static/learning-resource/close.svg" class="button-icon-svg" />
          </view>
        </view>
        <view class="filter-popup-body">
          <view class="filter-grid">
            <view
              class="filter-item"
              :class="{ active: tempSelectedSources.includes('全部') }"
              @click="handleSelectSource('全部')"
            >
              全部
            </view>
            <view
              class="filter-item"
              v-for="source in sourceList"
              :key="source.code"
              :class="{ active: tempSelectedSources.includes(source.name) }"
              @click="handleSelectSource(source.name)"
            >
              {{ source.name }}
            </view>
          </view>
        </view>
        <view class="filter-popup-footer">
          <view class="filter-btn reset-btn" @click="handleResetSource">清空</view>
          <view class="filter-btn confirm-btn" @click="handleConfirmSource">完成</view>
        </view>
      </view>
    </LkPopup>

    <!-- 资源类型选择弹窗 -->
    <LkPopup
      :show="showTypePopup"
      mode="bottom"
      :round="24"
      :safeAreaInsetBottom="true"
      :constrainToContainer="true"
      @close="showTypePopup = false"
    >
      <view class="filter-popup-content">
        <view class="filter-popup-header">
          <view class="filter-popup-title">选择资源类型</view>
          <view class="filter-popup-close" @click="showTypePopup = false">
            <!-- <LkSvg width="20px" height="20px" src="/static/icons/close.svg" /> -->
            <image src="/static/learning-resource/close.svg" class="button-icon-svg" />
          </view>
        </view>
        <view class="filter-popup-body">
          <view class="filter-grid">
            <view
              class="filter-item"
              :class="{ active: tempSelectedTypes.includes('全部') }"
              @click="handleSelectType('全部')"
            >
              全部
            </view>
            <view
              class="filter-item"
              v-for="type in typeList"
              :key="type.id"
              :class="{ active: tempSelectedTypes.includes(type.name) }"
              @click="handleSelectType(type.name)"
            >
              {{ type.name }}
            </view>
          </view>
        </view>
        <view class="filter-popup-footer">
          <view class="filter-btn reset-btn" @click="handleResetType">清空</view>
          <view class="filter-btn confirm-btn" @click="handleConfirmType">完成</view>
        </view>
      </view>
    </LkPopup>

    <!-- 格式选择弹窗 -->
    <LkPopup
      :show="showFormatPopup"
      mode="bottom"
      :round="24"
      :safeAreaInsetBottom="true"
      :constrainToContainer="true"
      @close="showFormatPopup = false"
    >
      <view class="filter-popup-content">
        <view class="filter-popup-header">
          <view class="filter-popup-title">选择文件格式</view>
          <view class="filter-popup-close" @click="showFormatPopup = false">
            <!-- <LkSvg width="20px" height="20px" src="/static/icons/close.svg" /> -->
            <image src="/static/learning-resource/close.svg" class="button-icon-svg" />
          </view>
        </view>
        <view class="filter-popup-body">
          <view class="format-grid">
            <view
              class="format-item"
              :class="{ active: tempSelectedFormats.includes('全部') }"
              @click="handleSelectFormat('全部')"
            >
              全部
            </view>
            <!-- 修改为展示格式大类及其子格式 -->
            <template v-for="format in formatList" :key="format.id">
              <!-- 格式大类标题 -->
              <view class="format-category-title">{{ format.name }}</view>
              <view class="format-category-warp">
                <!-- 格式子项 -->
                <view
                  class="format-item"
                  v-for="childFormat in format.children"
                  :key="childFormat.id"
                  :class="{ active: tempSelectedFormats.includes(childFormat.name) }"
                  @click="handleSelectFormat(childFormat.name, childFormat.id)"
                >
                  {{ childFormat.name }}
                </view>
              </view>
            </template>
          </view>
        </view>
        <view class="filter-popup-footer">
          <view class="filter-btn reset-btn" @click="handleResetFormat">清空</view>
          <view class="filter-btn confirm-btn" @click="handleConfirmFormat">完成</view>
        </view>
      </view>
    </LkPopup>

    <!-- 年级选择弹窗 -->
    <LkPopup
      :show="showGradePopup"
      mode="bottom"
      :round="24"
      :safeAreaInsetBottom="true"
      :constrainToContainer="true"
      @close="showGradePopup = false"
    >
      <view class="filter-popup-content">
        <view class="filter-popup-header">
          <view class="filter-popup-title">选择年级</view>
          <view class="filter-popup-close" @click="showGradePopup = false">
            <!-- <LkSvg width="20px" height="20px" src="/static/icons/close.svg" /> -->
            <image src="/static/learning-resource/close.svg" class="button-icon-svg" />
          </view>
        </view>
        <view class="filter-popup-body">
          <view class="filter-grid">
            <view
              class="filter-item"
              :class="{ active: tempSelectedGrades.includes('全部') }"
              @click="handleSelectGrade('全部')"
            >
              全部
            </view>
            <view
              class="filter-item"
              v-for="grade in gradeList"
              :key="grade.id"
              :class="{ active: tempSelectedGrades.includes(grade.id) }"
              @click="handleSelectGrade(grade.name, grade.id)"
            >
              {{ grade.name }}
            </view>
          </view>
        </view>
        <view class="filter-popup-footer">
          <view class="filter-btn reset-btn" @click="handleResetGrade">清空</view>
          <view class="filter-btn confirm-btn" @click="handleConfirmGrade">完成</view>
        </view>
      </view>
    </LkPopup>
  </view>
</template>

<style lang="scss" scoped>
.search-result-page {
  min-height: 100vh;
  background-color: #f7f7fd;
  /* 防止页面整体左右滑动 */
  overflow: hidden;
  position: relative;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 16px 16px 0 0;
  padding: 0 16px;
  padding-top: var(--status-bar-height);
  .nav-bar-left {
    margin-right: 16px;
  }
  .nav-bar-right {
    flex: 1;
  }
}

.main-content {
  /* 防止主要内容区域左右滑动 */
  overflow-x: hidden;
  width: 100%;
}

.back-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
}

.search-input-box {
  display: flex;
  border-radius: 999px;
  background: #fff;
  height: 40px;
  width: 100%;
  padding: 0px 4px 0px 12px;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  flex: 1;

  .input {
    flex: 1;
    height: 100%;
    font-size: 14px;
    color: #333;
    border: none;
    outline: none;
    background: transparent;

    &::placeholder {
      color: #4e5969;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 28.8px;
    }
  }

  .voice-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    flex-shrink: 0;

    .voice-icon {
      opacity: 0.6;

      &:active {
        opacity: 0.4;
      }
    }
  }

  .search-btn {
    display: flex;
    width: 60px;
    height: 32px;
    padding: 5px 16px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    background: linear-gradient(135deg, #6b8aff 0%, #4a6fff 100%);
    color: #fff;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(75, 144, 255, 0.3);

    &:active {
      background: linear-gradient(135deg, #5b7aff 0%, #3a5fff 100%);
      transform: scale(0.95);
      box-shadow: 0 2rpx 8rpx rgba(75, 144, 255, 0.4);
    }
  }
}

.filter-area {
  border-radius: 24rpx;
  z-index: 1000;
}

.filter-area-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  width: 100%;
  min-height: 80rpx;
  /* 暂时移除overflow: hidden，让scroll-view正常工作 */
}

.grade-filter {
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  min-width: 160rpx;
  flex-shrink: 0;
}

.grade-info {
  display: flex;
  flex-direction: column;
  margin-right: 12rpx;
}

.grade-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.grade-version {
  font-size: 24rpx;
  color: #666;
  line-height: 1.2;
  margin-top: 4rpx;
}

.arrow-icon {
  flex-shrink: 0;
}

.subject-scroll {
  flex: 1;
  white-space: nowrap;
  overflow-x: auto;
  /* 让scroll-view组件完全处理滚动 */

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
  margin: 10px 0;

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.subject-list {
  display: inline-flex;
  padding: 0 16rpx;
  /* 确保列表可以水平滚动 */
  min-width: max-content;
  width: max-content;
}

.subject-item {
  display: inline-flex;
  min-width: 80px;
  padding: 8px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border-radius: 50px;
  background: rgba(184, 184, 184, 0.1);
  white-space: nowrap;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 16rpx;

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    background: rgba(184, 184, 184, 0.2);
  }

  &:active {
    transform: scale(0.95);
  }
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-right: 24rpx;
  min-width: 80rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  flex: 1;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;

  &.active {
    background: linear-gradient(90deg, #5199ff 0%, #774cff 100%);
    color: #fff;
    font-weight: 600;
  }
}

.search-results {
  height: calc(100vh - 120px - var(--status-bar-height));
  position: relative; // 添加相对定位，使 LkPopup 可以约束在此容器内
  padding: 0 16px;
}

// 学科选择弹窗样式
.subject-popup {
  padding: 16px;
  padding-bottom: 0;
  .subject-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24rpx;
    padding-bottom: 43px;

    .subject-grid-item {
      display: flex;
      height: 32px;
      padding: 3px 16px;
      justify-content: center;
      align-items: center;
      flex: 1 0 0;
      transition: all 0.3s ease;
      background: #f3f6fd;
      border-radius: 999px;
      &.active {
        border-radius: 999px;
        border: 1px solid #7d4dff;
        background: #efecff;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}
.popup-actions {
  display: flex;
  gap: 24rpx;
  padding: 16px;
  border-radius: 0px 0px 8px 8px;
  border-top: 1px solid #e7e7e7;
  .action-button {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s ease;

    &.clear-button {
      background: #f5f5f5;
      color: #666;

      &:active {
        background: #e5e5e5;
      }
    }

    &.confirm-button {
      background: #7d4dff;
      color: white;

      &:active {
        opacity: 0.8;
      }
    }
  }
}
.ai-selected-section,
.regular-results-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.section-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  line-height: 24px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .section-title-text {
    color: var(--Brand-Brand5, #7d4dff);
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
  }
}

.collect-all-btn {
  display: flex;
  padding: 5px 12px;
  width: 100%;
  text-align: center;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  background: #f6f6f6;
  font-size: 24rpx;
  color: #333;
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}

.no-results-text {
  color: var(--text-icon-text-2, #4e5969);
  text-align: center;
  /* Body/Medium */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.no-results-tip {
  font-size: 28rpx;
  color: #999;
}

/* 骨架屏样式 */
.skeleton-container {
  padding: 32rpx;
}

.skeleton-item {
  margin: 0 0 14px 0;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid var(--Gray-Gray3, #e7e7e7);
  background: #fff;

  .skeleton-main-wrapper {
    border-bottom: 1px solid #f3f3f3;
    margin-bottom: 12rpx;
    padding-bottom: 12rpx;

    .skeleton-main {
      display: flex;
      align-items: center;

      .skeleton-icon {
        width: 48px;
        height: 48px;
        margin-right: 24rpx;
        border-radius: 12rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        flex-shrink: 0;
      }

      .skeleton-info {
        flex: 1;
        overflow: hidden;

        .skeleton-title {
          height: 30rpx;
          background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4rpx;
          margin-bottom: 12rpx;
          width: 75%;
        }

        .skeleton-tags {
          display: flex;
          gap: 8rpx;
          margin: 12rpx 0;

          .skeleton-tag {
            height: 22rpx;
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 8rpx;
            width: 60rpx;
          }
        }
      }
    }
  }

  .skeleton-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .skeleton-meta {
      display: flex;
      align-items: center;
      gap: 8px;

      .skeleton-meta-item {
        height: 22rpx;
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4rpx;
        width: 80rpx;
      }
    }

    .skeleton-source-tag {
      height: 20px;
      background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      border-radius: 999px;
      width: 40px;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 弹窗底部按钮样式
.popup-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1px solid #f0f0f0;

  .popup-btn {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16rpx;
    font-size: 32rpx;
    font-weight: 500;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    &.cancel {
      background: #f8f9fa;
      color: #6b7280;
      border: 1px solid #e5e7eb;
    }

    &.confirm {
      background: linear-gradient(90deg, #5199ff 0%, #774cff 100%);
      color: #fff;
    }
  }
}

// 通用筛选弹窗样式
.filter-popup-content {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  max-height: 80vh;
}

.filter-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1px solid #f0f0f0;

  .filter-popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1e293b;
  }

  .filter-popup-close {
    padding: 8rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    .button-icon-svg {
      width: 27px;
      height: 27px;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.filter-popup-body {
  padding: 24rpx 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;

  .filter-item {
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40rpx;
    background: #f5f5f5;
    font-size: 28rpx;
    color: #333;
    transition: all 0.3s ease;
    cursor: pointer;

    &:active {
      transform: scale(0.95);
    }

    &.active {
      background: #efecff;
      color: #7d4dff;
      font-weight: 500;
      border: 1px solid #7d4dff;
    }
  }
}

.filter-popup-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1px solid #f0f0f0;

  .filter-btn {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    &.reset-btn {
      border-radius: 100px;
      border: 1px solid var(--Gray-Gray4, #dcdcdc);
      background: var(--Gray-White, #fff);
      color: var(--text-icon-text-2, #4e5969);
      text-align: center;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 120% */
    }

    &.confirm-btn {
      background: #7d4dff;
      color: #fff;
    }
  }
}

.format-grid {
  width: 100%;

  .format-category-warp {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    width: 100%;
  }
  .format-item {
    width: calc((100% - 24px) / 3); /* 一行3个，减去两个间隙的宽度 */
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40rpx;
    background: #f5f5f5;
    font-size: 28rpx;
    color: #333;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 12px;

    &:active {
      transform: scale(0.95);
    }

    &.active {
      background: #efecff;
      color: #7d4dff;
      font-weight: 500;
      border: 1px solid #7d4dff;
    }
  }
  .format-category-title {
    width: 100%;
    padding: 8px 0;
    margin-top: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    display: block;
  }
}
.filter-icon {
  padding: 0 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
