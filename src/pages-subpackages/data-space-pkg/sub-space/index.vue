<script setup lang="ts">
import { ref, reactive, nextTick, watch, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import {
  mixBatchDelete,
  myMixBatchDelete,
  deleteSpaceFile,
  batchUpdateParentFileAndFolder,
  getSpaceFileList,
  getMySpaceFileList,
} from '@/api/database';
import AddSelect from '@/pages/data-space/components/AddSelect.vue';

import LkDatabaseItem from '@/components/LkDatabase/LkDatabaseItem.vue';
import OptionTool from '@/pages/data-space/components/OptionTool.vue';
import OptionShare from '@/pages/data-space/components/OptionShare.vue';
import OptionDel from '@/pages/data-space/components/OptionDel.vue';
import LkStepGuide from '@/components/LkStepGuide/index.vue';
import LkSvg from '@/components/svg/index.vue';
import { useUserStore } from '@/store/userStore';
import { SearchType } from '@/constants/search';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';
import LkTree from '@/components/LkTree/index.vue';
import type { TreeItem } from '@/components/LkTree/types';
import LkToast from '@/components/LkToast/index.vue';

const uToastRef = ref();
type NavOptions = {
  id: string;
  navTitle: string;
  bizType: string;
  privileges: string;
};

const urlOptions = ref<NavOptions>();
const addSelectRef = ref<any>(null);
const userStore = useUserStore();
const userInfo = ref(userStore.userInfo);
const isEditing = ref(false);
const batchSelectedIdArr = ref<any[]>([]);
const batchSelectedArr = ref<any[]>([]);
const listExtraParams = reactive<{ parentId: string | number }>({ parentId: '0' });

const canBatchShare = ref(false);
const canBatchMove = ref(false);
const canBatchDelete = ref(false);

const calculateBatchPermissions = (selectedItems: any[]) => {
  if (selectedItems.length === 0) {
    canBatchShare.value = false;
    canBatchMove.value = false;
    canBatchDelete.value = false;
    return;
  }
  const currentUserInfo = userStore.getUserInfo;
  const isAdmin =
    Number(currentUserInfo?.roleType) === 1 || Number(currentUserInfo?.roleType) === 3;
  const isAdminMode = urlOptions.value?.bizType === '2';

  const checkItemPermissions = (item: any) => {
    const isUploader = item.uploader === currentUserInfo?.username;
    const privileges = Number(item.privileges?.[0] || 4);
    const isManagePrivilege = privileges >= 3 || isAdmin;
    const isFile = item.fileType === 1;

    const hasSharePermission =
      isAdminMode || isManagePrivilege || privileges >= 2 || (privileges === 1 && isUploader);
    const canShare = isFile && hasSharePermission;

    const canMove = isAdminMode || isManagePrivilege || privileges >= 2;

    const canDelete =
      isAdminMode || isManagePrivilege || (isUploader && (privileges === 1 || privileges === 2));

    return { canShare, canMove, canDelete };
  };

  canBatchShare.value = selectedItems.every(item => checkItemPermissions(item).canShare);
  canBatchMove.value = selectedItems.every(item => checkItemPermissions(item).canMove);
  canBatchDelete.value = selectedItems.every(item => checkItemPermissions(item).canDelete);
};

const fabClick = () => {
  addSelectRef.value.onOpen({
    title: urlOptions.value?.navTitle,
    type: urlOptions.value?.bizType === '1' ? 'subAdd' : 'add',
  });
};
const stepGuideRef = ref();
const needGuide = ref(false);
const guideSteps = [
  {
    target: '.addSpace',
    title: '',
    content: '在学校空间和我的空间中，点击「添加」工作成果文件。',
    position: 'topRight',
    lollipop: true,
    offsetX: 35,
    offsetY: -20,
  },
  {
    target: '.more-checked',
    title: '',
    content: '点击「多选」文件支持批量操作。',
    position: 'bottom',
    lollipop: true,
    offsetX: 18,
    offsetY: -30,
  },
  {
    target: '.u-swipe-action-item:nth-child(3)',
    title: '',
    content: '左滑可对文件进行分享等操作哦~',
    position: 'bottom',
  },
] as any[];

const onGuideComplete = () => {
  const data = uni.getStorageSync('Guide') || {};
  uni.setStorageSync('Guide', { ...data, DataSpace: true });
  // 重置引导状态，恢复原来的显示逻辑
  needGuide.value = false;

  nextTick(() => uni.navigateBack());
};

onLoad((options: any) => {
  console.log('页面加载选项:', options);
  urlOptions.value = { ...options } as NavOptions;
  const initialId = options.id || '0';
  currentSpaceId.value = initialId;
  listExtraParams.parentId = initialId;
  if (options.guide === 'true' && !uni.getStorageSync('Guide')?.DataSpace) {
    needGuide.value = true;
  }
});

const navBack = () => {
  uni.navigateBack();
};

const optionsList = ref<any[]>([
  {
    style: {
      backgroundColor: '#A6A6A6',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/5849fcbac6a1721968c83830dfb3f622.svg',
    actionType: 'more',
  },
  {
    style: {
      backgroundColor: '#8B8B8B',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/8122eec722aa08581f49a2b32a27317a.svg',
    actionType: 'share',
  },
  {
    style: {
      backgroundColor: '#D54941',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/bf5e481fc72554fa10d8a5c6390553ab.svg',
    actionType: 'delete',
  },
]);

const optionTool = ref<InstanceType<typeof OptionTool>>();
const optionShare = ref<InstanceType<typeof OptionShare>>();
const optionDel = ref<InstanceType<typeof OptionDel>>();
const lkDatabaseItemRef = ref<InstanceType<typeof LkDatabaseItem>>();
const lkDatabasePopupRef = ref<InstanceType<typeof LkDatabasePopup>>();

const clickOption = async (payload: { eventData: any; itemData: any; fn: Function }) => {
  const { eventData: optionsData, itemData: currentItem, fn } = payload;
  console.log('点击的操作数据:', optionsData);

  // 获取当前项可用的选项列表
  const currentOptions = fn(currentItem);
  // 通过索引获取选项，并使用actionType判断
  const selectedOption = currentOptions?.[optionsData.index];
  console.log('选中的操作类型:', selectedOption?.actionType);
  if (selectedOption?.actionType === 'more') {
    // 文件夹 且 可查看+不是上传人,提示暂无操作权限
    if (
      currentItem.fileType === 2 &&
      (currentItem.privileges?.length ? currentItem.privileges?.[0] < 2 : false) &&
      currentItem.uploader !== userInfo.value?.username
    ) {
      uni.showToast({ title: '暂无操作权限', icon: 'none' });
      return;
    } else {
      // 更多
      optionTool?.value?.onOpen({
        user:
          (urlOptions.value?.bizType === '2' ? userInfo.value?.username : currentItem.uploader) +
          ' | ' +
          currentItem.updateTime,
        title: currentItem.fileType === 3 ? currentItem.spaceName : currentItem.fileName,
        fileObj: currentItem,
      });
    }
  } else if (selectedOption?.actionType === 'share') {
    // 分享
    console.log('触发分享操作');
    optionShare?.value?.onOpen({ fileObj: currentItem });
  } else if (selectedOption?.actionType === 'delete') {
    // 删除
    console.log('触发删除操作');
    optionDel?.value?.onOpen({
      fileObj: currentItem,
    });
  }
};

const clickBatchDel = () => {
  if (batchSelectedIdArr.value.length === 0) {
    uni.showToast({ title: '请选择文件', icon: 'none' });
    return;
  }
  optionDel.value?.onOpen({ fileObj: batchSelectedArr.value });
};

const toolCb = () => {};
const shareCb = () => {};

const delCb = async (items: any) => {
  const itemsToDelete = Array.isArray(items) ? items : [items];
  console.log('确认批量删除:', itemsToDelete);

  try {
    const filesToDelete = itemsToDelete.filter(item => item.fileType === 1 || item.fileType === 2);
    const spacesToDelete = itemsToDelete.filter(item => item.fileType === 3);

    if (filesToDelete.length > 0) {
      const api = urlOptions.value?.bizType === '1' ? mixBatchDelete : myMixBatchDelete;
      await api({
        list: filesToDelete.map(item => ({ id: item.id, fileType: item.fileType })),
      });
    }

    if (spacesToDelete.length > 0) {
      for (const space of spacesToDelete) {
        await deleteSpaceFile({ id: space.id });
        uToastRef.value.show({
          message: '空间删除成功',
          type: 'success',
        });
      }
    }

    uToastRef.value.show({
      message: '文件删除成功',
      type: 'success',
    });
    isEditing.value = false;
    batchSelectedArr.value = [];
    batchSelectedIdArr.value = [];
    refreshPageList();
  } catch (error) {
    console.error('删除失败:', error);
    uni.showToast({ title: '删除失败', icon: 'none' });
  }
};

const confirmMove = async (payload: { currentParentObj: any; fileObj: any }) => {
  console.log('确认移动到:', payload.currentParentObj);
  console.log('被移动的文件:', batchSelectedArr.value);
  const { currentParentObj } = payload;

  try {
    const fileIds = batchSelectedArr.value.filter(item => item.fileType === 1).map(item => item.id);

    const folderItems = batchSelectedArr.value.filter(item => item.fileType === 2);

    const isSchoolSpace = urlOptions.value?.bizType === '1';
    const folderIds = !isSchoolSpace ? folderItems.map(item => item.id) : [];
    const spaceIds = isSchoolSpace ? folderItems.map(item => item.id) : [];

    await batchUpdateParentFileAndFolder({
      parentId: currentParentObj.id || '0',
      ids: fileIds,
      folderIds,
      spaceIds,
    });

    uToastRef.value.show({
      message: '文件移动位置已完成',
      type: 'success',
    });
    lkDatabasePopupRef.value?.closePopup();
    isEditing.value = false;
    batchSelectedArr.value = [];
    batchSelectedIdArr.value = [];
    refreshPageList();
  } catch (error) {
    console.error('移动失败:', error);
    uni.showToast({ title: '移动失败', icon: 'none' });
  }
};

const refreshPageList = () => {
  console.log('刷新页面列表');
  lkDatabaseItemRef.value?.refreshPageList();
  addSelectRef.value?.onClose();
};

// 添加数据加载完成事件处理
const handleDataLoaded = (data: any[]) => {
  console.log('数据加载完成:', data);
  if (!needGuide.value) return;
  const index = data.length > 0 ? (data.length <= 3 ? data.length : 3) : 0;
  nextTick(() => {
    setTimeout(() => {
      startGuideWhenListReady(index);
    }, 200);
  });
};

const startGuideWhenListReady = (index: number) => {
  if (index <= 0) {
    onGuideComplete();
    return;
  }
  const checkQuery = uni.createSelectorQuery();
  const target = `.u-swipe-action-item:nth-child(${index})`;

  checkQuery
    .select(target)
    .boundingClientRect((rect: any) => {
      if (rect) {
        guideSteps[2].target = target;
        // 等待滑动单元格展开后启动引导
        stepGuideRef.value?.start();
      }
    })
    .exec();
};

const clickSearch = () => {
  console.log('跳转搜索页面');
  // 使用storage存储activeType，避免URL参数在安卓打包时的问题
  uni.setStorageSync('searchActiveType', SearchType.FILE);
  uni.navigateTo({
    url: '/pages/search/index',
  });
};

const clickEditItem = () => {
  isEditing.value = !isEditing.value;
};

const updateSelectedItems = (items: any[]) => {
  batchSelectedIdArr.value = items.map((item: any) => item.id);
  batchSelectedArr.value = items;
  console.log('更新选中的ID数组:', batchSelectedIdArr.value);
  calculateBatchPermissions(items);
};

const clickCheckAll = () => {
  console.log('全选');
  lkDatabaseItemRef.value?.toggleCheckAll();
};

const clickBatchShare = () => {
  console.log('批量分享');
  if (batchSelectedArr.value.length === 0) {
    uni.showToast({ title: '请选择文件', icon: 'none' });
    return;
  }
  optionShare.value?.onOpen({ fileObj: batchSelectedArr.value });
};

const clickBatchMove = () => {
  console.log('批量移动');
  if (batchSelectedIdArr.value.length === 0) {
    uni.showToast({ title: '请选择文件', icon: 'none' });
    return;
  }
  lkDatabasePopupRef.value?.openPopup();
};

const showTree = ref(false);
const treeData = ref<TreeItem[]>([]);
const currentSpaceId = ref<string | number>('0');
const expandedKeys = ref<(string | number)[]>([]);

const mapApiDataToTreeItem = (item: any): TreeItem => {
  let name = '';
  // 只处理空间
  if (item.fileType === 3) {
    name = item.spaceName;
  }
  const treeNode = {
    id: item.id,
    name: name,
    fileType: item.fileType,
    privileges: item.privileges,
    // 使用API返回的`hasChildren`属性来判断是否为叶子节点
    isLeaf: !item.hasChildren,
    childrenLoaded: false,
    children: [],
  };
  console.log(`[数据映射] 原始数据:`, item, `映射后:`, treeNode);
  return treeNode;
};

function findNodeAndSetChildren(nodes: TreeItem[], nodeId: string | number, children: TreeItem[]) {
  for (const node of nodes) {
    if (node.id === nodeId) {
      node.children = children;
      node.childrenLoaded = true;
      // 如果没有加载到子节点，则将该节点标记为叶子节点
      // 这样展开/折叠图标就会被隐藏
      if (children.length === 0) {
        node.isLeaf = true;
      }
      return true;
    }
    if (node.children) {
      if (findNodeAndSetChildren(node.children, nodeId, children)) {
        return true;
      }
    }
  }
  return false;
}

const handleLoadChildren = async (node: TreeItem) => {
  if (node.fileType !== 3) return;
  console.log(`🌳 [树] 开始加载节点ID的子节点: ${node.id}`);

  try {
    const api = urlOptions.value?.bizType === '1' ? getSpaceFileList : getMySpaceFileList;
    const res = await api({ parentId: node.id, privilege: 4 });
    console.log('完整的API响应 (res):', res);
    const records = res?.records || [];
    console.log('API响应 (原始 records):', records);

    const filteredRecords = records.filter((item: any) => item.fileType === 3);
    console.log('过滤后的记录:', filteredRecords);

    const childrenItems = filteredRecords.map(mapApiDataToTreeItem);
    console.log('处理后的子节点:', childrenItems);

    findNodeAndSetChildren(treeData.value, node.id, childrenItems);
  } catch (error) {
    console.error(`加载节点 ${node.id} 的子节点失败:`, error);
  } finally {
  }
};

// 递归查找并收集所有父节点的ID
const findParentIds = (
  nodes: TreeItem[],
  targetId: string | number,
  path: (string | number)[] = []
): (string | number)[] | null => {
  for (const node of nodes) {
    const currentPath = [...path, node.id];
    if (node.id === targetId) {
      return currentPath;
    }
    if (node.children) {
      const result = findParentIds(node.children, targetId, currentPath);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

const loadInitialTree = async () => {
  console.log('🌳 [树] 开始加载初始数据');
  try {
    const api = urlOptions.value?.bizType === '1' ? getSpaceFileList : getMySpaceFileList;
    const res = await api({ parentId: '0', privilege: 4 });
    console.log('完整的API响应 (res):', res);
    const records = res?.records || [];
    console.log('API响应 (原始 records):', records);

    const filteredRecords = records.filter((item: any) => item.fileType === 3);
    console.log('过滤后的记录:', filteredRecords);

    treeData.value = filteredRecords.map(mapApiDataToTreeItem);
    console.log('最终处理的树数据:', treeData.value);

    // 自动展开到当前选中的节点
    if (currentSpaceId.value) {
      const path = findParentIds(treeData.value, currentSpaceId.value);
      if (path) {
        // 展开路径上的所有节点，但不包括当前节点本身
        expandedKeys.value = path.slice(0, -1);
      }
    }
  } catch (error) {
    console.error('加载空间树失败:', error);
  } finally {
  }
};

const openTree = () => {
  loadInitialTree();
  showTree.value = true;
};

const handleTreeSelect = (node: TreeItem) => {
  console.log('跳转空间', node);

  // 如果点击的是当前已在的空间，则不执行任何操作
  if (node.id.toString() === listExtraParams.parentId.toString()) {
    console.log('已在当前空间，无需跳转。');
    return;
  }

  const privileges = node.privileges?.length ? node.privileges?.[0] : 4;
  const url = `/pages-subpackages/data-space-pkg/sub-space/index?id=${node.id}&navTitle=${
    node.name
  }&bizType=${urlOptions.value?.bizType || ''}&privileges=${privileges}`;

  // 执行页面跳转
  uni.navigateTo({
    url,
  });
};

defineOptions({ name: 'dataSubSpace' });
</script>
<template>
  <view class="sub-space">
    <up-navbar :title="urlOptions?.navTitle">
      <template #left>
        <up-icon name="arrow-left" size="24px" color="#000" @tap="navBack" />
        <LkSvg
          v-if="urlOptions?.bizType === '1'"
          @tap="openTree"
          width="24px"
          height="24px"
          src="/static/database/sitemap_line.svg"
          class="sitemap"
        />
      </template>
      <template #right>
        <template v-if="Number(urlOptions?.privileges) >= 2 || needGuide">
          <view v-if="!isEditing" class="more-checked">
            <LkSvg
              width="24px"
              height="24px"
              src="/static/recycleBin/editItem.svg"
              @click="clickEditItem"
            />
          </view>
          <text v-else class="nav-action-text" @click="clickCheckAll">全选</text>
        </template>
        <LkSvg
          width="24px"
          height="24px"
          src="/static/database/search.svg"
          class="search"
          @tap="clickSearch"
        />
      </template>
    </up-navbar>
    <view class="sub-space__body">
      <!-- 全局数据空间组件 -->
      <LkDatabaseItem
        ref="lkDatabaseItemRef"
        :layoutType="isEditing ? 6 : 1"
        :parentId="urlOptions?.id || ''"
        :bizType="urlOptions?.bizType || ''"
        :optionsList="optionsList"
        :needGuide="needGuide"
        @clickOption="clickOption"
        @dataLoaded="handleDataLoaded"
        :isEditing="isEditing"
        @updateSelectedItems="updateSelectedItems"
        :extra-params="listExtraParams"
      />
      <LkStepGuide ref="stepGuideRef" :steps="guideSteps" @complete="onGuideComplete" />
    </view>
    <!-- +号 -->
    <view class="addSpace" @tap="fabClick">
      <LkSvg width="24px" height="24px" src="/static/database/addSpace.svg" />
    </view>
    <view v-if="isEditing" class="wrapEdit">
      <view class="editItem" @click="clickBatchShare" :class="{ disabled: !canBatchShare }">
        <LkSvg width="24px" height="24px" src="/static/database/tool_share.svg" />
        <view class="txt">分享</view>
      </view>
      <view class="editItem" @click="clickBatchMove" :class="{ disabled: !canBatchMove }">
        <LkSvg width="24px" height="24px" src="/static/database/tool_move.svg" />
        <view class="txt">移动</view>
      </view>
      <view class="editItem" @click="clickBatchDel" :class="{ disabled: !canBatchDelete }">
        <LkSvg width="24px" height="24px" src="/static/database/tool_delete.svg" />
        <view class="txt">删除</view>
      </view>
      <view class="editItem" @click="isEditing = false">
        <LkSvg width="24px" height="24px" src="/static/recycleBin/checkAll_close.svg" />
        <view class="txt close">取消</view>
      </view>
    </view>
    <AddSelect
      ref="addSelectRef"
      :bizType="urlOptions?.bizType || ''"
      :parentId="String(currentSpaceId)"
      @refreshPageList="refreshPageList"
    />
    <OptionTool
      ref="optionTool"
      @refreshPageList="refreshPageList"
      :bizType="urlOptions?.bizType || ''"
      @cb="toolCb"
    />
    <OptionShare ref="optionShare" @cb="shareCb" />
    <OptionDel ref="optionDel" @confirm="delCb" />
    <LkDatabasePopup
      ref="lkDatabasePopupRef"
      :layoutType="5"
      :bizType="urlOptions?.bizType || ''"
      :fileObj="batchSelectedArr"
      @confirm-move="confirmMove"
    ></LkDatabasePopup>
    <LkTree
      v-model:show="showTree"
      :data="treeData"
      @select="handleTreeSelect"
      :on-load-children="handleLoadChildren"
    />
    <LkToast ref="uToastRef" />
  </view>
</template>
<style lang="scss" scoped>
::v-deep .u-navbar__content__title {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
}
.sub-space {
  background-color: white;
  &__body {
    padding: 0 10px;
    display: flex;
    flex-direction: column;
    padding-top: calc(var(--status-bar-height) + 44px);
  }
  .sitemap {
    margin-left: 8px;
  }
  .nav-action-text {
    font-size: 32rpx;
    color: #7d4dff;
    font-weight: 600;
  }
  .search {
    margin-left: 12px;
  }
}
.more-checked {
  width: 34px;
  height: 34px;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.addSpace {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(180deg, #4da3ff 0%, #7d4dff 100%);
  box-shadow:
    0px 3px 14px 2px rgba(0, 0, 0, 0.05),
    0px 8px 10px 1px rgba(0, 0, 0, 0.06),
    0px 5px 5px -3px rgba(0, 0, 0, 0.1);
  position: fixed;
  bottom: 70px;
  right: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.wrapEdit {
  width: calc(100vw - 20px);
  position: fixed;
  bottom: 14px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  padding: 8.5px 57.83px;
  border-top: 1px solid #f4f4f4;
  border-radius: 14px;
  box-shadow:
    0px -1px 15.9px 0px rgba(0, 0, 0, 0.14),
    0px 1px 10px 0px rgba(0, 0, 0, 0.05),
    0px 2px 6.9px -1px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-between;
  .editItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    &.disabled {
      opacity: 0.5;
      pointer-events: none;
    }
    .txt {
      font-size: 26rpx;
      color: #303133;
      margin-top: 4px;
      &.close {
        color: #d54941;
      }
    }
  }
}
</style>
