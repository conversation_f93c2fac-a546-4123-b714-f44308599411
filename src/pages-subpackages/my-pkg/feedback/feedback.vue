<template>
  <view class="feedback">
    <view>
      <u-navbar bg-color="#fff" :autoBack="true" leftIconColor="#000" placeholder height="88rpx">
        <template #center>
          <view class="feedback-navbar-title">{{ isStudent ? '帮助反馈' : '用户反馈' }}</view>
        </template>
      </u-navbar>
    </view>

    <u-tabs
      class="tabs"
      :list="tabList"
      :current="tabIndex"
      scrollable
      lineColor="#7D4DFF"
      :activeStyle="{
        color: '#7D4DFF',
        fontSize: '32rpx',
        lineHeight: '44rpx',
        fontWeight: '500',
        paddingBottom: '10rpx',
      }"
      :inactiveStyle="{
        color: '#86909C',
        fontSize: '32rpx',
        lineHeight: '44rpx',
        fontWeight: '400',
        paddingBottom: '10rpx',
      }"
      @click="clickTab"
    ></u-tabs>

    <view class="line"></view>

    <view v-if="tabIndex === 0">
      <view class="feedback-type">
        <view class="feedback-type-title"> 反馈类型 </view>
        <view class="feedback-type-box">
          <view
            class="item"
            @tap="handleType(index, item.id)"
            :class="{ active: index === feedTypeIndex }"
            v-for="(item, index) in feedbackType"
            :key="item.id"
          >
            {{ item.dictValue }}
          </view>
        </view>
      </view>

      <view class="input-class">
        <view class="feedback-title">
          详细描述
          <!-- <text class="required">*</text> -->
        </view>
        <u-form
          ref="form"
          labelWidth="0"
          :labelStyle="{ fontSize: '26rpx' }"
          :model="newForm"
          :rules="rules"
          errorType="toast"
        >
          <u-form-item prop="content">
            <u-textarea
              class="feedback-textarea"
              height="10em"
              v-model="newForm.content"
              border="none"
              placeholder="请输入您的意见和建议"
              :count="isStudent"
              confirmType="done"
              :maxlength="isStudent ? 500 : 2000"
            >
            </u-textarea>
          </u-form-item>
          <view class="upload-title-box">
            <view class="title">上传图片</view>
            <view class="text">支持上传3张图片,单张图片大小不超5M</view>
          </view>
          <u-form-item prop="logo" v-if="true">
            <view class="album">
              <view class="upload-placeholder" @click="chooseImage">+</view>
              <view
                class="uploaded-image-box"
                v-for="(item, index) in newForm.logoSignedUrl"
                :key="item?.id"
              >
                <image
                  :src="item.fileUrl"
                  class="uploaded-image"
                  @click="handleImgPreview(index)"
                ></image>
                <view class="close" @click="handleLogoSignedUrlDelete(item?.id)">
                  <image
                    class="close-img"
                    src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/4a3477b2bef159252de0f080e45591e7.svg"
                  ></image>
                </view>
              </view>
            </view>
          </u-form-item>

          <!-- <lk-upload
            v-model="form.file"
            :maxCount="1"
            type="file"
            @submit="submit"
            :isFileShow="isFileShow"
            :confirmTrigger="true"
            v-if="showUploadFile"
          >
            <template #button>
              <u-button type="primary" plain> 上传文件 </u-button>
            </template>
          </lk-upload> -->
        </u-form>
      </view>

      <view class="btn-box">
        <LkButton
          @click="confirm"
          class="confirm-button"
          type="primary"
          size="large"
          shape="round"
          :loading="submiting"
          :disabled="isSubmitDisabled"
        >
          提交
        </LkButton>
      </view>
    </view>

    <view v-else class="record-list">
      <scroll-view scroll-y @scrolltolower="loadMore" :lower-threshold="50" style="height: 100vh">
        <view v-for="(item, index) in feedbackRecords" :key="index" class="record-item">
          <view class="record-item-box">
            <view class="record-content" @tap="viewDetail(item)">{{ item.content }}</view>
            <view class="record-bottom">
              <view class="record-bottom-left">
                <!-- 1-已处理；0-待处理 -->
                <view class="record-status record-pending" v-if="item.status === 0"> 待处理 </view>
                <view class="record-status record-processed" v-else="item.status === 1">
                  已处理
                </view>

                <view class="record-text-box">
                  <view class="record-text">{{ item.feedbackType }}</view>
                  <view class="record-shu"></view>
                  <view class="record-time">{{ formatTime(item.feedbackTime!) }}</view>
                </view>
              </view>

              <view class="btn">
                <image
                  src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/e4547d36d00894a59b1f261da1eb9386.svg"
                  class="record-edit"
                  :style="{
                    visibility: item.status === 0 ? 'visible' : 'hidden',
                  }"
                  @click="handleEdit(item)"
                />
                <image
                  src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/8ff848f1a9f7dc9d02750ebcd1aea3a8.svg"
                  class="record-delete"
                  @click="handleDelete(item)"
                />
              </view>
            </view>
          </view>
        </view>
        <view class="loading-text" v-if="loading">
          <text>上拉加载更多</text>
        </view>
        <view class="loading-text" v-if="!hasMore">
          <text>没有更多了</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import {
  getDictSublist,
  feedBackSubmit,
  feedBackPage,
  deleteFeedback,
  updateFeedback,
} from '@/api/feedback';
import { useUserStore } from '@/store/userStore';
import type { GetDictSublistResponse } from '@/types/api/feedback';
import { getBaseUrl } from '@/common/ai/url';
import dayjs from '@/utils/day';

const userStore = useUserStore();

// 表单验证规则
const rules = {
  content: {
    type: 'string',
    message: '请输入反馈内容',
    required: true,
    trigger: ['change'],
  },
};

// 定义类型接口
interface FeedbackFile {
  id: number | string;
  fileUrl: string;
  fileKey?: string;
}

interface FeedbackRecord {
  id: number | string;
  feedbackContent: string;
  feedbackType: string;
  createTime: string;
  feedbackFiles: FeedbackFile[];
  content?: string;
  time: string;
  status: number; // 1-已处理；0-待处理
  dictId?: string;
  tmbId?: string | number;
  feedbackTime?: string;
}

interface TabItem {
  name: string;
  value: string;
}

interface FeedbackType {
  id: number;
  dictValue: string;
}

// 反馈记录列表
const feedbackRecords = ref<FeedbackRecord[]>([]);

// 分页数据
const page = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const loading = ref(false);
const isStudent = ref(uni.getStorageSync('userMode') === 'student');

// 添加计算属性判断按钮禁用状态
const isSubmitDisabled = computed(() => {
  if (isStudent.value) {
    const contentLength = newForm.value.content.length;
    return contentLength < 2 || contentLength > 500;
  }
  return !newForm.value.content; // 非学生用户只要求有内容即可
});

// 标签页数据
const tabIndex = ref(0);
const tabList = ref<TabItem[]>([
  { name: isStudent.value ? '我要反馈' : '用户反馈', value: 'feedback' },
  { name: '反馈记录', value: 'record' },
]);

function clickTab({ index }: { index: number }) {
  tabIndex.value = index;
  uni.pageScrollTo({
    scrollTop: 0,
  });
}

// 其他状态数据
const submiting = ref(false);
const feedbackType = ref<GetDictSublistResponse>([]);
const feedTypeIndex = ref<number | undefined>(undefined);
const dictId = ref<string | undefined>(undefined);
const isEdit = ref(false);
const feedbackId = ref<number | string | undefined>(undefined);
const showUploadFile = ref(false); // 文件上传相关
const isFileShow = ref(false); // 文件显示相关

const newForm = ref({
  content: '',
  logoSignedUrl: [] as FeedbackFile[],
  file: [] as any[],
});

// 格式化时间
const formatTime = (time: string) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '';
};

function submit() {
  console.log('submit');
}

// 确认提交
const confirm = () => {
  if (!newForm.value.content) {
    uni.showToast({
      title: '请填写反馈内容',
      icon: 'none',
    });
    return;
  }

  const { tenantId, tmbId, userId } = userStore.getUserInfo!;

  const fileKeys = newForm.value.logoSignedUrl?.map(file => file?.fileKey || '');

  const params = {
    feedbackContent: newForm.value.content,
    fileKeys,
    tenantId,
    tmbId,
    userId,
    dictId: dictId.value!,
  };

  submiting.value = true;

  if (isEdit.value) {
    // 编辑已有反馈
    updateFeedback({ ...params, id: String(feedbackId.value) })
      .then(() => {
        uni.showToast({
          icon: 'success',
          title: '编辑成功',
        });
        resetUserFeedbackData();
      })
      .catch(() => {
        uni.showToast({
          icon: 'error',
          title: '编辑失败',
        });
      })
      .finally(() => {
        submiting.value = false;
        tabIndex.value = 1;
      });
  } else {
    // 提交新反馈
    feedBackSubmit(params)
      .then(() => {
        uni.showToast({
          title: '反馈成功，请耐心等待平台回复！',
          icon: 'none',
        });
        tabIndex.value = 1;
      })
      .catch(() => {
        uni.showToast({
          title: '反馈失败，请稍后再试！',
          icon: 'none',
        });
      })
      .finally(() => {
        submiting.value = false;
        // uni.hideLoading();
      });
  }
};

// 查看详情
const viewDetail = (item: FeedbackRecord) => {
  uni.navigateTo({
    url: `/pages-subpackages/my-pkg/feedback/feedDetail?id=${item.id}&tmbId=${item.tmbId}`,
    success: res => {
      // 页面参数传递
      // res.eventChannel?.emit('params', {
      //   id: item.id,
      //   tmbId: item.tmbId,
      // });
    },
  });
};

// 选择图片
const chooseImage = () => {
  const length = newForm.value.logoSignedUrl.length;
  if (length >= 3) {
    uni.showToast({
      title: '仅能传3张图片',
      icon: 'error',
    });
    return;
  }

  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      const tempFilePaths = res.tempFilePaths;
      // @ts-ignore - 处理tempFilePaths类型问题
      tempFilePaths.forEach((filePath: string) => {
        uploadFile(filePath);
      });
    },
  });
};

// 上传文件
const uploadFile = (filePath: string) => {
  const baseUrl = getBaseUrl();
  uni.uploadFile({
    url: `${baseUrl}/huayun-ai/system/file/public/upload`,
    header: {
      Authorization: uni.getStorageSync('token'),
    },
    filePath: filePath,
    name: 'file',
    success: uploadFileRes => {
      // @ts-ignore - parse返回值类型
      const result = JSON.parse(uploadFileRes.data);
      if (result.data.fileUrl) {
        newForm.value.logoSignedUrl.push(result.data);
      }
    },
  });
};

// 获取反馈记录
const fetchFeedbackRecords = (loadMore = false) => {
  if (!loadMore) {
    page.value = 1;
    feedbackRecords.value = [];
    hasMore.value = true;
  }

  if (!hasMore.value || loading.value) return;

  loading.value = true;
  // const { account, tmbId, userName } = store.state.userInfo;
  const { account, tmbId, username } = userStore.getUserInfo!;

  const params = {
    account,
    current: page.value,
    size: pageSize.value,
    tmbId,
    username,
  };

  feedBackPage(params)
    .then(res => {
      const records = res.records.map((record: any) => ({
        ...record,
        content: record.feedbackContent,
        time: record.createTime,
        id: record.id,
        tmbId: record.tmbId,
        status: record.status,
        dictId: record.dictId,
        feedbackTime: record.feedbackTime,
      }));

      feedbackRecords.value = [...feedbackRecords.value, ...records];

      if (records.length < pageSize.value) {
        hasMore.value = false;
      } else {
        page.value += 1;
      }
    })
    .catch(() => {
      uni.showToast({
        title: '获取反馈记录失败，请稍后再试！',
        icon: 'none',
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

// 加载更多
const loadMore = () => {
  if (tabIndex.value === 1) {
    fetchFeedbackRecords(true);
  }
};

// 选择反馈类型
const handleType = (index: number, id: string) => {
  feedTypeIndex.value = index;
  dictId.value = id;
};

// 编辑反馈
const handleEdit = (item: FeedbackRecord) => {
  tabIndex.value = 0;

  const index = feedbackType.value.findIndex(i => String(i.id) === String(item.dictId));
  feedTypeIndex.value = index;
  dictId.value = item.dictId;
  // form.value.content = item.content || '';
  // form.value.logoSignedUrl = item.feedbackFiles || [];

  newForm.value.content = item.content || '';
  newForm.value.logoSignedUrl = item.feedbackFiles || [];

  isEdit.value = true;
  feedbackId.value = item.id;
};

// 删除反馈
const handleDelete = (item: FeedbackRecord) => {
  uni.showModal({
    title: '确认操作',
    content: '确定删除吗?',
    success(res) {
      if (res.confirm) {
        deleteFeedback({ id: item.id })
          .then(() => {
            uni.showToast({
              icon: 'success',
              title: '删除成功',
            });
          })
          .catch(() => {
            uni.showToast({
              icon: 'error',
              title: '删除失败',
            });
          })
          .finally(() => {
            fetchFeedbackRecords();
          });
      }
    },
  });
};

// 重置表单数据
const resetUserFeedbackData = () => {
  feedTypeIndex.value = undefined;
  dictId.value = undefined;
  // form.value.content = '';
  // form.value.logoSignedUrl = [];
  isEdit.value = false;
  newForm.value.content = '';
  newForm.value.logoSignedUrl = [];
};

// 删除已上传图片
const handleLogoSignedUrlDelete = (id: number | string) => {
  newForm.value.logoSignedUrl = newForm.value.logoSignedUrl.filter(item => item.id !== id);
};

// 图片预览
const handleImgPreview = (index: number) => {
  const urls = newForm.value.logoSignedUrl.map(item => item.fileUrl);
  uni.previewImage({
    urls: urls,
    current: index,
  });
};

// 监听tabIndex变化
watch(tabIndex, newVal => {
  if (newVal === 1) {
    resetUserFeedbackData();
    fetchFeedbackRecords();
  }
});

// 生命周期函数
onMounted(() => {
  // 获取反馈类型列表
  getDictSublist({ code: 'feedback_type' }).then(res => {
    feedbackType.value = res;
    console.log('测试请求', res);
  });
  console.log('feedbackType', feedbackType.value);
});

// 页面触底事件
// 这个方法在uniapp中通过onReachBottom生命周期自动调用
const onReachBottom = () => {
  loadMore();
};

// 暴露给页面的方法
defineExpose({
  onReachBottom,
});
</script>
<style lang="scss" scoped>
.feedback-navbar-title {
  font-size: 18 * 2rpx;
  font-weight: 600;
  color: #1d2129;
}

::v-deep .u-navbar__content__title {
  color: #000000 !important;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
}

::v-deep .u-tabs__wrapper__nav__item {
  padding: 0 61px !important;
}

.feedback {
  background: #fff;
  min-height: 100vh;
  overflow: auto;
}

.input-class {
  margin: 32rpx;
  background: #fff;
  border-radius: 8rpx;
}

.feedback-title {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 14 * 2rpx;
  font-size: 600;
  color: rgba(0, 0, 0, 0.9);
  line-height: 48rpx;
  margin-bottom: 12 * 2rpx;
  font-weight: 600;
}

::v-deep .u-form-item__body {
  padding: 0;
}

.required {
  margin-left: 5rpx;
  color: red;
}

::v-deep .u-textarea {
  background: #f9f9f9 !important;
  border-radius: 16rpx;
  padding: 8rpx 24rpx;
}

.album {
  display: flex;
  flex-wrap: wrap;
  border-radius: 8rpx;
}

.uploaded-image-box {
  position: relative;
  width: 80 * 2rpx;
  height: 77 * 2rpx;
  border-radius: 8rpx;
  margin-right: 7 * 2rpx;
  overflow: hidden;

  &:last-child {
    margin-right: 0;
  }
  .close {
    position: absolute;
    top: 0;
    right: 0;
    width: 19 * 2rpx;
    height: 19 * 2rpx;
    border-radius: 0 0 0 5.766 * 2rpx;
    background: rgba(0, 0, 0, 0.4);
    .close-img {
      width: 100%;
      height: 100%;
      // width: 15rpx;
      // height: 15rpx;
    }
  }
}

.uploaded-image {
  position: relative;
  width: 80 * 2rpx;
  height: 77 * 2rpx;
  border-radius: 8rpx;
  margin-right: 7 * 2rpx;
}

.upload-placeholder {
  width: 160rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 56rpx;
  color: #ccc;
  background: #f9f9f9;
  margin-right: 7 * 2rpx;
}

.record-list {
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
  background: #fff;
  box-sizing: border-box;
  height: 112 * 2rpx;

  .record-item-box {
    width: 100%;
    height: 100%;
    padding: 14 * 2rpx 16 * 2rpx;
    box-sizing: border-box;
  }

  .record-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 22 * 2rpx;
    color: #626262;
    font-size: 13 * 2rpx;
    .record-bottom-left {
      display: flex;
      // flex-direction: column;
    }

    .record-text-box {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
    }

    .record-text {
      width: 52 * 2rpx;
      color: #626262;
      font-family: 'PingFang SC';
      font-size: 13 * 2rpx;
      font-style: normal;
      font-weight: 400;
      margin-left: 20 * 2rpx;
      text-align: center;
      white-space: nowrap;
    }

    .record-shu {
      height: 14 * 2rpx;
      border-right: 1px solid #bababa;
      margin: 0 8 * 2rpx;
    }
    .record-time {
      width: 119 * 2rpx;
      white-space: nowrap;
    }

    .btn {
      display: flex;
      justify-content: space-between;
      width: 64 * 2rpx;
      margin-left: 28 * 2rpx;
    }

    .record-edit,
    .record-delete {
      width: 22 * 2rpx;
      height: 22 * 2rpx;
    }

    .record-edit {
      margin-right: 16 * 2rpx;
    }
  }

  .record-status {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 58 * 2rpx;
    height: 24 * 2rpx;
    font-size: 14 * 2rpx;
    border-radius: 8 * 2rpx;
    white-space: nowrap;
    padding: 0 8 * 2rpx;
  }

  .record-pending {
    background: #fff7e8;
    color: #ff7d00;
  }

  .record-processed {
    background-color: #e8ffea;
    color: #00b42a;
  }
}

.record-left {
  flex: 1;
}

.record-content {
  color: #1d2129;
  text-overflow: ellipsis;
  font-family: 'PingFang SC';
  font-size: 14 * 2rpx;
  font-style: normal;
  font-weight: 500;
  display: -webkit-box; /* 必须结合 -webkit-box */
  -webkit-box-orient: vertical; /* 设置盒子垂直排列 */
  -webkit-line-clamp: 2; /* 显示的行数 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 文本溢出显示省略号 */
  margin-bottom: 16 * 2rpx;
  height: 42 * 2rpx;
  line-height: 24 * 2rpx;
}

.record-time {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #909399;
  line-height: 44rpx;
}

.record-arrow {
  width: 48rpx;
  height: 48rpx;
}

.loading-text {
  text-align: center;
  padding: 20rpx 0;
  color: #ccc;
  font-size: 24rpx;
}

.btn-box {
  position: fixed;
  left: 30rpx;
  right: 30rpx;
  bottom: 0rpx;
  width: auto;
  display: flex;

  .confirm-button {
    width: 100%;
    margin-bottom: 30 * 2rpx;
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: #eee;
}
.feedback-type {
  margin-left: 16 * 2rpx;
  .feedback-type-title {
    font-size: 14 * 2rpx;
    margin-top: 20 * 2rpx;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.9);
  }

  .feedback-type-box {
    display: flex;
    flex-wrap: wrap;
    .item {
      display: flex;
      flex-wrap: wrap;
      width: 106 * 2rpx;
      height: 40 * 2rpx;
      justify-content: center;
      align-items: center;
      font-size: 14 * 2rpx;
      border-radius: 100 * 2rpx;
      background: #f3f3f3;
      margin-right: 12 * 2rpx;
      margin-top: 12 * 2rpx;
      box-sizing: border-box;
    }
    .active {
      border: 1px solid #7d4dff;
      background: #e8dbff;
      color: #7d4dff;
    }
  }
}

.upload-title-box {
  display: flex;
  align-items: center;
  margin-top: 16 * 2rpx;
  margin-bottom: 12 * 2rpx;
  .title {
    color: rgba(0, 0, 0, 0.9);
    font-family: 'PingFang SC';
    font-size: 14 * 2rpx;
    font-style: normal;
    font-weight: 600;
    margin-right: 11 * 2rpx;
  }
  .text {
    color: #606266;
    font-family: 'PingFang SC';
    font-size: 12 * 2rpx;
    font-style: normal;
    font-weight: 400;
  }
}

::v-deep .u-navbar__content__title {
  font-size: 32rpx;
}

::v-deep .u-icon__icon {
  font-size: 40rpx !important;
}

.feedback-textarea {
  ::v-deep .u-textarea__field {
    font-size: 16 * 2rpx;
  }
  /* padding: 0; */
  height: 175 * 2rpx;

  /* 添加字数统计区域背景色 */
  ::v-deep .u-textarea__count {
    background-color: #f9f9f9 !important;
    padding-right: 16rpx;
    border-radius: 0 0 16rpx 16rpx;
  }
}
.tabs {
  ::v-deep .uni-scroll-view-content {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
