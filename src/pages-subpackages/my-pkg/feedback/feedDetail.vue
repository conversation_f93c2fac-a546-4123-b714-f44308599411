<template>
  <view class="feedback-detail">
    <u-navbar bg-color="#fff" title="用户反馈" :autoBack="true" leftIconColor="#000" placeholder>
      <template #center>
        <view class="feedback-detail-navbar-title">用户反馈</view>
      </template>
    </u-navbar>

    <view class="feedback-container">
      <!-- 反馈信息 -->
      <view class="feedback-info">
        <view class="header-title">
          <view class="drop"></view>
          反馈信息
        </view>

        <view class="subheading line1">
          <view class="title"> 反馈类型： </view>
          <view class="text">{{ feedbackDetail.feedbackType }}</view>
        </view>

        <view class="subheading line2">
          <view class="title"> 反馈内容： </view>
          <view class="text">{{ feedbackDetail.feedbackContent }}</view>
        </view>

        <view class="img-list">
          <image
            v-for="(item, index) in feedbackDetail.feedbackFiles"
            :key="item.id"
            class="item"
            :src="item.fileUrl"
            @click="handleImgPreview(feedbackDetail.feedbackFiles, index)"
            mode="aspectFill"
          ></image>
        </view>

        <view class="subheading">
          <view class="title"> 反馈时间： </view>
          <view class="text">{{ formatTime(feedbackDetail.feedbackTime) }}</view>
        </view>
      </view>

      <!-- 处理回复 -->
      <view class="reply">
        <view class="header-title">
          <view class="drop"></view>
          处理回复
        </view>

        <view v-if="feedbackDetail.replyTime">
          <view class="subheading line2">
            <view class="title"> 回复内容： </view>
            <view class="text">{{ feedbackDetail.replyContent }}</view>
          </view>

          <view class="img-list">
            <image
              v-for="(item, index) in feedbackDetail.replyFiles"
              :key="item.id"
              class="item"
              :src="item.fileUrl"
              @click="handleImgPreview(feedbackDetail.replyFiles, index)"
              mode="aspectFill"
            ></image>
          </view>

          <view class="subheading">
            <view class="title"> 处理时间： </view>
            <view class="text">{{ formatTime(feedbackDetail.replyTime) }}</view>
          </view>
        </view>

        <view class="not-data" v-else>
          <LkSvg width="260rpx" height="260rpx" src="/static/my/not_data_text.svg" />
          <view class="not-data-text">暂无处理回复</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getFeedbackDetail } from '@/api/feedback';
import dayjs from '@/utils/day';

// 定义反馈详情类型接口
interface FeedbackFile {
  id: number | string;
  fileUrl: string;
}

interface FeedbackDetail {
  feedbackContent: string;
  feedbackType?: string;
  createTime: string;
  feedbackFiles: FeedbackFile[];
  replyContent: string;
  replyTime: string;
  replyFiles: FeedbackFile[];
  feedbackTime: string;
}

// 状态定义
const feedbackDetail = ref<FeedbackDetail>({
  feedbackContent: '',
  createTime: '',
  feedbackFiles: [],
  replyContent: '',
  replyTime: '',
  replyFiles: [],
  feedbackTime: '',
});

// 格式化时间方法
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm');
};

// 获取反馈详情
const handleGetFeedbackDetail = (params: any) => {
  getFeedbackDetail(params)
    .then((response: any) => {
      feedbackDetail.value = response;
      console.log('response', response);
    })
    .catch((error: any) => {
      // 错误处理
    });
};

// 图片预览方法
const handleImgPreview = (data: FeedbackFile[], index: number) => {
  const urls = data.map(item => item.fileUrl);
  uni.previewImage({
    urls: urls,
    current: index, // 当前显示图片的http链接，默认是第一个
    success: function (res) {},
    fail: function (res) {},
    complete: function (res) {},
  });
};

onLoad((options: any) => {
  console.log('options', options);
  handleGetFeedbackDetail({ id: options.id, tmbId: options.tmbId });
});
</script>

<style lang="scss" scoped>
view {
  box-sizing: border-box;
}

.feedback-detail-navbar-title {
  font-size: 18 * 2rpx;
  font-weight: 600;
  color: #1d2129;
}

.feedback-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.feedback-info {
  background-color: #fff;
  padding: 16 * 2rpx;
  // height: 241*2rpx;
}
.drop {
  width: 5 * 2rpx;
  height: 18 * 2rpx;
  border-radius: 50 * 2rpx;
  background: #7f4dff;
  margin-right: 10rpx;
}
.header-title {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.subheading {
  display: flex;
  align-items: center;
  .title {
    width: 70 * 2rpx;
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14 * 2rpx;
    font-style: normal;
    font-weight: 500;
    margin-right: 10 * 2rpx;
    align-self: baseline;
  }
  .text {
    width: 263 * 2rpx;
    color: #000;
    font-family: 'PingFang SC';
    font-size: 14 * 2rpx;
    font-style: normal;
    font-weight: 400;
    white-space: pre-wrap;
  }
}

.line1 {
  margin-top: 14 * 2rpx;
}
.line2 {
  margin-top: 10 * 2rpx;
  margin-bottom: 7 * 2rpx;
}

.img-list {
  display: flex;
  margin-bottom: 10 * 2rpx;

  .item {
    width: 110 * 2rpx;
    height: 74 * 2rpx;
    border-radius: 2px;
    margin-right: 7 * 2rpx;
    overflow: hidden;

    &:last-child {
      margin-right: 0;
    }
  }
}

.reply {
  position: relative;
  flex: 1;
  background-color: #fff;
  padding: 16 * 2rpx;
  margin-top: 14 * 2rpx;
  min-height: 470 * 2rpx;
}

.not-data {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .not-data-img {
    width: 77.612 * 2rpx;
    height: 80 * 2rpx;
  }
  .not-data-text {
    color: #626262;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 14 * 2rpx;
    font-style: normal;
    font-weight: 400;
    margin-top: 14 * 2rpx;
  }
}
</style>
