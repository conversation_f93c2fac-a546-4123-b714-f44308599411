<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { getAuthToken, getUser, updateUser } from '@/api/my-pkg/modify';
import { useUserStore } from '@/store/userStore';
import { getBaseUrl } from '@/common/ai/url';
import { LkLoading } from '@/components';
import { checkPermission } from '@/utils/permission';
import LKpermissionModal from '@/components/LKpermissionModal/index.vue';
import { useTabStore } from '@/store/tabStore';
const tabStore = useTabStore();
const userStore = useUserStore();
// 获取modify-navbar的高度
const modifyNavbarHeight = ref(0);

const avatarLoading = ref(false);
const utoastRef = ref();

// 标记是否需要在页面显示时重试图片选择
const shouldRetryImageSelection = ref(false);
const isStudent = ref(uni.getStorageSync('userMode') === 'student');

const userInfo = ref<any>({
  userId: userStore.getUserInfo?.userId,
  username: userStore.getUserInfo?.username,
  phone: userStore.getUserInfo?.phone,
  avatar: userStore.getUserInfo?.avatar,
  account: userStore.getUserInfo?.account,
  clazzName: '', // 班级名称
  gradeName: '', // 年级名称
});

// 权限弹窗相关状态
const permissionModalShow = ref(false);
const permissionModalTitle = ref('');
const permissionModalContent = ref('');
const permissionOpenSettings = ref(null);

onMounted(() => {
  const query = uni.createSelectorQuery();
  query
    .select('.modify-navbar')
    .boundingClientRect((data: any) => {
      modifyNavbarHeight.value = data.height;
    })
    .exec();

  fetchGetUser();
});

function fetchGetUser() {
  getUser({ id: userStore.getUserInfo?.userId! }).then(res => {
    userInfo.value.gender = res.gender;
    userInfo.value.username = res.username;
    userInfo.value.phone = res.phone;
    userInfo.value.avatar = res.avatar;
    userInfo.value.account = res.account;
    // 直接从用户信息接口获取班级和年级信息
    userInfo.value.clazzName = res.clazzName || '';
    userInfo.value.gradeName = res.gradeName || '';
  });
}

const hadleSaveUserInfo = () => {
  // username 长度限制2-10个字符
  if (userInfo.value.username.length < 2 || userInfo.value.username.length > 10) {
    uni.showToast({
      title: '请输入2-10个字符的姓名',
      icon: 'none',
    });
    return;
  }

  updateUser({
    userId: userInfo.value.userId,
    username: userInfo.value.username,
    phone: userInfo.value.phone,
    avatar: userInfo.value.avatar,
    gender: userInfo.value.gender,
  })
    .then(() => {
      return getAuthToken({});
    })
    .then(res => {
      userStore.setUserInfo(res);

      uni.hideLoading();

      utoastRef.value?.show({
        message: '保存成功',
        type: 'success',
      });

      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
        });
      }, 1500);
    })
    .catch(err => {
      utoastRef.value?.show({
        message: '保存失败',
        type: 'info',
      });
    });
};

async function chooseImage() {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      const tempFilePaths = res.tempFilePaths;
      // @ts-ignore - 处理tempFilePaths类型问题
      tempFilePaths.forEach((filePath: string) => {
        uploadFile(filePath);
      });
    },
    fail: async err => {
      console.log('chooseImage fail', err, `------errmsg`, err.errMsg);
      if (
        ['chooseImage:fail No filming permission', 'chooseImage:fail No Permission'].includes(
          err.errMsg
        )
      ) {
        const permissionResult = await checkPermission('camera');
        console.log('执行了吗????????');
        permissionModalShow.value = true;
        permissionModalTitle.value = '提示';
        permissionModalContent.value =
          '您拒绝了相机权限，部分功能可能无法使用。是否前往设置开启权限？';
        permissionOpenSettings.value = permissionResult.openSettings;
      }
    },
  });
}

// 上传文件
const uploadFile = (filePath: string) => {
  avatarLoading.value = true;
  const baseUrl = getBaseUrl();
  uni.uploadFile({
    // url: `${envcfg.baseUrl}/huayun-ai/system/file/public/upload`,
    url: `${baseUrl}/huayun-ai/system/file/public/upload`,
    header: {
      Authorization: uni.getStorageSync('token'),
    },
    filePath: filePath,
    name: 'file',
    success: uploadFileRes => {
      // @ts-ignore - parse返回值类型
      const result = JSON.parse(uploadFileRes.data);
      console.log('上传', result);
      if (result.data.fileUrl) {
        userInfo.value.avatar = result.data.fileUrl;
      }
    },
    complete: () => {
      avatarLoading.value = false;
    },
  });
};

// 权限弹窗事件处理
function handlePermissionCancel() {
  permissionModalShow.value = false;
}

function handlePermissionConfirm() {
  permissionModalShow.value = false;
  console.log('前往设置, permissionOpenSettings:', permissionOpenSettings.value);

  if (permissionOpenSettings.value && typeof permissionOpenSettings.value === 'function') {
    // 直接调用permissionOpenSettings函数
    try {
      permissionOpenSettings.value();
    } catch (e) {
      console.error('调用permissionOpenSettings错误:', e);
      // 如果调用失败，尝试直接调用系统API
      try {
        // #ifdef APP-PLUS
        const isIOS = plus.os.name === 'iOS';
        if (isIOS) {
          try {
            // 对于iOS 10+，使用新的API
            const UIApplication = plus.ios.import('UIApplication');
            const application = UIApplication.sharedApplication();
            const NSURL = plus.ios.import('NSURL');
            const settingURL = NSURL.URLWithString('app-settings:');

            // 检查iOS版本
            const iosVersion = plus.os.version || '9.0';
            if (parseInt(iosVersion) >= 10) {
              // iOS 10+ 使用新方法
              console.log('使用iOS 10+方法打开设置');
              const options = plus.ios.newObject('NSDictionary');
              application.openURL_options_completionHandler(settingURL, options, null);
              plus.ios.deleteObject(options);
            } else {
              // iOS 9及以下使用旧方法
              application.openURL(settingURL);
            }

            plus.ios.deleteObject(settingURL);
            plus.ios.deleteObject(NSURL);
            plus.ios.deleteObject(application);
          } catch (iosError) {
            console.error('iOS原生方法打开设置失败:', iosError);
            // 备选方案
            plus.runtime.openURL('app-settings:');
          }
        } else {
          // Android
          plus.runtime.openURL(`package:${plus.runtime.appid}`);
        }
        // #endif

        // #ifndef APP-PLUS
        uni.openSetting({
          success: res => {
            console.log('打开设置成功:', res);
          },
          fail: err => {
            console.error('打开设置失败:', err);
            uni.showToast({ title: '无法打开设置页面', icon: 'none' });
          },
        });
        // #endif
      } catch (e2) {
        console.error('备选打开设置方式错误:', e2);
        uni.showToast({ title: '无法打开设置页面', icon: 'none' });
      }
    }
  } else {
    console.log('1111111', plus.os.name);
    console.error('permissionOpenSettings不是函数或为null');
    // 如果permissionOpenSettings不是函数，直接尝试打开设置
    try {
      // #ifdef APP-PLUS
      const isIOS = plus.os.name === 'iOS';
      if (isIOS) {
        try {
          // 对于iOS 10+，使用新的API
          const UIApplication = plus.ios.import('UIApplication');
          const application = UIApplication.sharedApplication();
          const NSURL = plus.ios.import('NSURL');
          const settingURL = NSURL.URLWithString('app-settings:');

          // 检查iOS版本
          const iosVersion = plus.os.version || '9.0';
          if (parseInt(iosVersion) >= 10) {
            // iOS 10+ 使用新方法
            console.log('使用iOS 10+方法打开设置');
            const options = plus.ios.newObject('NSDictionary');
            application.openURL_options_completionHandler(settingURL, options, null);
            plus.ios.deleteObject(options);
          } else {
            // iOS 9及以下使用旧方法
            application.openURL(settingURL);
          }

          plus.ios.deleteObject(settingURL);
          plus.ios.deleteObject(NSURL);
          plus.ios.deleteObject(application);
        } catch (iosError) {
          console.error('iOS原生方法打开设置失败:', iosError);
          // 备选方案
          plus.runtime.openURL('app-settings:');
        }
      } else {
        // Android
        plus.runtime.openURL(`package:${plus.runtime.appid}`);
      }
      // #endif

      // #ifndef APP-PLUS
      uni.openSetting({
        success: res => {
          console.log('打开设置成功:', res);
        },
        fail: err => {
          console.error('打开设置失败:', err);
          uni.showToast({ title: '无法打开设置页面', icon: 'none' });
        },
      });
      // #endif
    } catch (e) {
      console.error('直接打开设置页面错误:', e);
      uni.showToast({ title: '无法打开设置页面', icon: 'none' });
    }
  }
}
</script>

<template>
  <LkToast ref="utoastRef"></LkToast>
  <!-- 权限弹窗组件 -->
  <LKpermissionModal
    v-model:show="permissionModalShow"
    :title="permissionModalTitle"
    :content="permissionModalContent"
    cancel-text="取消"
    confirm-text="前往设置"
    @cancel="handlePermissionCancel"
    @confirm="handlePermissionConfirm"
  />
  <view class="modify-container" :style="{ height: `calc(100vh - ${modifyNavbarHeight}px)` }">
    <u-navbar class="modify-navbar" :autoBack="true" placeholder border bgColor="#f6f6fc">
      <template #center>
        <view class="modify-navbar-title">个人信息</view>
      </template>
    </u-navbar>
    <view class="container">
      <view class="content">
        <view class="content-item">
          <view class="content-item-label">头像</view>
          <view class="content-item-avatar">
            <up-image
              width="84rpx"
              height="84rpx"
              class="avatar-image"
              mode="scaleToFill"
              :show-loading="avatarLoading"
              :src="userInfo.avatar"
              :fade="true"
              @click="chooseImage"
            >
              <template v-slot:loading>
                <up-loading-icon></up-loading-icon>
              </template>
            </up-image>
          </view>
        </view>
        <view class="content-item">
          <view class="content-item-label">姓名</view>
          <view class="content-item-value">
            <input
              v-model="userInfo.username"
              class="content-item-input"
              type="text"
              placeholder="请输入"
              maxlength="10"
              min="2"
              :disabled="isStudent"
            />
          </view>
        </view>
        <!-- 性别 -->
        <view class="content-item">
          <view class="content-item-label">性别</view>
          <view class="content-item-gender">
            <up-radio-group placement="row" v-model="userInfo.gender" :disabled="isStudent">
              <up-radio
                :customStyle="{ marginBottom: '8px', marginRight: '16px' }"
                label="男"
                :name="1"
              >
              </up-radio>
              <up-radio :customStyle="{ marginBottom: '8px' }" label="女" :name="2"> </up-radio>
            </up-radio-group>
          </view>
        </view>

        <view class="content-item">
          <view class="content-item-label">{{ isStudent ? '账号' : '手机号' }}</view>
          <view class="content-item-value">
            <text class="phone">{{ isStudent ? userInfo.account : userInfo.phone }}</text>
          </view>
        </view>

        <!-- 所在班级 (仅学生模式显示) -->
        <view class="content-item" v-if="isStudent && userInfo.clazzName">
          <view class="content-item-label">所在班级</view>
          <view class="content-item-value">
            <text class="class-name">{{ userInfo.gradeName }}{{ userInfo.clazzName }}</text>
          </view>
        </view>
      </view>

      <LkButton
        @click="hadleSaveUserInfo"
        class="save-btn"
        type="primary"
        size="large"
        shape="round"
        >保存</LkButton
      >
    </view>
  </view>
</template>

<style lang="scss">
* {
  box-sizing: border-box;
}
.modify-navbar-title {
  font-size: 18 * 2rpx;
  font-weight: 600;
  color: #1d2129;
}
.modify-container {
  background-color: #f6f6fc;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f6f6fc;
  padding: 0 16 * 2rpx;
}
.content {
  height: auto; /* 改为自适应高度 */
  min-height: 242 * 2rpx; /* 保持最小高度 */
  background-color: #fff;
  border-radius: 12 * 2rpx;
  padding-left: 16 * 2rpx;
  /* padding-right: 0; */
  margin-top: 16 * 2rpx;
}
.content-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56 * 2rpx;
  border-bottom: 0.5px solid #e7e7e7;
  padding-right: 16 * 2rpx;

  &:nth-child(1) {
    height: 74 * 2rpx;
  }

  &:last-child {
    border-bottom: none;
  }

  .content-item-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42 * 2rpx;
    height: 42 * 2rpx;
    border-radius: 50%;
    overflow: hidden;
    image {
      width: 100%;
      height: 100%;
    }
    .avatar-image {
      width: 100%;
      height: 100%;
    }
  }

  .content-item-input {
    width: 160 * 2rpx;
    height: 24 * 2rpx;
    font-size: 16 * 2rpx;
    text-align: right;
  }

  .content-item-gender {
    display: flex;
  }

  .phone {
    color: rgba(0, 0, 0, 0.4);
  }

  .class-name {
    color: rgba(0, 0, 0, 0.6);
    font-size: 16 * 2rpx;
  }
}
.save-btn {
  width: 100%;
  margin-top: auto;
  margin-bottom: 30 * 2rpx;
}
</style>
