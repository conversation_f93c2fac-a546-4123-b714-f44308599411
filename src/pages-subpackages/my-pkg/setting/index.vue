<script setup lang="ts">
import { useUserStore } from '@/store/userStore';
import { ref, computed, onMounted } from 'vue';
import { useSystemStore } from '@/store/systemStore';
import { unregister } from '@/api/my-pkg/setting';
import { RouteConfigManager } from '@/router/config';

const userStore = useUserStore();
const isStudent = ref(false);

onMounted(() => {
  try {
    const userMode = uni.getStorageSync('userMode');
    isStudent.value = userMode === 'student';
  } catch (e) {
    console.error('获取用户模式失败:', e);
  }
});

const buttonStyle = {
  height: '84rpx',
  borderRadius: '100rpx',
  fontSize: '32rpx',
  fontWeight: '600',
  marginBottom: '40rpx',
  border: 'none',
};
const linkList = [
  {
    text: '关于我们',
    url: '/pages-subpackages/my-pkg/about/index',
  },
];
function navBack() {
  uni.navigateBack();
}

const systemStore = useSystemStore();
const uNavbarHeight = computed(() => systemStore.getNavBarHeight);

// 确认弹窗引用
const unregisterConfirmRef = ref();

// 注销账号
function handleUnregister() {
  unregisterConfirmRef.value
    ?.open({
      title: '注销账号',
      content: '注销当前登录账号及角色权限后用户数据将丢失，确定注销账号？',
      confirmButtonText: '注销账号',
    })
    .then(() => {
      unregister()
        .then(() => {
          handleLogout();
        })
        .catch((error: any) => {
          uni.showToast({
            title: error.msg || '注销失败，请重试',
            icon: 'none',
          });
        });
    })
    .catch(() => {
      // 用户取消注销
    });
}

function handleLogout() {
  if (userStore.userInfo) {
    userStore.logout();
    uni.reLaunch({
      url: RouteConfigManager.getLoginPage(),
    });
  }
}
</script>

<template>
  <view class="setting-page">
    <!-- 状态栏和导航栏 -->
    <u-navbar bgColor="#FFFFFF" :border="true" :borderStyle="{ color: '#0000001a' }">
      <template #left>
        <view class="back-icon" @click="navBack">
          <u-icon name="arrow-left" color="#000000"></u-icon>
        </view>
      </template>
      <template #center>
        <text class="nav-title">设置</text>
      </template>
    </u-navbar>
    <!-- 表单区域 -->
    <view class="container" :style="{ paddingTop: `${uNavbarHeight}px` }">
      <view class="setting">
        <LkLinkList :list="linkList" />

        <LkLinkList
          v-if="!isStudent"
          style="margin-top: 20rpx"
          :list="[
            {
              text: '注销账号',
            },
          ]"
          @click="handleUnregister"
        />
      </view>
      <!-- 退出登录 -->
      <LkButton
        class="button"
        shape="round"
        :disabled="!userStore.userInfo"
        :type="'plain'"
        :customStyle="buttonStyle"
        @click="handleLogout"
      >
        退出登录
      </LkButton>
    </view>
    <up-safe-bottom></up-safe-bottom>

    <!-- 注销确认弹窗 -->
    <LKPopUpDiaLog ref="unregisterConfirmRef" />
  </view>
</template>

<style lang="scss" scoped>
.nav-title {
  font-size: 18 * 2rpx;
  font-weight: 600;
  color: #1d2129;
}
.setting-page {
  background-color: #f6f6fd;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx 20px 40rpx;
  box-sizing: border-box;
  .setting {
    width: 100%;
  }
  .container {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    .button {
      width: 100%;
      margin-bottom: 20px;
      padding: 32rpx 0rpx;
    }
  }

  .unregister-section {
    background-color: #fff;
    border-radius: 24rpx;
    margin-top: 20rpx;

    .unregister-item {
      padding: 8rpx 32rpx;
      height: 112rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      width: 100%;

      .unregister-item-content {
        display: flex;
        align-items: center;
        flex: 1;

        text {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 32rpx;
          color: rgba(0, 0, 0, 0.9);
        }
      }
    }
  }
}
</style>
