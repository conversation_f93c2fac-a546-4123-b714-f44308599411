<script setup lang="ts">
import { useSystemStore } from '@/store/systemStore';
import LkSvg from '@/components/svg/index.vue';
import { ref, computed } from 'vue';
import RecycleBinList from './RecycleBinList.vue';
import {
  deleteRecycleBinFile,
  recoveryRecycleBinFile,
  batchDeleteRecycleBinFile,
  batchRecoveryRecycleBinFile,
  hasParent,
  batchHasParent,
  getSpaceFileList,
  getMySpaceFileList,
} from '@/api/database';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';
import LkTree from '@/components/LkTree/index.vue';
import type { TreeItem } from '@/components/LkTree/types';
import LkButton from '@/components/LkButton/index.vue';
import LkToast from '@/components/LkToast/index.vue';

const navBack = () => {
  uni.navigateBack();
};
const searchValue = ref('');
const uToastRef = ref();

const placeholderStyle = 'color: #86909C; font-size: 28rpx;';

const systemStore = useSystemStore();
const uNavbarHeight = computed(() => systemStore.getNavBarHeight);

const optionsList = ref<any[]>([
  {
    style: {
      backgroundColor: '#A6A6A6',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/23b8c65f90b28a6b6af694668aadd869.svg',
    actionType: 'more',
  },
  {
    style: {
      backgroundColor: '#D54941',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/bf5e481fc72554fa10d8a5c6390553ab.svg',
    actionType: 'delete',
  },
]);

const recycleBinListRef = ref<InstanceType<typeof RecycleBinList>>();
const lkDatabasePopupRef = ref<InstanceType<typeof LkDatabasePopup>>();
const isRecovery = ref(false);
const isDel = ref(false);
const isBatchDel = ref(false);
const isBatchRecovery = ref(false);
const curItem = ref<any>(null);
const isEditing = ref(false);
const batchSelectedIdArr = ref<any[]>([]);
const batchSelectedArr = ref<any[]>([]);
const pathStack = ref<any[]>([]);
const selectErrorText = ref('');

const isInDetailView = computed(() => pathStack.value.length > 0);

const recoveryBizType = computed(() => {
  return curItem.value?.bizType || batchSelectedArr.value[0]?.bizType;
});

const clickOption = async (payload: { eventData: any; itemData: any; fn: Function }) => {
  const { eventData: optionsData, itemData: currentItem, fn } = payload;
  const currentOptions = fn(currentItem);
  const selectedOption = currentOptions?.[optionsData.index];

  console.log('选中的操作类型:', selectedOption?.actionType);

  if (selectedOption?.actionType === 'more') {
    // 恢复
    console.log('恢复');
    const isHasParent = await hasParent({ id: currentItem.id });
    console.log('父级是否存在:', isHasParent);
    isBatchRecovery.value = false;
    // 父级目录当前还存在,则恢复到父级
    if (isHasParent) {
      const res = await recoveryRecycleBinFile({ id: currentItem.id });
      uToastRef.value.show({
        message: '文件已恢复成功',
        type: 'success',
      });
      console.log('恢复成功', res);
      // 拉取空间数据并重新渲染
      recycleBinListRef.value?.refresh();
    } else {
      // 显示弹窗-选择恢复路径
      isRecovery.value = true;
      curItem.value = currentItem;
      // 清空多选项
      batchSelectedIdArr.value = [];
    }
    // 删除
  } else if (selectedOption?.actionType === 'delete') {
    console.log('删除');
    isDel.value = true;
    isBatchDel.value = false;
    curItem.value = currentItem;
    // 清空多选项
    batchSelectedArr.value = [];
  }
};

const clickSelectRecoveryLocation = async () => {
  console.log('选择恢复位置, bizType 为:', recoveryBizType.value);
  isRecovery.value = false; // 先关闭提示弹窗
  const bizType = recoveryBizType.value;

  if (bizType === '1') {
    console.log(111);
    // 学校空间, 使用 LkTree
    openTree(bizType);
  } else if (bizType === '2') {
    console.log(222);
    // 我的空间, 使用 LkDatabasePopup
    lkDatabasePopupRef.value?.openPopup();
  }
};

// 是否可以恢复
const isCanRecovery = computed(() => {
  // 学校空间和我的空间互斥
  const isNotSameBizType = batchSelectedArr.value.some(
    (item: any) => item.bizType !== batchSelectedArr.value[0]?.bizType
  );
  // 空间和文件/文件夹互斥
  const isNotSameFileType = batchSelectedArr.value.some((item: any) => {
    const firstFileType = batchSelectedArr.value[0]?.fileType;
    // 如果第一个是1或2，则后续只能是1或2
    if (firstFileType === 1 || firstFileType === 2) {
      return item.fileType === 3;
    }
    // 如果第一个是3，则后续只能是3
    if (firstFileType === 3) {
      return item.fileType === 1 || item.fileType === 2;
    }
    selectErrorText.value = '不能同时选择数据空间和文件/文件夹';
    return false;
  });
  // 学校空间和我的空间互斥
  if (isNotSameBizType) {
    selectErrorText.value = '不能同时选择学校数据空间和我的数据空间的内容';
    return false;
    // 空间和文件/文件夹互斥
  } else if (isNotSameFileType) {
    selectErrorText.value = '不能同时选择数据空间和文件/文件夹';
    return false;
    // 没选任何文件
  } else if (batchSelectedArr.value.length === 0) {
    selectErrorText.value = '请选择文件';
    return false;
    // 可以恢复
  } else {
    return true;
  }
});

const clickBatchRecovery = async () => {
  console.log('批量恢复');
  // 未选择文件
  if (batchSelectedIdArr.value.length === 0) {
    uToastRef.value.show({
      message: '请选择文件',
      type: 'info',
    });
    return;
  }
  // 检测是否不可使用恢复功能
  if (!isCanRecovery.value) {
    uni.showToast({
      title: selectErrorText.value,
      icon: 'none',
    });
    return;
  }
  const isBatchHasParent = await batchHasParent(batchSelectedIdArr.value);
  // 父级目录当前还存在,则恢复到父级
  if (isBatchHasParent) {
    const res = await batchRecoveryRecycleBinFile(batchSelectedIdArr.value);
    uToastRef.value.show({
      message: '文件已恢复成功',
      type: 'success',
    });
    console.log('批量恢复成功', res);
    // 拉取空间数据并重新渲染
    recycleBinListRef.value?.refresh();
    isEditing.value = false;
  } else {
    isRecovery.value = true;
    isBatchRecovery.value = true;
  }
};

const confirmRecovery = async (currentParentId: string) => {
  if (isBatchRecovery.value) {
    // 构造包含 parentId 的新数组
    const recoveryPayload = batchSelectedIdArr.value.map(item => ({
      ...item,
      parentId: currentParentId || '0',
    }));
    const res = await batchRecoveryRecycleBinFile(recoveryPayload);
    uToastRef.value.show({
      message: '文件已恢复成功',
      type: 'success',
    });
    console.log('批量恢复成功', res);
    // 拉取空间数据并重新渲染
    recycleBinListRef.value?.refresh();
    isRecovery.value = false;
    isBatchRecovery.value = false;
    isEditing.value = false;
    // 如果是通过 LkDatabasePopup 恢复的，则关闭它
    if (recoveryBizType.value === '2') {
      lkDatabasePopupRef.value?.closePopup();
    }
  } else {
    const res = await recoveryRecycleBinFile({
      id: curItem.value.id,
      parentId: currentParentId || '0',
    });
    uToastRef.value.show({
      message: '文件已恢复成功',
      type: 'success',
    });
    console.log('恢复成功', res);
    // 拉取空间数据并重新渲染
    recycleBinListRef.value?.refresh();
    isRecovery.value = false;
    // 如果是通过 LkDatabasePopup 恢复的，则关闭它
    if (recoveryBizType.value === '2') {
      lkDatabasePopupRef.value?.closePopup();
    }
  }
};

const clickBatchDel = () => {
  console.log('批量删除');
  if (batchSelectedIdArr.value.length === 0) {
    // 未选择文件
    uToastRef.value.show({
      message: '请选择文件',
      type: 'info',
    });
    return;
  }
  isDel.value = true;
  isBatchDel.value = true;
};

const confirmDel = async () => {
  if (isBatchDel.value) {
    console.log('确认批量删除');
    await batchDeleteRecycleBinFile(batchSelectedIdArr.value);
    uToastRef.value.show({
      message: '文件彻底删除成功',
      type: 'success',
    });
  } else {
    console.log('确认删除');
    await deleteRecycleBinFile({ id: curItem.value.id });
    uToastRef.value.show({
      message: '文件彻底删除成功',
      type: 'success',
    });
  }
  // 拉取空间数据并重新渲染
  recycleBinListRef.value?.refresh();
  isDel.value = false;
  isBatchDel.value = false;
  isEditing.value = false;
};

const clickEditItem = () => {
  isEditing.value = !isEditing.value;
  recycleBinListRef.value?.toggleCheckAll(false);
};

const updateSelectedItems = (items: any[]) => {
  batchSelectedIdArr.value = items.map(item => ({ id: item.id }));
  batchSelectedArr.value = items;
  // 清空单选项
  curItem.value = null;
  console.log(
    '更新选中项:',
    batchSelectedIdArr.value.map(i => i.id)
  );
};

const clickCheckAll = () => {
  console.log('全选');
  recycleBinListRef.value?.toggleCheckAll();
};

// --- Tree component logic ---
const showTree = ref(false);
const treeData = ref<TreeItem[]>([]);
const pendingRecoveryNode = ref<TreeItem | null>(null);
const currentTreeBizType = ref('');

const mapApiDataToTreeItem = (item: any): TreeItem => ({
  id: item.id,
  name: item.spaceName,
  fileType: item.fileType,
  isLeaf: !item.hasChildren,
  childrenLoaded: false,
  children: [],
});

function findNodeAndSetChildren(nodes: TreeItem[], nodeId: string | number, children: TreeItem[]) {
  for (const node of nodes) {
    if (node.id === nodeId) {
      node.children = children;
      node.childrenLoaded = true;
      if (children.length === 0) {
        node.isLeaf = true;
      }
      return true;
    }
    if (node.children) {
      if (findNodeAndSetChildren(node.children, nodeId, children)) {
        return true;
      }
    }
  }
  return false;
}

const handleLoadChildren = async (node: TreeItem) => {
  if (node.fileType !== 3) return;
  // 根据当前树的 bizType 来决定调用哪个API
  const api = currentTreeBizType.value === '1' ? getSpaceFileList : getMySpaceFileList;
  const res = await api({ parentId: node.id, privilege: 4 });
  const records = res?.records || [];
  const filteredRecords = records.filter((item: any) => item.fileType === 3);
  const childrenItems = filteredRecords.map(mapApiDataToTreeItem);
  findNodeAndSetChildren(treeData.value, node.id, childrenItems);
};

const loadInitialTree = async (bizType: string) => {
  // 根据当前树的 bizType 来决定调用哪个API
  const api = bizType === '1' ? getSpaceFileList : getMySpaceFileList;
  const res = await api({ parentId: '0', privilege: 4 });
  const records = res?.records || [];
  const filteredRecords = records.filter((item: any) => item.fileType === 3);
  treeData.value = filteredRecords.map(mapApiDataToTreeItem);
};

const openTree = (bizType: string) => {
  currentTreeBizType.value = bizType;
  loadInitialTree(bizType);
  showTree.value = true;
};

const handleTreeSelect = (node: TreeItem) => {
  console.log('已选择恢复位置节点:', node);
  pendingRecoveryNode.value = node;
};

const handleTreeSave = () => {
  console.log('确认恢复到:', pendingRecoveryNode.value?.name);
  confirmRecovery(pendingRecoveryNode.value?.id as string);
  showTree.value = false; // 手动关闭弹窗
  pendingRecoveryNode.value = null; // 清空暂存
};

const goBack = () => {
  if (pathStack.value.length > 0) {
    recycleBinListRef.value?.goBackOneLevel();
    pathStack.value.pop();
  } else {
    navBack();
  }
};

const updatePath = (item: any) => {
  pathStack.value.push(item);
};
</script>
<template>
  <view class="container">
    <!-- header -->
    <u-navbar>
      <template #left>
        <up-icon name="arrow-left" size="24px" color="#000" @tap="goBack" />
      </template>
      <template #center>
        <text class="nav-title">回收站</text>
      </template>
      <template #right>
        <template v-if="!isInDetailView">
          <LkSvg
            v-if="!isEditing"
            width="24px"
            height="24px"
            src="/static/recycleBin/editItem.svg"
            @click="clickEditItem"
          />
          <text v-else class="nav-action-text" @click="clickCheckAll">全选</text>
        </template>
      </template>
    </u-navbar>
    <view class="main" :style="{ paddingTop: `${uNavbarHeight}px` }">
      <!-- search -->
      <view class="wrapSearch">
        <view class="searchLogo">
          <LkSvg width="24px" height="24px" src="/static/recycleBin/search.svg" />
          <up-input
            v-model="searchValue"
            :show-action="false"
            placeholder="请输入关键词搜索"
            border="none"
            :placeholderStyle="placeholderStyle"
            fontSize="32rpx"
            clearable
          ></up-input>
        </view>
      </view>
      <!-- 全局数据空间组件 -->
      <RecycleBinList
        ref="recycleBinListRef"
        :optionsList="optionsList"
        @clickOption="clickOption"
        :isEditing="isEditing"
        :disable-swipe="isInDetailView"
        @updateSelectedItems="updateSelectedItems"
        @updatePath="updatePath"
        :search-keyword="searchValue"
      />
    </view>
    <view v-if="isRecovery">
      <up-modal
        :show="isRecovery"
        @close="isRecovery = false"
        :showConfirmButton="false"
        :showCancelButton="false"
        width="600rpx"
      >
        <view class="modal-content-wrapper">
          <view class="title">提示</view>
          <view class="content">
            恢复文件原所在上级文件夹或空间不存在，如需恢复，请选择恢复存储地址
          </view>
          <view class="wrapBtn">
            <LkButton type="plain" block @click="isRecovery = false">取消</LkButton>
            <LkButton type="primary" block @click="clickSelectRecoveryLocation"
              >选择恢复地址</LkButton
            >
          </view>
        </view>
      </up-modal>
    </view>
    <view v-if="isDel">
      <up-modal
        :show="isDel"
        @close="isDel = false"
        :showConfirmButton="false"
        :closeOnClickOverlay="true"
      >
        <view class="title">彻底删除提示</view>
        <view class="content"> 彻底删除文件将不可找回文件，确定删除文件？ </view>
        <view class="wrapBtn">
          <LkButton type="plain" block @click="isDel = false">取消</LkButton>
          <LkButton type="danger" block @click="confirmDel">删除</LkButton>
        </view>
      </up-modal>
    </view>
    <LkDatabasePopup
      ref="lkDatabasePopupRef"
      :bizType="recoveryBizType"
      :layoutType="4"
      @confirmRecovery="confirmRecovery"
    ></LkDatabasePopup>
    <LkTree
      v-model:show="showTree"
      :data="treeData"
      :is-save-btn="true"
      :on-load-children="handleLoadChildren"
      title="选择恢复位置"
      @select="handleTreeSelect"
      @save="handleTreeSave"
    />
    <view v-if="isEditing" class="wrapEdit">
      <view
        class="editItem"
        @click="clickBatchRecovery"
        :class="{ disabled: !isCanRecovery, actived: isCanRecovery }"
      >
        <LkSvg width="24px" height="24px" src="/static/database/tool_share.svg" />
        <view class="txt">恢复</view>
      </view>
      <view
        class="editItem"
        @click="clickBatchDel"
        :class="{ disabled: batchSelectedIdArr.length === 0 }"
      >
        <LkSvg width="24px" height="24px" src="/static/database/tool_delete.svg" />
        <view class="txt">删除</view>
      </view>
      <view class="editItem" @click="clickEditItem">
        <LkSvg width="24px" height="24px" src="/static/recycleBin/checkAll_close.svg" />
        <view class="txt close">取消</view>
      </view>
    </view>
    <LkToast ref="uToastRef" />
  </view>
</template>
<style lang="scss" scoped>
.container {
  padding: 0 16px;
  background-color: white;
  min-height: 100vh;
  .nav-title {
    font-weight: 600;
    font-size: 18px;
    color: #1d2129;
  }
  .nav-action-text {
    font-size: 16px;
    color: #7d4dff;
    font-weight: 600;
  }
  .main {
  }
  .wrapSearch {
    width: 100%;
    background: #f3f3f3;
    border-radius: 8px;
    padding: 8px 12px;
    margin-top: 8px;
    .searchLogo {
      display: flex;
      align-items: center;
      .u-input {
        margin-left: 4px;
      }
    }
  }
}
.wrapEdit {
  width: calc(100vw - 20px);
  position: fixed;
  bottom: 14px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  padding: 8.5px 57.83px;
  border-top: 1px solid #f4f4f4;
  border-radius: 14px;
  box-shadow:
    0px -1px 15.9px 0px rgba(0, 0, 0, 0.14),
    0px 1px 10px 0px rgba(0, 0, 0, 0.05),
    0px 2px 6.9px -1px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-between;
  .editItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    &.disabled {
      opacity: 0.5;
    }
    .txt {
      font-size: 26rpx;
      color: #303133;
      margin-top: 4px;
      &.close {
        color: #d54941;
      }
    }
  }
}

.modal-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

::v-deep .u-modal__content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-content-wrapper,
::v-deep .u-modal__content {
  .title {
    color: #1d2129;
    font-size: 36rpx;
    font-weight: 600;
  }

  .content {
    color: #4e5969;
    font-size: 32rpx;
    margin-top: 8px;
    text-align: center;
  }

  .wrapBtn {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 24px;
    .lk-button {
      &:not(:first-child) {
        margin-left: 12px;
      }
    }
  }
}
</style>
