<script setup lang="ts">
import LkSvg from '@/components/svg/index.vue';
import { getRecycleBinFileList, getRecycleBinFileSubList } from '@/api/database';
import { ref, watch } from 'vue';
import LkPageList from '@/components/LkPageList/index.vue';

const props = defineProps({
  isEditing: {
    type: Boolean,
    default: false,
  },
  disableSwipe: {
    type: Boolean,
    default: false,
  },
  optionsList: {
    type: Array as () => any[],
    default: () => [{}],
  },
  searchKeyword: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['updateSelectedItems', 'clickOption', 'dataLoaded', 'updatePath']);

const lkPageListRef = ref<InstanceType<typeof LkPageList>>();
const currentList = ref<any[]>([]);
const checkboxValue = ref<string[]>([]);
const selectedItems = ref<any[]>([]);
const currentParentId = ref('0');
const clickedItemData = ref<any>(null);
const baseItemForSubList = ref<any>(null);
const pathStack = ref<any[]>([]);

const fetchFileList = async (params: any) => {
  const apiParams = {
    ...params,
    searchKey: props.searchKeyword,
  };

  // For sub-lists, we only fetch once.
  if (currentParentId.value !== '0' && params.pageNo > 1) {
    return { records: [], total: 0 };
  }

  let result;
  if (currentParentId.value === '0') {
    result = await getRecycleBinFileList(apiParams);
  } else {
    const itemData = clickedItemData.value;
    if (!itemData || !baseItemForSubList.value) {
      return { records: [], total: 0 };
    }

    const isFirstSubLevel = itemData.id === baseItemForSubList.value.id;

    const subListResult = await getRecycleBinFileSubList({
      parentId: isFirstSubLevel ? itemData.bizId : itemData.id,
      id: baseItemForSubList.value.id,
      fileType: itemData.fileType,
      tmbId: baseItemForSubList.value.tmbId,
      bizType: baseItemForSubList.value.bizType,
      current: 1,
      size: 10,
    });
    if (subListResult && subListResult.records) {
      const parentData = clickedItemData.value;
      const augmentedRecords = subListResult.records.map((subItem: any) => ({
        ...subItem,
        deleteTime: parentData.deleteTime,
        path: parentData.path,
        deleterName: parentData.deleterName,
      }));
      result = {
        records: augmentedRecords,
        total: augmentedRecords.length,
      };
    } else {
      result = { records: [], total: 0 };
    }
  }

  if (result?.records) {
    emit('dataLoaded', result.records);
  }
  return result;
};

const processSpaceFileData = (data: any) => {
  console.log(data);
  return data;
};

const clickItem = async (item: any) => {
  console.log('点击的item值为:', item);
  if (props.isEditing) return;

  if (item.fileType === 3 || item.fileType === 2) {
    pathStack.value.push(item);
    if (currentParentId.value === '0') {
      baseItemForSubList.value = item;
    }
    currentParentId.value = item.id;
    clickedItemData.value = item;
    emit('updatePath', item);
    lkPageListRef.value?.refresh();
  } else {
    console.log('点击了文件:', item.fileName);
  }
};

const goBackOneLevel = () => {
  if (pathStack.value.length === 0) return;

  pathStack.value.pop();

  if (pathStack.value.length > 0) {
    const lastItem = pathStack.value[pathStack.value.length - 1];
    currentParentId.value = lastItem.id;
    clickedItemData.value = lastItem;
  } else {
    currentParentId.value = '0';
    clickedItemData.value = null;
    baseItemForSubList.value = null;
  }
  lkPageListRef.value?.refresh();
};

const fileIcon = (item: any): string => {
  if (item.fileType === 3) return `/static/fileTypeIcon/space.svg`;
  if (item.fileType === 2) return `/static/fileTypeIcon/folder.svg`;
  if (item.fileType === 1) {
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
};

const getOptionsByItem = (item: any) => {
  if (!props.optionsList || props.optionsList.length === 0) return [{}];
  if (item.fileType === 1 || item.fileType === 2 || item.fileType === 3) {
    return props.optionsList;
  }
  return [{}];
};

const clickOption = (obj: any, item: any) => {
  emit('clickOption', { eventData: obj, itemData: item, fn: getOptionsByItem });
};

const syncList = (listFromSlot: any[]) => {
  currentList.value = listFromSlot;
};

const changeCheckbox = (value: string[], item: any) => {
  const index = selectedItems.value.findIndex(i => i.id === item.id);
  if (index > -1) {
    selectedItems.value.splice(index, 1);
  } else {
    selectedItems.value.push(item);
  }
  emit('updateSelectedItems', [...selectedItems.value]);
};

watch(
  () => props.isEditing,
  (isEditing, wasEditing) => {
    if (isEditing !== wasEditing) {
      checkboxValue.value = [];
      selectedItems.value = [];
      emit('updateSelectedItems', []);
    }
  }
);

watch(
  () => props.searchKeyword,
  () => {
    refresh();
  }
);

const refresh = (resetPath = false) => {
  if (resetPath) {
    currentParentId.value = '0';
    clickedItemData.value = null;
    baseItemForSubList.value = null;
    pathStack.value = [];
  }
  lkPageListRef.value?.refresh();
};

const toggleCheckAll = (forceState?: boolean) => {
  // 使用布尔变量判断是否为全选状态
  const isAllSelected =
    currentList.value.length > 0 && checkboxValue.value.length === currentList.value.length;

  // 如果明确指定为false或当前已是全选状态，则清空选择
  if (forceState === false || isAllSelected) {
    checkboxValue.value = [];
    selectedItems.value = [];
  } else {
    // 全选逻辑：只有当所有项目都符合规则时，才允许全选

    // 检查列表中是否存在不同的bizType
    const hasDifferentBizTypes =
      currentList.value.length > 1 && new Set(currentList.value.map(item => item.bizType)).size > 1;

    // 检查列表中是否同时存在空间(fileType=3)和文件/文件夹(fileType=1/2)
    const hasSpaceAndFiles =
      currentList.value.some(item => item.fileType === 3) &&
      currentList.value.some(item => item.fileType === 1 || item.fileType === 2);

    // 如果存在规则冲突，则不执行全选
    if (hasSpaceAndFiles) {
      uni.showToast({
        title: '不能同时选择数据空间和文件/文件夹',
        icon: 'none',
      });
      return;
    } else if (hasDifferentBizTypes) {
      uni.showToast({
        title: '不能同时选择学校数据空间和我的数据空间的内容',
        icon: 'none',
      });
      return;
    }

    // 所有项目都符合规则，执行全选
    checkboxValue.value = currentList.value.map(item => item.id);
    selectedItems.value = [...currentList.value];
  }

  // 更新选中项
  emit('updateSelectedItems', [...selectedItems.value]);
};

const getSpacePath = (item: any) => {
  let segments = [];
  if (item.path && item.path.length > 3) {
    segments = [
      { type: 'text', content: item.path[0] },
      { type: 'separator' },
      { type: 'text', content: '...' },
      { type: 'separator' },
      { type: 'text', content: item.path[item.path.length - 1] },
    ];
  } else if (item.path && item.path.length > 0) {
    segments = item.path
      .map((p: string) => [{ type: 'text', content: p }, { type: 'separator' }])
      .flat()
      .slice(0, -1);
  } else {
    segments = [{ type: 'text', content: item.location || '' }];
  }
  return segments;
};

defineExpose({ refresh, toggleCheckAll, goBackOneLevel });
</script>

<template>
  <view class="recycle-bin-list">
    <u-swipe-action>
      <LkPageList
        ref="lkPageListRef"
        :fetch-method="fetchFileList"
        :extra-params="{ parentId: currentParentId }"
        :process-data="processSpaceFileData"
      >
        <template #default="{ list }">
          <component
            :is="
              () => {
                syncList(list);
                return null;
              }
            "
          />
          <u-checkbox-group v-model="checkboxValue" placement="column">
            <u-swipe-action-item
              v-for="item in list"
              :key="item.id"
              :options="getOptionsByItem(item)"
              @click="clickOption($event, item)"
              :disabled="disableSwipe"
            >
              <view class="list-item" @click="clickItem(item)">
                <view class="wrapContent">
                  <up-checkbox
                    v-if="isEditing"
                    :name="item.id"
                    activeColor="#6d51f6"
                    shape="circle"
                    @change="changeCheckbox($event, item)"
                  ></up-checkbox>
                  <view class="content">
                    <LkSvg
                      class="fileIcon"
                      width="84rpx"
                      height="84rpx"
                      :src="fileIcon(item)"
                      errorSrc="/static/fileTypeIcon/unknown.svg"
                    />
                    <view class="wrapTxt">
                      <view class="fileName">{{ item.fileName }}</view>
                      <view class="fileInfo">
                        <text class="fileSize" v-if="item.file">{{
                          formatFileSize(item.file.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.file && item.updateTime"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="bottom">
                  <LkSvg width="36rpx" height="36rpx" src="/static/recycleBin/pathBg.svg" />
                  <view class="wrapPath">
                    <template
                      v-for="(segment, index) in getSpacePath(item)"
                      :key="index"
                      class="path"
                    >
                      <view v-if="segment.type === 'text'">{{ segment.content }}</view>
                      <LkSvg
                        class="separator"
                        v-else-if="segment.type === 'separator'"
                        width="4px"
                        height="13px"
                        src="/static/database/pathArrow.svg"
                      />
                    </template>
                  </view>
                  <view class="delUser">
                    {{ item.deleterName }}
                  </view>
                </view>
              </view>
            </u-swipe-action-item>
          </u-checkbox-group>
        </template>
      </LkPageList>
    </u-swipe-action>
  </view>
</template>

<style lang="scss" scoped>
.recycle-bin-list {
  .u-swipe-action-item {
    margin-top: 12px;
    background: #fff;
    box-shadow: 0px 0px 6px 0px rgba(237, 237, 237, 0.62);
    border-radius: 14px;
    border: 1px solid #f4f4f4;
  }
  .u-swipe-action {
    height: calc(100vh - 44px);
  }

  ::v-deep .u-swipe-action-item__content {
    display: flex;
    align-items: center;
  }
  ::v-deep .u-swipe-action-item__right {
    .u-swipe-action-item__right__button {
      &:last-child {
        border-radius: 0 14px 14px 0;
      }
    }
  }

  .list-item {
    display: flex;
    flex-direction: column;
    padding: 14px 16px;
    overflow: hidden;
    width: 100%;

    .wrapContent {
      display: flex;
      align-items: center;

      ::v-deep .u-checkbox {
        flex-shrink: 0;
        margin-right: 12px;
      }

      .content {
        display: flex;
        align-items: center;
        width: 100%;

        .fileIcon {
          flex-shrink: 0;
          margin-right: 10px;
        }

        .wrapTxt {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .fileName {
            color: #1d2129;
            font-size: 32rpx;
          }

          .fileInfo {
            display: flex;
            align-items: center;
            margin-top: 3px;
            font-size: 24rpx;
            color: #86909c;

            .sign {
              width: 1px;
              height: 9px;
              background: #86909c;
              margin: 0 6px;
            }
          }
        }
      }
    }

    .bottom {
      width: 100%;
      margin-top: 12px;
      padding-top: 10px;
      border-top: 1px solid #f3f3f3;
      display: flex;
      align-items: center;
      > uni-image {
        flex-shrink: 0;
      }
      .wrapPath {
        margin-left: 4px;
        color: #86909c;
        font-size: 24rpx;
        display: flex;
        align-items: center;
        .separator {
          margin-left: 3px;
          margin-right: 2px;
        }
      }
      .delUser {
        margin-left: auto;
        color: #86909c;
        font-size: 24rpx;
      }
    }
  }
}
</style>
