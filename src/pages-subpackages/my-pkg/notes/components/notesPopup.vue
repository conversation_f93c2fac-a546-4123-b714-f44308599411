<template>
  <u-popup :show="show" mode="bottom" round="16" @close="handleClose">
    <view class="edit-popup">
      <view class="edit-popup-header">
        <view></view>
        <view class="edit-popup-title">做笔记</view>
        <view class="edit-popup-close" @click="handleClose">
          <u-icon name="close" size="16" color="#1D2129"></u-icon>
        </view>
      </view>
      <view class="edit-popup-content">
        <view class="edit-popup-content-title">笔记</view>
        <up-textarea
          v-model="localContent"
          :placeholder="isEditMode ? '请输入笔记内容...' : '记录你的解题思路,好记性不如烂笔头'"
          height="300"
          :maxlength="10000"
          border="none"
          placeholderStyle="color: #86909C;"
          style="background-color: #f9f9f9; margin-bottom: 60rpx"
        ></up-textarea>
      </view>
      <view class="edit-popup-footer">
        <u-button
          type="primary"
          :text="isEditMode ? '保存' : '保存'"
          @click="handleSave"
          color="#7D4DFF"
          customStyle="border-radius: 200rpx;"
        ></u-button>
      </view>
    </view>
  </u-popup>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, defineOptions, ref, watch } from 'vue';

defineOptions({
  name: 'NotesPopup',
});

interface Props {
  show: boolean;
  isEditMode: boolean;
  noteContent: string;
}

interface Emits {
  (e: 'update:show', value: boolean): void;
  (e: 'update:noteContent', value: string): void;
  (e: 'save'): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  isEditMode: false,
  noteContent: '',
});

const emit = defineEmits<Emits>();

const localContent = ref(props.noteContent);

watch(
  () => props.noteContent,
  newValue => {
    localContent.value = newValue;
  },
  { immediate: true }
);

watch(localContent, newValue => {
  emit('update:noteContent', newValue);
});

const handleClose = () => {
  emit('update:show', false);
  emit('close');
};

const handleSave = () => {
  emit('save');
};
</script>

<style lang="scss" scoped>
.edit-popup {
  padding: 0;
  background-color: #ffffff;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.edit-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.edit-popup-close {
  padding: 10rpx;
  cursor: pointer;
}

.edit-popup-title {
  font-family: 'PingFang SC';
  font-size: 36rpx;
  font-weight: 600;
  color: #1d2129;
  flex: 1;
  text-align: center;
}

.edit-popup-content {
  flex: 1;
  padding: 30rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .edit-popup-content-title {
    font-family: 'PingFang SC';
    font-size: 32rpx;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 20rpx;
    flex-shrink: 0;
  }
}

.edit-popup-footer {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  gap: 20rpx;
  flex-shrink: 0;
  border-top: 1rpx solid #f0f0f0;
}

/* 适配底部安全区 */
:deep(.u-popup) {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
