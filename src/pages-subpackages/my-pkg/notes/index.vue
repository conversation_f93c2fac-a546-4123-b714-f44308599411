<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import {
  getStudentNoteList,
  createStudentNote,
  updateStudentNote,
  deleteStudentNote,
} from '@/api/students/studentnote';
import { NoteType } from '@/constants/students/studentnote';
import { onShow } from '@dcloudio/uni-app';
import type {
  StudentNoteItem,
  StudentNoteAddParams,
  StudentNoteUpdateParams,
} from '@/types/students/studentnote';
import DeletePopup from './components/deletePopup.vue';
import NotesPopup from './components/notesPopup.vue';

// 标签数据
const tabs = ref(['全部', '做题笔记', '学习笔记', '自由笔记']);
const currentTab = ref(0);
// 当前显示的笔记列表
const notesList = ref<StudentNoteItem[]>([]);

// 添加/编辑笔记相关
const showAddPopup = ref(false); //弹窗是否弹出
const newNoteContent = ref(''); //输入框内容
const isEditMode = ref(false); //是否编辑
const currentEditingNote = ref<StudentNoteItem | null>(null);
const textareaKey = ref(0); // 用于强制重新渲染 textarea

// 删除弹窗相关
const deletePopupRef = ref();
const currentDeleteNote = ref<StudentNoteItem | null>(null);
const currentDeleteIndex = ref<number>(-1);
const uToastRef = ref();

// 笔记类型映射
const NOTE_TYPES = {
  EXERCISE: 'exercise',
  STUDY: 'study',
  FREE: 'free',
} as const;

const tabsList = computed(() => {
  return tabs.value.map(item => ({ name: item }));
});

const getSwipeOptions = (note: StudentNoteItem, index: number) => {
  return [
    {
      // text: '编辑',
      icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/7672864e483dc6824b9e052e6bc86f3f.svg',
      style: {
        backgroundColor: '#7D4DFF',
        color: '#ffffff',
        fontSize: '28rpx',
        width: '104rpx',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '0',
      },
      // 传递笔记信息
      noteData: { note, index, action: 'edit' },
    },
    {
      // text: '删除',
      icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/e939e83a7ba8333ad9d02b3f31fc16e5.svg',
      style: {
        backgroundColor: '#D54941',
        color: '#ffffff',
        fontSize: '28rpx',
        width: '104rpx',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '0',
      },
      // 传递笔记信息
      noteData: { note, index, action: 'delete' },
    },
  ];
};

onMounted(() => {
  getNoteList(0);
});

onShow(() => {
  getNoteList(currentTab.value);
});

const getNoteList = async (tabIndex: number) => {
  try {
    let type = 0;
    if (tabIndex === 1) type = 1; // 做题笔记
    if (tabIndex === 2) type = 2; // 学习笔记
    if (tabIndex === 3) type = 3; // 自由笔记

    const res = await getStudentNoteList({
      pageNum: 1,
      pageSize: 999,
      type: type,
    });

    notesList.value = [];

    if (res && res.records && Array.isArray(res.records) && res.records.length > 0) {
      notesList.value = res.records;
    } else {
      console.log('没有找到笔记数据');
    }
  } catch (error) {
    notesList.value = [];
    uni.showToast({
      title: '获取笔记失败',
      icon: 'none',
    });
  }
};

// 时间转换函数
const formatDateTime = (dateStr: string): string => {
  if (!dateStr) return '';

  try {
    let date: Date;

    if (typeof dateStr === 'string') {
      if (/^\d+$/.test(dateStr)) {
        const timestamp = parseInt(dateStr);
        date = new Date(timestamp > 9999999999 ? timestamp : timestamp * 1000);
      } else if (dateStr.includes('CST')) {
        const parts = dateStr.split(' ');
        if (parts.length >= 6) {
          const month = {
            Jan: '01',
            Feb: '02',
            Mar: '03',
            Apr: '04',
            May: '05',
            Jun: '06',
            Jul: '07',
            Aug: '08',
            Sep: '09',
            Oct: '10',
            Nov: '11',
            Dec: '12',
          }[parts[1]];

          const day = parts[2].padStart(2, '0');
          const time = parts[3];
          const year = parts[5];

          const standardDateStr = `${year}-${month}-${day}T${time}+08:00`;
          date = new Date(standardDateStr);
        } else {
          date = new Date(dateStr);
        }
      } else {
        // 处理日期字符串，确保格式正确
        const normalizedDateStr = dateStr.replace(/\s+/g, ' ').trim();
        date = new Date(normalizedDateStr);
      }
    } else {
      date = new Date(dateStr);
    }

    if (isNaN(date.getTime())) {
      console.warn('Invalid date format:', dateStr);
      return '';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('Date formatting error:', error, 'Input:', dateStr);
    return '';
  }
};

// 新增笔记函数
const createNote = async (noteContent: string) => {
  const createParams: StudentNoteAddParams = {
    noteContent,
    refId: 0, // 自由笔记没有关联资源
    type: 3, // 固定为3（自由笔记）
  };

  const result = await createStudentNote(createParams);

  await getNoteList(currentTab.value);

  uni.hideLoading();
  uToastRef.value.show({
    message: '笔记新增成功',
    type: 'success',
  });

  showAddPopup.value = false;
  isEditMode.value = false;
  currentEditingNote.value = null;
  newNoteContent.value = '';

  if (currentTab.value !== 0 && currentTab.value !== 3) {
    handleTabClick({ index: 0 });
  }
};

// 修改笔记函数
const updateNote = async (noteContent: string) => {
  if (!currentEditingNote.value) {
    throw new Error('当前编辑笔记信息丢失');
  }

  const updateParams: StudentNoteUpdateParams = {
    id: currentEditingNote.value.id,
    noteContent,
  };

  const result = await updateStudentNote(updateParams);

  await getNoteList(currentTab.value);

  uni.hideLoading();
  uToastRef.value.show({
    message: '笔记修改成功',
    type: 'success',
  });

  showAddPopup.value = false;
  isEditMode.value = false;
  currentEditingNote.value = null;
  newNoteContent.value = '';
};

// 保存笔记主函数
const saveNote = async () => {
  if (!newNoteContent.value.trim()) {
    uni.showToast({
      title: '请输入笔记内容',
      icon: 'none',
      duration: 2000,
    });
    return;
  }

  try {
    uni.showLoading({
      title: '保存中...',
      mask: true,
    });

    const noteContent = newNoteContent.value.trim();

    if (isEditMode.value && currentEditingNote.value) {
      await updateNote(noteContent);
    } else {
      await createNote(noteContent);
    }
  } catch (error) {
    uni.hideLoading();

    const errorTitle = isEditMode.value ? '修改失败，请重试' : '新增失败，请重试';
    uni.showToast({
      title: errorTitle,
      icon: 'none',
    });
  }
};

// 删除笔记函数
const deleteNote = (noteIndex: number) => {
  const note = notesList.value[noteIndex];
  if (!note) return;

  currentDeleteNote.value = note;
  currentDeleteIndex.value = noteIndex;

  deletePopupRef.value?.onOpen({ fileObj: note });
};

const handleDeleteConfirm = async (note: StudentNoteItem) => {
  try {
    uni.showLoading({
      title: '删除中...',
      mask: true,
    });

    await deleteStudentNote(note.id);
    await getNoteList(currentTab.value);

    uni.hideLoading();
    uToastRef.value.show({
      message: '笔记删除成功',
      type: 'success',
    });
  } catch (error) {
    uni.hideLoading();

    uni.showToast({
      title: '删除失败，请重试',
      icon: 'none',
    });
  }
};

// 处理删除取消
const handleDeleteCancel = () => {
  currentDeleteNote.value = null;
  currentDeleteIndex.value = -1;
};

//滑动选择模块
const handleSwipeClick = (buttonIndex: number, note: StudentNoteItem, noteIndex: number) => {
  console.log(
    '右滑点击:',
    buttonIndex,
    note.id,
    note.noteContent ? note.noteContent.substring(0, 20) : '无内容'
  );

  if (!note) {
    return;
  }

  if (buttonIndex === 0) {
    // 编辑笔记
    if (showAddPopup.value) {
      showAddPopup.value = false;
      nextTick(() => {
        isEditMode.value = true;
        currentEditingNote.value = note;
        newNoteContent.value = note.noteContent;
        textareaKey.value++; // 强制重新渲染 textarea
        showAddPopup.value = true;
      });
    } else {
      isEditMode.value = true;
      currentEditingNote.value = note;
      newNoteContent.value = note.noteContent;
      textareaKey.value++; // 强制重新渲染 textarea
      showAddPopup.value = true;
    }
  } else if (buttonIndex === 1) {
    // 删除笔记
    deleteNote(noteIndex);
  }
};

const handleTabClick = (e: { index: number }) => {
  const index = e.index;
  currentTab.value = index;
  getNoteList(index);

  uni.pageScrollTo({
    scrollTop: 0,
  });
};

const handlePopupClose = () => {
  showAddPopup.value = false;
  isEditMode.value = false;
  currentEditingNote.value = null;
  newNoteContent.value = '';
};

const handleNoteClick = (note: StudentNoteItem) => {
  if (note.type === NoteType.FREE) {
    // 编辑笔记
    if (showAddPopup.value) {
      showAddPopup.value = false;
      // 使用 nextTick 确保 DOM 更新后再重新打开
      nextTick(() => {
        isEditMode.value = true;
        currentEditingNote.value = note;
        newNoteContent.value = note.noteContent;
        textareaKey.value++; // 强制重新渲染 textarea
        showAddPopup.value = true;
      });
    } else {
      isEditMode.value = true;
      currentEditingNote.value = note;
      newNoteContent.value = note.noteContent;
      textareaKey.value++; // 强制重新渲染 textarea
      showAddPopup.value = true;
    }
  } else if (note.type === NoteType.EXERCISE) {
    if (note.homeworkId !== 0) {
      uni.navigateTo({
        url: `/pages-subpackages/students/homework/detail?id=${note.homeworkId}&locateQuestionId=${note.questionId}`,
        fail: error => {
          uni.showToast({
            title: '跳转失败，请重试',
            icon: 'none',
          });
        },
      });
    }
  } else if (note.type === NoteType.STUDY) {
    if (note.resourceId !== 0 && note.homeworkId === 0) {
      uni.navigateTo({
        url: `/pages-subpackages/students/learning-resource/resource-details?id=${note.resourceId}&type=note&noteId=${note.id}&noteContent=${encodeURIComponent(note.noteContent)}`,
        fail: error => {
          uni.showToast({
            title: '跳转失败，请重试',
            icon: 'none',
          });
        },
      });
    }
  }
};
</script>

<template>
  <view class="notes-container">
    <u-navbar :autoBack="true" placeholder bg-color="#fff">
      <template #center>
        <view class="navbar-title">我的笔记</view>
      </template>
      <template #right>
        <view
          class="right-text"
          @click="
            isEditMode = false;
            currentEditingNote = null;
            newNoteContent = '';
            textareaKey++;
            showAddPopup = true;
          "
          >添加笔记</view
        >
      </template>
    </u-navbar>

    <u-tabs
      class="tabs"
      :list="tabsList"
      :current="currentTab"
      :scrollable="true"
      lineColor="transparent"
      @click="handleTabClick"
      itemStyle="padding: 10rpx 20rpx; height: 80rpx; min-width: 160rpx;"
    >
      <template #default="{ index }">
        <view class="tab-item" :class="{ active: currentTab === index }">
          <text class="tab-text">{{ tabsList[index].name }}</text>
        </view>
      </template>
    </u-tabs>

    <view class="line"></view>

    <!-- 笔记列表 -->
    <scroll-view class="notes-list" scroll-y :style="{ height: '100vh' }">
      <view v-for="(note, index) in notesList" :key="note.id" class="note-item-wrapper">
        <up-swipe-action>
          <up-swipe-action-item
            :options="getSwipeOptions(note, index)"
            :auto-close="true"
            @click="e => handleSwipeClick(e.index, note, index)"
          >
            <view class="note-item" @click="handleNoteClick(note)">
              <view class="note-content">
                <view class="note-date">{{ formatDateTime(note.updateTime) }}</view>
                <view class="note-title">{{
                  note.noteContent
                    ? note.noteContent.substring(0, 30) +
                      (note.noteContent.length > 30 ? '...' : '')
                    : '无标题'
                }}</view>

                <view v-if="note.refTitle" class="note-preview">
                  {{ note.refTitle.substring(0, 15) }}{{ note.refTitle.length > 15 ? '...' : '' }}
                </view>
              </view>
            </view>
          </up-swipe-action-item>
        </up-swipe-action>
      </view>

      <!-- 空状态 -->
      <view v-if="notesList.length === 0" class="empty-state">
        <u-empty mode="data" text="暂无笔记"></u-empty>
      </view>
    </scroll-view>

    <NotesPopup
      v-model:show="showAddPopup"
      v-model:noteContent="newNoteContent"
      :isEditMode="isEditMode"
      @save="saveNote"
      @close="handlePopupClose"
    />

    <DeletePopup ref="deletePopupRef" @confirm="handleDeleteConfirm" @cancel="handleDeleteCancel" />

    <LkToast ref="uToastRef" />
  </view>
</template>

<style lang="scss" scoped>
.notes-container {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1d2129;
}

.right-text {
  font-size: 26rpx;
  color: #7d4dff;
  padding: 10rpx 24rpx;
  border: 1px solid #7d4dff;
  border-radius: 200rpx;
}

.line {
  width: 100%;
  height: 1px;
  background-color: #eee;
}

.notes-list {
  flex: 1;
  height: calc(100vh - 176rpx);
}

.note-item-wrapper {
  overflow: hidden;
  // margin-bottom: 20rpx;
}

/* 滑动操作样式 */
:deep(.up-swipe-action) {
  border-radius: 16rpx;
  overflow: hidden;
  background-color: transparent;
}

:deep(.up-swipe-action-item) {
  border-radius: 16rpx;
  overflow: hidden;
}

/* 滑动按钮容器样式 */
:deep(.up-swipe-action__right) {
  display: flex;
  height: 100%;
  border-radius: 0 16rpx 16rpx 0;
  overflow: hidden;
}

/* 滑动按钮通用样式 */
:deep(.up-swipe-action__button) {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx !important;
  color: #ffffff !important;
  border-radius: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 120rpx !important;
  height: 100% !important;
  border: none !important;
}

/* 编辑按钮样式 */
:deep(.up-swipe-action__button:first-child) {
  background-color: #7d4dff !important;
}

/* 删除按钮样式 */
:deep(.up-swipe-action__button:last-child) {
  background-color: #d54941 !important;
}

.note-item {
  background-color: #ffffff;
  padding: 30rpx;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1rpx solid #e5e6eb;
}

.note-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;

  .note-date {
    font-size: 28rpx;
    color: #86909c;
    margin-bottom: 16rpx;
  }

  .note-title {
    font-size: 32rpx;
    color: #1d2129;
    line-height: 1.5;
    margin-bottom: 16rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .note-preview {
    width: 100%;
    height: 68rpx;
    background-color: #f7f9ff;
    line-height: 68rpx;
    font-size: 16px;
    font-weight: 500;
    padding: 0 12rpx;
    border-radius: 12rpx;
  }
}

.note-quote {
  background-color: #f7f9ff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 16rpx;
}

.quote-content {
  font-size: 28rpx;
  color: #1d2129;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.note-duration {
  position: absolute;
  bottom: 30rpx;
  right: 30rpx;
  background-color: #f56c6c;
  color: #ffffff;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 220rpx);
  width: 100%;
}

.edit-popup {
  padding: 0;
  background-color: #ffffff;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.edit-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.edit-popup-cancel {
  padding: 10rpx 20rpx;
}

.cancel-text {
  font-size: 32rpx;
  color: #86909c;
}

.tabs {
  background-color: #fff;
  box-sizing: border-box;
  padding: 0 20rpx;
  margin: 16rpx 0;

  :deep(.uni-scroll-view-content) {
    display: flex;
    align-items: center;
    width: auto;
    min-width: 100%;
  }

  :deep(.u-tabs__wrapper__nav) {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
  }

  :deep(.u-tabs__wrapper__nav__item) {
    flex-shrink: 0;
    padding: 6rpx 12rpx;
    min-width: 160rpx;
  }

  :deep(.u-tabs__wrapper__nav__line) {
    display: none;
  }
}

.tab-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 100rpx;
  transition: all 0.3s;
  background-color: rgba(143, 143, 143, 0.13);
  height: 80rpx;
  width: 100%;
  min-width: 140rpx;
  flex-shrink: 0;
}

.tab-item.active {
  background: linear-gradient(103deg, #f1f8ff 6.83%, #f3efff 90.91%);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab-text {
  font-size: 14px;
  color: #1d2129;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 30rpx;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 500;
}
</style>
