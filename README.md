# 华云天图APP

<div align="center">
  <img src="https://huayun-ai-obs-public.huayuntiantu.com/af8e099f-e740-43ef-aa04-52c9dc02eda5.png" alt="华云天图" width="200"/>

  <h3>基于AI的智能应用平台</h3>

  [![Vue](https://img.shields.io/badge/Vue-3.4.21-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-4.9.4-3178C6?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
  [![UniApp](https://img.shields.io/badge/UniApp-3.0.0-2B2B2B?style=flat-square&logo=dcloud)](https://uniapp.dcloud.io/)
  [![Vite](https://img.shields.io/badge/Vite-5.2.8-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
  [![Pinia](https://img.shields.io/badge/Pinia-2.1.7-FFD859?style=flat-square&logo=pinia)](https://pinia.vuejs.org/)
</div>

## 📖 项目简介

华云天图是一个基于AI技术的智能应用平台，提供丰富的AI应用和数据处理能力。项目采用uniapp框架开发，支持H5、小程序、iOS和Android多平台部署，为用户提供统一的跨平台体验。

### ✨ 核心功能

- 🤖 **AI聊天对话** - 智能对话系统，支持多轮对话和上下文理解
- 🏪 **应用中心** - 丰富的AI应用商店，支持应用创建、编辑和管理
- 📊 **数据空间** - 数据管理和处理平台
- 👤 **个人中心** - 用户信息管理、历史记录、设置等
- 🔍 **智能搜索** - 全局搜索功能，快速找到所需内容
- 📱 **响应式设计** - 适配各种屏幕尺寸和设备类型

## 🛠️ 技术栈

### 核心框架
- **[UniApp](https://uniapp.dcloud.io/)** - 跨平台应用开发框架
- **[Vue 3](https://vuejs.org/)** - 渐进式JavaScript框架
- **[TypeScript](https://www.typescriptlang.org/)** - JavaScript的超集，提供类型安全
- **[Vite](https://vitejs.dev/)** - 下一代前端构建工具

### 状态管理与UI
- **[Pinia](https://pinia.vuejs.org/)** - Vue 3官方推荐的状态管理库
- **[uView Plus](https://uviewui.com/)** - 全面兼容Vue3的uni-app生态UI框架

### 工具库
- **[Day.js](https://dayjs.gitee.io/)** - 轻量级日期处理库
- **[Crypto-js](https://cryptojs.gitee.io/)** - 加密算法库
- **[Lottie](https://airbnb.design/lottie/)** - 动画渲染库

## 📁 项目结构

```
src/
├── api/                      # API接口模块
│   ├── index.js              # API入口文件
│   └── modules/              # API模块
│       ├── user.js           # 用户相关API
│       ├── common.js         # 通用API
│       ├── chat.js           # 聊天相关API
│       └── my.js             # 个人中心API
├── common/                   # 通用工具
│   ├── config/               # 配置文件
│   │   └── index.js          # 环境配置
│   ├── http/                 # HTTP请求模块
│   │   ├── request.js        # 请求封装
│   │   └── token.js          # token管理
│   └── ai/                   # AI相关工具
├── components/               # 全局组件
│   ├── TabbarLayout.vue      # 自定义Tabbar布局
│   ├── LkImage/              # 图片组件（支持缓存）
│   ├── LkButton/             # 按钮组件
│   ├── LkNavBar/             # 导航栏组件
│   └── chat/                 # 聊天相关组件
├── pages/                    # 主包页面
│   ├── index/                # 主页面（Tab容器）
│   ├── home/                 # 首页
│   ├── app-center/           # 应用中心
│   ├── data-space/           # 数据空间
│   ├── my/                   # 个人中心
│   ├── chat/                 # 聊天页面
│   └── search/               # 搜索页面
├── pages-subpackages/        # 分包页面
│   ├── auth-pkg/             # 认证相关页面
│   ├── app-center-pkg/       # 应用中心子页面
│   └── my-pkg/               # 个人中心子页面
├── router/                   # 路由系统
│   ├── config.ts             # 路由配置（TabBar配置、角色配置映射）
│   ├── utils.ts              # 路由工具类和核心路由系统
│   └── index.ts              # 统一导出接口
├── store/                    # Pinia状态管理
│   ├── systemStore.ts        # 系统状态
│   ├── userStore.ts          # 用户状态
│   ├── chatStore.ts          # 聊天状态
│   ├── tabStore.ts           # Tab状态
│   └── useAppCenterStore.ts  # 应用中心状态
├── types/                    # TypeScript类型定义
│   ├── api.d.ts              # API类型声明
│   ├── http.d.ts             # HTTP请求类型声明
│   └── uni.d.ts              # uni类型扩展
├── utils/                    # 工具函数
├── styles/                   # 全局样式
├── static/                   # 静态资源
└── fastgpt/                  # FastGPT相关模块
```

## 🚀 快速开始

### 环境要求

- **Node.js** >= 16.0.0
- **pnpm** >= 7.0.0 (推荐) 或 npm >= 8.0.0
- **HBuilderX** 3.0+ (用于App打包)

### 安装依赖

```bash
# 使用pnpm安装依赖（推荐）
pnpm install

# 或使用npm
npm install
```

### 开发环境启动

```bash
# H5开发（推荐，无Sass警告）
pnpm dev:h5:nowarning

# H5开发（标准模式）
pnpm dev:h5

# 微信小程序
pnpm dev:mp-weixin

# App开发
pnpm dev:app

# 其他平台
pnpm dev:mp-alipay    # 支付宝小程序
pnpm dev:mp-baidu     # 百度小程序
pnpm dev:mp-qq        # QQ小程序
```

### 生产环境构建

```bash
# H5构建
pnpm build:h5

# 微信小程序构建
pnpm build:mp-weixin

# App构建
pnpm build:app
```

## 🏗️ 核心架构

### HTTP请求封装

HTTP请求封装基于uView Plus的HTTP模块，实现了以下功能：

1. **请求拦截** - 自动添加token到请求头
2. **响应拦截** - 统一处理后端返回的数据格式
3. **错误处理** - 统一处理错误信息，包括401、500等状态码
4. **Token刷新** - 当token过期时，自动刷新token并重试请求
5. **请求参数适配** - 支持各种请求方式和参数传递

### API组织结构

API按业务模块进行组织，主要包括：

1. **用户模块 (user)** - 登录、注册、密码修改等
2. **通用模块 (common)** - 文件上传、系统配置等
3. **聊天模块 (chat)** - AI对话、消息管理等
4. **个人中心模块 (my)** - 收藏、消息、设置等

### API使用方式

API调用有三种方式：

#### 1. 组件选项API
```javascript
export default {
  methods: {
    async fetchData() {
      try {
        const res = await this.$api.user.getUserInfo();
        this.userInfo = res;
      } catch (error) {
        console.error(error);
      }
    }
  }
}
```

#### 2. Composition API + getCurrentInstance
```javascript
import { getCurrentInstance } from 'vue';

export default {
  setup() {
    const { proxy } = getCurrentInstance();

    async function fetchData() {
      try {
        const res = await proxy.$api.user.getUserInfo();
        // 处理数据
      } catch (error) {
        console.error(error);
      }
    }

    return { fetchData };
  }
}
```

#### 3. Inject注入方式
```javascript
import { inject } from 'vue';

export default {
  setup() {
    const $api = inject('$api');

    async function fetchData() {
      try {
        const res = await $api.user.getUserInfo();
        // 处理数据
      } catch (error) {
        console.error(error);
      }
    }

    return { fetchData };
  }
}
```

### 路由系统

项目采用简化的路由系统，基于用户模式进行动态路由管理：

#### 🏗️ 架构设计
- **config.ts** - 纯配置文件，包含TabBar配置和角色配置映射
- **utils.ts** - 核心路由系统，包含SimpleRouter类的所有功能
- **index.ts** - 统一导出接口

#### 🎯 核心功能
- **用户模式管理** - 支持教师/学生模式切换
- **动态TabBar** - 根据用户模式动态显示TabBar
- **登录处理** - 统一的登录成功/退出处理
- **工具方法** - 路径获取、参数解析、URL构建等

#### 💻 使用示例
```typescript
import { SimpleRouter, RouteConfigManager } from '@/router';

// 基础工具方法
const currentPath = SimpleRouter.getCurrentPath();
const url = SimpleRouter.buildUrl('/pages/test', { id: 123 });

// 页面路径获取（从配置项中获取）
const homePage = SimpleRouter.getHomePage('teacher');
const loginPage = SimpleRouter.getLoginPage('teacher');

// 用户模式管理
await SimpleRouter.switchMode('teacher');
await SimpleRouter.loginSuccess();
await SimpleRouter.logout();

// 配置获取
const tabBarConfig = RouteConfigManager.getTabBarConfig('teacher');
```

### 状态管理 (Pinia)

项目使用Pinia进行状态管理，主要Store包括：

- **systemStore** - 系统配置、设备信息等
- **userStore** - 用户信息、认证状态等
- **chatStore** - 聊天记录、对话状态等
- **tabStore** - Tab切换状态管理
- **appCenterStore** - 应用中心数据缓存

### Token刷新机制

当接口返回401、403、406状态码时，会自动触发token刷新机制：

1. 从本地存储获取refreshToken
2. 调用刷新token接口获取新的token
3. 更新本地存储中的token
4. 使用新token重试之前失败的请求
5. 如果refreshToken也失效，则跳转到登录页面

## 🎯 核心特性

### 图片缓存功能

项目实现了智能图片缓存机制，提升用户体验：

- ✅ **平台兼容** - 支持Android和iOS平台
- ✅ **条件编译** - 使用uniapp条件编译，web端不启用缓存
- ✅ **精准缓存** - 仅缓存"我的应用"部分的avatarUrl图片
- ✅ **自动管理** - 自动处理缓存更新和清理

### 自定义动态Tabbar

基于用户模式的动态Tabbar系统：

- 🎯 **模式驱动** - 根据用户模式（教师/学生）动态显示Tab项
- 🎨 **自定义样式** - 完全自定义的UI设计
- 📱 **响应式** - 适配不同屏幕尺寸
- ⚡ **高性能** - 优化的渲染机制

### AI聊天系统

强大的AI对话功能：

- 🤖 **多模型支持** - 支持多种AI模型
- 💬 **流式对话** - 实时流式响应
- 📝 **上下文记忆** - 保持对话上下文
- 🔄 **工作流支持** - 集成FastGPT工作流

## 🛠️ 开发指南

### 代码规范

项目使用以下代码规范工具：

```bash
# 代码格式化
pnpm format

# 类型检查
pnpm type-check

# Git提交前自动格式化
# 已配置husky + lint-staged
```

### 环境配置

项目支持多环境配置：

```javascript
// src/common/config/index.js
const config = {
  development: {
    baseUrl: 'http://gpt-pre.hwzxs.com',
  },
  production: {
    baseUrl: 'https://ai.huayuntiantu.com',
  }
};
```

## ⚠️ Sass警告处理方案

项目使用了uview-plus组件库，该库使用了旧版的Sass @import语法，在新版Sass编译时会产生大量废弃警告。这些警告不影响项目功能，但会影响开发体验。

### 🎯 推荐解决方案

使用增强版无警告启动脚本：

```bash
# 无警告启动（推荐）
pnpm dev:h5:nowarning
```

### 🔧 技术实现

1. **sass-filter.js脚本** - 使用正则表达式精确匹配并过滤Sass相关警告
2. **Vite配置优化** - 在vite.config.ts中配置quietDeps选项
3. **多重过滤机制** - 结合console.warn重写和进程输出过滤

### 📋 可用启动命令

```bash
# 标准启动（会显示Sass警告）
pnpm dev:h5

# 无警告启动（推荐）
pnpm dev:h5:nowarning

# 其他启动方式
pnpm dev:h5:clean    # 使用旧版过滤脚本
pnpm dev:h5:silent   # 使用环境变量方式
```

## 📱 自定义动态Tabbar详解

### 🎯 设计理念

基于用户模式的动态Tabbar系统，根据用户模式（教师/学生）智能显示Tab选项。

### 🏗️ 实现原理

1. **移除原生配置** - 不使用pages.json中的tabBar配置
2. **模式驱动** - 通过用户模式动态控制Tab项显示
3. **组件化设计** - 使用全局组件注册，简化使用

### 📦 组件结构

- **TabbarLayout.vue** - Tabbar布局容器
- **components/index.ts** - 组件导出和全局注册

### 🎯 模式控制

#### TabbarItem接口定义
```typescript
interface TabbarItem {
  pagePath: string;        // 页面路径
  text: string;           // 显示文本
  iconPath: string;       // 默认图标
  selectedIconPath: string; // 选中图标
}
```

#### 用户模式
- **`teacher`** - 教师模式，显示教师相关Tab
- **`student`** - 学生模式，显示学生相关Tab

### 💻 使用示例

#### 基础使用
```vue
<template>
  <tabbar-layout>
    <view class="container">
      <!-- 页面内容 -->
    </view>
  </tabbar-layout>
</template>
```

#### 添加新Tab项
```javascript
const allTabbarItems: TabbarItem[] = [
  {
    pagePath: '/pages/new-tab/index',
    text: '新功能',
    iconPath: '/static/tabbar/new.png',
    selectedIconPath: '/static/tabbar/new-active.png',
  }
];
```

#### 模式配置
```typescript
import { SimpleRouter, RouteConfigManager } from '@/router';

// 用户模式设置和切换
await SimpleRouter.switchMode('teacher'); // 或 'student'

// 获取当前用户模式
const userMode = uni.getStorageSync('userMode');

// 根据模式获取TabBar配置
const tabBarConfig = RouteConfigManager.getTabBarConfig(userMode);

// 获取模式对应的首页路径
const homePage = SimpleRouter.getHomePage(userMode);
```

## 🔧 配置说明

### 应用配置

```json
{
  "name": "华云天图",
  "appid": "__UNI__3402EE1",
  "versionName": "1.0.0",
  "versionCode": "100"
}
```

### 路由系统特性

#### 🎯 简化设计
- **单一职责** - 每个文件都有明确的职责
- **配置驱动** - 页面路径从配置项中获取
- **模式切换** - 支持教师/学生模式无缝切换
- **工具集成** - 路由工具和核心功能集成在一个类中

#### 🔧 核心方法
```typescript
// SimpleRouter 类提供的核心方法
class SimpleRouter {
  // 基础工具方法
  static getCurrentPath(): string
  static getCurrentParams(): Record<string, any>
  static buildUrl(path: string, params?: Record<string, any>): string
  static parseUrlParams(url: string): Record<string, string>

  // 页面路径获取（从配置项中获取）
  static getHomePage(userMode?: UserMode): string
  static getLoginPage(userMode?: UserMode): string

  // 用户模式管理
  static switchMode(mode: UserMode): Promise<{ success: boolean; message?: string }>
  static loginSuccess(): Promise<{ success: boolean; message?: string }>
  static logout(): Promise<{ success: boolean; message?: string }>

  // 返回操作
  static back(): Promise<boolean>
}
```

### 平台支持

- ✅ **H5** - 支持现代浏览器
- ✅ **微信小程序** - 完整功能支持
- ✅ **支付宝小程序** - 基础功能支持
- ✅ **Android App** - 原生性能
- ✅ **iOS App** - 原生性能

### 权限配置

#### Android权限
- 网络访问
- 相机使用
- 文件读写
- 设备信息获取

#### iOS权限
- 相册访问
- 相机使用
- 麦克风使用
- 位置信息

## 🚀 部署指南

### H5部署

```bash
# 构建H5版本
pnpm build:h5

# 构建文件位于 dist/build/h5
# 可直接部署到静态服务器
```

### 小程序部署

```bash
# 微信小程序
pnpm build:mp-weixin
# 使用微信开发者工具打开 dist/build/mp-weixin

# 支付宝小程序
pnpm build:mp-alipay
# 使用支付宝开发者工具打开 dist/build/mp-alipay
```

### App部署

```bash
# 构建App资源
pnpm build:app

# 使用HBuilderX打开项目
# 选择发行 -> 原生App-云打包
```

## 🤝 贡献指南

### 开发流程

1. **Fork项目** - 创建项目副本
2. **创建分支** - `git checkout -b feature/new-feature`
3. **提交代码** - `git commit -m 'Add new feature'`
4. **推送分支** - `git push origin feature/new-feature`
5. **创建PR** - 提交Pull Request

### 代码规范

- 使用TypeScript进行类型安全开发
- 遵循Vue 3 Composition API最佳实践
- 使用Prettier进行代码格式化
- 提交前运行类型检查

### 提交规范

```bash
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📞 技术支持

### 问题反馈

- **Issue** - [GitLab Issues](https://gitlab.hwzxs.com/ai/huayuntiantu_app/-/issues)
- **邮箱** - <EMAIL>

### 文档资源

- **UniApp官方文档** - [https://uniapp.dcloud.io/](https://uniapp.dcloud.io/)
- **Vue 3文档** - [https://vuejs.org/](https://vuejs.org/)
- **uView Plus文档** - [https://uviewui.com/](https://uviewui.com/)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目的支持：

- [UniApp](https://uniapp.dcloud.io/) - 跨平台应用开发框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [uView Plus](https://uviewui.com/) - Vue3 uni-app生态UI框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Pinia](https://pinia.vuejs.org/) - Vue状态管理库

---

<div align="center">
  <p>Made with ❤️ by 华云天图团队</p>
  <p>© 2024 华云天图. All rights reserved.</p>
</div>
