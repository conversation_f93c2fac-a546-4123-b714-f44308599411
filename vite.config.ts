import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import vueClickToComponent from 'vue-click-to-component/vite-plugin'
import { Plugin } from 'vite';

// 保存原始的console.warn
const originalWarn = console.warn;

// 修改console.warn以过滤Sass废弃警告
console.warn = function (...args) {
  // 如果警告消息包含Sass或@import相关的废弃信息，则不输出
  const warningStr = args.join(' ');
  if (
    warningStr.includes('Sass') ||
    warningStr.includes('@import') ||
    warningStr.includes('deprecated')
  ) {
    return;
  }
  originalWarn.apply(console, args);
};

export default defineConfig({
  plugins: [
    uni(),
    {
      name: 'silence-sass-warnings',
      enforce: 'pre' as const,
      transform(code, id) {
        // 忽略所有Sass警告
        if (/\.(scss|sass)$/.test(id)) {
          return {
            code,
            map: null
          }
        }
      },
      configureServer(server) {
        // 过滤服务器输出的警告
        const originalPrint = server.printUrls;
        server.printUrls = () => {
          const originalWrite = process.stdout.write;
          process.stdout.write = (data) => {
            if (typeof data === 'string' &&
              (data.includes('Deprecation Warning') ||
                data.includes('Sass') ||
                data.includes('@import'))) {
              return true;
            }
            return originalWrite.call(process.stdout, data);
          };
          originalPrint();
          // 恢复原始输出函数
          setTimeout(() => {
            process.stdout.write = originalWrite;
          }, 100);
        };
      }
    }
  ],
  css: {
    preprocessorOptions: {
      scss: {
        quietDeps: true,
        // 禁止Sass对第三方库的废弃警告
        additionalData: `$suppress-deprecation-warnings: true;`
      }
    }
  },
  server: {
    port: 5173,
    proxy: {
      '/huayun-ai': {
        target: 'http://gpt-pre.hwzxs.com',
        //生产环境 https://ai.huayuntiantu.com
        changeOrigin: true,
        secure: false,
        // 如果后端接口有统一前缀，可以重写路径
        // rewrite: (path) => path.replace(/^\/huayun-ai/, '')
      },
      '/ai-resource': {
        target: 'http://gpt-pre.hwzxs.com',
        changeOrigin: true,
        secure: false,
      },
      '/ai-homework': {
        target: 'http://gpt-pre.hwzxs.com',
        changeOrigin: true,
        secure: false,
      },
      '/huayun-tool': {
        target: 'http://gpt-pre.hwzxs.com',
        changeOrigin: true,
        secure: false,
      },
      '/ai-videos': {
        // target: 'https://ali-video.hwzxs.com',
        target: 'http://gpt-pre.hwzxs.com',
        changeOrigin: true,
        secure: false,
      },
      '/ai-notice': {
        target: 'https://gpt-pre.hwzxs.com',
        changeOrigin: true,
        secure: false,
      }
    }
  }
});
